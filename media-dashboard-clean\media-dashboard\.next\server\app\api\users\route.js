/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/route";
exports.ids = ["app/api/users/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/users/route.ts */ \"(rsc)/./src/app/api/users/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/route\",\n        pathname: \"/api/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/route\"\n    },\n    resolvedPagePath: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZ1c2VycyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGdXNlcnMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZ1c2VycyUyRnJvdXRlLnRzJmFwcERpcj1EJTNBJTVDRG9jJTIwZGF0YWJhc2UlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUNtZWRpYS1kYXNoYm9hcmQlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUQlM0ElNUNEb2MlMjBkYXRhYmFzZSU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1Q21lZGlhLWRhc2hib2FyZCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDd0M7QUFDckg7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHVzZXJzXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS91c2Vycy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3VzZXJzXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS91c2Vycy9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHVzZXJzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   addUser: () => (/* binding */ addUser)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// قائمة المستخدمين المشتركة - نفس القائمة الموجودة في users/route.ts\nlet users = [\n    {\n        id: '1',\n        username: 'admin',\n        password: 'admin123',\n        name: 'مدير النظام الرئيسي',\n        email: '<EMAIL>',\n        role: 'ADMIN',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        lastLogin: null,\n        permissions: [\n            'ALL'\n        ]\n    },\n    {\n        id: '2',\n        username: 'media_manager',\n        password: 'media123',\n        name: 'أحمد محمد - مدير المحتوى',\n        email: '<EMAIL>',\n        role: 'MEDIA_MANAGER',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        lastLogin: null,\n        permissions: [\n            'MEDIA_CREATE',\n            'MEDIA_READ',\n            'MEDIA_UPDATE',\n            'MEDIA_DELETE'\n        ]\n    },\n    {\n        id: '3',\n        username: 'scheduler',\n        password: 'schedule123',\n        name: 'فاطمة علي - مجدولة البرامج',\n        email: '<EMAIL>',\n        role: 'SCHEDULER',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        lastLogin: null,\n        permissions: [\n            'SCHEDULE_CREATE',\n            'SCHEDULE_READ',\n            'SCHEDULE_UPDATE',\n            'SCHEDULE_DELETE',\n            'MEDIA_READ'\n        ]\n    },\n    {\n        id: '4',\n        username: 'viewer',\n        password: 'view123',\n        name: 'محمد سالم - مستخدم عرض',\n        email: '<EMAIL>',\n        role: 'VIEWER',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        lastLogin: null,\n        permissions: [\n            'MEDIA_READ',\n            'SCHEDULE_READ'\n        ]\n    }\n];\n// دالة للحصول على المستخدمين مع المستخدمين الجدد\nfunction getUsers() {\n    // محاولة الحصول على المستخدمين الجدد من الذاكرة المشتركة\n    try {\n        const usersModule = __webpack_require__(Object(function webpackMissingModule() { var e = new Error(\"Cannot find module '../../../users/route'\"); e.code = 'MODULE_NOT_FOUND'; throw e; }()));\n        if (usersModule.users && Array.isArray(usersModule.users)) {\n            return usersModule.users;\n        }\n    } catch (error) {\n        console.log('استخدام المستخدمين الافتراضيين');\n    }\n    return users;\n}\n// دالة لإضافة مستخدم جديد\nfunction addUser(newUser) {\n    users.push(newUser);\n}\nasync function POST(request) {\n    try {\n        const { username, password } = await request.json();\n        if (!username || !password) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يرجى إدخال اسم المستخدم وكلمة المرور'\n            }, {\n                status: 400\n            });\n        }\n        // الحصول على قائمة المستخدمين المحدثة\n        const users = getUsers();\n        // البحث عن المستخدم مع التحقق من كلمة المرور\n        const user = users.find((u)=>u.username === username && u.password === password);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المستخدم أو كلمة المرور غير صحيحة'\n            }, {\n                status: 401\n            });\n        }\n        // إنشاء token مؤقت (في التطبيق الحقيقي استخدم JWT)\n        const token = `token_${user.id}_${Date.now()}`;\n        // إرجاع بيانات المستخدم (بدون كلمة المرور)\n        const { password: _, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: userWithoutPassword,\n            token,\n            message: 'تم تسجيل الدخول بنجاح'\n        });\n    } catch (error) {\n        console.error('Login error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في الخادم'\n        }, {\n            status: 500\n        });\n    }\n}\n// API للحصول على معلومات المستخدم الحالي\nasync function GET(request) {\n    try {\n        const token = request.headers.get('Authorization')?.replace('Bearer ', '');\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'غير مصرح'\n            }, {\n                status: 401\n            });\n        }\n        // استخراج معرف المستخدم من التوكن (مؤقت)\n        const userId = token.split('_')[1];\n        const user = users.find((u)=>u.id === userId);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'مستخدم غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        const { password: _, ...userWithoutPassword } = user;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: userWithoutPassword\n        });\n    } catch (error) {\n        console.error('Get user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في الخادم'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/users/route.ts":
/*!************************************!*\
  !*** ./src/app/api/users/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   ROLES: () => (/* binding */ ROLES),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// تعريف الأدوار والصلاحيات\nconst ROLES = {\n    ADMIN: {\n        name: 'مدير النظام',\n        permissions: [\n            'ALL'\n        ],\n        description: 'صلاحيات كاملة لجميع أجزاء النظام'\n    },\n    MEDIA_MANAGER: {\n        name: 'مدير المحتوى',\n        permissions: [\n            'MEDIA_CREATE',\n            'MEDIA_READ',\n            'MEDIA_UPDATE',\n            'MEDIA_DELETE'\n        ],\n        description: 'إدارة المواد الإعلامية (إضافة، تعديل، حذف)'\n    },\n    SCHEDULER: {\n        name: 'مجدول البرامج',\n        permissions: [\n            'SCHEDULE_CREATE',\n            'SCHEDULE_READ',\n            'SCHEDULE_UPDATE',\n            'SCHEDULE_DELETE',\n            'MEDIA_READ'\n        ],\n        description: 'إدارة الجداول الإذاعية والخريطة البرامجية'\n    },\n    VIEWER: {\n        name: 'مستخدم عرض',\n        permissions: [\n            'MEDIA_READ',\n            'SCHEDULE_READ'\n        ],\n        description: 'عرض المحتوى فقط بدون إمكانية التعديل'\n    }\n};\n// بيانات المستخدمين المؤقتة\nlet users = [\n    {\n        id: '1',\n        username: 'admin',\n        password: 'admin123',\n        name: 'مدير النظام الرئيسي',\n        email: '<EMAIL>',\n        role: 'ADMIN',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        lastLogin: null\n    },\n    {\n        id: '2',\n        username: 'media_manager',\n        password: 'media123',\n        name: 'أحمد محمد - مدير المحتوى',\n        email: '<EMAIL>',\n        role: 'MEDIA_MANAGER',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        lastLogin: null\n    },\n    {\n        id: '3',\n        username: 'scheduler',\n        password: 'schedule123',\n        name: 'فاطمة علي - مجدولة البرامج',\n        email: '<EMAIL>',\n        role: 'SCHEDULER',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        lastLogin: null\n    },\n    {\n        id: '4',\n        username: 'viewer',\n        password: 'view123',\n        name: 'محمد سالم - مستخدم عرض',\n        email: '<EMAIL>',\n        role: 'VIEWER',\n        isActive: true,\n        createdAt: new Date().toISOString(),\n        lastLogin: null\n    }\n];\n// GET - الحصول على قائمة المستخدمين\nasync function GET(request) {\n    try {\n        const url = new URL(request.url);\n        const role = url.searchParams.get('role');\n        let filteredUsers = users;\n        if (role) {\n            filteredUsers = users.filter((user)=>user.role === role);\n        }\n        // إزالة كلمات المرور من النتائج\n        const usersWithoutPasswords = filteredUsers.map((user)=>{\n            const { password, ...userWithoutPassword } = user;\n            return {\n                ...userWithoutPassword,\n                roleInfo: ROLES[user.role]\n            };\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            users: usersWithoutPasswords,\n            roles: ROLES,\n            totalUsers: filteredUsers.length\n        });\n    } catch (error) {\n        console.error('Get users error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب بيانات المستخدمين'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مستخدم جديد\nasync function POST(request) {\n    try {\n        const userData = await request.json();\n        const { username, password, name, email, role } = userData;\n        // التحقق من البيانات المطلوبة\n        if (!username || !password || !name || !role) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'جميع الحقول مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من عدم وجود اسم المستخدم مسبقاً\n        if (users.find((user)=>user.username === username)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المستخدم موجود مسبقاً'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من صحة الدور\n        if (!ROLES[role]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'دور المستخدم غير صحيح'\n            }, {\n                status: 400\n            });\n        }\n        // إنشاء المستخدم الجديد\n        const newUser = {\n            id: Date.now().toString(),\n            username,\n            password,\n            name,\n            email: email || '',\n            role,\n            isActive: true,\n            createdAt: new Date().toISOString(),\n            lastLogin: null\n        };\n        users.push(newUser);\n        // إضافة المستخدم لملف تسجيل الدخول أيضاً\n        try {\n            const authModule = __webpack_require__(/*! ../auth/login/route */ \"(rsc)/./src/app/api/auth/login/route.ts\");\n            if (authModule.addUser) {\n                authModule.addUser(newUser);\n            }\n        } catch (error) {\n            console.log('تعذر مزامنة المستخدم مع ملف تسجيل الدخول');\n        }\n        // إرجاع المستخدم بدون كلمة المرور\n        const { password: _, ...userWithoutPassword } = newUser;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                ...userWithoutPassword,\n                roleInfo: ROLES[role]\n            },\n            message: 'تم إنشاء المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Create user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في إنشاء المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث مستخدم\nasync function PUT(request) {\n    try {\n        const url = new URL(request.url);\n        const userId = url.searchParams.get('id');\n        const userData = await request.json();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المستخدم مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const userIndex = users.findIndex((user)=>user.id === userId);\n        if (userIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // تحديث بيانات المستخدم\n        users[userIndex] = {\n            ...users[userIndex],\n            ...userData,\n            id: userId // التأكد من عدم تغيير المعرف\n        };\n        const { password: _, ...userWithoutPassword } = users[userIndex];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                ...userWithoutPassword,\n                roleInfo: ROLES[users[userIndex].role]\n            },\n            message: 'تم تحديث المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Update user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في تحديث المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مستخدم\nasync function DELETE(request) {\n    try {\n        const url = new URL(request.url);\n        const userId = url.searchParams.get('id');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المستخدم مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const userIndex = users.findIndex((user)=>user.id === userId);\n        if (userIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // منع حذف المدير الرئيسي\n        if (users[userIndex].role === 'ADMIN' && users[userIndex].id === '1') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكن حذف المدير الرئيسي'\n            }, {\n                status: 403\n            });\n        }\n        users.splice(userIndex, 1);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Delete user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في حذف المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/users/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();