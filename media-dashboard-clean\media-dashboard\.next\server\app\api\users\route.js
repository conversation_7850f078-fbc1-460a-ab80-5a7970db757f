/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/route";
exports.ids = ["app/api/users/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/users/route.ts */ \"(rsc)/./src/app/api/users/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/route\",\n        pathname: \"/api/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/route\"\n    },\n    resolvedPagePath: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/users/route.ts":
/*!************************************!*\
  !*** ./src/app/api/users/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   ROLES: () => (/* binding */ ROLES)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// مسار ملف بيانات المستخدمين\nconst USERS_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'users-data.json');\n// تعريف الأدوار والصلاحيات\nconst ROLES = {\n    ADMIN: {\n        name: 'مدير النظام',\n        permissions: [\n            'ALL'\n        ],\n        description: 'صلاحيات كاملة لجميع أجزاء النظام'\n    },\n    MEDIA_MANAGER: {\n        name: 'مدير المحتوى',\n        permissions: [\n            'MEDIA_CREATE',\n            'MEDIA_READ',\n            'MEDIA_UPDATE',\n            'MEDIA_DELETE'\n        ],\n        description: 'إدارة المواد الإعلامية (إضافة، تعديل، حذف)'\n    },\n    SCHEDULER: {\n        name: 'مجدول البرامج',\n        permissions: [\n            'SCHEDULE_CREATE',\n            'SCHEDULE_READ',\n            'SCHEDULE_UPDATE',\n            'SCHEDULE_DELETE',\n            'MEDIA_READ'\n        ],\n        description: 'إدارة الجداول الإذاعية والخريطة البرامجية'\n    },\n    VIEWER: {\n        name: 'مستخدم عرض',\n        permissions: [\n            'MEDIA_READ',\n            'SCHEDULE_READ'\n        ],\n        description: 'عرض المحتوى فقط بدون إمكانية التعديل'\n    }\n};\n// دالة لتحميل المستخدمين من الملف\nfunction loadUsers() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(USERS_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(USERS_FILE, 'utf8');\n            const users = JSON.parse(data);\n            return users;\n        } else {\n            return [];\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحميل المستخدمين:', error);\n        return [];\n    }\n}\n// دالة لحفظ المستخدمين في الملف\nfunction saveUsers(users) {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));\n        console.log(`💾 تم حفظ ${users.length} مستخدم في الملف`);\n    } catch (error) {\n        console.error('❌ خطأ في حفظ المستخدمين:', error);\n    }\n}\n// GET - الحصول على قائمة المستخدمين\nasync function GET(request) {\n    try {\n        const url = new URL(request.url);\n        const role = url.searchParams.get('role');\n        let filteredUsers = loadUsers();\n        if (role) {\n            filteredUsers = filteredUsers.filter((user)=>user.role === role);\n        }\n        // إزالة كلمات المرور من النتائج\n        const usersWithoutPasswords = filteredUsers.map((user)=>{\n            const { password, ...userWithoutPassword } = user;\n            return {\n                ...userWithoutPassword,\n                roleInfo: ROLES[user.role]\n            };\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            users: usersWithoutPasswords,\n            roles: ROLES,\n            totalUsers: filteredUsers.length\n        });\n    } catch (error) {\n        console.error('Get users error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب بيانات المستخدمين'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مستخدم جديد\nasync function POST(request) {\n    try {\n        const userData = await request.json();\n        const { username, password, name, email, role, phone } = userData;\n        // التحقق من البيانات المطلوبة\n        if (!username || !password || !name || !role) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'جميع الحقول مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // تحميل المستخدمين الحاليين\n        const users = loadUsers();\n        // التحقق من عدم وجود اسم المستخدم مسبقاً\n        if (users.find((user)=>user.username === username)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المستخدم موجود مسبقاً'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من صحة الدور\n        if (!ROLES[role]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'دور المستخدم غير صحيح'\n            }, {\n                status: 400\n            });\n        }\n        // إنشاء المستخدم الجديد\n        const newUser = {\n            id: Date.now().toString(),\n            username,\n            password,\n            name,\n            email: email || '',\n            phone: phone || '',\n            role,\n            isActive: true,\n            createdAt: new Date().toISOString(),\n            lastLogin: null\n        };\n        // إضافة المستخدم للقائمة وحفظها\n        users.push(newUser);\n        saveUsers(users);\n        // إرجاع المستخدم بدون كلمة المرور\n        const { password: _, ...userWithoutPassword } = newUser;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                ...userWithoutPassword,\n                roleInfo: ROLES[role]\n            },\n            message: 'تم إنشاء المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Create user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في إنشاء المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث مستخدم\nasync function PUT(request) {\n    try {\n        const url = new URL(request.url);\n        const userId = url.searchParams.get('id');\n        const userData = await request.json();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المستخدم مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // تحميل المستخدمين\n        const users = loadUsers();\n        const userIndex = users.findIndex((user)=>user.id === userId);\n        if (userIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // تحديث بيانات المستخدم\n        users[userIndex] = {\n            ...users[userIndex],\n            ...userData,\n            id: userId // التأكد من عدم تغيير المعرف\n        };\n        // حفظ التحديثات\n        saveUsers(users);\n        const { password: _, ...userWithoutPassword } = users[userIndex];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                ...userWithoutPassword,\n                roleInfo: ROLES[users[userIndex].role]\n            },\n            message: 'تم تحديث المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Update user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في تحديث المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مستخدم\nasync function DELETE(request) {\n    try {\n        const url = new URL(request.url);\n        const userId = url.searchParams.get('id');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المستخدم مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // تحميل المستخدمين\n        const users = loadUsers();\n        const userIndex = users.findIndex((user)=>user.id === userId);\n        if (userIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // منع حذف المدير الرئيسي\n        if (users[userIndex].role === 'ADMIN' && users[userIndex].id === '1') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكن حذف المدير الرئيسي'\n            }, {\n                status: 403\n            });\n        }\n        // حذف المستخدم\n        users.splice(userIndex, 1);\n        saveUsers(users);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Delete user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في حذف المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/users/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();