// ملف مشترك لإدارة المستخدمين - يضمن التزامن بين جميع APIs
import fs from 'fs';
import path from 'path';

// مسار ملف بيانات المستخدمين
const USERS_FILE = path.join(process.cwd(), 'users-data.json');

// تعريف الأدوار والصلاحيات
export const ROLES = {
  ADMIN: {
    name: 'مدير النظام',
    permissions: ['ALL'],
    description: 'صلاحيات كاملة لجميع أجزاء النظام'
  },
  MEDIA_MANAGER: {
    name: 'مدير المحتوى',
    permissions: ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE'],
    description: 'إدارة المواد الإعلامية (إضافة، تعديل، حذف)'
  },
  SCHEDULER: {
    name: 'مجدول البرامج',
    permissions: ['SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', 'MEDIA_READ'],
    description: 'إدارة الجداول الإذاعية والخريطة البرامجية'
  },
  VIEWER: {
    name: 'مستخدم عرض',
    permissions: ['MEDIA_READ', 'SCHEDULE_READ'],
    description: 'عرض المحتوى فقط بدون إمكانية التعديل'
  }
};

// المستخدمون الافتراضيون
const defaultUsers = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123',
    name: 'مدير النظام الرئيسي',
    email: '<EMAIL>',
    role: 'ADMIN',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '2',
    username: 'media_manager',
    password: 'media123',
    name: 'أحمد محمد - مدير المحتوى',
    email: '<EMAIL>',
    role: 'MEDIA_MANAGER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '3',
    username: 'scheduler',
    password: 'schedule123',
    name: 'فاطمة علي - مجدولة البرامج',
    email: '<EMAIL>',
    role: 'SCHEDULER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '4',
    username: 'viewer',
    password: 'view123',
    name: 'محمد سالم - مستخدم عرض',
    email: '<EMAIL>',
    role: 'VIEWER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  }
];

// قائمة المستخدمين في الذاكرة
let users: any[] = [];

// تحميل البيانات من الملف عند بدء التشغيل
function loadUsers() {
  try {
    if (fs.existsSync(USERS_FILE)) {
      const data = fs.readFileSync(USERS_FILE, 'utf8');
      users = JSON.parse(data);
      console.log(`👥 تم تحميل ${users.length} مستخدم من الملف`);
    } else {
      // إذا لم يكن الملف موجوداً، استخدم المستخدمين الافتراضيين
      users = [...defaultUsers];
      saveUsers(); // حفظ المستخدمين الافتراضيين
      console.log(`👥 تم إنشاء ملف المستخدمين مع ${users.length} مستخدم افتراضي`);
    }
  } catch (error) {
    console.error('❌ خطأ في تحميل المستخدمين:', error);
    users = [...defaultUsers];
  }
}

// حفظ البيانات في الملف
function saveUsers() {
  try {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
    console.log(`💾 تم حفظ ${users.length} مستخدم في الملف`);
  } catch (error) {
    console.error('❌ خطأ في حفظ المستخدمين:', error);
  }
}

// تحميل البيانات عند استيراد الملف
loadUsers();

// دالة للحصول على جميع المستخدمين
export function getAllUsers() {
  return users;
}

// دالة للحصول على مستخدم بالمعرف
export function getUserById(id: string) {
  return users.find(user => user.id === id);
}

// دالة للحصول على مستخدم باسم المستخدم
export function getUserByUsername(username: string) {
  return users.find(user => user.username === username);
}

// دالة لإضافة مستخدم جديد
export function addUser(newUser: any) {
  users.push(newUser);
  saveUsers(); // حفظ فوري
  console.log(`✅ تم إضافة مستخدم جديد: ${newUser.name} (المجموع: ${users.length})`);
  return true;
}

// دالة لتحديث مستخدم
export function updateUser(id: string, updatedUser: any) {
  const index = users.findIndex(user => user.id === id);
  if (index > -1) {
    users[index] = { ...users[index], ...updatedUser, id }; // التأكد من عدم تغيير المعرف
    saveUsers(); // حفظ فوري
    console.log(`✏️ تم تحديث المستخدم: ${updatedUser.name} (المعرف: ${id})`);
    return true;
  }
  return false;
}

// دالة لحذف مستخدم
export function deleteUser(id: string) {
  const index = users.findIndex(user => user.id === id);
  if (index > -1) {
    const removed = users.splice(index, 1)[0];
    saveUsers(); // حفظ فوري
    console.log(`🗑️ تم حذف المستخدم: ${removed.name} (المجموع: ${users.length})`);
    return true;
  }
  return false;
}

// دالة للتحقق من صحة تسجيل الدخول
export function validateLogin(username: string, password: string) {
  return users.find(user => user.username === username && user.password === password && user.isActive);
}
