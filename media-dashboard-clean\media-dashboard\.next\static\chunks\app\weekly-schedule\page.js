/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/weekly-schedule/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cweekly-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cweekly-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/weekly-schedule/page.tsx */ \"(app-pages-browser)/./src/app/weekly-schedule/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDd2Vla2x5LXNjaGVkdWxlJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcd2Vla2x5LXNjaGVkdWxlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cweekly-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WeeklySchedulePage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع\n    const days = [\n        'الأحد',\n        'الاثنين',\n        'الثلاثاء',\n        'الأربعاء',\n        'الخميس',\n        'الجمعة',\n        'السبت'\n    ];\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        const startDate = new Date(selectedWeek + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n        const dates = [];\n        console.log('📅 حساب تواريخ الأسبوع من:', selectedWeek);\n        for(let i = 0; i < 7; i++){\n            const date = new Date(startDate);\n            date.setDate(startDate.getDate() + i);\n            // استخدام الأرقام العربية العادية (1234567890) مع السنة\n            const dateStr = date.toLocaleDateString('en-GB', {\n                day: '2-digit',\n                month: '2-digit',\n                year: 'numeric'\n            });\n            dates.push(dateStr);\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(date.toISOString().split('T')[0], \" → \").concat(dateStr));\n        }\n        return dates;\n    };\n    const weekDates = getWeekDates();\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || 'غير معروف';\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - الجزء \".concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم:', today.toISOString().split('T')[0]);\n            console.log('  📊 يوم الأسبوع:', today.getDay(), '(0=أحد)');\n            const sunday = new Date(today);\n            sunday.setDate(today.getDate() - today.getDay());\n            const weekStart = sunday.toISOString().split('T')[0];\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current > 0) {\n                // تأخير قصير للتأكد من اكتمال الرندر\n                setTimeout({\n                    \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n                        window.scrollTo({\n                            top: scrollPositionRef.current,\n                            behavior: 'instant'\n                        });\n                        shouldRestoreScroll.current = false;\n                        console.log('📍 تم استعادة موضع التمرير:', scrollPositionRef.current);\n                    }\n                }[\"WeeklySchedulePage.useLayoutEffect\"], 100);\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"خطأ في تحديث البيانات: \".concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"خطأ في الاتصال: \".concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert('يرجى إدخال اسم المادة');\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حفظ المادة المؤقتة');\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert('خطأ في حفظ المادة المؤقتة');\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm('هل تريد حذف هذه المادة المؤقتة؟')) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حذف المادة المؤقتة');\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert('خطأ في حذف المادة المؤقتة');\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ يوجد تداخل في الأوقات! اختر وقت آخر.');\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                await fetchScheduleData();\n            } else {\n                alert(result.error);\n                shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'المادة';\n        const itemType = item.isRerun ? 'إعادة' : item.isTemporary ? 'مادة مؤقتة' : 'مادة أصلية';\n        const confirmed = window.confirm(\"هل أنت متأكد من حذف \".concat(itemType, ': \"').concat(itemName, '\"؟\\n\\n') + \"الوقت: \".concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + (item.isRerun ? 'تحذير: حذف الإعادة لن يؤثر على المادة الأصلية' : item.isTemporary ? 'سيتم حذف المادة المؤقتة من الجدول' : 'تحذير: حذف المادة الأصلية سيحذف جميع إعاداتها'));\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف محلي\n            if (item.isTemporary) {\n                if (item.isRerun) {\n                    // حذف إعادة مؤقتة فقط\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                    console.log(\"✅ تم حذف إعادة مؤقتة: \".concat(itemName));\n                } else {\n                    // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isTemporary && scheduleItem.originalId === item.id)));\n                    console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها: \".concat(itemName));\n                }\n                return;\n            }\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        const currentDate = new Date(selectedWeek);\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        setSelectedWeek(currentDate.toISOString().split('T')[0]);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"⏳ جاري تحميل الجدول الأسبوعي...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 706,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 705,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"\\uD83D\\uDCC5 جاري تحديد التاريخ...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 715,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 714,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"الخريطة البرامجية الأسبوعية\",\n            subtitle: \"جدولة البرامج الأسبوعية\",\n            icon: \"\\uD83D\\uDCC5\",\n            fullWidth: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    height: 'calc(100vh - 120px)',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: 'rtl',\n                    gap: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '320px',\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            border: '1px solid #6b7280',\n                            padding: '20px',\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: '0 0 15px 0',\n                                    color: '#f3f4f6'\n                                },\n                                children: \"\\uD83D\\uDCDA قائمة المواد\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    border: '2px solid #f59e0b',\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            margin: '0 0 10px 0',\n                                            color: '#fbbf24',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: \"⚡ إضافة مادة مؤقتة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 749,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"اسم المادة...\",\n                                        value: tempMediaName,\n                                        onChange: (e)=>setTempMediaName(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: tempMediaType,\n                                        onChange: (e)=>setTempMediaType(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROGRAM\",\n                                                children: \"\\uD83D\\uDCFB برنامج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SERIES\",\n                                                children: \"\\uD83D\\uDCFA مسلسل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 785,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MOVIE\",\n                                                children: \"\\uD83C\\uDFA5 فيلم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 786,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"LIVE\",\n                                                children: \"\\uD83D\\uDD34 برنامج هواء مباشر\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PENDING\",\n                                                children: \"\\uD83D\\uDFE1 مادة قيد التسليم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 788,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"المدة (مثل: 01:30:00)\",\n                                        value: tempMediaDuration,\n                                        onChange: (e)=>setTempMediaDuration(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"ملاحظات (اختياري)...\",\n                                        value: tempMediaNotes,\n                                        onChange: (e)=>setTempMediaNotes(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '10px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: addTempMedia,\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            background: '#ff9800',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '13px',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            marginBottom: '8px'\n                                        },\n                                        children: \"➕ إضافة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            console.log('🔄 تحديث الإعادات...');\n                                            scrollPositionRef.current = window.scrollY;\n                                            shouldRestoreScroll.current = true;\n                                            await fetchScheduleData();\n                                        },\n                                        style: {\n                                            width: '100%',\n                                            padding: '6px',\n                                            background: '#4caf50',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"♻️ تحديث الإعادات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 742,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedType,\n                                onChange: (e)=>setSelectedType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '10px',\n                                    fontSize: '14px',\n                                    backgroundColor: 'white',\n                                    color: '#333'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"\\uD83C\\uDFAC جميع الأنواع\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: \"\\uD83D\\uDCFA مسلسل\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MOVIE\",\n                                        children: \"\\uD83C\\uDFA5 فيلم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: \"\\uD83D\\uDCFB برنامج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 883,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROMO\",\n                                        children: \"\\uD83D\\uDCE2 إعلان\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"STING\",\n                                        children: \"⚡ ستينج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 885,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILL_IN\",\n                                        children: \"\\uD83D\\uDD04 فيل إن\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILLER\",\n                                        children: \"⏸️ فيلر\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 887,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"\\uD83D\\uDD0D البحث في المواد...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '15px',\n                                    fontSize: '14px',\n                                    color: '#333',\n                                    background: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 891,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '12px',\n                                    color: '#d1d5db',\n                                    marginBottom: '10px',\n                                    textAlign: 'center',\n                                    padding: '5px',\n                                    background: '#1f2937',\n                                    borderRadius: '4px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    filteredMedia.length,\n                                    \" من \",\n                                    allAvailableMedia.length,\n                                    \" مادة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 909,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '8px'\n                                },\n                                children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '20px',\n                                        color: '#666',\n                                        background: '#f8f9fa',\n                                        borderRadius: '8px',\n                                        border: '2px dashed #dee2e6'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: 'bold',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"لا توجد مواد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 934,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: searchTerm || selectedType ? 'جرب تغيير الفلتر أو البحث' : 'أضف مواد جديدة من صفحة المستخدم'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 935,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 925,\n                                    columnNumber: 17\n                                }, this) : filteredMedia.map((item)=>{\n                                    // تحديد لون المادة حسب النوع\n                                    const getItemStyle = ()=>{\n                                        if (item.isTemporary) {\n                                            switch(item.type){\n                                                case 'LIVE':\n                                                    return {\n                                                        background: '#ffebee',\n                                                        border: '2px solid #f44336',\n                                                        borderLeft: '5px solid #f44336'\n                                                    };\n                                                case 'PENDING':\n                                                    return {\n                                                        background: '#fff8e1',\n                                                        border: '2px solid #ffc107',\n                                                        borderLeft: '5px solid #ffc107'\n                                                    };\n                                                default:\n                                                    return {\n                                                        background: '#f3e5f5',\n                                                        border: '2px solid #9c27b0',\n                                                        borderLeft: '5px solid #9c27b0'\n                                                    };\n                                            }\n                                        }\n                                        return {\n                                            background: '#fff',\n                                            border: '1px solid #ddd'\n                                        };\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>handleDragStart(e, item),\n                                        style: {\n                                            ...getItemStyle(),\n                                            borderRadius: '8px',\n                                            padding: '12px',\n                                            cursor: 'grab',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                            position: 'relative'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(-2px)';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(0)';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteTempMedia(item.id);\n                                                },\n                                                style: {\n                                                    position: 'absolute',\n                                                    top: '5px',\n                                                    left: '5px',\n                                                    background: '#f44336',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '50%',\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    fontSize: '12px',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                title: \"حذف المادة المؤقتة\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 996,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: 'bold',\n                                                    color: '#333',\n                                                    marginBottom: '4px'\n                                                },\n                                                children: [\n                                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '10px',\n                                                            background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                            color: 'white',\n                                                            padding: '2px 6px',\n                                                            borderRadius: '10px',\n                                                            marginLeft: '5px'\n                                                        },\n                                                        children: item.type === 'LIVE' ? '🔴 هواء' : item.type === 'PENDING' ? '🟡 قيد التسليم' : '🟣 مؤقت'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1025,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    getMediaDisplayText(item)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1023,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '12px',\n                                                    color: '#666'\n                                                },\n                                                children: [\n                                                    item.type,\n                                                    \" • \",\n                                                    item.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '11px',\n                                                    color: '#888',\n                                                    marginTop: '4px'\n                                                },\n                                                children: item.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1044,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 972,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 923,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 731,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px',\n                                    background: '#4a5568',\n                                    padding: '15px',\n                                    borderRadius: '15px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/daily-schedule',\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: \"\\uD83D\\uDCCB الجدول الإذاعي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1069,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                        const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                        if (!response.ok) {\n                                                            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                        }\n                                                        const blob = await response.blob();\n                                                        const url = window.URL.createObjectURL(blob);\n                                                        const a = document.createElement('a');\n                                                        a.href = url;\n                                                        a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                        document.body.appendChild(a);\n                                                        a.click();\n                                                        window.URL.revokeObjectURL(url);\n                                                        document.body.removeChild(a);\n                                                        console.log('✅ تم تصدير الخريطة بنجاح');\n                                                    } catch (error) {\n                                                        console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                        alert('فشل في تصدير الخريطة: ' + error.message);\n                                                    }\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: \"\\uD83D\\uDCCA تصدير الخريطة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1094,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#f3f4f6',\n                                                    fontSize: '1.2rem'\n                                                },\n                                                children: \"\\uD83D\\uDCC5 الأسبوع المحدد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1068,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(-1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: \"← الأسبوع السابق\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1145,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedWeek,\n                                                onChange: (e)=>{\n                                                    const selectedDate = new Date(e.target.value + 'T12:00:00');\n                                                    const dayOfWeek = selectedDate.getDay();\n                                                    console.log('📅 تغيير التاريخ من التقويم:', {\n                                                        selectedDate: e.target.value,\n                                                        dayOfWeek: dayOfWeek,\n                                                        dayName: [\n                                                            'الأحد',\n                                                            'الاثنين',\n                                                            'الثلاثاء',\n                                                            'الأربعاء',\n                                                            'الخميس',\n                                                            'الجمعة',\n                                                            'السبت'\n                                                        ][dayOfWeek]\n                                                    });\n                                                    // حساب بداية الأسبوع (الأحد)\n                                                    const sunday = new Date(selectedDate);\n                                                    sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                                    const weekStart = sunday.toISOString().split('T')[0];\n                                                    console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                                    setSelectedWeek(weekStart);\n                                                },\n                                                style: {\n                                                    padding: '8px',\n                                                    border: '1px solid #6b7280',\n                                                    borderRadius: '5px',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1159,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: \"الأسبوع التالي →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1190,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1058,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',\n                                    borderRadius: '10px',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                    minHeight: '80vh'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#f8f9fa'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '12px',\n                                                            border: '1px solid #dee2e6',\n                                                            fontWeight: 'bold',\n                                                            width: '80px',\n                                                            color: '#000'\n                                                        },\n                                                        children: \"الوقت\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1218,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '12px',\n                                                                border: '1px solid #dee2e6',\n                                                                fontWeight: 'bold',\n                                                                width: \"\".concat(100 / 7, \"%\"),\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        marginBottom: '4px',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: day\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1235,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: weekDates[index]\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1236,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1217,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                background: '#f8f9fa',\n                                                                fontWeight: 'bold',\n                                                                textAlign: 'center',\n                                                                padding: '8px',\n                                                                border: '1px solid #dee2e6',\n                                                                color: '#000'\n                                                            },\n                                                            children: hour\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1248,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        days.map((_, dayIndex)=>{\n                                                            const cellItems = getItemsForCell(dayIndex, hour);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                onDragOver: handleDragOver,\n                                                                onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                                style: {\n                                                                    border: '1px solid #dee2e6',\n                                                                    padding: '8px',\n                                                                    height: '150px',\n                                                                    cursor: 'pointer',\n                                                                    background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',\n                                                                    verticalAlign: 'top'\n                                                                },\n                                                                children: cellItems.map((item)=>{\n                                                                    var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        draggable: true,\n                                                                        onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                        onClick: ()=>deleteItem(item),\n                                                                        style: {\n                                                                            background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                            border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                            borderRadius: '4px',\n                                                                            padding: '8px 6px',\n                                                                            marginBottom: '4px',\n                                                                            fontSize: '1rem',\n                                                                            cursor: 'grab',\n                                                                            transition: 'all 0.2s ease',\n                                                                            minHeight: '60px',\n                                                                            display: 'flex',\n                                                                            flexDirection: 'column',\n                                                                            justifyContent: 'center'\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1.02)';\n                                                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1)';\n                                                                            e.currentTarget.style.boxShadow = 'none';\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    gap: '4px',\n                                                                                    color: '#000'\n                                                                                },\n                                                                                children: [\n                                                                                    item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#666'\n                                                                                        },\n                                                                                        children: [\n                                                                                            \"♻️ \",\n                                                                                            item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                            item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1316,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                        },\n                                                                                        children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1320,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#ff9800'\n                                                                                        },\n                                                                                        children: \"\\uD83C\\uDF1F\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1328,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#000'\n                                                                                        },\n                                                                                        children: item.mediaItem ? getMediaDisplayText(item.mediaItem) : 'غير معروف'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1330,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1314,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.8rem',\n                                                                                    color: '#000',\n                                                                                    marginTop: '4px'\n                                                                                },\n                                                                                children: [\n                                                                                    item.startTime,\n                                                                                    \" - \",\n                                                                                    item.endTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1334,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.5rem',\n                                                                                    color: '#888',\n                                                                                    fontStyle: 'italic'\n                                                                                },\n                                                                                children: \"إعادة - يمكن الحذف للتعديل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1338,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, item.id, true, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1276,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, dayIndex, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1262,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, hourIndex, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1247,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1245,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1214,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    padding: '20px',\n                                    borderRadius: '15px',\n                                    marginTop: '20px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 20px 0',\n                                            fontSize: '1.3rem',\n                                            textAlign: 'center'\n                                        },\n                                        children: \"\\uD83D\\uDCCB تعليمات الاستخدام:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1361,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDFAF إضافة المواد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"اسحب المواد من القائمة اليمنى إلى الجدول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1367,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83D\\uDD04 اسحب المواد من الجدول نفسه لنسخها لمواعيد أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1368,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83C\\uDFAC استخدم فلتر النوع للتصفية حسب نوع المادة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1369,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83D\\uDD0D استخدم البحث للعثور على المواد بسرعة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1370,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1366,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1364,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDDD1️ حذف المواد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1374,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"المواد الأصلية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1376,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" حذف نهائي مع جميع إعاداتها\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"الإعادات:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1377,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" حذف مع ترك الحقل فارغ للتعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1377,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"سيظهر تأكيد قبل الحذف\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1378,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1375,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1373,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1363,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDF1F المواد الأصلية (البرايم تايم):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1385,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"الأحد-الأربعاء: 18:00-00:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1387,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"الخميس-السبت: 18:00-02:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1388,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    \"\\uD83D\\uDFE1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        style: {\n                                                                            color: '#fbbf24'\n                                                                        },\n                                                                        children: \"لون ذهبي في الجدول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1389,\n                                                                        columnNumber: 56\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1389,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1384,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"♻️ الإعادات التلقائية (جزئين):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1393,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الأحد-الأربعاء:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1395,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1395,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج1: نفس العمود 00:00-07:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1397,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج2: العمود التالي 08:00-17:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1398,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1396,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الخميس-السبت:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1400,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1400,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج1: نفس العمود 02:00-07:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1402,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج2: العمود التالي 08:00-17:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1403,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1401,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    \"\\uD83D\\uDD18 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        style: {\n                                                                            color: '#9ca3af'\n                                                                        },\n                                                                        children: \"لون رمادي - يمكن حذفها للتعديل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 56\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1394,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1392,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1383,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #f59e0b'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: \"\\uD83D\\uDCC5 إدارة التواريخ:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1411,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \" استخدم الكالندر والأزرار للتنقل بين الأسابيع • كل أسبوع يُحفظ بشكل منفصل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1412,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1410,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #3b82f6'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: \"\\uD83D\\uDCA1 ملاحظة مهمة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1416,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \" عند إضافة مواد قليلة (١-٣ مواد) في البرايم، ستظهر مع فواصل زمنية في الإعادات لتجنب التكرار المفرط. أضف المزيد من المواد للحصول على تنوع أكبر.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1417,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1415,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1354,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1056,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 723,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 722,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 721,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"v2z1JHXlWVqbyv5DN3U+nYUgV3s=\");\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction AuthGuard(param) {\n    let { children, requiredPermissions = [], requiredRole, fallbackComponent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        // المدير له صلاحيات كاملة\n        if (user.role === 'ADMIN') {\n            return true;\n        }\n        // التحقق من الدور المطلوب\n        if (role && user.role !== role) {\n            return false;\n        }\n        // التحقق من الصلاحيات المطلوبة\n        if (permissions.length > 0) {\n            const userPermissions = getUserPermissions(user.role);\n            return permissions.every((permission)=>userPermissions.includes(permission) || userPermissions.includes('ALL'));\n        }\n        return true;\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user === null || user === void 0 ? void 0 : user.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user === null || user === void 0 ? void 0 : user.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AuthGuard, \"fqF8YvhaHbrPIfzSUHTCm0cPUfQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthGuard;\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        if (user.role === 'ADMIN') return true;\n        const userPermissions = getUserPermissions(user.role);\n        return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN',\n        isMediaManager: (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER',\n        isScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER',\n        isViewer: (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER'\n    };\n}\n_s1(useAuth, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AuthGuard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardLayout(param) {\n    let { children, title = 'لوحة التحكم', subtitle = 'إدارة النظام', icon = '📊', requiredPermissions, requiredRole, fullWidth = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const timer = setInterval({\n                \"DashboardLayout.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"DashboardLayout.useEffect.timer\"], 1000);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>clearInterval(timer)\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: false,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'جدول الإذاعة اليومي',\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: requiredPermissions,\n        requiredRole: requiredRole,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        fontSize: '1.2rem',\n                                        fontWeight: '900',\n                                        fontFamily: 'Arial, sans-serif',\n                                        gap: '5px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '900',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"X\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '1rem',\n                                                fontWeight: '300'\n                                            },\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '800',\n                                                letterSpacing: '1px'\n                                            },\n                                            children: \"Prime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        marginRight: sidebarOpen ? '280px' : '0',\n                        transition: 'margin-right 0.3s ease'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"متصل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: currentTime.toLocaleTimeString('ar-EG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '20px',\n                                padding: '25px',\n                                border: '1px solid #4a5568',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                                maxWidth: fullWidth ? 'none' : '1000px',\n                                margin: '0 auto',\n                                width: fullWidth ? '100%' : 'auto'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"KNYedtH3V/oElqzTesyis3gRu2I=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DashboardLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Sidebar(param) {\n    let { isOpen, onToggle } = param;\n    var _user_name;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const menuItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'جدول الإذاعة اليومي',\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'إدارة المستخدمين',\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null,\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            path: '/statistics',\n            permission: null,\n            adminOnly: true\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    right: isOpen ? 0 : '-280px',\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    borderLeft: '1px solid #2d3748',\n                    transition: 'right 0.3s ease',\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            fontSize: '1.5rem',\n                                            fontWeight: '900',\n                                            fontFamily: 'Arial, sans-serif',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '900',\n                                                    fontSize: '1.8rem'\n                                                },\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#6c757d',\n                                                    fontSize: '1.2rem',\n                                                    fontWeight: '300'\n                                                },\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '800',\n                                                    letterSpacing: '1px'\n                                                },\n                                                children: \"Prime\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '1.1rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: \"نظام الإدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: \"المحتوى الإعلامي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                style: {\n                                    background: 'transparent',\n                                    border: 'none',\n                                    color: '#a0aec0',\n                                    fontSize: '1.2rem',\n                                    cursor: 'pointer',\n                                    padding: '5px'\n                                },\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    marginBottom: '10px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '35px',\n                                            height: '35px',\n                                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                            borderRadius: '50%',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            color: 'white',\n                                            fontSize: '1rem',\n                                            fontWeight: 'bold'\n                                        },\n                                        children: (user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0)) || 'U'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: (user === null || user === void 0 ? void 0 : user.name) || 'مستخدم'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: [\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && '👑 مدير النظام',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER' && '📝 مدير المحتوى',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER' && '📅 مجدول البرامج',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER' && '👁️ مستخدم عرض'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '5px',\n                                    color: '#68d391',\n                                    fontSize: '0.8rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '6px',\n                                            height: '6px',\n                                            background: '#68d391',\n                                            borderRadius: '50%'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"متصل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#4299e1' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    padding: '12px 20px',\n                                    textAlign: 'right',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    fontSize: '0.9rem',\n                                    transition: 'all 0.2s',\n                                    borderRight: isActive ? '3px solid #4299e1' : '3px solid transparent'\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = '#2d3748';\n                                        e.target.style.color = 'white';\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '1.1rem'\n                                        },\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    // إضافة وظيفة تسجيل الخروج هنا\n                                    localStorage.removeItem('user');\n                                    localStorage.removeItem('token');\n                                    router.push('/login');\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    padding: '10px',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    gap: '8px',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '15px'\n                                },\n                                children: \"\\uD83D\\uDEAA تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    color: '#6c757d',\n                                    fontSize: '0.75rem',\n                                    fontFamily: 'Arial, sans-serif',\n                                    direction: 'ltr'\n                                },\n                                children: \"Powered By Mahmoud Ismail\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"F0XCIKCOXO7Aj/2LRHqPM/x8tuo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cweekly-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);