/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/weekly-schedule/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cweekly-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cweekly-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/weekly-schedule/page.tsx */ \"(app-pages-browser)/./src/app/weekly-schedule/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDd2Vla2x5LXNjaGVkdWxlJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcd2Vla2x5LXNjaGVkdWxlXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cweekly-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction WeeklySchedulePage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع\n    const days = [\n        'الأحد',\n        'الاثنين',\n        'الثلاثاء',\n        'الأربعاء',\n        'الخميس',\n        'الجمعة',\n        'السبت'\n    ];\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        const startDate = new Date(selectedWeek + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n        const dates = [];\n        console.log('📅 حساب تواريخ الأسبوع من:', selectedWeek);\n        for(let i = 0; i < 7; i++){\n            const date = new Date(startDate);\n            date.setDate(startDate.getDate() + i);\n            // استخدام الأرقام العربية العادية (1234567890)\n            const dateStr = date.toLocaleDateString('en-US', {\n                day: '2-digit',\n                month: '2-digit'\n            });\n            dates.push(dateStr);\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(date.toISOString().split('T')[0], \" → \").concat(dateStr));\n        }\n        return dates;\n    };\n    const weekDates = getWeekDates();\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || 'غير معروف';\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - الجزء \".concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم:', today.toISOString().split('T')[0]);\n            console.log('  📊 يوم الأسبوع:', today.getDay(), '(0=أحد)');\n            const sunday = new Date(today);\n            sunday.setDate(today.getDate() - today.getDay());\n            const weekStart = sunday.toISOString().split('T')[0];\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current > 0) {\n                window.scrollTo(0, scrollPositionRef.current);\n                shouldRestoreScroll.current = false;\n                console.log('📍 تم استعادة موضع التمرير:', scrollPositionRef.current);\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"خطأ في تحديث البيانات: \".concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"خطأ في الاتصال: \".concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert('يرجى إدخال اسم المادة');\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حفظ المادة المؤقتة');\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert('خطأ في حفظ المادة المؤقتة');\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm('هل تريد حذف هذه المادة المؤقتة؟')) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حذف المادة المؤقتة');\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert('خطأ في حذف المادة المؤقتة');\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ يوجد تداخل في الأوقات! اختر وقت آخر.');\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                await fetchScheduleData();\n            } else {\n                alert(result.error);\n                shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'المادة';\n        const itemType = item.isRerun ? 'إعادة' : item.isTemporary ? 'مادة مؤقتة' : 'مادة أصلية';\n        const confirmed = window.confirm(\"هل أنت متأكد من حذف \".concat(itemType, ': \"').concat(itemName, '\"؟\\n\\n') + \"الوقت: \".concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + (item.isRerun ? 'تحذير: حذف الإعادة لن يؤثر على المادة الأصلية' : item.isTemporary ? 'سيتم حذف المادة المؤقتة من الجدول' : 'تحذير: حذف المادة الأصلية سيحذف جميع إعاداتها'));\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف محلي\n            if (item.isTemporary) {\n                if (item.isRerun) {\n                    // حذف إعادة مؤقتة فقط\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                    console.log(\"✅ تم حذف إعادة مؤقتة: \".concat(itemName));\n                } else {\n                    // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isTemporary && scheduleItem.originalId === item.id)));\n                    console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها: \".concat(itemName));\n                }\n                return;\n            }\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        const currentDate = new Date(selectedWeek);\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        setSelectedWeek(currentDate.toISOString().split('T')[0]);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"⏳ جاري تحميل الجدول الأسبوعي...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 697,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 696,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"\\uD83D\\uDCC5 جاري تحديد التاريخ...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 706,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 705,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: 'flex',\n            height: '100vh',\n            fontFamily: 'Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: '300px',\n                    background: '#f8f9fa',\n                    borderLeft: '2px solid #dee2e6',\n                    padding: '20px',\n                    overflowY: 'auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            margin: '0 0 15px 0',\n                            color: '#333'\n                        },\n                        children: \"\\uD83D\\uDCDA قائمة المواد\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 726,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#fff3e0',\n                            border: '2px solid #ff9800',\n                            borderRadius: '8px',\n                            padding: '12px',\n                            marginBottom: '15px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    margin: '0 0 10px 0',\n                                    color: '#e65100',\n                                    fontSize: '0.9rem'\n                                },\n                                children: \"⚡ إضافة مادة مؤقتة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"اسم المادة...\",\n                                value: tempMediaName,\n                                onChange: (e)=>setTempMediaName(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    border: '1px solid #ddd',\n                                    borderRadius: '4px',\n                                    marginBottom: '8px',\n                                    fontSize: '13px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: tempMediaType,\n                                onChange: (e)=>setTempMediaType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    border: '1px solid #ddd',\n                                    borderRadius: '4px',\n                                    marginBottom: '8px',\n                                    fontSize: '13px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: \"\\uD83D\\uDCFB برنامج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: \"\\uD83D\\uDCFA مسلسل\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MOVIE\",\n                                        children: \"\\uD83C\\uDFA5 فيلم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 769,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"LIVE\",\n                                        children: \"\\uD83D\\uDD34 برنامج هواء مباشر\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PENDING\",\n                                        children: \"\\uD83D\\uDFE1 مادة قيد التسليم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 755,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"المدة (مثل: 01:30:00)\",\n                                value: tempMediaDuration,\n                                onChange: (e)=>setTempMediaDuration(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    border: '1px solid #ddd',\n                                    borderRadius: '4px',\n                                    marginBottom: '8px',\n                                    fontSize: '13px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 774,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"ملاحظات (اختياري)...\",\n                                value: tempMediaNotes,\n                                onChange: (e)=>setTempMediaNotes(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    border: '1px solid #ddd',\n                                    borderRadius: '4px',\n                                    marginBottom: '10px',\n                                    fontSize: '13px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: addTempMedia,\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    background: '#ff9800',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '4px',\n                                    fontSize: '13px',\n                                    fontWeight: 'bold',\n                                    cursor: 'pointer',\n                                    marginBottom: '8px'\n                                },\n                                children: \"➕ إضافة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 804,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: async ()=>{\n                                    console.log('🔄 تحديث الإعادات...');\n                                    scrollPositionRef.current = window.scrollY;\n                                    shouldRestoreScroll.current = true;\n                                    await fetchScheduleData();\n                                },\n                                style: {\n                                    width: '100%',\n                                    padding: '6px',\n                                    background: '#4caf50',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '4px',\n                                    fontSize: '12px',\n                                    cursor: 'pointer'\n                                },\n                                children: \"♻️ تحديث الإعادات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 822,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: selectedType,\n                        onChange: (e)=>setSelectedType(e.target.value),\n                        style: {\n                            width: '100%',\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '5px',\n                            marginBottom: '10px',\n                            fontSize: '14px',\n                            backgroundColor: '#f8f9fa'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"\\uD83C\\uDFAC جميع الأنواع\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 858,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"SERIES\",\n                                children: \"\\uD83D\\uDCFA مسلسل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"MOVIE\",\n                                children: \"\\uD83C\\uDFA5 فيلم\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 860,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"PROGRAM\",\n                                children: \"\\uD83D\\uDCFB برنامج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"PROMO\",\n                                children: \"\\uD83D\\uDCE2 إعلان\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 862,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"STING\",\n                                children: \"⚡ ستينج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"FILL_IN\",\n                                children: \"\\uD83D\\uDD04 فيل إن\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"FILLER\",\n                                children: \"⏸️ فيلر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 865,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 845,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"\\uD83D\\uDD0D البحث في المواد...\",\n                        value: searchTerm,\n                        onChange: (e)=>setSearchTerm(e.target.value),\n                        style: {\n                            width: '100%',\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '5px',\n                            marginBottom: '15px',\n                            fontSize: '14px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 869,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '12px',\n                            color: '#666',\n                            marginBottom: '10px',\n                            textAlign: 'center',\n                            padding: '5px',\n                            background: '#e9ecef',\n                            borderRadius: '4px'\n                        },\n                        children: [\n                            \"\\uD83D\\uDCCA \",\n                            filteredMedia.length,\n                            \" من \",\n                            allAvailableMedia.length,\n                            \" مادة\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 885,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '8px'\n                        },\n                        children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center',\n                                padding: '20px',\n                                color: '#666',\n                                background: '#f8f9fa',\n                                borderRadius: '8px',\n                                border: '2px dashed #dee2e6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 908,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontWeight: 'bold',\n                                        marginBottom: '5px'\n                                    },\n                                    children: \"لا توجد مواد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '12px'\n                                    },\n                                    children: searchTerm || selectedType ? 'جرب تغيير الفلتر أو البحث' : 'أضف مواد جديدة من صفحة المستخدم'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 13\n                        }, this) : filteredMedia.map((item)=>{\n                            // تحديد لون المادة حسب النوع\n                            const getItemStyle = ()=>{\n                                if (item.isTemporary) {\n                                    switch(item.type){\n                                        case 'LIVE':\n                                            return {\n                                                background: '#ffebee',\n                                                border: '2px solid #f44336',\n                                                borderLeft: '5px solid #f44336'\n                                            };\n                                        case 'PENDING':\n                                            return {\n                                                background: '#fff8e1',\n                                                border: '2px solid #ffc107',\n                                                borderLeft: '5px solid #ffc107'\n                                            };\n                                        default:\n                                            return {\n                                                background: '#f3e5f5',\n                                                border: '2px solid #9c27b0',\n                                                borderLeft: '5px solid #9c27b0'\n                                            };\n                                    }\n                                }\n                                return {\n                                    background: '#fff',\n                                    border: '1px solid #ddd'\n                                };\n                            };\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                draggable: true,\n                                onDragStart: (e)=>handleDragStart(e, item),\n                                style: {\n                                    ...getItemStyle(),\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    cursor: 'grab',\n                                    transition: 'all 0.2s',\n                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                    position: 'relative'\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.transform = 'translateY(-2px)';\n                                    e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.transform = 'translateY(0)';\n                                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                },\n                                children: [\n                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            deleteTempMedia(item.id);\n                                        },\n                                        style: {\n                                            position: 'absolute',\n                                            top: '5px',\n                                            left: '5px',\n                                            background: '#f44336',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '50%',\n                                            width: '20px',\n                                            height: '20px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center'\n                                        },\n                                        title: \"حذف المادة المؤقتة\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 971,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontWeight: 'bold',\n                                            color: '#333',\n                                            marginBottom: '4px'\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '10px',\n                                                    background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                    color: 'white',\n                                                    padding: '2px 6px',\n                                                    borderRadius: '10px',\n                                                    marginLeft: '5px'\n                                                },\n                                                children: item.type === 'LIVE' ? '🔴 هواء' : item.type === 'PENDING' ? '🟡 قيد التسليم' : '🟣 مؤقت'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1000,\n                                                columnNumber: 23\n                                            }, this),\n                                            getMediaDisplayText(item)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '12px',\n                                            color: '#666'\n                                        },\n                                        children: [\n                                            item.type,\n                                            \" • \",\n                                            item.duration\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '11px',\n                                            color: '#888',\n                                            marginTop: '4px'\n                                        },\n                                        children: item.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, item.id, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 947,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 898,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 719,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1,\n                    padding: '20px',\n                    overflowY: 'auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center',\n                            marginBottom: '20px',\n                            background: '#fff',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = '/daily-schedule',\n                                        style: {\n                                            background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            padding: '10px 20px',\n                                            fontSize: '0.9rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                            transition: 'transform 0.2s ease',\n                                            marginLeft: '10px'\n                                        },\n                                        onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                        onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                        children: \"\\uD83D\\uDCCB الجدول الإذاعي\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1043,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            try {\n                                                console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                if (!response.ok) {\n                                                    throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                }\n                                                const blob = await response.blob();\n                                                const url = window.URL.createObjectURL(blob);\n                                                const a = document.createElement('a');\n                                                a.href = url;\n                                                a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                document.body.appendChild(a);\n                                                a.click();\n                                                window.URL.revokeObjectURL(url);\n                                                document.body.removeChild(a);\n                                                console.log('✅ تم تصدير الخريطة بنجاح');\n                                            } catch (error) {\n                                                console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                alert('فشل في تصدير الخريطة: ' + error.message);\n                                            }\n                                        },\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            padding: '10px 20px',\n                                            fontSize: '0.9rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                            transition: 'transform 0.2s ease',\n                                            marginLeft: '10px'\n                                        },\n                                        onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                        onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                        children: \"\\uD83D\\uDCCA تصدير الخريطة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1068,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = '/',\n                                        style: {\n                                            background: 'linear-gradient(45deg, #e74c3c, #c0392b)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            padding: '10px 20px',\n                                            fontSize: '0.9rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                            transition: 'transform 0.2s ease',\n                                            marginLeft: '10px'\n                                        },\n                                        onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                        onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            margin: 0,\n                                            color: '#333'\n                                        },\n                                        children: \"\\uD83D\\uDCC5 الخريطة البرامجية الأسبوعية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>changeWeek(-1),\n                                        style: {\n                                            padding: '8px 15px',\n                                            background: '#007bff',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"← الأسبوع السابق\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: selectedWeek,\n                                        onChange: (e)=>{\n                                            const selectedDate = new Date(e.target.value + 'T12:00:00');\n                                            const dayOfWeek = selectedDate.getDay();\n                                            console.log('📅 تغيير التاريخ من التقويم:', {\n                                                selectedDate: e.target.value,\n                                                dayOfWeek: dayOfWeek,\n                                                dayName: [\n                                                    'الأحد',\n                                                    'الاثنين',\n                                                    'الثلاثاء',\n                                                    'الأربعاء',\n                                                    'الخميس',\n                                                    'الجمعة',\n                                                    'السبت'\n                                                ][dayOfWeek]\n                                            });\n                                            // حساب بداية الأسبوع (الأحد)\n                                            const sunday = new Date(selectedDate);\n                                            sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                            const weekStart = sunday.toISOString().split('T')[0];\n                                            console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                            setSelectedWeek(weekStart);\n                                        },\n                                        style: {\n                                            padding: '8px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1152,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>changeWeek(1),\n                                        style: {\n                                            padding: '8px 15px',\n                                            background: '#007bff',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"الأسبوع التالي →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1181,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1032,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#fff',\n                            borderRadius: '10px',\n                            overflow: 'hidden',\n                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            style: {\n                                width: '100%',\n                                borderCollapse: 'collapse'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        style: {\n                                            background: '#f8f9fa'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: '12px',\n                                                    border: '1px solid #dee2e6',\n                                                    fontWeight: 'bold',\n                                                    minWidth: '80px'\n                                                },\n                                                children: \"الوقت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1208,\n                                                columnNumber: 17\n                                            }, this),\n                                            days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '12px',\n                                                        border: '1px solid #dee2e6',\n                                                        fontWeight: 'bold',\n                                                        minWidth: '120px',\n                                                        textAlign: 'center'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '1rem',\n                                                                marginBottom: '4px'\n                                                            },\n                                                            children: day\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1224,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                color: '#666',\n                                                                fontWeight: 'normal'\n                                                            },\n                                                            children: weekDates[index]\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1225,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1217,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        background: '#f8f9fa',\n                                                        fontWeight: 'bold',\n                                                        textAlign: 'center',\n                                                        padding: '8px',\n                                                        border: '1px solid #dee2e6'\n                                                    },\n                                                    children: hour\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                days.map((_, dayIndex)=>{\n                                                    const cellItems = getItemsForCell(dayIndex, hour);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        onDragOver: handleDragOver,\n                                                        onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                        style: {\n                                                            border: '1px solid #dee2e6',\n                                                            padding: '4px',\n                                                            minHeight: '40px',\n                                                            cursor: 'pointer',\n                                                            background: cellItems.length > 0 ? 'transparent' : '#fafafa',\n                                                            verticalAlign: 'top'\n                                                        },\n                                                        children: cellItems.map((item)=>{\n                                                            var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                draggable: true,\n                                                                onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                onClick: ()=>deleteItem(item),\n                                                                style: {\n                                                                    background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                    border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                    borderRadius: '4px',\n                                                                    padding: '2px 4px',\n                                                                    marginBottom: '2px',\n                                                                    fontSize: '0.7rem',\n                                                                    cursor: 'grab',\n                                                                    transition: 'all 0.2s ease'\n                                                                },\n                                                                onMouseEnter: (e)=>{\n                                                                    e.currentTarget.style.transform = 'scale(1.02)';\n                                                                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                },\n                                                                onMouseLeave: (e)=>{\n                                                                    e.currentTarget.style.transform = 'scale(1)';\n                                                                    e.currentTarget.style.boxShadow = 'none';\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontWeight: 'bold',\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            gap: '4px'\n                                                                        },\n                                                                        children: [\n                                                                            item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: '#666'\n                                                                                },\n                                                                                children: [\n                                                                                    \"♻️ \",\n                                                                                    item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                    item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1300,\n                                                                                columnNumber: 33\n                                                                            }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                },\n                                                                                children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1304,\n                                                                                columnNumber: 33\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: '#ff9800'\n                                                                                },\n                                                                                children: \"\\uD83C\\uDF1F\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1312,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            item.mediaItem ? getMediaDisplayText(item.mediaItem) : 'غير معروف'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1298,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: '0.6rem',\n                                                                            color: '#666'\n                                                                        },\n                                                                        children: [\n                                                                            item.startTime,\n                                                                            \" - \",\n                                                                            item.endTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1316,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: '0.5rem',\n                                                                            color: '#888',\n                                                                            fontStyle: 'italic'\n                                                                        },\n                                                                        children: \"إعادة - يمكن الحذف للتعديل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1320,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, item.id, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1264,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, dayIndex, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1250,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            ]\n                                        }, hourIndex, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1236,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1234,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                            lineNumber: 1204,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1198,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#e8f5e8',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginTop: '20px',\n                            border: '2px solid #4caf50'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    color: '#2e7d32',\n                                    margin: '0 0 15px 0'\n                                },\n                                children: \"\\uD83D\\uDCCB تعليمات الاستخدام:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1343,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: '1fr 1fr',\n                                    gap: '15px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83C\\uDFAF إضافة المواد:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1347,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    margin: '5px 0',\n                                                    paddingRight: '20px',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"اسحب المواد من القائمة اليمنى إلى الجدول\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1349,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDD04 اسحب المواد من الجدول نفسه لنسخها لمواعيد أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1350,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83C\\uDFAC استخدم فلتر النوع للتصفية حسب نوع المادة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1351,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDD0D استخدم البحث للعثور على المواد بسرعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1352,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1348,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1346,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83D\\uDDD1️ حذف المواد:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1356,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    margin: '5px 0',\n                                                    paddingRight: '20px',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"المواد الأصلية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" حذف نهائي مع جميع إعاداتها\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"الإعادات:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1359,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" حذف مع ترك الحقل فارغ للتعديل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"سيظهر تأكيد قبل الحذف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1360,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1357,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1355,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1345,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: '1fr 1fr',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83C\\uDF1F المواد الأصلية (البرايم تايم):\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1367,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    margin: '5px 0',\n                                                    paddingRight: '20px',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"الأحد-الأربعاء: 18:00-00:00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1369,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"الخميس-السبت: 18:00-02:00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1370,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"لون ذهبي في الجدول\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1371,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1368,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1366,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"♻️ الإعادات التلقائية (جزئين):\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1375,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    margin: '5px 0',\n                                                    paddingRight: '20px',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الأحد-الأربعاء:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1377,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '2px 0',\n                                                            paddingRight: '15px',\n                                                            fontSize: '0.8rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"ج1: نفس العمود 00:00-07:59\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1379,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"ج2: العمود التالي 08:00-17:59\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1380,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1378,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الخميس-السبت:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1382,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '2px 0',\n                                                            paddingRight: '15px',\n                                                            fontSize: '0.8rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"ج1: نفس العمود 02:00-07:59\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1384,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"ج2: العمود التالي 08:00-17:59\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1385,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1383,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"لون رمادي - يمكن حذفها للتعديل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1387,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1376,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1374,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '10px',\n                                    padding: '10px',\n                                    background: '#fff3e0',\n                                    borderRadius: '8px',\n                                    border: '1px solid #ff9800'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCC5 إدارة التواريخ:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1393,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" استخدم الكالندر والأزرار للتنقل بين الأسابيع • كل أسبوع يُحفظ بشكل منفصل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1392,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '10px',\n                                    padding: '10px',\n                                    background: '#e3f2fd',\n                                    borderRadius: '8px',\n                                    border: '1px solid #2196f3'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCA1 ملاحظة مهمة:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" عند إضافة مواد قليلة (١-٣ مواد) في البرايم، ستظهر مع فواصل زمنية في الإعادات لتجنب التكرار المفرط. أضف المزيد من المواد للحصول على تنوع أكبر.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1396,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1336,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 1030,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 712,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"v2z1JHXlWVqbyv5DN3U+nYUgV3s=\");\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cweekly-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);