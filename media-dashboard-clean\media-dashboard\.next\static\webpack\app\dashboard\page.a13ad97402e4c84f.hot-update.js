"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_StatsCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/StatsCard */ \"(app-pages-browser)/./src/components/StatsCard.tsx\");\n/* harmony import */ var _components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NavigationCard */ \"(app-pages-browser)/./src/components/NavigationCard.tsx\");\n/* harmony import */ var _styles_dashboard_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/styles/dashboard.css */ \"(app-pages-browser)/./src/styles/dashboard.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Dashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Dashboard.useEffect.timer\"], 1000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // بيانات الإحصائيات الحقيقية\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMedia: 0,\n        validMedia: 0,\n        rejectedMedia: 0,\n        expiredMedia: 0,\n        pendingMedia: 0,\n        activeUsers: 0,\n        onlineUsers: 0,\n        todayAdded: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // جلب البيانات الحقيقية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchRealStats();\n            // تحديث البيانات كل 30 ثانية\n            const interval = setInterval(fetchRealStats, 30000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(interval)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchRealStats = async ()=>{\n        try {\n            setLoading(true);\n            // جلب البيانات من API مع timeout\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 ثواني timeout\n            const [mediaResponse, usersResponse] = await Promise.all([\n                fetch('/api/media', {\n                    signal: controller.signal\n                }),\n                fetch('/api/users', {\n                    signal: controller.signal\n                })\n            ]);\n            clearTimeout(timeoutId);\n            let mediaData = [];\n            let userData = [];\n            if (mediaResponse.ok) {\n                const mediaResult = await mediaResponse.json();\n                mediaData = mediaResult.success ? mediaResult.data : [];\n            }\n            if (usersResponse.ok) {\n                const usersResult = await usersResponse.json();\n                userData = usersResult.success ? usersResult.users : [];\n            }\n            // حساب الإحصائيات الحقيقية\n            const totalMedia = mediaData.length;\n            const validMedia = mediaData.filter((item)=>item.status === 'VALID').length;\n            const rejectedMedia = mediaData.filter((item)=>item.status === 'REJECTED_CENSORSHIP' || item.status === 'REJECTED_TECHNICAL').length;\n            const expiredMedia = mediaData.filter((item)=>item.status === 'EXPIRED').length;\n            const pendingMedia = mediaData.filter((item)=>item.status === 'PENDING').length;\n            // حساب المواد المضافة اليوم\n            const today = new Date().toDateString();\n            const todayAdded = mediaData.filter((item)=>{\n                if (!item.createdAt) return false;\n                return new Date(item.createdAt).toDateString() === today;\n            }).length;\n            setStats({\n                totalMedia,\n                validMedia,\n                rejectedMedia,\n                expiredMedia,\n                pendingMedia,\n                activeUsers: userData.length,\n                onlineUsers: userData.filter((u)=>u.isActive).length,\n                todayAdded\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // بيانات افتراضية في حالة الخطأ\n            setStats({\n                totalMedia: 0,\n                validMedia: 0,\n                rejectedMedia: 0,\n                expiredMedia: 0,\n                pendingMedia: 0,\n                activeUsers: 4,\n                onlineUsers: 1,\n                todayAdded: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    color: 'white'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            fontSize: '2rem',\n                            fontWeight: '900',\n                            fontFamily: 'Arial, sans-serif',\n                            gap: '8px',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    fontWeight: '900',\n                                    fontSize: '3rem'\n                                },\n                                children: \"X\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    color: '#6c757d',\n                                    fontSize: '2rem',\n                                    fontWeight: '300'\n                                },\n                                children: \"-\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    fontWeight: '800',\n                                    letterSpacing: '1px'\n                                },\n                                children: \"Prime\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '3rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.5rem',\n                            marginBottom: '10px'\n                        },\n                        children: \"جاري تحميل البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#a0aec0',\n                            fontSize: '1rem'\n                        },\n                        children: \"يرجى الانتظار\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        fontSize: '1.2rem',\n                                        fontWeight: '900',\n                                        fontFamily: 'Arial, sans-serif',\n                                        gap: '5px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '900',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"X\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '1rem',\n                                                fontWeight: '300'\n                                            },\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '800',\n                                                letterSpacing: '1px'\n                                            },\n                                            children: \"Prime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        marginRight: sidebarOpen ? '280px' : '0',\n                        transition: 'margin-right 0.3s ease'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: \"لوحة التحكم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: \"رؤية عامة للعملية في الوقت الفعلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#68d391',\n                                                        margin: '5px 0 0 0',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"مراقبة \",\n                                                        stats.totalMedia,\n                                                        \" مادة إعلامية و \",\n                                                        stats.activeUsers,\n                                                        \" مستخدمين نشطين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"البيانات المحلية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"المزامنة: \",\n                                                        currentTime.toLocaleTimeString('ar-EG')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83C\\uDFAC\",\n                                    title: \"المواد الإعلامية\",\n                                    subtitle: \"عرض وإدارة المحتوى\",\n                                    path: \"/media-list\",\n                                    permission: \"MEDIA_READ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"➕\",\n                                    title: \"إضافة مادة\",\n                                    subtitle: \"إضافة محتوى جديد\",\n                                    path: \"/add-media\",\n                                    permission: \"MEDIA_CREATE\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    title: \"الخريطة البرامجية\",\n                                    subtitle: \"جدولة البرامج الأسبوعية\",\n                                    path: \"/weekly-schedule\",\n                                    permission: \"SCHEDULE_READ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCA\",\n                                    title: \"جدول الإذاعة اليومي\",\n                                    subtitle: \"البرامج المجدولة اليوم\",\n                                    path: \"/daily-schedule\",\n                                    permission: \"SCHEDULE_READ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83D\\uDC65\",\n                                    title: \"إدارة المستخدمين\",\n                                    subtitle: \"إضافة وتعديل المستخدمين\",\n                                    path: \"/admin-dashboard\",\n                                    adminOnly: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC8\",\n                                    title: \"الإحصائيات\",\n                                    subtitle: \"تقارير وإحصائيات مفصلة\",\n                                    path: \"/statistics\",\n                                    adminOnly: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StatsCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    title: \"التاريخ الحالي\",\n                                    value: new Date().toLocaleDateString('en-GB', {\n                                        weekday: 'long',\n                                        day: 'numeric',\n                                        month: 'short'\n                                    }),\n                                    subtitle: currentTime.toLocaleTimeString('ar-EG', {\n                                        hour: '2-digit',\n                                        minute: '2-digit'\n                                    }),\n                                    badge: \"مباشر\",\n                                    badgeColor: \"#68d391\",\n                                    gradient: \"linear-gradient(135deg, #1a1d29 0%, #2d3748 100%)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StatsCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCA\",\n                                    title: \"إجمالي المواد\",\n                                    value: stats.totalMedia,\n                                    subtitle: \"جميع المواد المسجلة\",\n                                    badge: \"+\".concat(stats.todayAdded, \" اليوم\"),\n                                    badgeColor: stats.todayAdded > 0 ? '#68d391' : '#6c757d',\n                                    gradient: \"linear-gradient(135deg, #1a1d29 0%, #2d3748 100%)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"uzZWCdRhffBS169JAH9V1+bLGc8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});