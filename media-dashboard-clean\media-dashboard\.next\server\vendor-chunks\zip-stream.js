/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zip-stream";
exports.ids = ["vendor-chunks/zip-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/zip-stream/index.js":
/*!******************************************!*\
  !*** ./node_modules/zip-stream/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * ZipStream\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-zip-stream/blob/master/LICENSE}\n * @copyright (c) 2014 Chris Talkington, contributors.\n */\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar ZipArchiveOutputStream = (__webpack_require__(/*! compress-commons */ \"(rsc)/./node_modules/compress-commons/lib/compress-commons.js\").ZipArchiveOutputStream);\nvar ZipArchiveEntry = (__webpack_require__(/*! compress-commons */ \"(rsc)/./node_modules/compress-commons/lib/compress-commons.js\").ZipArchiveEntry);\n\nvar util = __webpack_require__(/*! archiver-utils */ \"(rsc)/./node_modules/zip-stream/node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @extends external:ZipArchiveOutputStream\n * @param {Object} [options]\n * @param {String} [options.comment] Sets the zip archive comment.\n * @param {Boolean} [options.forceLocalTime=false] Forces the archive to contain local file times instead of UTC.\n * @param {Boolean} [options.forceZip64=false] Forces the archive to contain ZIP64 headers.\n * @param {Boolean} [options.store=false] Sets the compression method to STORE.\n * @param {Object} [options.zlib] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n * to control compression.\n */\nvar ZipStream = module.exports = function(options) {\n  if (!(this instanceof ZipStream)) {\n    return new ZipStream(options);\n  }\n\n  options = this.options = options || {};\n  options.zlib = options.zlib || {};\n\n  ZipArchiveOutputStream.call(this, options);\n\n  if (typeof options.level === 'number' && options.level >= 0) {\n    options.zlib.level = options.level;\n    delete options.level;\n  }\n\n  if (!options.forceZip64 && typeof options.zlib.level === 'number' && options.zlib.level === 0) {\n    options.store = true;\n  }\n\n  options.namePrependSlash = options.namePrependSlash || false;\n\n  if (options.comment && options.comment.length > 0) {\n    this.setComment(options.comment);\n  }\n};\n\ninherits(ZipStream, ZipArchiveOutputStream);\n\n/**\n * Normalizes entry data with fallbacks for key properties.\n *\n * @private\n * @param  {Object} data\n * @return {Object}\n */\nZipStream.prototype._normalizeFileData = function(data) {\n  data = util.defaults(data, {\n    type: 'file',\n    name: null,\n    namePrependSlash: this.options.namePrependSlash,\n    linkname: null,\n    date: null,\n    mode: null,\n    store: this.options.store,\n    comment: ''\n  });\n\n  var isDir = data.type === 'directory';\n  var isSymlink = data.type === 'symlink';\n\n  if (data.name) {\n    data.name = util.sanitizePath(data.name);\n\n    if (!isSymlink && data.name.slice(-1) === '/') {\n      isDir = true;\n      data.type = 'directory';\n    } else if (isDir) {\n      data.name += '/';\n    }\n  }\n\n  if (isDir || isSymlink) {\n    data.store = true;\n  }\n\n  data.date = util.dateify(data.date);\n\n  return data;\n};\n\n/**\n * Appends an entry given an input source (text string, buffer, or stream).\n *\n * @param  {(Buffer|Stream|String)} source The input source.\n * @param  {Object} data\n * @param  {String} data.name Sets the entry name including internal path.\n * @param  {String} [data.comment] Sets the entry comment.\n * @param  {(String|Date)} [data.date=NOW()] Sets the entry date.\n * @param  {Number} [data.mode=D:0755/F:0644] Sets the entry permissions.\n * @param  {Boolean} [data.store=options.store] Sets the compression method to STORE.\n * @param  {String} [data.type=file] Sets the entry type. Defaults to `directory`\n * if name ends with trailing slash.\n * @param  {Function} callback\n * @return this\n */\nZipStream.prototype.entry = function(source, data, callback) {\n  if (typeof callback !== 'function') {\n    callback = this._emitErrorCallback.bind(this);\n  }\n\n  data = this._normalizeFileData(data);\n\n  if (data.type !== 'file' && data.type !== 'directory' && data.type !== 'symlink') {\n    callback(new Error(data.type + ' entries not currently supported'));\n    return;\n  }\n\n  if (typeof data.name !== 'string' || data.name.length === 0) {\n    callback(new Error('entry name must be a non-empty string value'));\n    return;\n  }\n\n  if (data.type === 'symlink' && typeof data.linkname !== 'string') {\n    callback(new Error('entry linkname must be a non-empty string value when type equals symlink'));\n    return;\n  }\n\n  var entry = new ZipArchiveEntry(data.name);\n  entry.setTime(data.date, this.options.forceLocalTime);\n\n  if (data.namePrependSlash) {\n    entry.setName(data.name, true);\n  }\n\n  if (data.store) {\n    entry.setMethod(0);\n  }\n\n  if (data.comment.length > 0) {\n    entry.setComment(data.comment);\n  }\n\n  if (data.type === 'symlink' && typeof data.mode !== 'number') {\n    data.mode = 40960; // 0120000\n  }\n\n  if (typeof data.mode === 'number') {\n    if (data.type === 'symlink') {\n      data.mode |= 40960;\n    }\n\n    entry.setUnixMode(data.mode);\n  }\n\n  if (data.type === 'symlink' && typeof data.linkname === 'string') {\n    source = Buffer.from(data.linkname);\n  }\n\n  return ZipArchiveOutputStream.prototype.entry.call(this, entry, source, callback);\n};\n\n/**\n * Finalizes the instance and prevents further appending to the archive\n * structure (queue will continue til drained).\n *\n * @return void\n */\nZipStream.prototype.finalize = function() {\n  this.finish();\n};\n\n/**\n * Returns the current number of bytes written to this stream.\n * @function ZipStream#getBytesWritten\n * @returns {Number}\n */\n\n/**\n * Compress Commons ZipArchiveOutputStream\n * @external ZipArchiveOutputStream\n * @see {@link https://github.com/archiverjs/node-compress-commons}\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zip-stream/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/zip-stream/node_modules/archiver-utils/file.js":
/*!*********************************************************************!*\
  !*** ./node_modules/zip-stream/node_modules/archiver-utils/file.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * archiver-utils\n *\n * Copyright (c) 2012-2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-archiver/blob/master/LICENSE-MIT\n */\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\n\nvar flatten = __webpack_require__(/*! lodash.flatten */ \"(rsc)/./node_modules/lodash.flatten/index.js\");\nvar difference = __webpack_require__(/*! lodash.difference */ \"(rsc)/./node_modules/lodash.difference/index.js\");\nvar union = __webpack_require__(/*! lodash.union */ \"(rsc)/./node_modules/lodash.union/index.js\");\nvar isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(rsc)/./node_modules/lodash.isplainobject/index.js\");\n\nvar glob = __webpack_require__(/*! glob */ \"(rsc)/./node_modules/glob/glob.js\");\n\nvar file = module.exports = {};\n\nvar pathSeparatorRe = /[\\/\\\\]/g;\n\n// Process specified wildcard glob patterns or filenames against a\n// callback, excluding and uniquing files in the result set.\nvar processPatterns = function(patterns, fn) {\n  // Filepaths to return.\n  var result = [];\n  // Iterate over flattened patterns array.\n  flatten(patterns).forEach(function(pattern) {\n    // If the first character is ! it should be omitted\n    var exclusion = pattern.indexOf('!') === 0;\n    // If the pattern is an exclusion, remove the !\n    if (exclusion) { pattern = pattern.slice(1); }\n    // Find all matching files for this pattern.\n    var matches = fn(pattern);\n    if (exclusion) {\n      // If an exclusion, remove matching files.\n      result = difference(result, matches);\n    } else {\n      // Otherwise add matching files.\n      result = union(result, matches);\n    }\n  });\n  return result;\n};\n\n// True if the file path exists.\nfile.exists = function() {\n  var filepath = path.join.apply(path, arguments);\n  return fs.existsSync(filepath);\n};\n\n// Return an array of all file paths that match the given wildcard patterns.\nfile.expand = function(...args) {\n  // If the first argument is an options object, save those options to pass\n  // into the File.prototype.glob.sync method.\n  var options = isPlainObject(args[0]) ? args.shift() : {};\n  // Use the first argument if it's an Array, otherwise convert the arguments\n  // object to an array and use that.\n  var patterns = Array.isArray(args[0]) ? args[0] : args;\n  // Return empty set if there are no patterns or filepaths.\n  if (patterns.length === 0) { return []; }\n  // Return all matching filepaths.\n  var matches = processPatterns(patterns, function(pattern) {\n    // Find all matching files for this pattern.\n    return glob.sync(pattern, options);\n  });\n  // Filter result set?\n  if (options.filter) {\n    matches = matches.filter(function(filepath) {\n      filepath = path.join(options.cwd || '', filepath);\n      try {\n        if (typeof options.filter === 'function') {\n          return options.filter(filepath);\n        } else {\n          // If the file is of the right type and exists, this should work.\n          return fs.statSync(filepath)[options.filter]();\n        }\n      } catch(e) {\n        // Otherwise, it's probably not the right type.\n        return false;\n      }\n    });\n  }\n  return matches;\n};\n\n// Build a multi task \"files\" object dynamically.\nfile.expandMapping = function(patterns, destBase, options) {\n  options = Object.assign({\n    rename: function(destBase, destPath) {\n      return path.join(destBase || '', destPath);\n    }\n  }, options);\n  var files = [];\n  var fileByDest = {};\n  // Find all files matching pattern, using passed-in options.\n  file.expand(options, patterns).forEach(function(src) {\n    var destPath = src;\n    // Flatten?\n    if (options.flatten) {\n      destPath = path.basename(destPath);\n    }\n    // Change the extension?\n    if (options.ext) {\n      destPath = destPath.replace(/(\\.[^\\/]*)?$/, options.ext);\n    }\n    // Generate destination filename.\n    var dest = options.rename(destBase, destPath, options);\n    // Prepend cwd to src path if necessary.\n    if (options.cwd) { src = path.join(options.cwd, src); }\n    // Normalize filepaths to be unix-style.\n    dest = dest.replace(pathSeparatorRe, '/');\n    src = src.replace(pathSeparatorRe, '/');\n    // Map correct src path to dest path.\n    if (fileByDest[dest]) {\n      // If dest already exists, push this src onto that dest's src array.\n      fileByDest[dest].src.push(src);\n    } else {\n      // Otherwise create a new src-dest file mapping object.\n      files.push({\n        src: [src],\n        dest: dest,\n      });\n      // And store a reference for later use.\n      fileByDest[dest] = files[files.length - 1];\n    }\n  });\n  return files;\n};\n\n// reusing bits of grunt's multi-task source normalization\nfile.normalizeFilesArray = function(data) {\n  var files = [];\n\n  data.forEach(function(obj) {\n    var prop;\n    if ('src' in obj || 'dest' in obj) {\n      files.push(obj);\n    }\n  });\n\n  if (files.length === 0) {\n    return [];\n  }\n\n  files = _(files).chain().forEach(function(obj) {\n    if (!('src' in obj) || !obj.src) { return; }\n    // Normalize .src properties to flattened array.\n    if (Array.isArray(obj.src)) {\n      obj.src = flatten(obj.src);\n    } else {\n      obj.src = [obj.src];\n    }\n  }).map(function(obj) {\n    // Build options object, removing unwanted properties.\n    var expandOptions = Object.assign({}, obj);\n    delete expandOptions.src;\n    delete expandOptions.dest;\n\n    // Expand file mappings.\n    if (obj.expand) {\n      return file.expandMapping(obj.src, obj.dest, expandOptions).map(function(mapObj) {\n        // Copy obj properties to result.\n        var result = Object.assign({}, obj);\n        // Make a clone of the orig obj available.\n        result.orig = Object.assign({}, obj);\n        // Set .src and .dest, processing both as templates.\n        result.src = mapObj.src;\n        result.dest = mapObj.dest;\n        // Remove unwanted properties.\n        ['expand', 'cwd', 'flatten', 'rename', 'ext'].forEach(function(prop) {\n          delete result[prop];\n        });\n        return result;\n      });\n    }\n\n    // Copy obj properties to result, adding an .orig property.\n    var result = Object.assign({}, obj);\n    // Make a clone of the orig obj available.\n    result.orig = Object.assign({}, obj);\n\n    if ('src' in result) {\n      // Expose an expand-on-demand getter method as .src.\n      Object.defineProperty(result, 'src', {\n        enumerable: true,\n        get: function fn() {\n          var src;\n          if (!('result' in fn)) {\n            src = obj.src;\n            // If src is an array, flatten it. Otherwise, make it into an array.\n            src = Array.isArray(src) ? flatten(src) : [src];\n            // Expand src files, memoizing result.\n            fn.result = file.expand(expandOptions, src);\n          }\n          return fn.result;\n        }\n      });\n    }\n\n    if ('dest' in result) {\n      result.dest = obj.dest;\n    }\n\n    return result;\n  }).flatten().value();\n\n  return files;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvemlwLXN0cmVhbS9ub2RlX21vZHVsZXMvYXJjaGl2ZXItdXRpbHMvZmlsZS5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsbUJBQU8sQ0FBQyxvRUFBYTtBQUM5QixXQUFXLG1CQUFPLENBQUMsa0JBQU07O0FBRXpCLGNBQWMsbUJBQU8sQ0FBQyxvRUFBZ0I7QUFDdEMsaUJBQWlCLG1CQUFPLENBQUMsMEVBQW1CO0FBQzVDLFlBQVksbUJBQU8sQ0FBQyxnRUFBYztBQUNsQyxvQkFBb0IsbUJBQU8sQ0FBQyxnRkFBc0I7O0FBRWxELFdBQVcsbUJBQU8sQ0FBQywrQ0FBTTs7QUFFekI7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLHdDQUF3QztBQUN4QztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLE9BQU87QUFDUDs7QUFFQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBLGtDQUFrQzs7QUFFbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLEdBQUc7O0FBRUg7QUFDQSIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcemlwLXN0cmVhbVxcbm9kZV9tb2R1bGVzXFxhcmNoaXZlci11dGlsc1xcZmlsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGFyY2hpdmVyLXV0aWxzXG4gKlxuICogQ29weXJpZ2h0IChjKSAyMDEyLTIwMTQgQ2hyaXMgVGFsa2luZ3RvbiwgY29udHJpYnV0b3JzLlxuICogTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxuICogaHR0cHM6Ly9naXRodWIuY29tL2FyY2hpdmVyanMvbm9kZS1hcmNoaXZlci9ibG9iL21hc3Rlci9MSUNFTlNFLU1JVFxuICovXG52YXIgZnMgPSByZXF1aXJlKCdncmFjZWZ1bC1mcycpO1xudmFyIHBhdGggPSByZXF1aXJlKCdwYXRoJyk7XG5cbnZhciBmbGF0dGVuID0gcmVxdWlyZSgnbG9kYXNoLmZsYXR0ZW4nKTtcbnZhciBkaWZmZXJlbmNlID0gcmVxdWlyZSgnbG9kYXNoLmRpZmZlcmVuY2UnKTtcbnZhciB1bmlvbiA9IHJlcXVpcmUoJ2xvZGFzaC51bmlvbicpO1xudmFyIGlzUGxhaW5PYmplY3QgPSByZXF1aXJlKCdsb2Rhc2guaXNwbGFpbm9iamVjdCcpO1xuXG52YXIgZ2xvYiA9IHJlcXVpcmUoJ2dsb2InKTtcblxudmFyIGZpbGUgPSBtb2R1bGUuZXhwb3J0cyA9IHt9O1xuXG52YXIgcGF0aFNlcGFyYXRvclJlID0gL1tcXC9cXFxcXS9nO1xuXG4vLyBQcm9jZXNzIHNwZWNpZmllZCB3aWxkY2FyZCBnbG9iIHBhdHRlcm5zIG9yIGZpbGVuYW1lcyBhZ2FpbnN0IGFcbi8vIGNhbGxiYWNrLCBleGNsdWRpbmcgYW5kIHVuaXF1aW5nIGZpbGVzIGluIHRoZSByZXN1bHQgc2V0LlxudmFyIHByb2Nlc3NQYXR0ZXJucyA9IGZ1bmN0aW9uKHBhdHRlcm5zLCBmbikge1xuICAvLyBGaWxlcGF0aHMgdG8gcmV0dXJuLlxuICB2YXIgcmVzdWx0ID0gW107XG4gIC8vIEl0ZXJhdGUgb3ZlciBmbGF0dGVuZWQgcGF0dGVybnMgYXJyYXkuXG4gIGZsYXR0ZW4ocGF0dGVybnMpLmZvckVhY2goZnVuY3Rpb24ocGF0dGVybikge1xuICAgIC8vIElmIHRoZSBmaXJzdCBjaGFyYWN0ZXIgaXMgISBpdCBzaG91bGQgYmUgb21pdHRlZFxuICAgIHZhciBleGNsdXNpb24gPSBwYXR0ZXJuLmluZGV4T2YoJyEnKSA9PT0gMDtcbiAgICAvLyBJZiB0aGUgcGF0dGVybiBpcyBhbiBleGNsdXNpb24sIHJlbW92ZSB0aGUgIVxuICAgIGlmIChleGNsdXNpb24pIHsgcGF0dGVybiA9IHBhdHRlcm4uc2xpY2UoMSk7IH1cbiAgICAvLyBGaW5kIGFsbCBtYXRjaGluZyBmaWxlcyBmb3IgdGhpcyBwYXR0ZXJuLlxuICAgIHZhciBtYXRjaGVzID0gZm4ocGF0dGVybik7XG4gICAgaWYgKGV4Y2x1c2lvbikge1xuICAgICAgLy8gSWYgYW4gZXhjbHVzaW9uLCByZW1vdmUgbWF0Y2hpbmcgZmlsZXMuXG4gICAgICByZXN1bHQgPSBkaWZmZXJlbmNlKHJlc3VsdCwgbWF0Y2hlcyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIE90aGVyd2lzZSBhZGQgbWF0Y2hpbmcgZmlsZXMuXG4gICAgICByZXN1bHQgPSB1bmlvbihyZXN1bHQsIG1hdGNoZXMpO1xuICAgIH1cbiAgfSk7XG4gIHJldHVybiByZXN1bHQ7XG59O1xuXG4vLyBUcnVlIGlmIHRoZSBmaWxlIHBhdGggZXhpc3RzLlxuZmlsZS5leGlzdHMgPSBmdW5jdGlvbigpIHtcbiAgdmFyIGZpbGVwYXRoID0gcGF0aC5qb2luLmFwcGx5KHBhdGgsIGFyZ3VtZW50cyk7XG4gIHJldHVybiBmcy5leGlzdHNTeW5jKGZpbGVwYXRoKTtcbn07XG5cbi8vIFJldHVybiBhbiBhcnJheSBvZiBhbGwgZmlsZSBwYXRocyB0aGF0IG1hdGNoIHRoZSBnaXZlbiB3aWxkY2FyZCBwYXR0ZXJucy5cbmZpbGUuZXhwYW5kID0gZnVuY3Rpb24oLi4uYXJncykge1xuICAvLyBJZiB0aGUgZmlyc3QgYXJndW1lbnQgaXMgYW4gb3B0aW9ucyBvYmplY3QsIHNhdmUgdGhvc2Ugb3B0aW9ucyB0byBwYXNzXG4gIC8vIGludG8gdGhlIEZpbGUucHJvdG90eXBlLmdsb2Iuc3luYyBtZXRob2QuXG4gIHZhciBvcHRpb25zID0gaXNQbGFpbk9iamVjdChhcmdzWzBdKSA/IGFyZ3Muc2hpZnQoKSA6IHt9O1xuICAvLyBVc2UgdGhlIGZpcnN0IGFyZ3VtZW50IGlmIGl0J3MgYW4gQXJyYXksIG90aGVyd2lzZSBjb252ZXJ0IHRoZSBhcmd1bWVudHNcbiAgLy8gb2JqZWN0IHRvIGFuIGFycmF5IGFuZCB1c2UgdGhhdC5cbiAgdmFyIHBhdHRlcm5zID0gQXJyYXkuaXNBcnJheShhcmdzWzBdKSA/IGFyZ3NbMF0gOiBhcmdzO1xuICAvLyBSZXR1cm4gZW1wdHkgc2V0IGlmIHRoZXJlIGFyZSBubyBwYXR0ZXJucyBvciBmaWxlcGF0aHMuXG4gIGlmIChwYXR0ZXJucy5sZW5ndGggPT09IDApIHsgcmV0dXJuIFtdOyB9XG4gIC8vIFJldHVybiBhbGwgbWF0Y2hpbmcgZmlsZXBhdGhzLlxuICB2YXIgbWF0Y2hlcyA9IHByb2Nlc3NQYXR0ZXJucyhwYXR0ZXJucywgZnVuY3Rpb24ocGF0dGVybikge1xuICAgIC8vIEZpbmQgYWxsIG1hdGNoaW5nIGZpbGVzIGZvciB0aGlzIHBhdHRlcm4uXG4gICAgcmV0dXJuIGdsb2Iuc3luYyhwYXR0ZXJuLCBvcHRpb25zKTtcbiAgfSk7XG4gIC8vIEZpbHRlciByZXN1bHQgc2V0P1xuICBpZiAob3B0aW9ucy5maWx0ZXIpIHtcbiAgICBtYXRjaGVzID0gbWF0Y2hlcy5maWx0ZXIoZnVuY3Rpb24oZmlsZXBhdGgpIHtcbiAgICAgIGZpbGVwYXRoID0gcGF0aC5qb2luKG9wdGlvbnMuY3dkIHx8ICcnLCBmaWxlcGF0aCk7XG4gICAgICB0cnkge1xuICAgICAgICBpZiAodHlwZW9mIG9wdGlvbnMuZmlsdGVyID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICAgICAgcmV0dXJuIG9wdGlvbnMuZmlsdGVyKGZpbGVwYXRoKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAvLyBJZiB0aGUgZmlsZSBpcyBvZiB0aGUgcmlnaHQgdHlwZSBhbmQgZXhpc3RzLCB0aGlzIHNob3VsZCB3b3JrLlxuICAgICAgICAgIHJldHVybiBmcy5zdGF0U3luYyhmaWxlcGF0aClbb3B0aW9ucy5maWx0ZXJdKCk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2goZSkge1xuICAgICAgICAvLyBPdGhlcndpc2UsIGl0J3MgcHJvYmFibHkgbm90IHRoZSByaWdodCB0eXBlLlxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cbiAgcmV0dXJuIG1hdGNoZXM7XG59O1xuXG4vLyBCdWlsZCBhIG11bHRpIHRhc2sgXCJmaWxlc1wiIG9iamVjdCBkeW5hbWljYWxseS5cbmZpbGUuZXhwYW5kTWFwcGluZyA9IGZ1bmN0aW9uKHBhdHRlcm5zLCBkZXN0QmFzZSwgb3B0aW9ucykge1xuICBvcHRpb25zID0gT2JqZWN0LmFzc2lnbih7XG4gICAgcmVuYW1lOiBmdW5jdGlvbihkZXN0QmFzZSwgZGVzdFBhdGgpIHtcbiAgICAgIHJldHVybiBwYXRoLmpvaW4oZGVzdEJhc2UgfHwgJycsIGRlc3RQYXRoKTtcbiAgICB9XG4gIH0sIG9wdGlvbnMpO1xuICB2YXIgZmlsZXMgPSBbXTtcbiAgdmFyIGZpbGVCeURlc3QgPSB7fTtcbiAgLy8gRmluZCBhbGwgZmlsZXMgbWF0Y2hpbmcgcGF0dGVybiwgdXNpbmcgcGFzc2VkLWluIG9wdGlvbnMuXG4gIGZpbGUuZXhwYW5kKG9wdGlvbnMsIHBhdHRlcm5zKS5mb3JFYWNoKGZ1bmN0aW9uKHNyYykge1xuICAgIHZhciBkZXN0UGF0aCA9IHNyYztcbiAgICAvLyBGbGF0dGVuP1xuICAgIGlmIChvcHRpb25zLmZsYXR0ZW4pIHtcbiAgICAgIGRlc3RQYXRoID0gcGF0aC5iYXNlbmFtZShkZXN0UGF0aCk7XG4gICAgfVxuICAgIC8vIENoYW5nZSB0aGUgZXh0ZW5zaW9uP1xuICAgIGlmIChvcHRpb25zLmV4dCkge1xuICAgICAgZGVzdFBhdGggPSBkZXN0UGF0aC5yZXBsYWNlKC8oXFwuW15cXC9dKik/JC8sIG9wdGlvbnMuZXh0KTtcbiAgICB9XG4gICAgLy8gR2VuZXJhdGUgZGVzdGluYXRpb24gZmlsZW5hbWUuXG4gICAgdmFyIGRlc3QgPSBvcHRpb25zLnJlbmFtZShkZXN0QmFzZSwgZGVzdFBhdGgsIG9wdGlvbnMpO1xuICAgIC8vIFByZXBlbmQgY3dkIHRvIHNyYyBwYXRoIGlmIG5lY2Vzc2FyeS5cbiAgICBpZiAob3B0aW9ucy5jd2QpIHsgc3JjID0gcGF0aC5qb2luKG9wdGlvbnMuY3dkLCBzcmMpOyB9XG4gICAgLy8gTm9ybWFsaXplIGZpbGVwYXRocyB0byBiZSB1bml4LXN0eWxlLlxuICAgIGRlc3QgPSBkZXN0LnJlcGxhY2UocGF0aFNlcGFyYXRvclJlLCAnLycpO1xuICAgIHNyYyA9IHNyYy5yZXBsYWNlKHBhdGhTZXBhcmF0b3JSZSwgJy8nKTtcbiAgICAvLyBNYXAgY29ycmVjdCBzcmMgcGF0aCB0byBkZXN0IHBhdGguXG4gICAgaWYgKGZpbGVCeURlc3RbZGVzdF0pIHtcbiAgICAgIC8vIElmIGRlc3QgYWxyZWFkeSBleGlzdHMsIHB1c2ggdGhpcyBzcmMgb250byB0aGF0IGRlc3QncyBzcmMgYXJyYXkuXG4gICAgICBmaWxlQnlEZXN0W2Rlc3RdLnNyYy5wdXNoKHNyYyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIE90aGVyd2lzZSBjcmVhdGUgYSBuZXcgc3JjLWRlc3QgZmlsZSBtYXBwaW5nIG9iamVjdC5cbiAgICAgIGZpbGVzLnB1c2goe1xuICAgICAgICBzcmM6IFtzcmNdLFxuICAgICAgICBkZXN0OiBkZXN0LFxuICAgICAgfSk7XG4gICAgICAvLyBBbmQgc3RvcmUgYSByZWZlcmVuY2UgZm9yIGxhdGVyIHVzZS5cbiAgICAgIGZpbGVCeURlc3RbZGVzdF0gPSBmaWxlc1tmaWxlcy5sZW5ndGggLSAxXTtcbiAgICB9XG4gIH0pO1xuICByZXR1cm4gZmlsZXM7XG59O1xuXG4vLyByZXVzaW5nIGJpdHMgb2YgZ3J1bnQncyBtdWx0aS10YXNrIHNvdXJjZSBub3JtYWxpemF0aW9uXG5maWxlLm5vcm1hbGl6ZUZpbGVzQXJyYXkgPSBmdW5jdGlvbihkYXRhKSB7XG4gIHZhciBmaWxlcyA9IFtdO1xuXG4gIGRhdGEuZm9yRWFjaChmdW5jdGlvbihvYmopIHtcbiAgICB2YXIgcHJvcDtcbiAgICBpZiAoJ3NyYycgaW4gb2JqIHx8ICdkZXN0JyBpbiBvYmopIHtcbiAgICAgIGZpbGVzLnB1c2gob2JqKTtcbiAgICB9XG4gIH0pO1xuXG4gIGlmIChmaWxlcy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gW107XG4gIH1cblxuICBmaWxlcyA9IF8oZmlsZXMpLmNoYWluKCkuZm9yRWFjaChmdW5jdGlvbihvYmopIHtcbiAgICBpZiAoISgnc3JjJyBpbiBvYmopIHx8ICFvYmouc3JjKSB7IHJldHVybjsgfVxuICAgIC8vIE5vcm1hbGl6ZSAuc3JjIHByb3BlcnRpZXMgdG8gZmxhdHRlbmVkIGFycmF5LlxuICAgIGlmIChBcnJheS5pc0FycmF5KG9iai5zcmMpKSB7XG4gICAgICBvYmouc3JjID0gZmxhdHRlbihvYmouc3JjKTtcbiAgICB9IGVsc2Uge1xuICAgICAgb2JqLnNyYyA9IFtvYmouc3JjXTtcbiAgICB9XG4gIH0pLm1hcChmdW5jdGlvbihvYmopIHtcbiAgICAvLyBCdWlsZCBvcHRpb25zIG9iamVjdCwgcmVtb3ZpbmcgdW53YW50ZWQgcHJvcGVydGllcy5cbiAgICB2YXIgZXhwYW5kT3B0aW9ucyA9IE9iamVjdC5hc3NpZ24oe30sIG9iaik7XG4gICAgZGVsZXRlIGV4cGFuZE9wdGlvbnMuc3JjO1xuICAgIGRlbGV0ZSBleHBhbmRPcHRpb25zLmRlc3Q7XG5cbiAgICAvLyBFeHBhbmQgZmlsZSBtYXBwaW5ncy5cbiAgICBpZiAob2JqLmV4cGFuZCkge1xuICAgICAgcmV0dXJuIGZpbGUuZXhwYW5kTWFwcGluZyhvYmouc3JjLCBvYmouZGVzdCwgZXhwYW5kT3B0aW9ucykubWFwKGZ1bmN0aW9uKG1hcE9iaikge1xuICAgICAgICAvLyBDb3B5IG9iaiBwcm9wZXJ0aWVzIHRvIHJlc3VsdC5cbiAgICAgICAgdmFyIHJlc3VsdCA9IE9iamVjdC5hc3NpZ24oe30sIG9iaik7XG4gICAgICAgIC8vIE1ha2UgYSBjbG9uZSBvZiB0aGUgb3JpZyBvYmogYXZhaWxhYmxlLlxuICAgICAgICByZXN1bHQub3JpZyA9IE9iamVjdC5hc3NpZ24oe30sIG9iaik7XG4gICAgICAgIC8vIFNldCAuc3JjIGFuZCAuZGVzdCwgcHJvY2Vzc2luZyBib3RoIGFzIHRlbXBsYXRlcy5cbiAgICAgICAgcmVzdWx0LnNyYyA9IG1hcE9iai5zcmM7XG4gICAgICAgIHJlc3VsdC5kZXN0ID0gbWFwT2JqLmRlc3Q7XG4gICAgICAgIC8vIFJlbW92ZSB1bndhbnRlZCBwcm9wZXJ0aWVzLlxuICAgICAgICBbJ2V4cGFuZCcsICdjd2QnLCAnZmxhdHRlbicsICdyZW5hbWUnLCAnZXh0J10uZm9yRWFjaChmdW5jdGlvbihwcm9wKSB7XG4gICAgICAgICAgZGVsZXRlIHJlc3VsdFtwcm9wXTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBDb3B5IG9iaiBwcm9wZXJ0aWVzIHRvIHJlc3VsdCwgYWRkaW5nIGFuIC5vcmlnIHByb3BlcnR5LlxuICAgIHZhciByZXN1bHQgPSBPYmplY3QuYXNzaWduKHt9LCBvYmopO1xuICAgIC8vIE1ha2UgYSBjbG9uZSBvZiB0aGUgb3JpZyBvYmogYXZhaWxhYmxlLlxuICAgIHJlc3VsdC5vcmlnID0gT2JqZWN0LmFzc2lnbih7fSwgb2JqKTtcblxuICAgIGlmICgnc3JjJyBpbiByZXN1bHQpIHtcbiAgICAgIC8vIEV4cG9zZSBhbiBleHBhbmQtb24tZGVtYW5kIGdldHRlciBtZXRob2QgYXMgLnNyYy5cbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShyZXN1bHQsICdzcmMnLCB7XG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIGdldDogZnVuY3Rpb24gZm4oKSB7XG4gICAgICAgICAgdmFyIHNyYztcbiAgICAgICAgICBpZiAoISgncmVzdWx0JyBpbiBmbikpIHtcbiAgICAgICAgICAgIHNyYyA9IG9iai5zcmM7XG4gICAgICAgICAgICAvLyBJZiBzcmMgaXMgYW4gYXJyYXksIGZsYXR0ZW4gaXQuIE90aGVyd2lzZSwgbWFrZSBpdCBpbnRvIGFuIGFycmF5LlxuICAgICAgICAgICAgc3JjID0gQXJyYXkuaXNBcnJheShzcmMpID8gZmxhdHRlbihzcmMpIDogW3NyY107XG4gICAgICAgICAgICAvLyBFeHBhbmQgc3JjIGZpbGVzLCBtZW1vaXppbmcgcmVzdWx0LlxuICAgICAgICAgICAgZm4ucmVzdWx0ID0gZmlsZS5leHBhbmQoZXhwYW5kT3B0aW9ucywgc3JjKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIGZuLnJlc3VsdDtcbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgaWYgKCdkZXN0JyBpbiByZXN1bHQpIHtcbiAgICAgIHJlc3VsdC5kZXN0ID0gb2JqLmRlc3Q7XG4gICAgfVxuXG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfSkuZmxhdHRlbigpLnZhbHVlKCk7XG5cbiAgcmV0dXJuIGZpbGVzO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zip-stream/node_modules/archiver-utils/file.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/zip-stream/node_modules/archiver-utils/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/zip-stream/node_modules/archiver-utils/index.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * archiver-utils\n *\n * Copyright (c) 2015 Chris Talkington.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/archiver-utils/blob/master/LICENSE\n */\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar lazystream = __webpack_require__(/*! lazystream */ \"(rsc)/./node_modules/lazystream/lib/lazystream.js\");\nvar normalizePath = __webpack_require__(/*! normalize-path */ \"(rsc)/./node_modules/normalize-path/index.js\");\nvar defaults = __webpack_require__(/*! lodash.defaults */ \"(rsc)/./node_modules/lodash.defaults/index.js\");\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\nvar PassThrough = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\").PassThrough);\n\nvar utils = module.exports = {};\nutils.file = __webpack_require__(/*! ./file.js */ \"(rsc)/./node_modules/zip-stream/node_modules/archiver-utils/file.js\");\n\nutils.collectStream = function(source, callback) {\n  var collection = [];\n  var size = 0;\n\n  source.on('error', callback);\n\n  source.on('data', function(chunk) {\n    collection.push(chunk);\n    size += chunk.length;\n  });\n\n  source.on('end', function() {\n    var buf = Buffer.alloc(size);\n    var offset = 0;\n\n    collection.forEach(function(data) {\n      data.copy(buf, offset);\n      offset += data.length;\n    });\n\n    callback(null, buf);\n  });\n};\n\nutils.dateify = function(dateish) {\n  dateish = dateish || new Date();\n\n  if (dateish instanceof Date) {\n    dateish = dateish;\n  } else if (typeof dateish === 'string') {\n    dateish = new Date(dateish);\n  } else {\n    dateish = new Date();\n  }\n\n  return dateish;\n};\n\n// this is slightly different from lodash version\nutils.defaults = function(object, source, guard) {\n  var args = arguments;\n  args[0] = args[0] || {};\n\n  return defaults(...args);\n};\n\nutils.isStream = function(source) {\n  return source instanceof Stream;\n};\n\nutils.lazyReadStream = function(filepath) {\n  return new lazystream.Readable(function() {\n    return fs.createReadStream(filepath);\n  });\n};\n\nutils.normalizeInputSource = function(source) {\n  if (source === null) {\n    return Buffer.alloc(0);\n  } else if (typeof source === 'string') {\n    return Buffer.from(source);\n  } else if (utils.isStream(source)) {\n    // Always pipe through a PassThrough stream to guarantee pausing the stream if it's already flowing,\n    // since it will only be processed in a (distant) future iteration of the event loop, and will lose\n    // data if already flowing now.\n    return source.pipe(new PassThrough());\n  }\n\n  return source;\n};\n\nutils.sanitizePath = function(filepath) {\n  return normalizePath(filepath, false).replace(/^\\w+:/, '').replace(/^(\\.\\.\\/|\\/)+/, '');\n};\n\nutils.trailingSlashIt = function(str) {\n  return str.slice(-1) !== '/' ? str + '/' : str;\n};\n\nutils.unixifyPath = function(filepath) {\n  return normalizePath(filepath, false).replace(/^\\w+:/, '');\n};\n\nutils.walkdir = function(dirpath, base, callback) {\n  var results = [];\n\n  if (typeof base === 'function') {\n    callback = base;\n    base = dirpath;\n  }\n\n  fs.readdir(dirpath, function(err, list) {\n    var i = 0;\n    var file;\n    var filepath;\n\n    if (err) {\n      return callback(err);\n    }\n\n    (function next() {\n      file = list[i++];\n\n      if (!file) {\n        return callback(null, results);\n      }\n\n      filepath = path.join(dirpath, file);\n\n      fs.stat(filepath, function(err, stats) {\n        results.push({\n          path: filepath,\n          relative: path.relative(base, filepath).replace(/\\\\/g, '/'),\n          stats: stats\n        });\n\n        if (stats && stats.isDirectory()) {\n          utils.walkdir(filepath, base, function(err, res) {\n            res.forEach(function(dirEntry) {\n              results.push(dirEntry);\n            });\n            next();\n          });\n        } else {\n          next();\n        }\n      });\n    })();\n  });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/zip-stream/node_modules/archiver-utils/index.js\n");

/***/ })

};
;