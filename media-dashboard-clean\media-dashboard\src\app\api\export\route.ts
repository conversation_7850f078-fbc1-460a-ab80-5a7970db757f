import { NextResponse } from 'next/server';
import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';

// تحميل البيانات من الملف
function loadMediaData() {
  try {
    // محاولة تحميل من الملف المؤقت أولاً
    const tempFilePath = path.join(process.cwd(), 'temp-data.json');
    console.log(`🔍 البحث عن الملف المؤقت في: ${tempFilePath}`);

    if (fs.existsSync(tempFilePath)) {
      const fileContent = fs.readFileSync(tempFilePath, 'utf8');
      const data = JSON.parse(fileContent);
      console.log(`📂 تم تحميل ${data.length} مادة من الملف المؤقت`);
      return data;
    } else {
      console.log('📂 الملف المؤقت غير موجود');
    }

    console.log('📂 لم يتم العثور على أي ملفات بيانات');
    return [];
  } catch (error) {
    console.error('❌ خطأ في تحميل البيانات:', error);
    return [];
  }
}

// تحديد لون الخلفية حسب الحالة
function getBackgroundColor(status: string, expiryDate?: string): string {
  console.log('🎨 تحديد اللون للحالة:', status, 'تاريخ الانتهاء:', expiryDate);

  // التحقق من انتهاء التاريخ
  if (expiryDate) {
    const expiry = new Date(expiryDate);
    const now = new Date();
    if (expiry < now) {
      console.log('🔴 مادة منتهية الصلاحية - لون أحمر');
      return 'FFFF0000'; // أحمر - منتهي الصلاحية
    }
  }

  // تطبيع الحالة للمقارنة
  const normalizedStatus = status?.toLowerCase().trim();
  console.log('📝 الحالة المطبعة:', normalizedStatus);

  // حسب الحالة
  if (normalizedStatus === 'rejected' ||
      normalizedStatus === 'مرفوض' ||
      normalizedStatus === 'مرفوض رقابي' ||
      normalizedStatus === 'rejected_censorship') {
    console.log('🔴 مادة مرفوضة رقابياً - لون أحمر');
    return 'FFFF0000'; // أحمر - مرفوض
  } else if (normalizedStatus === 'engineering_rejected' ||
             normalizedStatus === 'رفض هندسي' ||
             normalizedStatus === 'مرفوض هندسي' ||
             normalizedStatus === 'rejected_technical') {
    console.log('⚫ رفض هندسي - لون أسود');
    return 'FF000000'; // أسود - رفض هندسي
  } else if (normalizedStatus === 'pending' ||
             normalizedStatus === 'انتظار' ||
             normalizedStatus === 'في الانتظار' ||
             normalizedStatus === 'waiting') {
    console.log('🔘 في الانتظار - لون رمادي');
    return 'FF808080'; // رمادي - انتظار
  } else {
    console.log('🟢 مادة موافقة - لون أخضر');
    return 'FF90EE90'; // أخضر فاتح - موافق (افتراضي)
  }
}

// تحديد لون النص حسب خلفية
function getTextColor(backgroundColor: string): string {
  if (backgroundColor === 'FF000000') {
    return 'FFFFFFFF'; // نص أبيض على خلفية سوداء
  }
  return 'FF000000'; // نص أسود على باقي الخلفيات
}

// تنسيق البيانات للتصدير - كل سيجمنت في صف منفصل
function formatDataForExport(mediaData: any[], mediaType: string) {
  const formattedData: any[] = [];
  let segmentCounter = 1;

  mediaData.forEach((item) => {
    const segments = item.segments && item.segments.length > 0 ? item.segments : [{}];

    segments.forEach((segment: any) => {
      // البيانات الأساسية المشتركة حسب الترتيب الجديد
      const baseData = {
        'كود السيجمنت': segment?.code || `SEG${segmentCounter.toString().padStart(3, '0')}`,
        'نوع المادة': getTypeLabel(item.type) || '',
        'اسم المادة': item.name || '',
        'URL': item.source || '',
        'رقم الهارد': item.hardNumber || '',
        'Dur': segment?.duration || '00:00:00',
        'TC Out': segment?.timeOut || '00:00:00',
        'TC In': segment?.timeIn || '00:00:00'
      };

      // إضافة أعمدة خاصة حسب نوع المادة
      let specificColumns = {};

      if (mediaType === 'MOVIE') {
        // للأفلام: رقم الجزء بدلاً من رقم الحلقة
        specificColumns = {
          'رقم الجزء': item.partNumber || item.episodeNumber || ''
        };
      } else if (mediaType === 'SERIES') {
        // للمسلسلات: رقم الحلقة + رقم الجزء
        specificColumns = {
          'رقم الحلقة': item.episodeNumber || '',
          'رقم الجزء': item.partNumber || ''
        };
      } else if (mediaType === 'PROGRAM') {
        // للبرامج: رقم الحلقة + الموسم
        specificColumns = {
          'رقم الحلقة': item.episodeNumber || '',
          'الموسم': item.season || ''
        };
      } else {
        // للأنواع الأخرى: رقم الحلقة فقط
        specificColumns = {
          'رقم الحلقة': item.episodeNumber || ''
        };
      }

      // البيانات النهائية
      const finalData = {
        ...baseData,
        ...specificColumns,
        'الملاحظات': item.notes || item.description || '',
        'الحالة': getStatusLabel(item.status) || 'موافق',
        'تاريخ البداية': item.startDate || '',
        'تاريخ الانتهاء': item.endDate || ''
      };

      formattedData.push(finalData);
      segmentCounter++;
    });
  });

  return formattedData;
}

// دوال مساعدة للتسميات
function getTypeLabel(type: string): string {
  const types: { [key: string]: string } = {
    PROGRAM: 'برنامج',
    SERIES: 'مسلسل',
    MOVIE: 'فيلم',
    SONG: 'أغنية',
    STING: 'Sting',
    FILL_IN: 'Fill IN',
    FILLER: 'Filler',
    PROMO: 'Promo'
  };
  return types[type] || type;
}

function getStatusLabel(status: string): string {
  const statuses: { [key: string]: string } = {
    VALID: 'موافق',
    REJECTED_CENSORSHIP: 'مرفوض رقابي',
    REJECTED_TECHNICAL: 'مرفوض هندسي',
    WAITING: 'في الانتظار'
  };
  return statuses[status] || status;
}

function getChannelLabel(channel: string): string {
  const channels: { [key: string]: string } = {
    DOCUMENTARY: 'الوثائقية',
    NEWS: 'الأخبار',
    OTHER: 'أخرى'
  };
  return channels[channel] || channel;
}



export async function GET() {
  try {
    console.log('📊 بدء عملية تصدير قاعدة البيانات...');

    // تحميل البيانات
    const mediaData = loadMediaData();

    if (mediaData.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لا توجد بيانات للتصدير'
      });
    }

    // تجميع البيانات حسب النوع
    const groupedData: { [key: string]: any[] } = {};

    mediaData.forEach((item: any) => {
      const type = item.type || 'غير محدد';
      if (!groupedData[type]) {
        groupedData[type] = [];
      }
      groupedData[type].push(item);
    });

    console.log('📋 أنواع المواد المكتشفة:', Object.keys(groupedData));

    // إنشاء ملف Excel باستخدام ExcelJS
    const workbook = new ExcelJS.Workbook();

    // تعيين خصائص الملف
    workbook.creator = 'نظام إدارة المواد الإعلامية';
    workbook.title = 'قاعدة بيانات المواد الإعلامية';
    workbook.subject = 'تصدير شامل لجميع المواد';
    workbook.created = new Date();

    // إضافة ورقة عمل لكل نوع مادة
    for (const type of Object.keys(groupedData)) {
      const typeData = groupedData[type];
      const formattedData = formatDataForExport(typeData, type);

      // إنشاء ورقة عمل
      const worksheet = workbook.addWorksheet(getTypeLabel(type));

      // تعيين اتجاه الورقة من اليمين لليسار
      worksheet.views = [{
        rightToLeft: true,
        zoomScale: 70
      }];

      // إضافة الرؤوس حسب الترتيب الجديد من الصورة
      let headers = [
        'كود السيجمنت', 'نوع المادة', 'اسم المادة', 'URL', 'رقم الهارد',
        'Dur', 'TC Out', 'TC In'
      ];

      // إضافة أعمدة خاصة حسب نوع المادة
      if (type === 'MOVIE') {
        headers.push('رقم الجزء');
      } else if (type === 'SERIES') {
        headers.push('رقم الحلقة', 'رقم الجزء');
      } else if (type === 'PROGRAM') {
        headers.push('رقم الحلقة', 'الموسم');
      } else {
        headers.push('رقم الحلقة');
      }

      // إضافة باقي الأعمدة
      headers.push('الملاحظات', 'الحالة', 'تاريخ البداية', 'تاريخ الانتهاء');

      worksheet.addRow(headers);

      // تنسيق الرؤوس
      const headerRow = worksheet.getRow(1);
      headerRow.height = 25;
      headerRow.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FF4472C4' }
        };
        cell.font = {
          bold: true,
          color: { argb: 'FFFFFFFF' },
          size: 12
        };
        cell.alignment = {
          horizontal: 'center',
          vertical: 'middle'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });

      // إضافة البيانات
      formattedData.forEach((item) => {
        // إنشاء صف البيانات الأساسية حسب الترتيب الجديد
        let rowData = [
          item['كود السيجمنت'],
          item['نوع المادة'],
          item['اسم المادة'],
          item['URL'],
          item['رقم الهارد'],
          item['Dur'],
          item['TC Out'],
          item['TC In']
        ];

        // إضافة الأعمدة الخاصة حسب نوع المادة
        if (type === 'MOVIE') {
          rowData.push((item as any)['رقم الجزء'] || '');
        } else if (type === 'SERIES') {
          rowData.push((item as any)['رقم الحلقة'] || '', (item as any)['رقم الجزء'] || '');
        } else if (type === 'PROGRAM') {
          rowData.push((item as any)['رقم الحلقة'] || '', (item as any)['الموسم'] || '');
        } else {
          rowData.push((item as any)['رقم الحلقة'] || '');
        }

        // إضافة باقي البيانات
        rowData.push(
          item['الملاحظات'],
          item['الحالة'],
          item['تاريخ البداية'],
          item['تاريخ الانتهاء']
        );

        const row = worksheet.addRow(rowData);

        // تنسيق البيانات
        const backgroundColor = getBackgroundColor(item['الحالة'], item['تاريخ الانتهاء']);
        const textColor = getTextColor(backgroundColor);

        row.eachCell((cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: backgroundColor }
          };
          cell.font = {
            color: { argb: textColor },
            size: 10
          };
          cell.alignment = {
            horizontal: 'center',
            vertical: 'middle'
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });

      // تعيين عرض الأعمدة حسب الترتيب الجديد
      let columnWidths = [15, 12, 25, 20, 12, 10, 10, 10]; // الأعمدة الأساسية

      // إضافة عرض للأعمدة الخاصة
      if (type === 'MOVIE') {
        columnWidths.push(12); // رقم الجزء
      } else if (type === 'SERIES') {
        columnWidths.push(12, 12); // رقم الحلقة، رقم الجزء
      } else if (type === 'PROGRAM') {
        columnWidths.push(12, 12); // رقم الحلقة، الموسم
      } else {
        columnWidths.push(12); // رقم الحلقة
      }

      // إضافة عرض باقي الأعمدة
      columnWidths.push(20, 12, 15, 15); // الملاحظات، الحالة، تاريخ البداية، تاريخ الانتهاء

      columnWidths.forEach((width, index) => {
        worksheet.getColumn(index + 1).width = width;
      });

      console.log(`✅ تم إنشاء تاب "${type}" مع ${typeData.length} مادة`);
    }

    // تحويل إلى buffer
    const buffer = await workbook.xlsx.writeBuffer();

    console.log('✅ تم إنشاء ملف Excel بنجاح');

    // إرسال الملف
    const fileName = `Media_Database_${new Date().toISOString().split('T')[0]}.xlsx`;

    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': buffer.byteLength.toString(),
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('❌ خطأ في تصدير البيانات:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في تصدير البيانات'
    }, { status: 500 });
  }
}
