"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/crc32-stream";
exports.ids = ["vendor-chunks/crc32-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/crc32-stream/lib/crc32-stream.js":
/*!*******************************************************!*\
  !*** ./node_modules/crc32-stream/lib/crc32-stream.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n \n\nconst {Transform} = __webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\");\n\nconst crc32 = __webpack_require__(/*! crc-32 */ \"(rsc)/./node_modules/crc-32/crc32.js\");\n\nclass CRC32Stream extends Transform {\n  constructor(options) {\n    super(options);\n    this.checksum = Buffer.allocUnsafe(4);\n    this.checksum.writeInt32BE(0, 0);\n\n    this.rawSize = 0;\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (chunk) {\n      this.checksum = crc32.buf(chunk, this.checksum) >>> 0;\n      this.rawSize += chunk.length;\n    }\n\n    callback(null, chunk);\n  }\n\n  digest(encoding) {\n    const checksum = Buffer.allocUnsafe(4);\n    checksum.writeUInt32BE(this.checksum >>> 0, 0);\n    return encoding ? checksum.toString(encoding) : checksum;\n  }\n\n  hex() {\n    return this.digest('hex').toUpperCase();\n  }\n\n  size() {\n    return this.rawSize;\n  }\n}\n\nmodule.exports = CRC32Stream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/crc32-stream/lib/crc32-stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js":
/*!***************************************************************!*\
  !*** ./node_modules/crc32-stream/lib/deflate-crc32-stream.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n\n\nconst {DeflateRaw} = __webpack_require__(/*! zlib */ \"zlib\");\n\nconst crc32 = __webpack_require__(/*! crc-32 */ \"(rsc)/./node_modules/crc-32/crc32.js\");\n\nclass DeflateCRC32Stream extends DeflateRaw {\n  constructor(options) {\n    super(options);\n\n    this.checksum = Buffer.allocUnsafe(4);\n    this.checksum.writeInt32BE(0, 0);\n\n    this.rawSize = 0;\n    this.compressedSize = 0;\n  }\n\n  push(chunk, encoding) {\n    if (chunk) {\n      this.compressedSize += chunk.length;\n    }\n\n    return super.push(chunk, encoding);\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (chunk) {\n      this.checksum = crc32.buf(chunk, this.checksum) >>> 0;\n      this.rawSize += chunk.length;\n    }\n\n    super._transform(chunk, encoding, callback)\n  }\n\n  digest(encoding) {\n    const checksum = Buffer.allocUnsafe(4);\n    checksum.writeUInt32BE(this.checksum >>> 0, 0);\n    return encoding ? checksum.toString(encoding) : checksum;\n  }\n\n  hex() {\n    return this.digest('hex').toUpperCase();\n  }\n\n  size(compressed = false) {\n    if (compressed) {\n      return this.compressedSize;\n    } else {\n      return this.rawSize;\n    }\n  }\n}\n\nmodule.exports = DeflateCRC32Stream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/crc32-stream/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/crc32-stream/lib/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n\n\nmodule.exports = {\n  CRC32Stream: __webpack_require__(/*! ./crc32-stream */ \"(rsc)/./node_modules/crc32-stream/lib/crc32-stream.js\"),\n  DeflateCRC32Stream: __webpack_require__(/*! ./deflate-crc32-stream */ \"(rsc)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js\")\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY3JjMzItc3RyZWFtL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFYTs7QUFFYjtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw2RUFBZ0I7QUFDdkMsc0JBQXNCLG1CQUFPLENBQUMsNkZBQXdCO0FBQ3REIiwic291cmNlcyI6WyJEOlxcRG9jIGRhdGFiYXNlXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxjcmMzMi1zdHJlYW1cXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBub2RlLWNyYzMyLXN0cmVhbVxuICpcbiAqIENvcHlyaWdodCAoYykgMjAxNCBDaHJpcyBUYWxraW5ndG9uLCBjb250cmlidXRvcnMuXG4gKiBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UuXG4gKiBodHRwczovL2dpdGh1Yi5jb20vYXJjaGl2ZXJqcy9ub2RlLWNyYzMyLXN0cmVhbS9ibG9iL21hc3Rlci9MSUNFTlNFLU1JVFxuICovXG5cbid1c2Ugc3RyaWN0JztcblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIENSQzMyU3RyZWFtOiByZXF1aXJlKCcuL2NyYzMyLXN0cmVhbScpLFxuICBEZWZsYXRlQ1JDMzJTdHJlYW06IHJlcXVpcmUoJy4vZGVmbGF0ZS1jcmMzMi1zdHJlYW0nKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/crc32-stream/lib/index.js\n");

/***/ })

};
;