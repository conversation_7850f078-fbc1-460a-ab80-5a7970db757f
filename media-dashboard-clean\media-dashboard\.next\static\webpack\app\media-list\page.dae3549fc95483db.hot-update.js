"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/media-list/page",{

/***/ "(app-pages-browser)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MediaListPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction MediaListPage() {\n    _s();\n    const [mediaItems, setMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            fetchMediaItems();\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            filterAndSortItems();\n        }\n    }[\"MediaListPage.useEffect\"], [\n        mediaItems,\n        searchTerm,\n        selectedType,\n        selectedStatus,\n        sortBy\n    ]);\n    const fetchMediaItems = async ()=>{\n        try {\n            const response = await fetch('/api/media');\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(result.data);\n            } else {\n                setError(result.error);\n            }\n        } catch (error) {\n            console.error('Error fetching media items:', error);\n            setError('فشل في جلب المواد الإعلامية');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterAndSortItems = ()=>{\n        let filtered = [\n            ...mediaItems\n        ];\n        // البحث بالاسم\n        if (searchTerm) {\n            filtered = filtered.filter((item)=>item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // فلترة بالنوع\n        if (selectedType !== 'ALL') {\n            filtered = filtered.filter((item)=>item.type === selectedType);\n        }\n        // فلترة بالحالة\n        if (selectedStatus !== 'ALL') {\n            filtered = filtered.filter((item)=>item.status === selectedStatus);\n        }\n        // الترتيب\n        switch(sortBy){\n            case 'newest':\n                filtered.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                break;\n            case 'oldest':\n                filtered.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n                break;\n            case 'name':\n                filtered.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n                break;\n            case 'type':\n                filtered.sort((a, b)=>a.type.localeCompare(b.type));\n                break;\n        }\n        setFilteredItems(filtered);\n    };\n    const deleteMediaItem = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذه المادة؟')) return;\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(mediaItems.filter((item)=>item.id !== id));\n                alert('تم حذف المادة بنجاح');\n            } else {\n                alert('فشل في حذف المادة: ' + result.error);\n            }\n        } catch (error) {\n            console.error('Error deleting media item:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    const exportToExcel = async ()=>{\n        setIsExporting(true);\n        try {\n            console.log('🚀 بدء تصدير قاعدة البيانات...');\n            const response = await fetch('/api/export');\n            if (!response.ok) {\n                throw new Error('فشل في تصدير البيانات');\n            }\n            // الحصول على الملف كـ blob\n            const blob = await response.blob();\n            // إنشاء رابط التحميل\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            // تحديد اسم الملف\n            const fileName = \"Media_Database_\".concat(new Date().toISOString().split('T')[0], \".xlsx\");\n            link.download = fileName;\n            // تحميل الملف\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            // تنظيف الذاكرة\n            window.URL.revokeObjectURL(url);\n            console.log('✅ تم تصدير قاعدة البيانات بنجاح');\n            alert('✅ تم تصدير قاعدة البيانات بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في التصدير:', error);\n            alert('❌ فشل في تصدير قاعدة البيانات. يرجى المحاولة مرة أخرى.');\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const getTypeLabel = (type)=>{\n        const types = {\n            PROGRAM: 'برنامج',\n            SERIES: 'مسلسل',\n            MOVIE: 'فيلم',\n            SONG: 'أغنية',\n            STING: 'Sting',\n            FILL_IN: 'Fill IN',\n            FILLER: 'Filler',\n            PROMO: 'Promo'\n        };\n        return types[type] || type;\n    };\n    const getStatusLabel = (status)=>{\n        const statuses = {\n            VALID: 'صالح',\n            REJECTED_CENSORSHIP: 'مرفوض رقابي',\n            REJECTED_TECHNICAL: 'مرفوض هندسي',\n            WAITING: 'في الانتظار'\n        };\n        return statuses[status] || status;\n    };\n    const getChannelLabel = (channel)=>{\n        const channels = {\n            DOCUMENTARY: 'الوثائقية',\n            NEWS: 'الأخبار',\n            OTHER: 'أخرى'\n        };\n        return channels[channel] || channel;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: \"⏳ جاري التحميل...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"❌ خطأ: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 231,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 224,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: '#1a1d29',\n            padding: '20px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '30px',\n                            marginBottom: '30px',\n                            boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: '#2c3e50',\n                                    marginBottom: '20px',\n                                    fontSize: '2.5rem',\n                                    fontWeight: 'bold'\n                                },\n                                children: \"\\uD83D\\uDCDA قائمة المواد الإعلامية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center',\n                                    flexWrap: 'wrap'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Link, {\n                                        href: \"/add-media\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            textDecoration: 'none',\n                                            fontWeight: 'bold',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)'\n                                        },\n                                        children: \"➕ إضافة مادة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: exportToExcel,\n                                        disabled: isExporting,\n                                        style: {\n                                            background: isExporting ? 'linear-gradient(45deg, #6c757d, #5a6268)' : 'linear-gradient(45deg, #17a2b8, #138496)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            border: 'none',\n                                            fontWeight: 'bold',\n                                            cursor: isExporting ? 'not-allowed' : 'pointer',\n                                            boxShadow: '0 4px 15px rgba(23,162,184,0.3)',\n                                            fontSize: '1rem'\n                                        },\n                                        children: isExporting ? '⏳ جاري التصدير...' : '📊 تصدير Excel'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Link, {\n                                        href: \"/\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            textDecoration: 'none',\n                                            fontWeight: 'bold',\n                                            boxShadow: '0 4px 15px rgba(0,123,255,0.3)'\n                                        },\n                                        children: \"\\uD83C\\uDFE0 الرئيسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            boxShadow: '0 8px 25px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#2c3e50',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: \"\\uD83D\\uDD0D البحث والفلترة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"البحث بالاسم أو الوصف:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"ابحث عن مادة إعلامية...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"نوع المادة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedType,\n                                                onChange: (e)=>setSelectedType(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ALL\",\n                                                        children: \"جميع الأنواع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"PROGRAM\",\n                                                        children: \"برنامج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SERIES\",\n                                                        children: \"مسلسل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"MOVIE\",\n                                                        children: \"فيلم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SONG\",\n                                                        children: \"أغنية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"STING\",\n                                                        children: \"Sting\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FILL_IN\",\n                                                        children: \"Fill IN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FILLER\",\n                                                        children: \"Filler\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"PROMO\",\n                                                        children: \"Promo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"الحالة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ALL\",\n                                                        children: \"جميع الحالات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"VALID\",\n                                                        children: \"صالح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_CENSORSHIP\",\n                                                        children: \"مرفوض رقابي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_TECHNICAL\",\n                                                        children: \"مرفوض هندسي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"WAITING\",\n                                                        children: \"في الانتظار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"ترتيب حسب:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"newest\",\n                                                        children: \"الأحدث أولاً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"oldest\",\n                                                        children: \"الأقدم أولاً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: \"الاسم (أ-ي)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"type\",\n                                                        children: \"النوع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '15px',\n                                    padding: '10px',\n                                    background: '#f8f9fa',\n                                    borderRadius: '8px',\n                                    textAlign: 'center',\n                                    color: '#6c757d'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA عرض \",\n                                    filteredItems.length,\n                                    \" من أصل \",\n                                    mediaItems.length,\n                                    \" مادة إعلامية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '50px',\n                            textAlign: 'center',\n                            boxShadow: '0 10px 30px rgba(0,0,0,0.2)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#6c757d',\n                                    fontSize: '1.5rem'\n                                },\n                                children: \"\\uD83D\\uDCED لا توجد مواد إعلامية محفوظة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: '#6c757d',\n                                    marginTop: '10px'\n                                },\n                                children: \"ابدأ بإضافة مادة إعلامية جديدة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gap: '20px'\n                        },\n                        children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'rgba(255,255,255,0.95)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    boxShadow: '0 8px 25px rgba(0,0,0,0.1)',\n                                    border: '1px solid #e9ecef'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr auto',\n                                        gap: '20px',\n                                        alignItems: 'start'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#2c3e50',\n                                                        marginBottom: '15px',\n                                                        fontSize: '1.4rem'\n                                                    },\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                        gap: '15px',\n                                                        marginBottom: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"النوع:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getTypeLabel(item.type)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"القناة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getChannelLabel(item.channel)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الحالة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getStatusLabel(item.status)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"عدد السيجمانت:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                item.segments.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#6c757d',\n                                                        marginBottom: '10px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الوصف:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 491,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \" \",\n                                                        item.description\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.segments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"السيجمانت:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'grid',\n                                                                gap: '8px',\n                                                                marginTop: '8px'\n                                                            },\n                                                            children: item.segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        background: '#f8f9fa',\n                                                                        padding: '8px 12px',\n                                                                        borderRadius: '8px',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: [\n                                                                                \"#\",\n                                                                                segment.segmentNumber\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \" -\",\n                                                                        segment.code && \" \".concat(segment.code, \" - \"),\n                                                                        segment.timeIn,\n                                                                        \" → \",\n                                                                        segment.timeOut,\n                                                                        \" (\",\n                                                                        segment.duration,\n                                                                        \")\"\n                                                                    ]\n                                                                }, segment.id, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                gap: '10px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        // توجيه لصفحة التعديل مع معرف المادة\n                                                        window.location.href = \"/edit-media?id=\".concat(item.id);\n                                                    },\n                                                    style: {\n                                                        background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '8px 16px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '0.9rem',\n                                                        marginBottom: '5px'\n                                                    },\n                                                    children: \"✏️ تعديل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>deleteMediaItem(item.id),\n                                                    style: {\n                                                        background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '8px 16px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDDD1️ حذف\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 17\n                                }, this)\n                            }, item.id, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 459,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    bottom: '20px',\n                    left: '20px',\n                    color: '#6c757d',\n                    fontSize: '0.75rem',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: 'ltr'\n                },\n                children: \"Powered By Mahmoud Ismail\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(MediaListPage, \"EP5OjVEHjSPUFe6kTv2ugGTcOAs=\");\n_c = MediaListPage;\nvar _c;\n$RefreshReg$(_c, \"MediaListPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbWVkaWEtbGlzdC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNEM7QUErQjdCLFNBQVNFOztJQUN0QixNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR0osK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNLLGVBQWVDLGlCQUFpQixHQUFHTiwrQ0FBUUEsQ0FBYyxFQUFFO0lBQ2xFLE1BQU0sQ0FBQ08sU0FBU0MsV0FBVyxHQUFHUiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNTLE9BQU9DLFNBQVMsR0FBR1YsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ1csWUFBWUMsY0FBYyxHQUFHWiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNhLGNBQWNDLGdCQUFnQixHQUFHZCwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNlLGdCQUFnQkMsa0JBQWtCLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNpQixRQUFRQyxVQUFVLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUNtQixhQUFhQyxlQUFlLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUUvQ0MsZ0RBQVNBO21DQUFDO1lBQ1JvQjtRQUNGO2tDQUFHLEVBQUU7SUFFTHBCLGdEQUFTQTttQ0FBQztZQUNScUI7UUFDRjtrQ0FBRztRQUFDbkI7UUFBWVE7UUFBWUU7UUFBY0U7UUFBZ0JFO0tBQU87SUFFakUsTUFBTUksa0JBQWtCO1FBQ3RCLElBQUk7WUFDRixNQUFNRSxXQUFXLE1BQU1DLE1BQU07WUFDN0IsTUFBTUMsU0FBUyxNQUFNRixTQUFTRyxJQUFJO1lBRWxDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEJ2QixjQUFjcUIsT0FBT0csSUFBSTtZQUMzQixPQUFPO2dCQUNMbEIsU0FBU2UsT0FBT2hCLEtBQUs7WUFDdkI7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZG9CLFFBQVFwQixLQUFLLENBQUMsK0JBQStCQTtZQUM3Q0MsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNYyxxQkFBcUI7UUFDekIsSUFBSVEsV0FBVztlQUFJM0I7U0FBVztRQUU5QixlQUFlO1FBQ2YsSUFBSVEsWUFBWTtZQUNkbUIsV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQSxPQUN6QkEsS0FBS0MsSUFBSSxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ3hCLFdBQVd1QixXQUFXLE9BQ3RERixLQUFLSSxXQUFXLElBQUlKLEtBQUtJLFdBQVcsQ0FBQ0YsV0FBVyxHQUFHQyxRQUFRLENBQUN4QixXQUFXdUIsV0FBVztRQUV2RjtRQUVBLGVBQWU7UUFDZixJQUFJckIsaUJBQWlCLE9BQU87WUFDMUJpQixXQUFXQSxTQUFTQyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtLLElBQUksS0FBS3hCO1FBQ25EO1FBRUEsZ0JBQWdCO1FBQ2hCLElBQUlFLG1CQUFtQixPQUFPO1lBQzVCZSxXQUFXQSxTQUFTQyxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtNLE1BQU0sS0FBS3ZCO1FBQ3JEO1FBRUEsVUFBVTtRQUNWLE9BQVFFO1lBQ04sS0FBSztnQkFDSGEsU0FBU1MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU0sSUFBSUMsS0FBS0QsRUFBRUUsU0FBUyxFQUFFQyxPQUFPLEtBQUssSUFBSUYsS0FBS0YsRUFBRUcsU0FBUyxFQUFFQyxPQUFPO2dCQUN2RjtZQUNGLEtBQUs7Z0JBQ0hkLFNBQVNTLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNLElBQUlDLEtBQUtGLEVBQUVHLFNBQVMsRUFBRUMsT0FBTyxLQUFLLElBQUlGLEtBQUtELEVBQUVFLFNBQVMsRUFBRUMsT0FBTztnQkFDdkY7WUFDRixLQUFLO2dCQUNIZCxTQUFTUyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRVAsSUFBSSxDQUFDWSxhQUFhLENBQUNKLEVBQUVSLElBQUksRUFBRTtnQkFDckQ7WUFDRixLQUFLO2dCQUNISCxTQUFTUyxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRUgsSUFBSSxDQUFDUSxhQUFhLENBQUNKLEVBQUVKLElBQUk7Z0JBQ25EO1FBQ0o7UUFFQS9CLGlCQUFpQndCO0lBQ25CO0lBRUEsTUFBTWdCLGtCQUFrQixPQUFPQztRQUM3QixJQUFJLENBQUNDLFFBQVEsb0NBQW9DO1FBRWpELElBQUk7WUFDRixNQUFNekIsV0FBVyxNQUFNQyxNQUFNLGlCQUFvQixPQUFIdUIsS0FBTTtnQkFDbERFLFFBQVE7WUFDVjtZQUVBLE1BQU14QixTQUFTLE1BQU1GLFNBQVNHLElBQUk7WUFFbEMsSUFBSUQsT0FBT0UsT0FBTyxFQUFFO2dCQUNsQnZCLGNBQWNELFdBQVc0QixNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtlLEVBQUUsS0FBS0E7Z0JBQ3BERyxNQUFNO1lBQ1IsT0FBTztnQkFDTEEsTUFBTSx3QkFBd0J6QixPQUFPaEIsS0FBSztZQUM1QztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkb0IsUUFBUXBCLEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDeUMsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNQyxnQkFBZ0I7UUFDcEIvQixlQUFlO1FBQ2YsSUFBSTtZQUNGUyxRQUFRdUIsR0FBRyxDQUFDO1lBRVosTUFBTTdCLFdBQVcsTUFBTUMsTUFBTTtZQUU3QixJQUFJLENBQUNELFNBQVM4QixFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLDJCQUEyQjtZQUMzQixNQUFNQyxPQUFPLE1BQU1oQyxTQUFTZ0MsSUFBSTtZQUVoQyxxQkFBcUI7WUFDckIsTUFBTUMsTUFBTUMsT0FBT0MsR0FBRyxDQUFDQyxlQUFlLENBQUNKO1lBQ3ZDLE1BQU1LLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztZQUNwQ0YsS0FBS0csSUFBSSxHQUFHUDtZQUVaLGtCQUFrQjtZQUNsQixNQUFNUSxXQUFXLGtCQUF5RCxPQUF2QyxJQUFJdEIsT0FBT3VCLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUM7WUFDMUVOLEtBQUtPLFFBQVEsR0FBR0g7WUFFaEIsY0FBYztZQUNkSCxTQUFTTyxJQUFJLENBQUNDLFdBQVcsQ0FBQ1Q7WUFDMUJBLEtBQUtVLEtBQUs7WUFDVlQsU0FBU08sSUFBSSxDQUFDRyxXQUFXLENBQUNYO1lBRTFCLGdCQUFnQjtZQUNoQkgsT0FBT0MsR0FBRyxDQUFDYyxlQUFlLENBQUNoQjtZQUUzQjNCLFFBQVF1QixHQUFHLENBQUM7WUFDWkYsTUFBTTtRQUVSLEVBQUUsT0FBT3pDLE9BQU87WUFDZG9CLFFBQVFwQixLQUFLLENBQUMscUJBQXFCQTtZQUNuQ3lDLE1BQU07UUFDUixTQUFVO1lBQ1I5QixlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxNQUFNcUQsZUFBZSxDQUFDcEM7UUFDcEIsTUFBTXFDLFFBQW1DO1lBQ3ZDQyxTQUFTO1lBQ1RDLFFBQVE7WUFDUkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLE9BQU87WUFDUEMsU0FBUztZQUNUQyxRQUFRO1lBQ1JDLE9BQU87UUFDVDtRQUNBLE9BQU9SLEtBQUssQ0FBQ3JDLEtBQUssSUFBSUE7SUFDeEI7SUFFQSxNQUFNOEMsaUJBQWlCLENBQUM3QztRQUN0QixNQUFNOEMsV0FBc0M7WUFDMUNDLE9BQU87WUFDUEMscUJBQXFCO1lBQ3JCQyxvQkFBb0I7WUFDcEJDLFNBQVM7UUFDWDtRQUNBLE9BQU9KLFFBQVEsQ0FBQzlDLE9BQU8sSUFBSUE7SUFDN0I7SUFFQSxNQUFNbUQsa0JBQWtCLENBQUNDO1FBQ3ZCLE1BQU1DLFdBQXNDO1lBQzFDQyxhQUFhO1lBQ2JDLE1BQU07WUFDTkMsT0FBTztRQUNUO1FBQ0EsT0FBT0gsUUFBUSxDQUFDRCxRQUFRLElBQUlBO0lBQzlCO0lBRUEsSUFBSW5GLFNBQVM7UUFDWCxxQkFDRSw4REFBQ3dGO1lBQUlDLE9BQU87Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLFlBQVk7Z0JBQ1pDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGdCQUFnQjtZQUNsQjtzQkFDRSw0RUFBQ047Z0JBQUlDLE9BQU87b0JBQUVNLE9BQU87b0JBQVNDLFVBQVU7Z0JBQVM7MEJBQUc7Ozs7Ozs7Ozs7O0lBRzFEO0lBRUEsSUFBSTlGLE9BQU87UUFDVCxxQkFDRSw4REFBQ3NGO1lBQUlDLE9BQU87Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLFlBQVk7Z0JBQ1pDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGdCQUFnQjtZQUNsQjtzQkFDRSw0RUFBQ047Z0JBQUlDLE9BQU87b0JBQUVNLE9BQU87b0JBQVNDLFVBQVU7Z0JBQVM7O29CQUFHO29CQUFROUY7Ozs7Ozs7Ozs7OztJQUdsRTtJQUVBLHFCQUNFLDhEQUFDc0Y7UUFBSUMsT0FBTztZQUNWQyxXQUFXO1lBQ1hDLFlBQVk7WUFDWk0sU0FBUztRQUNYOzswQkFDRSw4REFBQ1Q7Z0JBQUlDLE9BQU87b0JBQUVTLFVBQVU7b0JBQVVDLFFBQVE7Z0JBQVM7O2tDQUVqRCw4REFBQ1g7d0JBQUlDLE9BQU87NEJBQ1ZFLFlBQVk7NEJBQ1pTLGNBQWM7NEJBQ2RILFNBQVM7NEJBQ1RJLGNBQWM7NEJBQ2RDLFdBQVc7NEJBQ1hDLFdBQVc7d0JBQ2I7OzBDQUNFLDhEQUFDQztnQ0FBR2YsT0FBTztvQ0FDVE0sT0FBTztvQ0FDUE0sY0FBYztvQ0FDZEwsVUFBVTtvQ0FDVlMsWUFBWTtnQ0FDZDswQ0FBRzs7Ozs7OzBDQUlILDhEQUFDakI7Z0NBQUlDLE9BQU87b0NBQUVHLFNBQVM7b0NBQVFjLEtBQUs7b0NBQVFaLGdCQUFnQjtvQ0FBVWEsVUFBVTtnQ0FBTzs7a0RBQ3JGLDhEQUFDQzt3Q0FBS3BELE1BQUs7d0NBQWFpQyxPQUFPOzRDQUM3QkUsWUFBWTs0Q0FDWkksT0FBTzs0Q0FDUEUsU0FBUzs0Q0FDVEcsY0FBYzs0Q0FDZFMsZ0JBQWdCOzRDQUNoQkosWUFBWTs0Q0FDWkgsV0FBVzt3Q0FDYjtrREFBRzs7Ozs7O2tEQUlILDhEQUFDUTt3Q0FDQ0MsU0FBU25FO3dDQUNUb0UsVUFBVXBHO3dDQUNWNkUsT0FBTzs0Q0FDTEUsWUFBWS9FLGNBQ1IsNkNBQ0E7NENBQ0ptRixPQUFPOzRDQUNQRSxTQUFTOzRDQUNURyxjQUFjOzRDQUNkYSxRQUFROzRDQUNSUixZQUFZOzRDQUNaUyxRQUFRdEcsY0FBYyxnQkFBZ0I7NENBQ3RDMEYsV0FBVzs0Q0FDWE4sVUFBVTt3Q0FDWjtrREFFQ3BGLGNBQWMsc0JBQXNCOzs7Ozs7a0RBR3ZDLDhEQUFDZ0c7d0NBQUtwRCxNQUFLO3dDQUFJaUMsT0FBTzs0Q0FDcEJFLFlBQVk7NENBQ1pJLE9BQU87NENBQ1BFLFNBQVM7NENBQ1RHLGNBQWM7NENBQ2RTLGdCQUFnQjs0Q0FDaEJKLFlBQVk7NENBQ1pILFdBQVc7d0NBQ2I7a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FPUCw4REFBQ2Q7d0JBQUlDLE9BQU87NEJBQ1ZFLFlBQVk7NEJBQ1pTLGNBQWM7NEJBQ2RILFNBQVM7NEJBQ1RJLGNBQWM7NEJBQ2RDLFdBQVc7d0JBQ2I7OzBDQUNFLDhEQUFDYTtnQ0FBRzFCLE9BQU87b0NBQUVNLE9BQU87b0NBQVdNLGNBQWM7b0NBQVFMLFVBQVU7Z0NBQVM7MENBQUc7Ozs7OzswQ0FJM0UsOERBQUNSO2dDQUFJQyxPQUFPO29DQUFFRyxTQUFTO29DQUFRd0IscUJBQXFCO29DQUF3Q1YsS0FBSztnQ0FBTzs7a0RBRXRHLDhEQUFDbEI7OzBEQUNDLDhEQUFDNkI7Z0RBQU01QixPQUFPO29EQUFFRyxTQUFTO29EQUFTUyxjQUFjO29EQUFPTixPQUFPO29EQUFXQyxVQUFVO2dEQUFTOzBEQUFHOzs7Ozs7MERBRy9GLDhEQUFDc0I7Z0RBQ0N4RixNQUFLO2dEQUNMeUYsYUFBWTtnREFDWkMsT0FBT3BIO2dEQUNQcUgsVUFBVSxDQUFDQyxJQUFNckgsY0FBY3FILEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnREFDN0MvQixPQUFPO29EQUNMbUMsT0FBTztvREFDUDNCLFNBQVM7b0RBQ1RnQixRQUFRO29EQUNSYixjQUFjO29EQUNkSixVQUFVO29EQUNWNkIsV0FBVztvREFDWDlCLE9BQU87b0RBQ1BKLFlBQVk7Z0RBQ2Q7Ozs7Ozs7Ozs7OztrREFLSiw4REFBQ0g7OzBEQUNDLDhEQUFDNkI7Z0RBQU01QixPQUFPO29EQUFFRyxTQUFTO29EQUFTUyxjQUFjO29EQUFPTixPQUFPO29EQUFXQyxVQUFVO2dEQUFTOzBEQUFHOzs7Ozs7MERBRy9GLDhEQUFDOEI7Z0RBQ0NOLE9BQU9sSDtnREFDUG1ILFVBQVUsQ0FBQ0MsSUFBTW5ILGdCQUFnQm1ILEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSztnREFDL0MvQixPQUFPO29EQUNMbUMsT0FBTztvREFDUDNCLFNBQVM7b0RBQ1RnQixRQUFRO29EQUNSYixjQUFjO29EQUNkSixVQUFVO29EQUNWNkIsV0FBVztvREFDWDlCLE9BQU87b0RBQ1BKLFlBQVk7Z0RBQ2Q7O2tFQUVBLDhEQUFDb0M7d0RBQU9QLE9BQU07a0VBQU07Ozs7OztrRUFDcEIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFVOzs7Ozs7a0VBQ3hCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBUzs7Ozs7O2tFQUN2Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQVE7Ozs7OztrRUFDdEIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFPOzs7Ozs7a0VBQ3JCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBUTs7Ozs7O2tFQUN0Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQVU7Ozs7OztrRUFDeEIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFTOzs7Ozs7a0VBQ3ZCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUsxQiw4REFBQ2hDOzswREFDQyw4REFBQzZCO2dEQUFNNUIsT0FBTztvREFBRUcsU0FBUztvREFBU1MsY0FBYztvREFBT04sT0FBTztvREFBV0MsVUFBVTtnREFBUzswREFBRzs7Ozs7OzBEQUcvRiw4REFBQzhCO2dEQUNDTixPQUFPaEg7Z0RBQ1BpSCxVQUFVLENBQUNDLElBQU1qSCxrQkFBa0JpSCxFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0RBQ2pEL0IsT0FBTztvREFDTG1DLE9BQU87b0RBQ1AzQixTQUFTO29EQUNUZ0IsUUFBUTtvREFDUmIsY0FBYztvREFDZEosVUFBVTtvREFDVjZCLFdBQVc7b0RBQ1g5QixPQUFPO29EQUNQSixZQUFZO2dEQUNkOztrRUFFQSw4REFBQ29DO3dEQUFPUCxPQUFNO2tFQUFNOzs7Ozs7a0VBQ3BCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBUTs7Ozs7O2tFQUN0Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQXNCOzs7Ozs7a0VBQ3BDLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBcUI7Ozs7OztrRUFDbkMsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSzVCLDhEQUFDaEM7OzBEQUNDLDhEQUFDNkI7Z0RBQU01QixPQUFPO29EQUFFRyxTQUFTO29EQUFTUyxjQUFjO29EQUFPTixPQUFPO29EQUFXQyxVQUFVO2dEQUFTOzBEQUFHOzs7Ozs7MERBRy9GLDhEQUFDOEI7Z0RBQ0NOLE9BQU85RztnREFDUCtHLFVBQVUsQ0FBQ0MsSUFBTS9HLFVBQVUrRyxFQUFFQyxNQUFNLENBQUNILEtBQUs7Z0RBQ3pDL0IsT0FBTztvREFDTG1DLE9BQU87b0RBQ1AzQixTQUFTO29EQUNUZ0IsUUFBUTtvREFDUmIsY0FBYztvREFDZEosVUFBVTtvREFDVjZCLFdBQVc7b0RBQ1g5QixPQUFPO29EQUNQSixZQUFZO2dEQUNkOztrRUFFQSw4REFBQ29DO3dEQUFPUCxPQUFNO2tFQUFTOzs7Ozs7a0VBQ3ZCLDhEQUFDTzt3REFBT1AsT0FBTTtrRUFBUzs7Ozs7O2tFQUN2Qiw4REFBQ087d0RBQU9QLE9BQU07a0VBQU87Ozs7OztrRUFDckIsOERBQUNPO3dEQUFPUCxPQUFNO2tFQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTTNCLDhEQUFDaEM7Z0NBQUlDLE9BQU87b0NBQ1Z1QyxXQUFXO29DQUNYL0IsU0FBUztvQ0FDVE4sWUFBWTtvQ0FDWlMsY0FBYztvQ0FDZEcsV0FBVztvQ0FDWFIsT0FBTztnQ0FDVDs7b0NBQUc7b0NBQ09qRyxjQUFjbUksTUFBTTtvQ0FBQztvQ0FBU3JJLFdBQVdxSSxNQUFNO29DQUFDOzs7Ozs7Ozs7Ozs7O29CQUszRG5JLGNBQWNtSSxNQUFNLEtBQUssa0JBQ3hCLDhEQUFDekM7d0JBQUlDLE9BQU87NEJBQ1ZFLFlBQVk7NEJBQ1pTLGNBQWM7NEJBQ2RILFNBQVM7NEJBQ1RNLFdBQVc7NEJBQ1hELFdBQVc7d0JBQ2I7OzBDQUNFLDhEQUFDYTtnQ0FBRzFCLE9BQU87b0NBQUVNLE9BQU87b0NBQVdDLFVBQVU7Z0NBQVM7MENBQUc7Ozs7OzswQ0FHckQsOERBQUNrQztnQ0FBRXpDLE9BQU87b0NBQUVNLE9BQU87b0NBQVdpQyxXQUFXO2dDQUFPOzBDQUFHOzs7Ozs7Ozs7Ozs2Q0FLckQsOERBQUN4Qzt3QkFBSUMsT0FBTzs0QkFBRUcsU0FBUzs0QkFBUWMsS0FBSzt3QkFBTztrQ0FDeEM1RyxjQUFjcUksR0FBRyxDQUFDLENBQUMxRyxxQkFDbEIsOERBQUMrRDtnQ0FBa0JDLE9BQU87b0NBQ3hCRSxZQUFZO29DQUNaUyxjQUFjO29DQUNkSCxTQUFTO29DQUNUSyxXQUFXO29DQUNYVyxRQUFRO2dDQUNWOzBDQUNFLDRFQUFDekI7b0NBQUlDLE9BQU87d0NBQUVHLFNBQVM7d0NBQVF3QixxQkFBcUI7d0NBQVlWLEtBQUs7d0NBQVFiLFlBQVk7b0NBQVE7O3NEQUMvRiw4REFBQ0w7OzhEQUNDLDhEQUFDNEM7b0RBQUczQyxPQUFPO3dEQUFFTSxPQUFPO3dEQUFXTSxjQUFjO3dEQUFRTCxVQUFVO29EQUFTOzhEQUNyRXZFLEtBQUtDLElBQUk7Ozs7Ozs4REFHWiw4REFBQzhEO29EQUFJQyxPQUFPO3dEQUFFRyxTQUFTO3dEQUFRd0IscUJBQXFCO3dEQUF3Q1YsS0FBSzt3REFBUUwsY0FBYztvREFBTzs7c0VBQzVILDhEQUFDYjs7OEVBQ0MsOERBQUM2Qzs4RUFBTzs7Ozs7O2dFQUFlO2dFQUFFbkUsYUFBYXpDLEtBQUtLLElBQUk7Ozs7Ozs7c0VBRWpELDhEQUFDMEQ7OzhFQUNDLDhEQUFDNkM7OEVBQU87Ozs7OztnRUFBZ0I7Z0VBQUVuRCxnQkFBZ0J6RCxLQUFLMEQsT0FBTzs7Ozs7OztzRUFFeEQsOERBQUNLOzs4RUFDQyw4REFBQzZDOzhFQUFPOzs7Ozs7Z0VBQWdCO2dFQUFFekQsZUFBZW5ELEtBQUtNLE1BQU07Ozs7Ozs7c0VBRXRELDhEQUFDeUQ7OzhFQUNDLDhEQUFDNkM7OEVBQU87Ozs7OztnRUFBdUI7Z0VBQUU1RyxLQUFLNkcsUUFBUSxDQUFDTCxNQUFNOzs7Ozs7Ozs7Ozs7O2dEQUl4RHhHLEtBQUtJLFdBQVcsa0JBQ2YsOERBQUNxRztvREFBRXpDLE9BQU87d0RBQUVNLE9BQU87d0RBQVdNLGNBQWM7b0RBQU87O3NFQUNqRCw4REFBQ2dDO3NFQUFPOzs7Ozs7d0RBQWU7d0RBQUU1RyxLQUFLSSxXQUFXOzs7Ozs7O2dEQUk1Q0osS0FBSzZHLFFBQVEsQ0FBQ0wsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQ3pDO29EQUFJQyxPQUFPO3dEQUFFdUMsV0FBVztvREFBTzs7c0VBQzlCLDhEQUFDSztzRUFBTzs7Ozs7O3NFQUNSLDhEQUFDN0M7NERBQUlDLE9BQU87Z0VBQUVHLFNBQVM7Z0VBQVFjLEtBQUs7Z0VBQU9zQixXQUFXOzREQUFNO3NFQUN6RHZHLEtBQUs2RyxRQUFRLENBQUNILEdBQUcsQ0FBQyxDQUFDSSx3QkFDbEIsOERBQUMvQztvRUFBcUJDLE9BQU87d0VBQzNCRSxZQUFZO3dFQUNaTSxTQUFTO3dFQUNURyxjQUFjO3dFQUNkSixVQUFVO29FQUNaOztzRkFDRSw4REFBQ3FDOztnRkFBTztnRkFBRUUsUUFBUUMsYUFBYTs7Ozs7Ozt3RUFBVTt3RUFDeENELFFBQVFFLElBQUksSUFBSSxJQUFpQixPQUFiRixRQUFRRSxJQUFJLEVBQUM7d0VBQ2pDRixRQUFRRyxNQUFNO3dFQUFDO3dFQUFJSCxRQUFRSSxPQUFPO3dFQUFDO3dFQUFHSixRQUFRSyxRQUFRO3dFQUFDOzttRUFSaERMLFFBQVEvRixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQWdCOUIsOERBQUNnRDs0Q0FBSUMsT0FBTztnREFBRUcsU0FBUztnREFBUWlELGVBQWU7Z0RBQVVuQyxLQUFLOzRDQUFPOzs4REFDbEUsOERBQUNJO29EQUNDQyxTQUFTO3dEQUNQLHFDQUFxQzt3REFDckM3RCxPQUFPNEYsUUFBUSxDQUFDdEYsSUFBSSxHQUFHLGtCQUEwQixPQUFSL0IsS0FBS2UsRUFBRTtvREFDbEQ7b0RBQ0FpRCxPQUFPO3dEQUNMRSxZQUFZO3dEQUNaSSxPQUFPO3dEQUNQa0IsUUFBUTt3REFDUmIsY0FBYzt3REFDZEgsU0FBUzt3REFDVGlCLFFBQVE7d0RBQ1JsQixVQUFVO3dEQUNWSyxjQUFjO29EQUNoQjs4REFDRDs7Ozs7OzhEQUlELDhEQUFDUztvREFDQ0MsU0FBUyxJQUFNeEUsZ0JBQWdCZCxLQUFLZSxFQUFFO29EQUN0Q2lELE9BQU87d0RBQ0xFLFlBQVk7d0RBQ1pJLE9BQU87d0RBQ1BrQixRQUFRO3dEQUNSYixjQUFjO3dEQUNkSCxTQUFTO3dEQUNUaUIsUUFBUTt3REFDUmxCLFVBQVU7b0RBQ1o7OERBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQkF0Rkd2RSxLQUFLZSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBCQWtHekIsOERBQUNnRDtnQkFBSUMsT0FBTztvQkFDVnNELFVBQVU7b0JBQ1ZDLFFBQVE7b0JBQ1JDLE1BQU07b0JBQ05sRCxPQUFPO29CQUNQQyxVQUFVO29CQUNWa0QsWUFBWTtvQkFDWnJCLFdBQVc7Z0JBQ2I7MEJBQUc7Ozs7Ozs7Ozs7OztBQUtUO0dBMWhCd0JsSTtLQUFBQSIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxtZWRpYS1saXN0XFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBBdXRoR3VhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvQXV0aEd1YXJkJztcbmltcG9ydCBEYXNoYm9hcmRMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dCc7XG5cbmludGVyZmFjZSBTZWdtZW50IHtcbiAgaWQ6IHN0cmluZztcbiAgc2VnbWVudE51bWJlcjogbnVtYmVyO1xuICB0aW1lSW46IHN0cmluZztcbiAgdGltZU91dDogc3RyaW5nO1xuICBkdXJhdGlvbjogc3RyaW5nO1xuICBjb2RlOiBzdHJpbmcgfCBudWxsO1xufVxuXG5pbnRlcmZhY2UgTWVkaWFJdGVtIHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICB0eXBlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBudWxsO1xuICBjaGFubmVsOiBzdHJpbmc7XG4gIHNvdXJjZTogc3RyaW5nIHwgbnVsbDtcbiAgc3RhdHVzOiBzdHJpbmc7XG4gIHN0YXJ0RGF0ZTogc3RyaW5nO1xuICBlbmREYXRlOiBzdHJpbmcgfCBudWxsO1xuICBub3Rlczogc3RyaW5nIHwgbnVsbDtcbiAgZXBpc29kZU51bWJlcjogbnVtYmVyIHwgbnVsbDtcbiAgc2Vhc29uTnVtYmVyOiBudW1iZXIgfCBudWxsO1xuICBwYXJ0TnVtYmVyOiBudW1iZXIgfCBudWxsO1xuICBzZWdtZW50czogU2VnbWVudFtdO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWVkaWFMaXN0UGFnZSgpIHtcbiAgY29uc3QgW21lZGlhSXRlbXMsIHNldE1lZGlhSXRlbXNdID0gdXNlU3RhdGU8TWVkaWFJdGVtW10+KFtdKTtcbiAgY29uc3QgW2ZpbHRlcmVkSXRlbXMsIHNldEZpbHRlcmVkSXRlbXNdID0gdXNlU3RhdGU8TWVkaWFJdGVtW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZWFyY2hUZXJtLCBzZXRTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkVHlwZSwgc2V0U2VsZWN0ZWRUeXBlXSA9IHVzZVN0YXRlKCdBTEwnKTtcbiAgY29uc3QgW3NlbGVjdGVkU3RhdHVzLCBzZXRTZWxlY3RlZFN0YXR1c10gPSB1c2VTdGF0ZSgnQUxMJyk7XG4gIGNvbnN0IFtzb3J0QnksIHNldFNvcnRCeV0gPSB1c2VTdGF0ZSgnbmV3ZXN0Jyk7XG4gIGNvbnN0IFtpc0V4cG9ydGluZywgc2V0SXNFeHBvcnRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hNZWRpYUl0ZW1zKCk7XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZpbHRlckFuZFNvcnRJdGVtcygpO1xuICB9LCBbbWVkaWFJdGVtcywgc2VhcmNoVGVybSwgc2VsZWN0ZWRUeXBlLCBzZWxlY3RlZFN0YXR1cywgc29ydEJ5XSk7XG5cbiAgY29uc3QgZmV0Y2hNZWRpYUl0ZW1zID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL21lZGlhJyk7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRNZWRpYUl0ZW1zKHJlc3VsdC5kYXRhKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldEVycm9yKHJlc3VsdC5lcnJvcik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG1lZGlhIGl0ZW1zOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCfZgdi02YQg2YHZiiDYrNmE2Kgg2KfZhNmF2YjYp9ivINin2YTYpdi52YTYp9mF2YrYqScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZmlsdGVyQW5kU29ydEl0ZW1zID0gKCkgPT4ge1xuICAgIGxldCBmaWx0ZXJlZCA9IFsuLi5tZWRpYUl0ZW1zXTtcblxuICAgIC8vINin2YTYqNit2Ksg2KjYp9mE2KfYs9mFXG4gICAgaWYgKHNlYXJjaFRlcm0pIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKGl0ZW0gPT5cbiAgICAgICAgaXRlbS5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICAoaXRlbS5kZXNjcmlwdGlvbiAmJiBpdGVtLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSlcbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8g2YHZhNiq2LHYqSDYqNin2YTZhtmI2LlcbiAgICBpZiAoc2VsZWN0ZWRUeXBlICE9PSAnQUxMJykge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoaXRlbSA9PiBpdGVtLnR5cGUgPT09IHNlbGVjdGVkVHlwZSk7XG4gICAgfVxuXG4gICAgLy8g2YHZhNiq2LHYqSDYqNin2YTYrdin2YTYqVxuICAgIGlmIChzZWxlY3RlZFN0YXR1cyAhPT0gJ0FMTCcpIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKGl0ZW0gPT4gaXRlbS5zdGF0dXMgPT09IHNlbGVjdGVkU3RhdHVzKTtcbiAgICB9XG5cbiAgICAvLyDYp9mE2KrYsdiq2YrYqFxuICAgIHN3aXRjaCAoc29ydEJ5KSB7XG4gICAgICBjYXNlICduZXdlc3QnOlxuICAgICAgICBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShiLmNyZWF0ZWRBdCkuZ2V0VGltZSgpIC0gbmV3IERhdGUoYS5jcmVhdGVkQXQpLmdldFRpbWUoKSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnb2xkZXN0JzpcbiAgICAgICAgZmlsdGVyZWQuc29ydCgoYSwgYikgPT4gbmV3IERhdGUoYS5jcmVhdGVkQXQpLmdldFRpbWUoKSAtIG5ldyBEYXRlKGIuY3JlYXRlZEF0KS5nZXRUaW1lKCkpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ25hbWUnOlxuICAgICAgICBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiBhLm5hbWUubG9jYWxlQ29tcGFyZShiLm5hbWUsICdhcicpKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICd0eXBlJzpcbiAgICAgICAgZmlsdGVyZWQuc29ydCgoYSwgYikgPT4gYS50eXBlLmxvY2FsZUNvbXBhcmUoYi50eXBlKSk7XG4gICAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIHNldEZpbHRlcmVkSXRlbXMoZmlsdGVyZWQpO1xuICB9O1xuXG4gIGNvbnN0IGRlbGV0ZU1lZGlhSXRlbSA9IGFzeW5jIChpZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFjb25maXJtKCfZh9mEINij2YbYqiDZhdiq2KPZg9ivINmF2YYg2K3YsNmBINmH2LDZhyDYp9mE2YXYp9iv2KnYnycpKSByZXR1cm47XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9tZWRpYT9pZD0ke2lkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRNZWRpYUl0ZW1zKG1lZGlhSXRlbXMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pZCAhPT0gaWQpKTtcbiAgICAgICAgYWxlcnQoJ9iq2YUg2K3YsNmBINin2YTZhdin2K/YqSDYqNmG2KzYp9itJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBhbGVydCgn2YHYtNmEINmB2Yog2K3YsNmBINin2YTZhdin2K/YqTogJyArIHJlc3VsdC5lcnJvcik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIG1lZGlhIGl0ZW06JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ9it2K/YqyDYrti32KMg2KPYq9mG2KfYoSDYrdiw2YEg2KfZhNmF2KfYr9ipJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGV4cG9ydFRvRXhjZWwgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0SXNFeHBvcnRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5qAINio2K/YoSDYqti12K/ZitixINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqi4uLicpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2V4cG9ydCcpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcign2YHYtNmEINmB2Yog2KrYtdiv2YrYsSDYp9mE2KjZitin2YbYp9iqJyk7XG4gICAgICB9XG5cbiAgICAgIC8vINin2YTYrdi12YjZhCDYudmE2Ykg2KfZhNmF2YTZgSDZg9mAIGJsb2JcbiAgICAgIGNvbnN0IGJsb2IgPSBhd2FpdCByZXNwb25zZS5ibG9iKCk7XG5cbiAgICAgIC8vINil2YbYtNin2KEg2LHYp9io2Lcg2KfZhNiq2K3ZhdmK2YRcbiAgICAgIGNvbnN0IHVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICAgIGxpbmsuaHJlZiA9IHVybDtcblxuICAgICAgLy8g2KrYrdiv2YrYryDYp9iz2YUg2KfZhNmF2YTZgVxuICAgICAgY29uc3QgZmlsZU5hbWUgPSBgTWVkaWFfRGF0YWJhc2VfJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXX0ueGxzeGA7XG4gICAgICBsaW5rLmRvd25sb2FkID0gZmlsZU5hbWU7XG5cbiAgICAgIC8vINiq2K3ZhdmK2YQg2KfZhNmF2YTZgVxuICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcbiAgICAgIGxpbmsuY2xpY2soKTtcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7XG5cbiAgICAgIC8vINiq2YbYuNmK2YEg2KfZhNiw2KfZg9ix2KlcbiAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKHVybCk7XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUg2KrZhSDYqti12K/ZitixINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqiDYqNmG2KzYp9itJyk7XG4gICAgICBhbGVydCgn4pyFINiq2YUg2KrYtdiv2YrYsSDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2Kog2KjZhtis2KfYrSEnKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg2K7Yt9ijINmB2Yog2KfZhNiq2LXYr9mK2LE6JywgZXJyb3IpO1xuICAgICAgYWxlcnQoJ+KdjCDZgdi02YQg2YHZiiDYqti12K/ZitixINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqi4g2YrYsdis2Ykg2KfZhNmF2K3Yp9mI2YTYqSDZhdix2Kkg2KPYrtix2YkuJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzRXhwb3J0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0VHlwZUxhYmVsID0gKHR5cGU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IHR5cGVzOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xuICAgICAgUFJPR1JBTTogJ9io2LHZhtin2YXYrCcsXG4gICAgICBTRVJJRVM6ICfZhdiz2YTYs9mEJyxcbiAgICAgIE1PVklFOiAn2YHZitmE2YUnLFxuICAgICAgU09ORzogJ9ij2LrZhtmK2KknLFxuICAgICAgU1RJTkc6ICdTdGluZycsXG4gICAgICBGSUxMX0lOOiAnRmlsbCBJTicsXG4gICAgICBGSUxMRVI6ICdGaWxsZXInLFxuICAgICAgUFJPTU86ICdQcm9tbydcbiAgICB9O1xuICAgIHJldHVybiB0eXBlc1t0eXBlXSB8fCB0eXBlO1xuICB9O1xuXG4gIGNvbnN0IGdldFN0YXR1c0xhYmVsID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgY29uc3Qgc3RhdHVzZXM6IHsgW2tleTogc3RyaW5nXTogc3RyaW5nIH0gPSB7XG4gICAgICBWQUxJRDogJ9i12KfZhNitJyxcbiAgICAgIFJFSkVDVEVEX0NFTlNPUlNISVA6ICfZhdix2YHZiNi2INix2YLYp9io2YonLFxuICAgICAgUkVKRUNURURfVEVDSE5JQ0FMOiAn2YXYsdmB2YjYtiDZh9mG2K/Ys9mKJyxcbiAgICAgIFdBSVRJTkc6ICfZgdmKINin2YTYp9mG2KrYuNin2LEnXG4gICAgfTtcbiAgICByZXR1cm4gc3RhdHVzZXNbc3RhdHVzXSB8fCBzdGF0dXM7XG4gIH07XG5cbiAgY29uc3QgZ2V0Q2hhbm5lbExhYmVsID0gKGNoYW5uZWw6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGNoYW5uZWxzOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZyB9ID0ge1xuICAgICAgRE9DVU1FTlRBUlk6ICfYp9mE2YjYq9in2KbZgtmK2KknLFxuICAgICAgTkVXUzogJ9in2YTYo9iu2KjYp9ixJyxcbiAgICAgIE9USEVSOiAn2KPYrtix2YknXG4gICAgfTtcbiAgICByZXR1cm4gY2hhbm5lbHNbY2hhbm5lbF0gfHwgY2hhbm5lbDtcbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7IFxuICAgICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsIFxuICAgICAgICBiYWNrZ3JvdW5kOiAnIzFhMWQyOScsXG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJ1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICd3aGl0ZScsIGZvbnRTaXplOiAnMS41cmVtJyB9fT7ij7Mg2KzYp9ix2Yog2KfZhNiq2K3ZhdmK2YQuLi48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBzdHlsZT17eyBcbiAgICAgICAgbWluSGVpZ2h0OiAnMTAwdmgnLCBcbiAgICAgICAgYmFja2dyb3VuZDogJyMxYTFkMjknLFxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcidcbiAgICAgIH19PlxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnd2hpdGUnLCBmb250U2l6ZTogJzEuNXJlbScgfX0+4p2MINiu2LfYozoge2Vycm9yfTwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17eyBcbiAgICAgIG1pbkhlaWdodDogJzEwMHZoJywgXG4gICAgICBiYWNrZ3JvdW5kOiAnIzFhMWQyOScsXG4gICAgICBwYWRkaW5nOiAnMjBweCdcbiAgICB9fT5cbiAgICAgIDxkaXYgc3R5bGU9e3sgbWF4V2lkdGg6ICcxMjAwcHgnLCBtYXJnaW46ICcwIGF1dG8nIH19PlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LDI1NSwyNTUsMC45NSknLFxuICAgICAgICAgIGJvcmRlclJhZGl1czogJzIwcHgnLFxuICAgICAgICAgIHBhZGRpbmc6ICczMHB4JyxcbiAgICAgICAgICBtYXJnaW5Cb3R0b206ICczMHB4JyxcbiAgICAgICAgICBib3hTaGFkb3c6ICcwIDEwcHggMzBweCByZ2JhKDAsMCwwLDAuMiknLFxuICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcidcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGgxIHN0eWxlPXt7IFxuICAgICAgICAgICAgY29sb3I6ICcjMmMzZTUwJywgXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcyMHB4JywgXG4gICAgICAgICAgICBmb250U2l6ZTogJzIuNXJlbScsXG4gICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIPCfk5og2YLYp9im2YXYqSDYp9mE2YXZiNin2K8g2KfZhNil2LnZhNin2YXZitipXG4gICAgICAgICAgPC9oMT5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMTVweCcsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJywgZmxleFdyYXA6ICd3cmFwJyB9fT5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRkLW1lZGlhXCIgc3R5bGU9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzI4YTc0NSwgIzIwYzk5NyknLFxuICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMjVweCcsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzI1cHgnLFxuICAgICAgICAgICAgICB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnLFxuICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICAgIGJveFNoYWRvdzogJzAgNHB4IDE1cHggcmdiYSg0MCwxNjcsNjksMC4zKSdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICDinpUg2KXYttin2YHYqSDZhdin2K/YqSDYrNiv2YrYr9ipXG4gICAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17ZXhwb3J0VG9FeGNlbH1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRXhwb3J0aW5nfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGlzRXhwb3J0aW5nXG4gICAgICAgICAgICAgICAgICA/ICdsaW5lYXItZ3JhZGllbnQoNDVkZWcsICM2Yzc1N2QsICM1YTYyNjgpJ1xuICAgICAgICAgICAgICAgICAgOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMTdhMmI4LCAjMTM4NDk2KScsXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMjVweCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMjVweCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgICAgIGN1cnNvcjogaXNFeHBvcnRpbmcgPyAnbm90LWFsbG93ZWQnIDogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgIGJveFNoYWRvdzogJzAgNHB4IDE1cHggcmdiYSgyMywxNjIsMTg0LDAuMyknLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbSdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzRXhwb3J0aW5nID8gJ+KPsyDYrNin2LHZiiDYp9mE2KrYtdiv2YrYsS4uLicgOiAn8J+TiiDYqti12K/ZitixIEV4Y2VsJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoNDVkZWcsICMwMDdiZmYsICMwMDU2YjMpJyxcbiAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcxMnB4IDI1cHgnLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcyNXB4JyxcbiAgICAgICAgICAgICAgdGV4dERlY29yYXRpb246ICdub25lJyxcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxNXB4IHJnYmEoMCwxMjMsMjU1LDAuMyknXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAg8J+PoCDYp9mE2LHYptmK2LPZitipXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDYp9mE2KjYrdirINmI2KfZhNmB2YTYqtix2KkgKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsMjU1LDI1NSwwLjk1KScsXG4gICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMjBweCcsXG4gICAgICAgICAgcGFkZGluZzogJzI1cHgnLFxuICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzI1cHgnLFxuICAgICAgICAgIGJveFNoYWRvdzogJzAgOHB4IDI1cHggcmdiYSgwLDAsMCwwLjEpJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8aDIgc3R5bGU9e3sgY29sb3I6ICcjMmMzZTUwJywgbWFyZ2luQm90dG9tOiAnMjBweCcsIGZvbnRTaXplOiAnMS4zcmVtJyB9fT5cbiAgICAgICAgICAgIPCflI0g2KfZhNio2K3YqyDZiNin2YTZgdmE2KrYsdipXG4gICAgICAgICAgPC9oMj5cblxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2dyaWQnLCBncmlkVGVtcGxhdGVDb2x1bW5zOiAncmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjUwcHgsIDFmcikpJywgZ2FwOiAnMTVweCcgfX0+XG4gICAgICAgICAgICB7Lyog2KfZhNio2K3YqyAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snLCBtYXJnaW5Cb3R0b206ICc1cHgnLCBjb2xvcjogJyM0OTUwNTcnLCBmb250U2l6ZTogJzAuOXJlbScgfX0+XG4gICAgICAgICAgICAgICAg2KfZhNio2K3YqyDYqNin2YTYp9iz2YUg2KPZiCDYp9mE2YjYtdmBOlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYp9io2K3YqyDYudmGINmF2KfYr9ipINil2LnZhNin2YXZitipLi4uXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMnB4IHNvbGlkICNlMGUwZTAnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgICBkaXJlY3Rpb246ICdydGwnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzMzJyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd3aGl0ZSdcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDZgdmE2KrYsdipINio2KfZhNmG2YjYuSAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snLCBtYXJnaW5Cb3R0b206ICc1cHgnLCBjb2xvcjogJyM0OTUwNTcnLCBmb250U2l6ZTogJzAuOXJlbScgfX0+XG4gICAgICAgICAgICAgICAg2YbZiNi5INin2YTZhdin2K/YqTpcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFR5cGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZFR5cGUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMnB4IHNvbGlkICNlMGUwZTAnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgICBkaXJlY3Rpb246ICdydGwnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzMzJyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd3aGl0ZSdcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkFMTFwiPtis2YXZiti5INin2YTYo9mG2YjYp9i5PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBST0dSQU1cIj7YqNix2YbYp9mF2Kw8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiU0VSSUVTXCI+2YXYs9mE2LPZhDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJNT1ZJRVwiPtmB2YrZhNmFPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlNPTkdcIj7Yo9i62YbZitipPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlNUSU5HXCI+U3Rpbmc8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRklMTF9JTlwiPkZpbGwgSU48L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRklMTEVSXCI+RmlsbGVyPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBST01PXCI+UHJvbW88L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qINmB2YTYqtix2Kkg2KjYp9mE2K3Yp9mE2KkgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgc3R5bGU9e3sgZGlzcGxheTogJ2Jsb2NrJywgbWFyZ2luQm90dG9tOiAnNXB4JywgY29sb3I6ICcjNDk1MDU3JywgZm9udFNpemU6ICcwLjlyZW0nIH19PlxuICAgICAgICAgICAgICAgINin2YTYrdin2YTYqTpcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFN0YXR1c31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNlbGVjdGVkU3RhdHVzKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxMHB4JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzJweCBzb2xpZCAjZTBlMGUwJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgICAgICAgZGlyZWN0aW9uOiAncnRsJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzMzMycsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnd2hpdGUnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJBTExcIj7YrNmF2YrYuSDYp9mE2K3Yp9mE2KfYqjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJWQUxJRFwiPti12KfZhNitPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlJFSkVDVEVEX0NFTlNPUlNISVBcIj7Zhdix2YHZiNi2INix2YLYp9io2Yo8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiUkVKRUNURURfVEVDSE5JQ0FMXCI+2YXYsdmB2YjYtiDZh9mG2K/Ys9mKPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIldBSVRJTkdcIj7ZgdmKINin2YTYp9mG2KrYuNin2LE8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qINin2YTYqtix2KrZitioICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGRpc3BsYXk6ICdibG9jaycsIG1hcmdpbkJvdHRvbTogJzVweCcsIGNvbG9yOiAnIzQ5NTA1NycsIGZvbnRTaXplOiAnMC45cmVtJyB9fT5cbiAgICAgICAgICAgICAgICDYqtix2KrZitioINit2LPYqDpcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXtzb3J0Qnl9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTb3J0QnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMnB4IHNvbGlkICNlMGUwZTAnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgICBkaXJlY3Rpb246ICdydGwnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzMzJyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd3aGl0ZSdcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5ld2VzdFwiPtin2YTYo9it2K/YqyDYo9mI2YTYp9mLPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm9sZGVzdFwiPtin2YTYo9mC2K/ZhSDYo9mI2YTYp9mLPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5hbWVcIj7Yp9mE2KfYs9mFICjYoy3Ziik8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwidHlwZVwiPtin2YTZhtmI2Lk8L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDYpdit2LXYp9im2YrYp9iqINin2YTYqNit2KsgKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgbWFyZ2luVG9wOiAnMTVweCcsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMTBweCcsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnI2Y4ZjlmYScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJyxcbiAgICAgICAgICAgIGNvbG9yOiAnIzZjNzU3ZCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIPCfk4og2LnYsdi2IHtmaWx0ZXJlZEl0ZW1zLmxlbmd0aH0g2YXZhiDYo9i12YQge21lZGlhSXRlbXMubGVuZ3RofSDZhdin2K/YqSDYpdi52YTYp9mF2YrYqVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTWVkaWEgSXRlbXMgKi99XG4gICAgICAgIHtmaWx0ZXJlZEl0ZW1zLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgyNTUsMjU1LDI1NSwwLjk1KScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcyMHB4JyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICc1MHB4JyxcbiAgICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwIDEwcHggMzBweCByZ2JhKDAsMCwwLDAuMiknXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8aDIgc3R5bGU9e3sgY29sb3I6ICcjNmM3NTdkJywgZm9udFNpemU6ICcxLjVyZW0nIH19PlxuICAgICAgICAgICAgICDwn5OtINmE2Kcg2KrZiNis2K8g2YXZiNin2K8g2KXYudmE2KfZhdmK2Kkg2YXYrdmB2YjYuNipXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAgPHAgc3R5bGU9e3sgY29sb3I6ICcjNmM3NTdkJywgbWFyZ2luVG9wOiAnMTBweCcgfX0+XG4gICAgICAgICAgICAgINin2KjYr9ijINio2KXYttin2YHYqSDZhdin2K/YqSDYpdi52YTYp9mF2YrYqSDYrNiv2YrYr9ipXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZ3JpZCcsIGdhcDogJzIwcHgnIH19PlxuICAgICAgICAgICAge2ZpbHRlcmVkSXRlbXMubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtpdGVtLmlkfSBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuOTUpJyxcbiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxNXB4JyxcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMjVweCcsXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OiAnMCA4cHggMjVweCByZ2JhKDAsMCwwLDAuMSknLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjZTllY2VmJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogJzFmciBhdXRvJywgZ2FwOiAnMjBweCcsIGFsaWduSXRlbXM6ICdzdGFydCcgfX0+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8aDMgc3R5bGU9e3sgY29sb3I6ICcjMmMzZTUwJywgbWFyZ2luQm90dG9tOiAnMTVweCcsIGZvbnRTaXplOiAnMS40cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZ3JpZCcsIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICdyZXBlYXQoYXV0by1maXQsIG1pbm1heCgyMDBweCwgMWZyKSknLCBnYXA6ICcxNXB4JywgbWFyZ2luQm90dG9tOiAnMTVweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+2KfZhNmG2YjYuTo8L3N0cm9uZz4ge2dldFR5cGVMYWJlbChpdGVtLnR5cGUpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPtin2YTZgtmG2KfYqTo8L3N0cm9uZz4ge2dldENoYW5uZWxMYWJlbChpdGVtLmNoYW5uZWwpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPtin2YTYrdin2YTYqTo8L3N0cm9uZz4ge2dldFN0YXR1c0xhYmVsKGl0ZW0uc3RhdHVzKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz7Yudiv2K8g2KfZhNiz2YrYrNmF2KfZhtiqOjwvc3Ryb25nPiB7aXRlbS5zZWdtZW50cy5sZW5ndGh9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLmRlc2NyaXB0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBjb2xvcjogJyM2Yzc1N2QnLCBtYXJnaW5Cb3R0b206ICcxMHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+2KfZhNmI2LXZgTo8L3N0cm9uZz4ge2l0ZW0uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLnNlZ21lbnRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMTVweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPtin2YTYs9mK2KzZhdin2YbYqjo8L3N0cm9uZz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2dyaWQnLCBnYXA6ICc4cHgnLCBtYXJnaW5Ub3A6ICc4cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5zZWdtZW50cy5tYXAoKHNlZ21lbnQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17c2VnbWVudC5pZH0gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjZjhmOWZhJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICc4cHggMTJweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPiN7c2VnbWVudC5zZWdtZW50TnVtYmVyfTwvc3Ryb25nPiAtIFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlZ21lbnQuY29kZSAmJiBgICR7c2VnbWVudC5jb2RlfSAtIGB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VnbWVudC50aW1lSW59IOKGkiB7c2VnbWVudC50aW1lT3V0fSAoe3NlZ21lbnQuZHVyYXRpb259KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBmbGV4RGlyZWN0aW9uOiAnY29sdW1uJywgZ2FwOiAnMTBweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyDYqtmI2KzZitmHINmE2LXZgdit2Kkg2KfZhNiq2LnYr9mK2YQg2YXYuSDZhdi52LHZgSDYp9mE2YXYp9iv2KlcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gYC9lZGl0LW1lZGlhP2lkPSR7aXRlbS5pZH1gO1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoNDVkZWcsICMwMDdiZmYsICMwMDU2YjMpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzhweCAxNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnNXB4J1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICDinI/vuI8g2KrYudiv2YrZhFxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZGVsZXRlTWVkaWFJdGVtKGl0ZW0uaWQpfVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjZGMzNTQ1LCAjYzgyMzMzKScsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICc4cHggMTZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJ1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICDwn5eR77iPINit2LDZgVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiDYp9mE2YbYtSDYp9mE2LPZgdmE2YogKi99XG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLFxuICAgICAgICBib3R0b206ICcyMHB4JyxcbiAgICAgICAgbGVmdDogJzIwcHgnLFxuICAgICAgICBjb2xvcjogJyM2Yzc1N2QnLFxuICAgICAgICBmb250U2l6ZTogJzAuNzVyZW0nLFxuICAgICAgICBmb250RmFtaWx5OiAnQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgICAgICBkaXJlY3Rpb246ICdsdHInXG4gICAgICB9fT5cbiAgICAgICAgUG93ZXJlZCBCeSBNYWhtb3VkIElzbWFpbFxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJNZWRpYUxpc3RQYWdlIiwibWVkaWFJdGVtcyIsInNldE1lZGlhSXRlbXMiLCJmaWx0ZXJlZEl0ZW1zIiwic2V0RmlsdGVyZWRJdGVtcyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInNlYXJjaFRlcm0iLCJzZXRTZWFyY2hUZXJtIiwic2VsZWN0ZWRUeXBlIiwic2V0U2VsZWN0ZWRUeXBlIiwic2VsZWN0ZWRTdGF0dXMiLCJzZXRTZWxlY3RlZFN0YXR1cyIsInNvcnRCeSIsInNldFNvcnRCeSIsImlzRXhwb3J0aW5nIiwic2V0SXNFeHBvcnRpbmciLCJmZXRjaE1lZGlhSXRlbXMiLCJmaWx0ZXJBbmRTb3J0SXRlbXMiLCJyZXNwb25zZSIsImZldGNoIiwicmVzdWx0IiwianNvbiIsInN1Y2Nlc3MiLCJkYXRhIiwiY29uc29sZSIsImZpbHRlcmVkIiwiZmlsdGVyIiwiaXRlbSIsIm5hbWUiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwiZGVzY3JpcHRpb24iLCJ0eXBlIiwic3RhdHVzIiwic29ydCIsImEiLCJiIiwiRGF0ZSIsImNyZWF0ZWRBdCIsImdldFRpbWUiLCJsb2NhbGVDb21wYXJlIiwiZGVsZXRlTWVkaWFJdGVtIiwiaWQiLCJjb25maXJtIiwibWV0aG9kIiwiYWxlcnQiLCJleHBvcnRUb0V4Y2VsIiwibG9nIiwib2siLCJFcnJvciIsImJsb2IiLCJ1cmwiLCJ3aW5kb3ciLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJsaW5rIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImZpbGVOYW1lIiwidG9JU09TdHJpbmciLCJzcGxpdCIsImRvd25sb2FkIiwiYm9keSIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJyZW1vdmVDaGlsZCIsInJldm9rZU9iamVjdFVSTCIsImdldFR5cGVMYWJlbCIsInR5cGVzIiwiUFJPR1JBTSIsIlNFUklFUyIsIk1PVklFIiwiU09ORyIsIlNUSU5HIiwiRklMTF9JTiIsIkZJTExFUiIsIlBST01PIiwiZ2V0U3RhdHVzTGFiZWwiLCJzdGF0dXNlcyIsIlZBTElEIiwiUkVKRUNURURfQ0VOU09SU0hJUCIsIlJFSkVDVEVEX1RFQ0hOSUNBTCIsIldBSVRJTkciLCJnZXRDaGFubmVsTGFiZWwiLCJjaGFubmVsIiwiY2hhbm5lbHMiLCJET0NVTUVOVEFSWSIsIk5FV1MiLCJPVEhFUiIsImRpdiIsInN0eWxlIiwibWluSGVpZ2h0IiwiYmFja2dyb3VuZCIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJjb2xvciIsImZvbnRTaXplIiwicGFkZGluZyIsIm1heFdpZHRoIiwibWFyZ2luIiwiYm9yZGVyUmFkaXVzIiwibWFyZ2luQm90dG9tIiwiYm94U2hhZG93IiwidGV4dEFsaWduIiwiaDEiLCJmb250V2VpZ2h0IiwiZ2FwIiwiZmxleFdyYXAiLCJMaW5rIiwidGV4dERlY29yYXRpb24iLCJidXR0b24iLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJib3JkZXIiLCJjdXJzb3IiLCJoMiIsImdyaWRUZW1wbGF0ZUNvbHVtbnMiLCJsYWJlbCIsImlucHV0IiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsIndpZHRoIiwiZGlyZWN0aW9uIiwic2VsZWN0Iiwib3B0aW9uIiwibWFyZ2luVG9wIiwibGVuZ3RoIiwicCIsIm1hcCIsImgzIiwic3Ryb25nIiwic2VnbWVudHMiLCJzZWdtZW50Iiwic2VnbWVudE51bWJlciIsImNvZGUiLCJ0aW1lSW4iLCJ0aW1lT3V0IiwiZHVyYXRpb24iLCJmbGV4RGlyZWN0aW9uIiwibG9jYXRpb24iLCJwb3NpdGlvbiIsImJvdHRvbSIsImxlZnQiLCJmb250RmFtaWx5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/media-list/page.tsx\n"));

/***/ })

});