"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [showUserInfo, setShowUserInfo] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // إذا لم يكن المستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول\n            if (!user && !localStorage.getItem('user')) {\n                router.push('/login');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: '1200px',\n                margin: '0 auto',\n                background: 'white',\n                borderRadius: '20px',\n                padding: '40px',\n                boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    style: {\n                        fontSize: '2.5rem',\n                        fontWeight: 'bold',\n                        color: '#333',\n                        textAlign: 'center',\n                        marginBottom: '40px',\n                        background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                        WebkitBackgroundClip: 'text',\n                        WebkitTextFillColor: 'transparent'\n                    },\n                    children: \"\\uD83C\\uDFAC نظام إدارة المحتوى الإعلامي\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                        gap: '20px',\n                        marginBottom: '40px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'linear-gradient(135deg, #1976d215 0%, #1976d225 100%)',\n                                border: '2px solid #1976d230',\n                                borderRadius: '15px',\n                                padding: '20px',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83C\\uDFAC\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#1976d2',\n                                        margin: '10px 0',\n                                        fontSize: '1.2rem'\n                                    },\n                                    children: \"إجمالي المواد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        fontWeight: 'bold',\n                                        color: '#1976d2'\n                                    },\n                                    children: \"1,234\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'linear-gradient(135deg, #2e7d3215 0%, #2e7d3225 100%)',\n                                border: '2px solid #2e7d3230',\n                                borderRadius: '15px',\n                                padding: '20px',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDCFA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#2e7d32',\n                                        margin: '10px 0',\n                                        fontSize: '1.2rem'\n                                    },\n                                    children: \"البرامج النشطة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        fontWeight: 'bold',\n                                        color: '#2e7d32'\n                                    },\n                                    children: \"89\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'linear-gradient(135deg, #ed6c0215 0%, #ed6c0225 100%)',\n                                border: '2px solid #ed6c0230',\n                                borderRadius: '15px',\n                                padding: '20px',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83C\\uDFB5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#ed6c02',\n                                        margin: '10px 0',\n                                        fontSize: '1.2rem'\n                                    },\n                                    children: \"الأغاني\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        fontWeight: 'bold',\n                                        color: '#ed6c02'\n                                    },\n                                    children: \"456\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'linear-gradient(135deg, #d32f2f15 0%, #d32f2f25 100%)',\n                                border: '2px solid #d32f2f30',\n                                borderRadius: '15px',\n                                padding: '20px',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"❌\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#d32f2f',\n                                        margin: '10px 0',\n                                        fontSize: '1.2rem'\n                                    },\n                                    children: \"المواد المرفوضة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        fontWeight: 'bold',\n                                        color: '#d32f2f'\n                                    },\n                                    children: \"12\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: '2fr 1fr',\n                        gap: '20px',\n                        marginTop: '40px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n                                borderRadius: '15px',\n                                padding: '30px',\n                                border: '1px solid #e0e0e0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: '#333',\n                                        marginBottom: '20px',\n                                        fontSize: '1.5rem'\n                                    },\n                                    children: \"\\uD83D\\uDCCA النشاط الأخير\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        color: '#666',\n                                        padding: '40px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDCC8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"لا توجد أنشطة حديثة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',\n                                borderRadius: '15px',\n                                padding: '30px',\n                                border: '1px solid #e0e0e0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: '#333',\n                                        marginBottom: '20px',\n                                        fontSize: '1.5rem'\n                                    },\n                                    children: \"\\uD83D\\uDD14 الإشعارات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        color: '#666',\n                                        padding: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDD15\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"لا توجد إشعارات جديدة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginTop: '40px',\n                        display: 'flex',\n                        justifyContent: 'center',\n                        gap: '20px',\n                        flexWrap: 'wrap'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/add-media'),\n                            style: {\n                                background: 'linear-gradient(45deg, #2e7d32, #2e7d32dd)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '25px',\n                                padding: '15px 30px',\n                                fontSize: '1rem',\n                                fontWeight: 'bold',\n                                cursor: 'pointer',\n                                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                transition: 'transform 0.2s ease'\n                            },\n                            onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                            onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                            children: \"➕ إضافة مادة إعلامية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/media-list'),\n                            style: {\n                                background: 'linear-gradient(45deg, #f093fb, #f5576c)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '25px',\n                                padding: '15px 30px',\n                                fontSize: '1rem',\n                                fontWeight: 'bold',\n                                cursor: 'pointer',\n                                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                transition: 'transform 0.2s ease'\n                            },\n                            onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                            onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                            children: \"\\uD83D\\uDCDA قائمة المواد الإعلامية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/statistics'),\n                            style: {\n                                background: 'linear-gradient(45deg, #9c27b0, #7b1fa2)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '25px',\n                                padding: '15px 30px',\n                                fontSize: '1rem',\n                                fontWeight: 'bold',\n                                cursor: 'pointer',\n                                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                transition: 'transform 0.2s ease'\n                            },\n                            onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                            onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                            children: \"\\uD83D\\uDCCA إحصائيات النظام\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/weekly-schedule'),\n                            style: {\n                                background: 'linear-gradient(45deg, #673ab7, #512da8)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '25px',\n                                padding: '15px 30px',\n                                fontSize: '1rem',\n                                fontWeight: 'bold',\n                                cursor: 'pointer',\n                                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                transition: 'transform 0.2s ease'\n                            },\n                            onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                            onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                            children: \"\\uD83D\\uDCC5 الخريطة البرامجية الأسبوعية الجديدة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/export'),\n                            style: {\n                                background: 'linear-gradient(45deg, #9c27b0, #9c27b0dd)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '25px',\n                                padding: '15px 30px',\n                                fontSize: '1rem',\n                                fontWeight: 'bold',\n                                cursor: 'pointer',\n                                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                transition: 'transform 0.2s ease'\n                            },\n                            onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                            onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                            children: \"\\uD83D\\uDCE4 تصدير البيانات\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginTop: '40px',\n                        textAlign: 'center',\n                        padding: '20px',\n                        background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',\n                        borderRadius: '15px',\n                        border: '1px solid #e0e0e0'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                color: '#333',\n                                marginBottom: '10px'\n                            },\n                            children: \"\\uD83D\\uDE80 مرحباً بك في نظام إدارة المحتوى الإعلامي المتطور\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: '#666',\n                                lineHeight: '1.6'\n                            },\n                            children: \"نظام شامل لإدارة المواد الإعلامية مع دعم كامل للغة العربية وواجهة مستخدم عصرية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '20px'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: '#555',\n                                    fontSize: '0.9rem'\n                                },\n                                children: \"✨ الميزات الرئيسية: إدارة المواد الإعلامية • الخريطة البرامجية الأسبوعية • جدول الإذاعة اليومية • تصدير إلى Excel مع دعم RTL\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"SlaB3zud4Dn3sgLC32VA8bZxTTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDNEM7QUFDSztBQUNMO0FBRTdCLFNBQVNJOztJQUN0QixNQUFNQyxTQUFTTCwwREFBU0E7SUFDeEIsTUFBTSxFQUFFTSxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsYUFBYSxFQUFFLEdBQUdQLDhEQUFPQTtJQUMvQyxNQUFNLENBQUNRLGNBQWNDLGdCQUFnQixHQUFHUCwrQ0FBUUEsQ0FBQztJQUVqREQsZ0RBQVNBOzBCQUFDO1lBQ1IsMERBQTBEO1lBQzFELElBQUksQ0FBQ0ksUUFBUSxDQUFDSyxhQUFhQyxPQUFPLENBQUMsU0FBUztnQkFDMUNQLE9BQU9RLElBQUksQ0FBQztZQUNkO1FBQ0Y7eUJBQUc7UUFBQ1A7UUFBTUQ7S0FBTztJQUNqQixxQkFDRSw4REFBQ1M7UUFBSUMsT0FBTztZQUNWQyxXQUFXO1lBQ1hDLFlBQVk7WUFDWkMsU0FBUztZQUNUQyxZQUFZO1lBQ1pDLFdBQVc7UUFDYjtrQkFDRSw0RUFBQ047WUFBSUMsT0FBTztnQkFDVk0sVUFBVTtnQkFDVkMsUUFBUTtnQkFDUkwsWUFBWTtnQkFDWk0sY0FBYztnQkFDZEwsU0FBUztnQkFDVE0sV0FBVztZQUNiOzs4QkFDRSw4REFBQ0M7b0JBQUdWLE9BQU87d0JBQ1RXLFVBQVU7d0JBQ1ZDLFlBQVk7d0JBQ1pDLE9BQU87d0JBQ1BDLFdBQVc7d0JBQ1hDLGNBQWM7d0JBQ2RiLFlBQVk7d0JBQ1pjLHNCQUFzQjt3QkFDdEJDLHFCQUFxQjtvQkFDdkI7OEJBQUc7Ozs7Ozs4QkFJSCw4REFBQ2xCO29CQUFJQyxPQUFPO3dCQUNWa0IsU0FBUzt3QkFDVEMscUJBQXFCO3dCQUNyQkMsS0FBSzt3QkFDTEwsY0FBYztvQkFDaEI7O3NDQUNFLDhEQUFDaEI7NEJBQUlDLE9BQU87Z0NBQ1ZFLFlBQVk7Z0NBQ1ptQixRQUFRO2dDQUNSYixjQUFjO2dDQUNkTCxTQUFTO2dDQUNUVyxXQUFXOzRCQUNiOzs4Q0FDRSw4REFBQ2Y7b0NBQUlDLE9BQU87d0NBQUVXLFVBQVU7d0NBQVFJLGNBQWM7b0NBQU87OENBQUc7Ozs7Ozs4Q0FDeEQsOERBQUNPO29DQUFHdEIsT0FBTzt3Q0FBRWEsT0FBTzt3Q0FBV04sUUFBUTt3Q0FBVUksVUFBVTtvQ0FBUzs4Q0FBRzs7Ozs7OzhDQUN2RSw4REFBQ1o7b0NBQUlDLE9BQU87d0NBQUVXLFVBQVU7d0NBQVFDLFlBQVk7d0NBQVFDLE9BQU87b0NBQVU7OENBQUc7Ozs7Ozs7Ozs7OztzQ0FHMUUsOERBQUNkOzRCQUFJQyxPQUFPO2dDQUNWRSxZQUFZO2dDQUNabUIsUUFBUTtnQ0FDUmIsY0FBYztnQ0FDZEwsU0FBUztnQ0FDVFcsV0FBVzs0QkFDYjs7OENBQ0UsOERBQUNmO29DQUFJQyxPQUFPO3dDQUFFVyxVQUFVO3dDQUFRSSxjQUFjO29DQUFPOzhDQUFHOzs7Ozs7OENBQ3hELDhEQUFDTztvQ0FBR3RCLE9BQU87d0NBQUVhLE9BQU87d0NBQVdOLFFBQVE7d0NBQVVJLFVBQVU7b0NBQVM7OENBQUc7Ozs7Ozs4Q0FDdkUsOERBQUNaO29DQUFJQyxPQUFPO3dDQUFFVyxVQUFVO3dDQUFRQyxZQUFZO3dDQUFRQyxPQUFPO29DQUFVOzhDQUFHOzs7Ozs7Ozs7Ozs7c0NBRzFFLDhEQUFDZDs0QkFBSUMsT0FBTztnQ0FDVkUsWUFBWTtnQ0FDWm1CLFFBQVE7Z0NBQ1JiLGNBQWM7Z0NBQ2RMLFNBQVM7Z0NBQ1RXLFdBQVc7NEJBQ2I7OzhDQUNFLDhEQUFDZjtvQ0FBSUMsT0FBTzt3Q0FBRVcsVUFBVTt3Q0FBUUksY0FBYztvQ0FBTzs4Q0FBRzs7Ozs7OzhDQUN4RCw4REFBQ087b0NBQUd0QixPQUFPO3dDQUFFYSxPQUFPO3dDQUFXTixRQUFRO3dDQUFVSSxVQUFVO29DQUFTOzhDQUFHOzs7Ozs7OENBQ3ZFLDhEQUFDWjtvQ0FBSUMsT0FBTzt3Q0FBRVcsVUFBVTt3Q0FBUUMsWUFBWTt3Q0FBUUMsT0FBTztvQ0FBVTs4Q0FBRzs7Ozs7Ozs7Ozs7O3NDQUcxRSw4REFBQ2Q7NEJBQUlDLE9BQU87Z0NBQ1ZFLFlBQVk7Z0NBQ1ptQixRQUFRO2dDQUNSYixjQUFjO2dDQUNkTCxTQUFTO2dDQUNUVyxXQUFXOzRCQUNiOzs4Q0FDRSw4REFBQ2Y7b0NBQUlDLE9BQU87d0NBQUVXLFVBQVU7d0NBQVFJLGNBQWM7b0NBQU87OENBQUc7Ozs7Ozs4Q0FDeEQsOERBQUNPO29DQUFHdEIsT0FBTzt3Q0FBRWEsT0FBTzt3Q0FBV04sUUFBUTt3Q0FBVUksVUFBVTtvQ0FBUzs4Q0FBRzs7Ozs7OzhDQUN2RSw4REFBQ1o7b0NBQUlDLE9BQU87d0NBQUVXLFVBQVU7d0NBQVFDLFlBQVk7d0NBQVFDLE9BQU87b0NBQVU7OENBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJNUUsOERBQUNkO29CQUFJQyxPQUFPO3dCQUNWa0IsU0FBUzt3QkFDVEMscUJBQXFCO3dCQUNyQkMsS0FBSzt3QkFDTEcsV0FBVztvQkFDYjs7c0NBQ0UsOERBQUN4Qjs0QkFBSUMsT0FBTztnQ0FDVkUsWUFBWTtnQ0FDWk0sY0FBYztnQ0FDZEwsU0FBUztnQ0FDVGtCLFFBQVE7NEJBQ1Y7OzhDQUNFLDhEQUFDRztvQ0FBR3hCLE9BQU87d0NBQUVhLE9BQU87d0NBQVFFLGNBQWM7d0NBQVFKLFVBQVU7b0NBQVM7OENBQUc7Ozs7Ozs4Q0FDeEUsOERBQUNaO29DQUFJQyxPQUFPO3dDQUFFYyxXQUFXO3dDQUFVRCxPQUFPO3dDQUFRVixTQUFTO29DQUFPOztzREFDaEUsOERBQUNKOzRDQUFJQyxPQUFPO2dEQUFFVyxVQUFVO2dEQUFRSSxjQUFjOzRDQUFPO3NEQUFHOzs7Ozs7c0RBQ3hELDhEQUFDVTtzREFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUlQLDhEQUFDMUI7NEJBQUlDLE9BQU87Z0NBQ1ZFLFlBQVk7Z0NBQ1pNLGNBQWM7Z0NBQ2RMLFNBQVM7Z0NBQ1RrQixRQUFROzRCQUNWOzs4Q0FDRSw4REFBQ0c7b0NBQUd4QixPQUFPO3dDQUFFYSxPQUFPO3dDQUFRRSxjQUFjO3dDQUFRSixVQUFVO29DQUFTOzhDQUFHOzs7Ozs7OENBQ3hFLDhEQUFDWjtvQ0FBSUMsT0FBTzt3Q0FBRWMsV0FBVzt3Q0FBVUQsT0FBTzt3Q0FBUVYsU0FBUztvQ0FBTzs7c0RBQ2hFLDhEQUFDSjs0Q0FBSUMsT0FBTztnREFBRVcsVUFBVTtnREFBUUksY0FBYzs0Q0FBTztzREFBRzs7Ozs7O3NEQUN4RCw4REFBQ1U7c0RBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLVCw4REFBQzFCO29CQUFJQyxPQUFPO3dCQUNWdUIsV0FBVzt3QkFDWEwsU0FBUzt3QkFDVFEsZ0JBQWdCO3dCQUNoQk4sS0FBSzt3QkFDTE8sVUFBVTtvQkFDWjs7c0NBQ0UsOERBQUNDOzRCQUNDQyxTQUFTLElBQU12QyxPQUFPUSxJQUFJLENBQUM7NEJBQzNCRSxPQUFPO2dDQUNQRSxZQUFZO2dDQUNaVyxPQUFPO2dDQUNQUSxRQUFRO2dDQUNSYixjQUFjO2dDQUNkTCxTQUFTO2dDQUNUUSxVQUFVO2dDQUNWQyxZQUFZO2dDQUNaa0IsUUFBUTtnQ0FDUnJCLFdBQVc7Z0NBQ1hzQixZQUFZOzRCQUNkOzRCQUNBQyxjQUFjLENBQUNDLElBQU1BLEVBQUVDLGFBQWEsQ0FBQ2xDLEtBQUssQ0FBQ21DLFNBQVMsR0FBRzs0QkFDdkRDLGNBQWMsQ0FBQ0gsSUFBTUEsRUFBRUMsYUFBYSxDQUFDbEMsS0FBSyxDQUFDbUMsU0FBUyxHQUFHO3NDQUN0RDs7Ozs7O3NDQUlELDhEQUFDUDs0QkFDQ0MsU0FBUyxJQUFNdkMsT0FBT1EsSUFBSSxDQUFDOzRCQUMzQkUsT0FBTztnQ0FDUEUsWUFBWTtnQ0FDWlcsT0FBTztnQ0FDUFEsUUFBUTtnQ0FDUmIsY0FBYztnQ0FDZEwsU0FBUztnQ0FDVFEsVUFBVTtnQ0FDVkMsWUFBWTtnQ0FDWmtCLFFBQVE7Z0NBQ1JyQixXQUFXO2dDQUNYc0IsWUFBWTs0QkFDZDs0QkFDQUMsY0FBYyxDQUFDQyxJQUFNQSxFQUFFQyxhQUFhLENBQUNsQyxLQUFLLENBQUNtQyxTQUFTLEdBQUc7NEJBQ3ZEQyxjQUFjLENBQUNILElBQU1BLEVBQUVDLGFBQWEsQ0FBQ2xDLEtBQUssQ0FBQ21DLFNBQVMsR0FBRztzQ0FDdEQ7Ozs7OztzQ0FJRCw4REFBQ1A7NEJBQ0NDLFNBQVMsSUFBTXZDLE9BQU9RLElBQUksQ0FBQzs0QkFDM0JFLE9BQU87Z0NBQ1BFLFlBQVk7Z0NBQ1pXLE9BQU87Z0NBQ1BRLFFBQVE7Z0NBQ1JiLGNBQWM7Z0NBQ2RMLFNBQVM7Z0NBQ1RRLFVBQVU7Z0NBQ1ZDLFlBQVk7Z0NBQ1prQixRQUFRO2dDQUNSckIsV0FBVztnQ0FDWHNCLFlBQVk7NEJBQ2Q7NEJBQ0FDLGNBQWMsQ0FBQ0MsSUFBTUEsRUFBRUMsYUFBYSxDQUFDbEMsS0FBSyxDQUFDbUMsU0FBUyxHQUFHOzRCQUN2REMsY0FBYyxDQUFDSCxJQUFNQSxFQUFFQyxhQUFhLENBQUNsQyxLQUFLLENBQUNtQyxTQUFTLEdBQUc7c0NBQ3REOzs7Ozs7c0NBWUQsOERBQUNQOzRCQUNDQyxTQUFTLElBQU12QyxPQUFPUSxJQUFJLENBQUM7NEJBQzNCRSxPQUFPO2dDQUNQRSxZQUFZO2dDQUNaVyxPQUFPO2dDQUNQUSxRQUFRO2dDQUNSYixjQUFjO2dDQUNkTCxTQUFTO2dDQUNUUSxVQUFVO2dDQUNWQyxZQUFZO2dDQUNaa0IsUUFBUTtnQ0FDUnJCLFdBQVc7Z0NBQ1hzQixZQUFZOzRCQUNkOzRCQUNBQyxjQUFjLENBQUNDLElBQU1BLEVBQUVDLGFBQWEsQ0FBQ2xDLEtBQUssQ0FBQ21DLFNBQVMsR0FBRzs0QkFDdkRDLGNBQWMsQ0FBQ0gsSUFBTUEsRUFBRUMsYUFBYSxDQUFDbEMsS0FBSyxDQUFDbUMsU0FBUyxHQUFHO3NDQUN0RDs7Ozs7O3NDQUlELDhEQUFDUDs0QkFDQ0MsU0FBUyxJQUFNdkMsT0FBT1EsSUFBSSxDQUFDOzRCQUMzQkUsT0FBTztnQ0FDUEUsWUFBWTtnQ0FDWlcsT0FBTztnQ0FDUFEsUUFBUTtnQ0FDUmIsY0FBYztnQ0FDZEwsU0FBUztnQ0FDVFEsVUFBVTtnQ0FDVkMsWUFBWTtnQ0FDWmtCLFFBQVE7Z0NBQ1JyQixXQUFXO2dDQUNYc0IsWUFBWTs0QkFDZDs0QkFDQUMsY0FBYyxDQUFDQyxJQUFNQSxFQUFFQyxhQUFhLENBQUNsQyxLQUFLLENBQUNtQyxTQUFTLEdBQUc7NEJBQ3ZEQyxjQUFjLENBQUNILElBQU1BLEVBQUVDLGFBQWEsQ0FBQ2xDLEtBQUssQ0FBQ21DLFNBQVMsR0FBRztzQ0FDdEQ7Ozs7Ozs7Ozs7Ozs4QkFLSCw4REFBQ3BDO29CQUFJQyxPQUFPO3dCQUNWdUIsV0FBVzt3QkFDWFQsV0FBVzt3QkFDWFgsU0FBUzt3QkFDVEQsWUFBWTt3QkFDWk0sY0FBYzt3QkFDZGEsUUFBUTtvQkFDVjs7c0NBQ0UsOERBQUNDOzRCQUFHdEIsT0FBTztnQ0FBRWEsT0FBTztnQ0FBUUUsY0FBYzs0QkFBTztzQ0FBRzs7Ozs7O3NDQUNwRCw4REFBQ1U7NEJBQUV6QixPQUFPO2dDQUFFYSxPQUFPO2dDQUFRd0IsWUFBWTs0QkFBTTtzQ0FBRzs7Ozs7O3NDQUdoRCw4REFBQ3RDOzRCQUFJQyxPQUFPO2dDQUFFdUIsV0FBVzs0QkFBTztzQ0FDOUIsNEVBQUNFO2dDQUFFekIsT0FBTztvQ0FBRWEsT0FBTztvQ0FBUUYsVUFBVTtnQ0FBUzswQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVE3RDtHQXpRd0J0Qjs7UUFDUEosc0RBQVNBO1FBQ2dCQywwREFBT0E7OztLQUZ6QkciLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbXBvbmVudHMvQXV0aEd1YXJkJztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCB7IHVzZXIsIGxvZ291dCwgaGFzUGVybWlzc2lvbiB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCBbc2hvd1VzZXJJbmZvLCBzZXRTaG93VXNlckluZm9dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g2KXYsNinINmE2YUg2YrZg9mGINin2YTZhdiz2KrYrtiv2YUg2YXYs9is2YQg2K/YrtmI2YTYjCDYqtmI2KzZitmHINmE2LXZgdit2Kkg2KrYs9is2YrZhCDYp9mE2K/YrtmI2YRcbiAgICBpZiAoIXVzZXIgJiYgIWxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJykpIHtcbiAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKTtcbiAgICB9XG4gIH0sIFt1c2VyLCByb3V0ZXJdKTtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKScsXG4gICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICBmb250RmFtaWx5OiAnQ2Fpcm8sIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICAgIGRpcmVjdGlvbjogJ3J0bCdcbiAgICB9fT5cbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgbWF4V2lkdGg6ICcxMjAwcHgnLFxuICAgICAgICBtYXJnaW46ICcwIGF1dG8nLFxuICAgICAgICBiYWNrZ3JvdW5kOiAnd2hpdGUnLFxuICAgICAgICBib3JkZXJSYWRpdXM6ICcyMHB4JyxcbiAgICAgICAgcGFkZGluZzogJzQwcHgnLFxuICAgICAgICBib3hTaGFkb3c6ICcwIDIwcHggNDBweCByZ2JhKDAsMCwwLDAuMSknXG4gICAgICB9fT5cbiAgICAgICAgPGgxIHN0eWxlPXt7XG4gICAgICAgICAgZm9udFNpemU6ICcyLjVyZW0nLFxuICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICAgICAgICBjb2xvcjogJyMzMzMnLFxuICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgbWFyZ2luQm90dG9tOiAnNDBweCcsXG4gICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzY2N2VlYSwgIzc2NGJhMiknLFxuICAgICAgICAgIFdlYmtpdEJhY2tncm91bmRDbGlwOiAndGV4dCcsXG4gICAgICAgICAgV2Via2l0VGV4dEZpbGxDb2xvcjogJ3RyYW5zcGFyZW50J1xuICAgICAgICB9fT5cbiAgICAgICAgICDwn46sINmG2LjYp9mFINil2K/Yp9ix2Kkg2KfZhNmF2K3YqtmI2Ykg2KfZhNil2LnZhNin2YXZilxuICAgICAgICA8L2gxPlxuXG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBkaXNwbGF5OiAnZ3JpZCcsXG4gICAgICAgICAgZ3JpZFRlbXBsYXRlQ29sdW1uczogJ3JlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKScsXG4gICAgICAgICAgZ2FwOiAnMjBweCcsXG4gICAgICAgICAgbWFyZ2luQm90dG9tOiAnNDBweCdcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICMxOTc2ZDIxNSAwJSwgIzE5NzZkMjI1IDEwMCUpJyxcbiAgICAgICAgICAgIGJvcmRlcjogJzJweCBzb2xpZCAjMTk3NmQyMzAnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTVweCcsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnM3JlbScsIG1hcmdpbkJvdHRvbTogJzEwcHgnIH19PvCfjqw8L2Rpdj5cbiAgICAgICAgICAgIDxoMyBzdHlsZT17eyBjb2xvcjogJyMxOTc2ZDInLCBtYXJnaW46ICcxMHB4IDAnLCBmb250U2l6ZTogJzEuMnJlbScgfX0+2KXYrNmF2KfZhNmKINin2YTZhdmI2KfYrzwvaDM+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIGZvbnRXZWlnaHQ6ICdib2xkJywgY29sb3I6ICcjMTk3NmQyJyB9fT4xLDIzNDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyZTdkMzIxNSAwJSwgIzJlN2QzMjI1IDEwMCUpJyxcbiAgICAgICAgICAgIGJvcmRlcjogJzJweCBzb2xpZCAjMmU3ZDMyMzAnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTVweCcsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnM3JlbScsIG1hcmdpbkJvdHRvbTogJzEwcHgnIH19PvCfk7o8L2Rpdj5cbiAgICAgICAgICAgIDxoMyBzdHlsZT17eyBjb2xvcjogJyMyZTdkMzInLCBtYXJnaW46ICcxMHB4IDAnLCBmb250U2l6ZTogJzEuMnJlbScgfX0+2KfZhNio2LHYp9mF2Kwg2KfZhNmG2LTYt9ipPC9oMz5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICcycmVtJywgZm9udFdlaWdodDogJ2JvbGQnLCBjb2xvcjogJyMyZTdkMzInIH19Pjg5PC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2VkNmMwMjE1IDAlLCAjZWQ2YzAyMjUgMTAwJSknLFxuICAgICAgICAgICAgYm9yZGVyOiAnMnB4IHNvbGlkICNlZDZjMDIzMCcsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxNXB4JyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICcyMHB4JyxcbiAgICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcidcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICczcmVtJywgbWFyZ2luQm90dG9tOiAnMTBweCcgfX0+8J+OtTwvZGl2PlxuICAgICAgICAgICAgPGgzIHN0eWxlPXt7IGNvbG9yOiAnI2VkNmMwMicsIG1hcmdpbjogJzEwcHggMCcsIGZvbnRTaXplOiAnMS4ycmVtJyB9fT7Yp9mE2KPYutin2YbZijwvaDM+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMnJlbScsIGZvbnRXZWlnaHQ6ICdib2xkJywgY29sb3I6ICcjZWQ2YzAyJyB9fT40NTY8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZDMyZjJmMTUgMCUsICNkMzJmMmYyNSAxMDAlKScsXG4gICAgICAgICAgICBib3JkZXI6ICcycHggc29saWQgI2QzMmYyZjMwJyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzE1cHgnLFxuICAgICAgICAgICAgcGFkZGluZzogJzIwcHgnLFxuICAgICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJ1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzNyZW0nLCBtYXJnaW5Cb3R0b206ICcxMHB4JyB9fT7inYw8L2Rpdj5cbiAgICAgICAgICAgIDxoMyBzdHlsZT17eyBjb2xvcjogJyNkMzJmMmYnLCBtYXJnaW46ICcxMHB4IDAnLCBmb250U2l6ZTogJzEuMnJlbScgfX0+2KfZhNmF2YjYp9ivINin2YTZhdix2YHZiNi22Kk8L2gzPlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzJyZW0nLCBmb250V2VpZ2h0OiAnYm9sZCcsIGNvbG9yOiAnI2QzMmYyZicgfX0+MTI8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIGRpc3BsYXk6ICdncmlkJyxcbiAgICAgICAgICBncmlkVGVtcGxhdGVDb2x1bW5zOiAnMmZyIDFmcicsXG4gICAgICAgICAgZ2FwOiAnMjBweCcsXG4gICAgICAgICAgbWFyZ2luVG9wOiAnNDBweCdcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNWY3ZmEgMCUsICNjM2NmZTIgMTAwJSknLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTVweCcsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMzBweCcsXG4gICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2UwZTBlMCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxoMiBzdHlsZT17eyBjb2xvcjogJyMzMzMnLCBtYXJnaW5Cb3R0b206ICcyMHB4JywgZm9udFNpemU6ICcxLjVyZW0nIH19PvCfk4og2KfZhNmG2LTYp9i3INin2YTYo9iu2YrYsTwvaDI+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicsIGNvbG9yOiAnIzY2NicsIHBhZGRpbmc6ICc0MHB4JyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzNyZW0nLCBtYXJnaW5Cb3R0b206ICcxMHB4JyB9fT7wn5OIPC9kaXY+XG4gICAgICAgICAgICAgIDxwPtmE2Kcg2KrZiNis2K8g2KPZhti02LfYqSDYrdiv2YrYq9ipPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZWNkMiAwJSwgI2ZjYjY5ZiAxMDAlKScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxNXB4JyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICczMHB4JyxcbiAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjZTBlMGUwJ1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPGgyIHN0eWxlPXt7IGNvbG9yOiAnIzMzMycsIG1hcmdpbkJvdHRvbTogJzIwcHgnLCBmb250U2l6ZTogJzEuNXJlbScgfX0+8J+UlCDYp9mE2KXYtNi52KfYsdin2Ko8L2gyPlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyB0ZXh0QWxpZ246ICdjZW50ZXInLCBjb2xvcjogJyM2NjYnLCBwYWRkaW5nOiAnMjBweCcgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICczcmVtJywgbWFyZ2luQm90dG9tOiAnMTBweCcgfX0+8J+UlTwvZGl2PlxuICAgICAgICAgICAgICA8cD7ZhNinINiq2YjYrNivINil2LTYudin2LHYp9iqINis2K/Zitiv2Kk8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIG1hcmdpblRvcDogJzQwcHgnLFxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgZ2FwOiAnMjBweCcsXG4gICAgICAgICAgZmxleFdyYXA6ICd3cmFwJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2FkZC1tZWRpYScpfVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoNDVkZWcsICMyZTdkMzIsICMyZTdkMzJkZCknLFxuICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzI1cHgnLFxuICAgICAgICAgICAgcGFkZGluZzogJzE1cHggMzBweCcsXG4gICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxNXB4IHJnYmEoMCwwLDAsMC4yKScsXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiAndHJhbnNmb3JtIDAuMnMgZWFzZSdcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IGUuY3VycmVudFRhcmdldC5zdHlsZS50cmFuc2Zvcm0gPSAndHJhbnNsYXRlWSgtMnB4KSd9XG4gICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICd0cmFuc2xhdGVZKDApJ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICDinpUg2KXYttin2YHYqSDZhdin2K/YqSDYpdi52YTYp9mF2YrYqVxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9tZWRpYS1saXN0Jyl9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgI2YwOTNmYiwgI2Y1NTc2YyknLFxuICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzI1cHgnLFxuICAgICAgICAgICAgcGFkZGluZzogJzE1cHggMzBweCcsXG4gICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxNXB4IHJnYmEoMCwwLDAsMC4yKScsXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiAndHJhbnNmb3JtIDAuMnMgZWFzZSdcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IGUuY3VycmVudFRhcmdldC5zdHlsZS50cmFuc2Zvcm0gPSAndHJhbnNsYXRlWSgtMnB4KSd9XG4gICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICd0cmFuc2xhdGVZKDApJ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICDwn5OaINmC2KfYptmF2Kkg2KfZhNmF2YjYp9ivINin2YTYpdi52YTYp9mF2YrYqVxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9zdGF0aXN0aWNzJyl9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzljMjdiMCwgIzdiMWZhMiknLFxuICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzI1cHgnLFxuICAgICAgICAgICAgcGFkZGluZzogJzE1cHggMzBweCcsXG4gICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxNXB4IHJnYmEoMCwwLDAsMC4yKScsXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiAndHJhbnNmb3JtIDAuMnMgZWFzZSdcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IGUuY3VycmVudFRhcmdldC5zdHlsZS50cmFuc2Zvcm0gPSAndHJhbnNsYXRlWSgtMnB4KSd9XG4gICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICd0cmFuc2xhdGVZKDApJ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICDwn5OKINil2K3Ytdin2KbZitin2Kog2KfZhNmG2LjYp9mFXG4gICAgICAgICAgPC9idXR0b24+XG5cblxuXG5cblxuXG5cblxuXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy93ZWVrbHktc2NoZWR1bGUnKX1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjNjczYWI3LCAjNTEyZGE4KScsXG4gICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMjVweCcsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMTVweCAzMHB4JyxcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgIGJveFNoYWRvdzogJzAgNHB4IDE1cHggcmdiYSgwLDAsMCwwLjIpJyxcbiAgICAgICAgICAgIHRyYW5zaXRpb246ICd0cmFuc2Zvcm0gMC4ycyBlYXNlJ1xuICAgICAgICAgIH19XG4gICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4gZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICd0cmFuc2xhdGVZKC0ycHgpJ31cbiAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVkoMCknfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIPCfk4Ug2KfZhNiu2LHZiti32Kkg2KfZhNio2LHYp9mF2KzZitipINin2YTYo9iz2KjZiNi52YrYqSDYp9mE2KzYr9mK2K/YqVxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9leHBvcnQnKX1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjOWMyN2IwLCAjOWMyN2IwZGQpJyxcbiAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcyNXB4JyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICcxNXB4IDMwcHgnLFxuICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgYm94U2hhZG93OiAnMCA0cHggMTVweCByZ2JhKDAsMCwwLDAuMiknLFxuICAgICAgICAgICAgdHJhbnNpdGlvbjogJ3RyYW5zZm9ybSAwLjJzIGVhc2UnXG4gICAgICAgICAgfX1cbiAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiBlLmN1cnJlbnRUYXJnZXQuc3R5bGUudHJhbnNmb3JtID0gJ3RyYW5zbGF0ZVkoLTJweCknfVxuICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IGUuY3VycmVudFRhcmdldC5zdHlsZS50cmFuc2Zvcm0gPSAndHJhbnNsYXRlWSgwKSd9XG4gICAgICAgICAgPlxuICAgICAgICAgICAg8J+TpCDYqti12K/ZitixINin2YTYqNmK2KfZhtin2KpcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIG1hcmdpblRvcDogJzQwcHgnLFxuICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgcGFkZGluZzogJzIwcHgnLFxuICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjYThlZGVhIDAlLCAjZmVkNmUzIDEwMCUpJyxcbiAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxNXB4JyxcbiAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2UwZTBlMCdcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGgzIHN0eWxlPXt7IGNvbG9yOiAnIzMzMycsIG1hcmdpbkJvdHRvbTogJzEwcHgnIH19PvCfmoAg2YXYsdit2KjYp9mLINio2YMg2YHZiiDZhti42KfZhSDYpdiv2KfYsdipINin2YTZhdit2KrZiNmJINin2YTYpdi52YTYp9mF2Yog2KfZhNmF2KrYt9mI2LE8L2gzPlxuICAgICAgICAgIDxwIHN0eWxlPXt7IGNvbG9yOiAnIzY2NicsIGxpbmVIZWlnaHQ6ICcxLjYnIH19PlxuICAgICAgICAgICAg2YbYuNin2YUg2LTYp9mF2YQg2YTYpdiv2KfYsdipINin2YTZhdmI2KfYryDYp9mE2KXYudmE2KfZhdmK2Kkg2YXYuSDYr9i52YUg2YPYp9mF2YQg2YTZhNi62Kkg2KfZhNi52LHYqNmK2Kkg2YjZiNin2KzZh9ipINmF2LPYqtiu2K/ZhSDYudi12LHZitipXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMjBweCcgfX0+XG4gICAgICAgICAgICA8cCBzdHlsZT17eyBjb2xvcjogJyM1NTUnLCBmb250U2l6ZTogJzAuOXJlbScgfX0+XG4gICAgICAgICAgICAgIOKcqCDYp9mE2YXZitiy2KfYqiDYp9mE2LHYptmK2LPZitipOiDYpdiv2KfYsdipINin2YTZhdmI2KfYryDYp9mE2KXYudmE2KfZhdmK2Kkg4oCiINin2YTYrtix2YrYt9ipINin2YTYqNix2KfZhdis2YrYqSDYp9mE2KPYs9io2YjYudmK2Kkg4oCiINis2K/ZiNmEINin2YTYpdiw2KfYudipINin2YTZitmI2YXZitipIOKAoiDYqti12K/ZitixINil2YTZiSBFeGNlbCDZhdi5INiv2LnZhSBSVExcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VSb3V0ZXIiLCJ1c2VBdXRoIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJIb21lIiwicm91dGVyIiwidXNlciIsImxvZ291dCIsImhhc1Blcm1pc3Npb24iLCJzaG93VXNlckluZm8iLCJzZXRTaG93VXNlckluZm8iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwicHVzaCIsImRpdiIsInN0eWxlIiwibWluSGVpZ2h0IiwiYmFja2dyb3VuZCIsInBhZGRpbmciLCJmb250RmFtaWx5IiwiZGlyZWN0aW9uIiwibWF4V2lkdGgiLCJtYXJnaW4iLCJib3JkZXJSYWRpdXMiLCJib3hTaGFkb3ciLCJoMSIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsImNvbG9yIiwidGV4dEFsaWduIiwibWFyZ2luQm90dG9tIiwiV2Via2l0QmFja2dyb3VuZENsaXAiLCJXZWJraXRUZXh0RmlsbENvbG9yIiwiZGlzcGxheSIsImdyaWRUZW1wbGF0ZUNvbHVtbnMiLCJnYXAiLCJib3JkZXIiLCJoMyIsIm1hcmdpblRvcCIsImgyIiwicCIsImp1c3RpZnlDb250ZW50IiwiZmxleFdyYXAiLCJidXR0b24iLCJvbkNsaWNrIiwiY3Vyc29yIiwidHJhbnNpdGlvbiIsIm9uTW91c2VFbnRlciIsImUiLCJjdXJyZW50VGFyZ2V0IiwidHJhbnNmb3JtIiwib25Nb3VzZUxlYXZlIiwibGluZUhlaWdodCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction AuthGuard(param) {\n    let { children, requiredPermissions = [], requiredRole, fallbackComponent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        // المدير له صلاحيات كاملة\n        if (user.role === 'ADMIN') {\n            return true;\n        }\n        // التحقق من الدور المطلوب\n        if (role && user.role !== role) {\n            return false;\n        }\n        // التحقق من الصلاحيات المطلوبة\n        if (permissions.length > 0) {\n            const userPermissions = getUserPermissions(user.role);\n            return permissions.every((permission)=>userPermissions.includes(permission) || userPermissions.includes('ALL'));\n        }\n        return true;\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user === null || user === void 0 ? void 0 : user.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user === null || user === void 0 ? void 0 : user.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AuthGuard, \"fqF8YvhaHbrPIfzSUHTCm0cPUfQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthGuard;\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        if (user.role === 'ADMIN') return true;\n        const userPermissions = getUserPermissions(user.role);\n        return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN',\n        isMediaManager: (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER',\n        isScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER',\n        isViewer: (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER'\n    };\n}\n_s1(useAuth, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AuthGuard.tsx\n"));

/***/ })

});