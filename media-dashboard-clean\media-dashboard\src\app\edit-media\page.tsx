'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AuthGuard } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useToast } from '@/components/Toast';

interface Segment {
  id: number;
  segmentCode: string;
  timeIn: string;
  timeOut: string;
  duration: string;
}

export default function EditMediaPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const mediaId = searchParams.get('id');
  const { showToast, ToastContainer } = useToast();

  const [formData, setFormData] = useState({
    name: '',
    type: '',
    description: '',
    channel: '',
    source: '',
    status: '',
    startDate: '',
    endDate: '',
    notes: '',
    episodeNumber: '',
    seasonNumber: '',
    partNumber: '',
    hardDiskNumber: '',
  });

  const [segments, setSegments] = useState<Segment[]>([
    {
      id: 1,
      segmentCode: 'SEG001',
      timeIn: '00:00:00',
      timeOut: '',
      duration: '00:00:00'
    }
  ]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  // جلب بيانات المادة للتعديل
  useEffect(() => {
    if (mediaId) {
      fetchMediaData();
    } else {
      router.push('/media-list');
    }
  }, [mediaId]);

  const fetchMediaData = async () => {
    try {
      const response = await fetch(`/api/media?id=${mediaId}`);
      const result = await response.json();

      if (result.success && result.data) {
        const media = result.data;
        setFormData({
          name: media.name || '',
          type: media.type || '',
          description: media.description || '',
          channel: media.channel || '',
          source: media.source || '',
          status: media.status || '',
          startDate: media.startDate ? media.startDate.split('T')[0] : '',
          endDate: media.endDate ? media.endDate.split('T')[0] : '',
          notes: media.notes || '',
          episodeNumber: media.episodeNumber?.toString() || '',
          seasonNumber: media.seasonNumber?.toString() || '',
          partNumber: media.partNumber?.toString() || '',
          hardDiskNumber: media.hardDiskNumber || '',
        });

        if (media.segments && media.segments.length > 0) {
          setSegments(media.segments.map((seg: any, index: number) => ({
            id: index + 1,
            segmentCode: seg.segmentCode || `SEG${(index + 1).toString().padStart(3, '0')}`,
            timeIn: seg.timeIn || '00:00:00',
            timeOut: seg.timeOut || '',
            duration: seg.duration || '00:00:00'
          })));
        }
      } else {
        showToast('خطأ في جلب بيانات المادة', 'error');
        router.push('/media-list');
      }
    } catch (error) {
      console.error('Error fetching media:', error);
      showToast('خطأ في الاتصال بالخادم', 'error');
      router.push('/media-list');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AuthGuard requiredPermissions={['MEDIA_UPDATE']}>
        <DashboardLayout title="تحديث مادة إعلامية" subtitle="جاري تحميل البيانات..." icon="✏️">
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <div style={{ fontSize: '2rem', marginBottom: '20px' }}>⏳</div>
            <div style={{ color: '#a0aec0' }}>جاري تحميل بيانات المادة...</div>
          </div>
        </DashboardLayout>
      </AuthGuard>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/media?id=${mediaId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          segments: segments.map(seg => ({
            segmentCode: seg.segmentCode,
            timeIn: seg.timeIn,
            timeOut: seg.timeOut,
            duration: seg.duration
          }))
        }),
      });

      const result = await response.json();

      if (result.success) {
        showToast('تم تحديث المادة بنجاح', 'success');
        setTimeout(() => {
          router.push('/media-list');
        }, 1500);
      } else {
        showToast('خطأ في تحديث المادة: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Error updating media:', error);
      showToast('خطأ في الاتصال بالخادم', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addSegment = () => {
    const newSegment: Segment = {
      id: segments.length + 1,
      segmentCode: `SEG${(segments.length + 1).toString().padStart(3, '0')}`,
      timeIn: '00:00:00',
      timeOut: '',
      duration: '00:00:00'
    };
    setSegments([...segments, newSegment]);
  };

  const removeSegment = (id: number) => {
    if (segments.length > 1) {
      setSegments(segments.filter(seg => seg.id !== id));
    }
  };

  const updateSegment = (id: number, field: keyof Segment, value: string) => {
    setSegments(segments.map(seg =>
      seg.id === id ? { ...seg, [field]: value } : seg
    ));
  };

  return (
    <AuthGuard requiredPermissions={['MEDIA_UPDATE']}>
      <DashboardLayout title="تحديث مادة إعلامية" subtitle="تعديل بيانات المادة الإعلامية" icon="✏️">
        <form onSubmit={handleSubmit} style={{ maxWidth: '800px', margin: '0 auto' }}>
          {/* معلومات أساسية */}
          <div style={{
            background: '#4a5568',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #6b7280'
          }}>
            <h2 style={{ color: '#f3f4f6', marginBottom: '20px', fontSize: '1.3rem' }}>
              📝 المعلومات الأساسية
            </h2>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  اسم المادة *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  نوع المادة *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({...formData, type: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                >
                  <option value="">اختر نوع المادة</option>
                  <option value="PROGRAM">برنامج</option>
                  <option value="SERIES">مسلسل</option>
                  <option value="MOVIE">فيلم</option>
                  <option value="SONG">أغنية</option>
                  <option value="STING">Sting</option>
                  <option value="FILL_IN">Fill IN</option>
                  <option value="FILLER">Filler</option>
                  <option value="PROMO">Promo</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  الوصف
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white',
                    minHeight: '80px',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  القناة *
                </label>
                <select
                  value={formData.channel}
                  onChange={(e) => setFormData({...formData, channel: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                >
                  <option value="">اختر القناة</option>
                  <option value="DOCUMENTARY">الوثائقية</option>
                  <option value="NEWS">قطاع الأخبار</option>
                  <option value="OTHER">أخرى</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  المصدر
                </label>
                <input
                  type="text"
                  value={formData.source}
                  onChange={(e) => setFormData({...formData, source: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  الحالة *
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({...formData, status: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                >
                  <option value="">اختر الحالة</option>
                  <option value="VALID">صالح</option>
                  <option value="REJECTED_CENSORSHIP">مرفوض رقابياً</option>
                  <option value="REJECTED_TECHNICAL">مرفوض هندسياً</option>
                  <option value="PENDING">في الانتظار</option>
                  <option value="EXPIRED">منتهي</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  تاريخ البداية
                </label>
                <input
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  تاريخ النهاية
                </label>
                <input
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => setFormData({...formData, endDate: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  رقم الحلقة
                </label>
                <input
                  type="number"
                  value={formData.episodeNumber}
                  onChange={(e) => setFormData({...formData, episodeNumber: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  رقم الموسم
                </label>
                <input
                  type="number"
                  value={formData.seasonNumber}
                  onChange={(e) => setFormData({...formData, seasonNumber: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  رقم الجزء
                </label>
                <input
                  type="number"
                  value={formData.partNumber}
                  onChange={(e) => setFormData({...formData, partNumber: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  رقم الهارد ديسك *
                </label>
                <input
                  type="text"
                  value={formData.hardDiskNumber}
                  onChange={(e) => setFormData({...formData, hardDiskNumber: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div style={{ gridColumn: '1 / -1' }}>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  ملاحظات
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white',
                    minHeight: '100px',
                    resize: 'vertical'
                  }}
                />
              </div>
            </div>
          </div>

          {/* السيجمانت */}
          <div style={{
            background: '#4a5568',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #6b7280'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ color: '#f3f4f6', fontSize: '1.3rem', margin: 0 }}>
                🎬 السيجمانت
              </h2>
              <button
                type="button"
                onClick={addSegment}
                style={{
                  background: 'linear-gradient(45deg, #10b981, #059669)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '0.9rem'
                }}
              >
                ➕ إضافة سيجمنت
              </button>
            </div>

            {segments.map((segment, index) => (
              <div key={segment.id} style={{
                background: '#1f2937',
                borderRadius: '10px',
                padding: '20px',
                marginBottom: '15px',
                border: '1px solid #6b7280'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                  <h3 style={{ color: '#f3f4f6', margin: 0 }}>سيجمنت #{segment.id}</h3>
                  {segments.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeSegment(segment.id)}
                      style={{
                        background: '#ef4444',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '6px 12px',
                        cursor: 'pointer',
                        fontSize: '0.8rem'
                      }}
                    >
                      🗑️ حذف
                    </button>
                  )}
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#d1d5db', fontSize: '0.9rem' }}>
                      كود السيجمنت
                    </label>
                    <input
                      type="text"
                      value={segment.segmentCode}
                      onChange={(e) => updateSegment(segment.id, 'segmentCode', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #6b7280',
                        borderRadius: '6px',
                        fontSize: '0.9rem',
                        direction: 'rtl',
                        color: '#333',
                        background: 'white'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#d1d5db', fontSize: '0.9rem' }}>
                      وقت البداية
                    </label>
                    <input
                      type="text"
                      placeholder="00:00:00"
                      value={segment.timeIn}
                      onChange={(e) => updateSegment(segment.id, 'timeIn', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #6b7280',
                        borderRadius: '6px',
                        fontSize: '0.9rem',
                        direction: 'ltr',
                        color: '#333',
                        background: 'white'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#d1d5db', fontSize: '0.9rem' }}>
                      وقت النهاية
                    </label>
                    <input
                      type="text"
                      placeholder="00:00:00"
                      value={segment.timeOut}
                      onChange={(e) => updateSegment(segment.id, 'timeOut', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #6b7280',
                        borderRadius: '6px',
                        fontSize: '0.9rem',
                        direction: 'ltr',
                        color: '#333',
                        background: 'white'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#d1d5db', fontSize: '0.9rem' }}>
                      المدة
                    </label>
                    <input
                      type="text"
                      placeholder="00:00:00"
                      value={segment.duration}
                      onChange={(e) => updateSegment(segment.id, 'duration', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #6b7280',
                        borderRadius: '6px',
                        fontSize: '0.9rem',
                        direction: 'ltr',
                        color: '#333',
                        background: 'white'
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* أزرار الحفظ */}
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
            <button
              type="button"
              onClick={() => router.push('/media-list')}
              style={{
                background: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '12px 30px',
                fontSize: '1rem',
                cursor: 'pointer'
              }}
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                background: isSubmitting
                  ? '#6c757d'
                  : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '12px 30px',
                fontSize: '1rem',
                cursor: isSubmitting ? 'not-allowed' : 'pointer'
              }}
            >
              {isSubmitting ? '⏳ جاري الحفظ...' : '💾 حفظ التعديلات'}
            </button>
          </div>
        </form>
        <ToastContainer />
      </DashboardLayout>
    </AuthGuard>
  );
}
