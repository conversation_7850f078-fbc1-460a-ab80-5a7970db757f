'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useToast } from '@/components/Toast';

export default function EditMediaPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const mediaId = searchParams.get('id');
  const { showToast, ToastContainer } = useToast();
  
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    type: 'PROGRAM',
    description: '',
    channel: 'DOCUMENTARY',
    source: '',
    status: 'WAITING',
    startDate: '',
    endDate: '',
    notes: '',
    episodeNumber: '',
    seasonNumber: '',
    partNumber: '',
    hardDiskNumber: 'SERVER',
  });

  const [segmentCount, setSegmentCount] = useState(1);
  const [segments, setSegments] = useState([
    {
      id: 1,
      segmentCode: 'SEG001',
      timeIn: '00:00:00',
      timeOut: '00:00:00',
      duration: '00:00:00'
    }
  ]);

  // تحميل بيانات المادة للتعديل
  useEffect(() => {
    if (!mediaId) {
      router.push('/media-list');
      return;
    }

    const fetchMediaItem = async () => {
      try {
        console.log('📡 جاري تحميل بيانات المادة:', mediaId);
        const response = await fetch(`/api/media?id=${mediaId}`);
        const result = await response.json();

        if (result.success && result.data) {
          const item = result.data;
          console.log('📋 البيانات المستلمة:', item);

          setFormData({
            name: item.name || '',
            type: item.type || 'PROGRAM',
            description: item.description || '',
            channel: item.channel || 'DOCUMENTARY',
            source: item.source || '',
            status: item.status || 'WAITING',
            startDate: item.startDate ? item.startDate.split('T')[0] : '',
            endDate: item.endDate ? item.endDate.split('T')[0] : '',
            notes: item.notes || '',
            episodeNumber: item.episodeNumber?.toString() || '',
            seasonNumber: item.seasonNumber?.toString() || '',
            partNumber: item.partNumber?.toString() || '',
            hardDiskNumber: item.hardDiskNumber || 'SERVER',
          });

          console.log('✅ تم تعيين بيانات النموذج بنجاح');

          if (item.segments && item.segments.length > 0) {
            const loadedSegments = item.segments.map((seg: any, index: number) => ({
              id: index + 1,
              segmentCode: seg.code || seg.segmentCode || `SEG${String(index + 1).padStart(3, '0')}`,
              timeIn: seg.timeIn || '00:00:00',
              timeOut: seg.timeOut || '00:00:00',
              duration: seg.duration || '00:00:00'
            }));
            setSegments(loadedSegments);
            setSegmentCount(item.segments.length);

            console.log('🎬 تم تحميل السيجمانت:', loadedSegments);
          }
        } else {
          console.error('❌ فشل في تحميل البيانات:', result);
          showToast('فشل في تحميل بيانات المادة', 'error');
          router.push('/media-list');
        }
      } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);
        showToast('خطأ في تحميل البيانات', 'error');
        router.push('/media-list');
      } finally {
        setLoading(false);
      }
    };

    fetchMediaItem();
  }, [mediaId]); // تحميل البيانات مرة واحدة فقط عند تغيير mediaId

  const handleInputChange = (field: string, value: string) => {
    console.log(`🔄 تغيير ${field}:`, value);
    setFormData(prev => {
      const updated = {
        ...prev,
        [field]: value
      };
      console.log('📝 حالة النموذج الجديدة:', updated);
      return updated;
    });
  };

  const handleSegmentChange = (segmentId: number, field: string, value: string) => {
    setSegments(prev => prev.map(segment => 
      segment.id === segmentId 
        ? { ...segment, [field]: value }
        : segment
    ));
  };

  const addSegment = () => {
    console.log('🔄 محاولة إضافة سيجمانت جديد...');
    const newSegmentCount = segmentCount + 1;
    setSegmentCount(newSegmentCount);
    const newSegment = {
      id: newSegmentCount,
      segmentCode: `SEG${String(newSegmentCount).padStart(3, '0')}`,
      timeIn: '00:00:00',
      timeOut: '00:00:00',
      duration: '00:00:00'
    };
    setSegments(prev => {
      const updated = [...prev, newSegment];
      console.log('➕ تم إضافة سيجمانت جديد. العدد الجديد:', updated.length);
      return updated;
    });
  };

  const removeSegment = (segmentId: number) => {
    if (segments.length > 1) {
      setSegments(prev => prev.filter(segment => segment.id !== segmentId));
      console.log('🗑️ تم حذف السيجمانت:', segmentId);
    } else {
      alert('لا يمكن حذف السيجمانت الوحيد المتبقي');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      showToast('يرجى إدخال اسم المادة', 'error');
      return;
    }

    try {
      const response = await fetch(`/api/media?id=${mediaId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData,
          segments
        }),
      });

      const result = await response.json();

      if (result.success) {
        showToast('تم تحديث المادة الإعلامية بنجاح!', 'success');
        router.push('/media-list');
      } else {
        showToast('خطأ: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Error updating media item:', error);
      showToast('خطأ في الاتصال بالخادم', 'error');
    }
  };

  const inputStyle = {
    width: '100%',
    padding: '12px',
    border: '2px solid #e0e0e0',
    borderRadius: '8px',
    fontSize: '1rem',
    transition: 'border-color 0.3s',
    direction: 'rtl' as const
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '40px',
          textAlign: 'center',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}>
          <h2>⏳ جاري تحميل البيانات...</h2>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        background: 'white',
        borderRadius: '20px',
        padding: '40px',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '30px' }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            color: '#333',
            margin: 0,
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            ✏️ تعديل المادة الإعلامية
          </h1>
          <div style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={() => router.push('/media-list')}
              style={{
                background: 'linear-gradient(45deg, #6c757d, #495057)',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '10px 20px',
                cursor: 'pointer',
                fontSize: '0.9rem'
              }}
            >
              🔙 العودة للقائمة
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div style={{
            background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #dee2e6'
          }}>
            <h2 style={{ color: '#495057', marginBottom: '20px', fontSize: '1.3rem' }}>📝 المعلومات الأساسية</h2>

            <div style={{ display: 'grid', gap: '15px' }}>
              {/* حقل رقم الهارد */}
              <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr', gap: '15px', alignItems: 'center' }}>
                <label style={{ color: '#495057', fontWeight: 'bold', fontSize: '0.9rem' }}>
                  💾 رقم الهارد:
                </label>
                <input
                  type="text"
                  placeholder="رقم الهارد"
                  value={formData.hardDiskNumber}
                  onChange={(e) => handleInputChange('hardDiskNumber', e.target.value)}
                  style={{
                    ...inputStyle,
                    maxWidth: '200px'
                  }}
                />
              </div>

              <input
                type="text"
                placeholder="اسم المادة *"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                style={inputStyle}
                required
              />

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <select
                  value={formData.type}
                  onChange={(e) => handleInputChange('type', e.target.value)}
                  style={inputStyle}
                >
                  <option value="PROGRAM">برنامج</option>
                  <option value="SERIES">مسلسل</option>
                  <option value="MOVIE">فيلم</option>
                  <option value="SONG">أغنية</option>
                  <option value="STING">Sting</option>
                  <option value="FILL_IN">Fill IN</option>
                  <option value="FILLER">Filler</option>
                  <option value="PROMO">Promo</option>
                </select>

                <select
                  value={formData.channel}
                  onChange={(e) => handleInputChange('channel', e.target.value)}
                  style={inputStyle}
                >
                  <option value="DOCUMENTARY">الوثائقية</option>
                  <option value="NEWS">الأخبار</option>
                  <option value="OTHER">أخرى</option>
                </select>
              </div>

              <textarea
                placeholder="وصف المادة"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                style={{
                  ...inputStyle,
                  minHeight: '80px',
                  resize: 'vertical'
                }}
              />

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <input
                  type="text"
                  placeholder="المصدر"
                  value={formData.source}
                  onChange={(e) => handleInputChange('source', e.target.value)}
                  style={inputStyle}
                />

                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  style={inputStyle}
                >
                  <option value="WAITING">في الانتظار</option>
                  <option value="VALID">صالح</option>
                  <option value="APPROVED">مقبول</option>
                  <option value="REJECTED">مرفوض</option>
                  <option value="EXPIRED">منتهي الصلاحية</option>
                </select>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#495057', fontSize: '0.9rem' }}>
                    تاريخ البداية:
                  </label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                    style={inputStyle}
                  />
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#495057', fontSize: '0.9rem' }}>
                    تاريخ النهاية:
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                    style={inputStyle}
                  />
                </div>
              </div>

              {/* الحقول الخاصة بنوع المادة */}
              {(formData.type === 'SERIES' || formData.type === 'PROGRAM') && (
                <div style={{ display: 'grid', gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr', gap: '15px' }}>
                  <input
                    type="number"
                    placeholder="رقم الحلقة"
                    value={formData.episodeNumber}
                    onChange={(e) => handleInputChange('episodeNumber', e.target.value)}
                    style={inputStyle}
                  />
                  {formData.type === 'SERIES' ? (
                    <input
                      type="number"
                      placeholder="رقم الجزء"
                      value={formData.partNumber}
                      onChange={(e) => handleInputChange('partNumber', e.target.value)}
                      style={inputStyle}
                    />
                  ) : (
                    <input
                      type="number"
                      placeholder="رقم الموسم"
                      value={formData.seasonNumber}
                      onChange={(e) => handleInputChange('seasonNumber', e.target.value)}
                      style={inputStyle}
                    />
                  )}
                </div>
              )}

              {formData.type === 'MOVIE' && (
                <input
                  type="number"
                  placeholder="رقم الجزء"
                  value={formData.partNumber}
                  onChange={(e) => handleInputChange('partNumber', e.target.value)}
                  style={inputStyle}
                />
              )}

              <textarea
                placeholder="ملاحظات إضافية"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                style={{
                  ...inputStyle,
                  minHeight: '60px',
                  resize: 'vertical'
                }}
              />
            </div>
          </div>

          {/* قسم السيجمانت */}
          <div style={{
            background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #90caf9'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ color: '#1976d2', fontSize: '1.3rem', margin: 0 }}>🎬 إدارة السيجمانت</h2>
              <button
                type="button"
                onClick={addSegment}
                style={{
                  background: 'linear-gradient(45deg, #4caf50, #45a049)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '0.9rem'
                }}
              >
                ➕ إضافة سيجمانت
              </button>
            </div>

            {segments.map((segment) => (
              <div key={segment.id} style={{
                background: 'white',
                borderRadius: '10px',
                padding: '20px',
                marginBottom: '15px',
                border: '1px solid #e0e0e0'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                  <h3 style={{ color: '#333', margin: 0 }}>سيجمانت {segment.id}</h3>
                  {segments.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeSegment(segment.id)}
                      style={{
                        background: '#ff4444',
                        color: 'white',
                        border: 'none',
                        borderRadius: '15px',
                        padding: '5px 10px',
                        cursor: 'pointer',
                        fontSize: '0.8rem'
                      }}
                    >
                      ❌ حذف
                    </button>
                  )}
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr', gap: '15px' }}>
                  <input
                    type="text"
                    placeholder="كود السيجمانت"
                    value={segment.segmentCode}
                    onChange={(e) => handleSegmentChange(segment.id, 'segmentCode', e.target.value)}
                    style={inputStyle}
                  />
                  <input
                    type="text"
                    placeholder="وقت البداية (00:00:00)"
                    value={segment.timeIn}
                    onChange={(e) => handleSegmentChange(segment.id, 'timeIn', e.target.value)}
                    style={inputStyle}
                  />
                  <input
                    type="text"
                    placeholder="وقت النهاية (00:00:00)"
                    value={segment.timeOut}
                    onChange={(e) => handleSegmentChange(segment.id, 'timeOut', e.target.value)}
                    style={inputStyle}
                  />
                  <input
                    type="text"
                    placeholder="المدة (00:00:00)"
                    value={segment.duration}
                    onChange={(e) => handleSegmentChange(segment.id, 'duration', e.target.value)}
                    style={inputStyle}
                  />
                </div>
              </div>
            ))}
          </div>

          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
            <button
              type="submit"
              style={{
                background: 'linear-gradient(45deg, #28a745, #20c997)',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                padding: '15px 40px',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                boxShadow: '0 4px 15px rgba(40,167,69,0.3)',
              }}
            >
              ✅ حفظ التعديلات
            </button>

            <button
              type="button"
              onClick={() => router.push('/media-list')}
              style={{
                background: 'linear-gradient(45deg, #6c757d, #495057)',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                padding: '15px 40px',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                boxShadow: '0 4px 15px rgba(108,117,125,0.3)',
              }}
            >
              ❌ إلغاء
            </button>
          </div>
        </form>
      </div>
      <ToastContainer />
    </div>
  );
}
