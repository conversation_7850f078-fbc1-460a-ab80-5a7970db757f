'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AuthGuard } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useToast } from '@/components/Toast';

interface Segment {
  id: number;
  segmentCode: string;
  timeIn: string;
  timeOut: string;
  duration: string;
}

export default function EditMediaPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const mediaId = searchParams.get('id');
  const { showToast, ToastContainer } = useToast();

  const [formData, setFormData] = useState({
    name: '',
    type: '',
    description: '',
    channel: '',
    source: '',
    status: '',
    startDate: '',
    endDate: '',
    notes: '',
    episodeNumber: '',
    seasonNumber: '',
    partNumber: '',
    hardDiskNumber: '',
  });

  const [segments, setSegments] = useState<Segment[]>([
    {
      id: 1,
      segmentCode: 'SEG001',
      timeIn: '00:00:00',
      timeOut: '',
      duration: '00:00:00'
    }
  ]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  // جلب بيانات المادة للتعديل
  useEffect(() => {
    if (mediaId) {
      fetchMediaData();
    } else {
      router.push('/media-list');
    }
  }, [mediaId]);

  const fetchMediaData = async () => {
    try {
      const response = await fetch(`/api/media?id=${mediaId}`);
      const result = await response.json();

      if (result.success && result.data) {
        const media = result.data;
        setFormData({
          name: media.name || '',
          type: media.type || '',
          description: media.description || '',
          channel: media.channel || '',
          source: media.source || '',
          status: media.status || '',
          startDate: media.startDate ? media.startDate.split('T')[0] : '',
          endDate: media.endDate ? media.endDate.split('T')[0] : '',
          notes: media.notes || '',
          episodeNumber: media.episodeNumber?.toString() || '',
          seasonNumber: media.seasonNumber?.toString() || '',
          partNumber: media.partNumber?.toString() || '',
          hardDiskNumber: media.hardDiskNumber || '',
        });

        if (media.segments && media.segments.length > 0) {
          setSegments(media.segments.map((seg: any, index: number) => ({
            id: index + 1,
            segmentCode: seg.segmentCode || `SEG${(index + 1).toString().padStart(3, '0')}`,
            timeIn: seg.timeIn || '00:00:00',
            timeOut: seg.timeOut || '',
            duration: seg.duration || '00:00:00'
          })));
        }
      } else {
        showToast('خطأ في جلب بيانات المادة', 'error');
        router.push('/media-list');
      }
    } catch (error) {
      console.error('Error fetching media:', error);
      showToast('خطأ في الاتصال بالخادم', 'error');
      router.push('/media-list');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AuthGuard requiredPermissions={['MEDIA_UPDATE']}>
        <DashboardLayout title="تحديث مادة إعلامية" subtitle="جاري تحميل البيانات..." icon="✏️">
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <div style={{ fontSize: '2rem', marginBottom: '20px' }}>⏳</div>
            <div style={{ color: '#a0aec0' }}>جاري تحميل بيانات المادة...</div>
          </div>
        </DashboardLayout>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard requiredPermissions={['MEDIA_UPDATE']}>
      <DashboardLayout title="تحديث مادة إعلامية" subtitle="تعديل بيانات المادة الإعلامية" icon="✏️">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <div style={{ fontSize: '2rem', marginBottom: '20px' }}>🚧</div>
          <div style={{ color: '#a0aec0' }}>صفحة التعديل قيد التطوير...</div>
          <button
            onClick={() => router.push('/media-list')}
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '10px 20px',
              cursor: 'pointer',
              marginTop: '20px'
            }}
          >
            العودة للقائمة
          </button>
        </div>
        <ToastContainer />
      </DashboardLayout>
    </AuthGuard>
  );
}
