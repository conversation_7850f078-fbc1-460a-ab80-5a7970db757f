/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/readdir-glob";
exports.ids = ["vendor-chunks/readdir-glob"];
exports.modules = {

/***/ "(rsc)/./node_modules/readdir-glob/index.js":
/*!********************************************!*\
  !*** ./node_modules/readdir-glob/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = readdirGlob;\n\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst { EventEmitter } = __webpack_require__(/*! events */ \"events\");\nconst { Minimatch } = __webpack_require__(/*! minimatch */ \"(rsc)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js\");\nconst { resolve } = __webpack_require__(/*! path */ \"path\");\n\nfunction readdir(dir, strict) {\n  return new Promise((resolve, reject) => {\n    fs.readdir(dir, {withFileTypes: true} ,(err, files) => {\n      if(err) {\n        switch (err.code) {\n          case 'ENOTDIR':      // Not a directory\n            if(strict) {\n              reject(err);\n            } else {\n              resolve([]);\n            }\n            break;\n          case 'ENOTSUP':      // Operation not supported\n          case 'ENOENT':       // No such file or directory\n          case 'ENAMETOOLONG': // Filename too long\n          case 'UNKNOWN':\n            resolve([]);\n            break;\n          case 'ELOOP':        // Too many levels of symbolic links\n          default:\n            reject(err);\n            break;\n        }\n      } else {\n        resolve(files);\n      }\n    });\n  });\n}\nfunction stat(file, followSymlinks) {\n  return new Promise((resolve, reject) => {\n    const statFunc = followSymlinks ? fs.stat : fs.lstat;\n    statFunc(file, (err, stats) => {\n      if(err) {\n        switch (err.code) {\n          case 'ENOENT':\n            if(followSymlinks) {\n              // Fallback to lstat to handle broken links as files\n              resolve(stat(file, false)); \n            } else {\n              resolve(null);\n            }\n            break;\n          default:\n            resolve(null);\n            break;\n        }\n      } else {\n        resolve(stats);\n      }\n    });\n  });\n}\n\nasync function* exploreWalkAsync(dir, path, followSymlinks, useStat, shouldSkip, strict) {\n  let files = await readdir(path + dir, strict);\n  for(const file of files) {\n    let name = file.name;\n    if(name === undefined) {\n      // undefined file.name means the `withFileTypes` options is not supported by node\n      // we have to call the stat function to know if file is directory or not.\n      name = file;\n      useStat = true;\n    }\n    const filename = dir + '/' + name;\n    const relative = filename.slice(1); // Remove the leading /\n    const absolute = path + '/' + relative;\n    let stats = null;\n    if(useStat || followSymlinks) {\n      stats = await stat(absolute, followSymlinks);\n    }\n    if(!stats && file.name !== undefined) {\n      stats = file;\n    }\n    if(stats === null) {\n      stats = { isDirectory: () => false };\n    }\n\n    if(stats.isDirectory()) {\n      if(!shouldSkip(relative)) {\n        yield {relative, absolute, stats};\n        yield* exploreWalkAsync(filename, path, followSymlinks, useStat, shouldSkip, false);\n      }\n    } else {\n      yield {relative, absolute, stats};\n    }\n  }\n}\nasync function* explore(path, followSymlinks, useStat, shouldSkip) {\n  yield* exploreWalkAsync('', path, followSymlinks, useStat, shouldSkip, true);\n}\n\n\nfunction readOptions(options) {\n  return {\n    pattern: options.pattern,\n    dot: !!options.dot,\n    noglobstar: !!options.noglobstar,\n    matchBase: !!options.matchBase,\n    nocase: !!options.nocase,\n    ignore: options.ignore,\n    skip: options.skip,\n\n    follow: !!options.follow,\n    stat: !!options.stat,\n    nodir: !!options.nodir,\n    mark: !!options.mark,\n    silent: !!options.silent,\n    absolute: !!options.absolute\n  };\n}\n\nclass ReaddirGlob extends EventEmitter {\n  constructor(cwd, options, cb) {\n    super();\n    if(typeof options === 'function') {\n      cb = options;\n      options = null;\n    }\n\n    this.options = readOptions(options || {});\n  \n    this.matchers = [];\n    if(this.options.pattern) {\n      const matchers = Array.isArray(this.options.pattern) ? this.options.pattern : [this.options.pattern];\n      this.matchers = matchers.map( m =>\n        new Minimatch(m, {\n          dot: this.options.dot,\n          noglobstar:this.options.noglobstar,\n          matchBase:this.options.matchBase,\n          nocase:this.options.nocase\n        })\n      );\n    }\n  \n    this.ignoreMatchers = [];\n    if(this.options.ignore) {\n      const ignorePatterns = Array.isArray(this.options.ignore) ? this.options.ignore : [this.options.ignore];\n      this.ignoreMatchers = ignorePatterns.map( ignore =>\n        new Minimatch(ignore, {dot: true})\n      );\n    }\n  \n    this.skipMatchers = [];\n    if(this.options.skip) {\n      const skipPatterns = Array.isArray(this.options.skip) ? this.options.skip : [this.options.skip];\n      this.skipMatchers = skipPatterns.map( skip =>\n        new Minimatch(skip, {dot: true})\n      );\n    }\n\n    this.iterator = explore(resolve(cwd || '.'), this.options.follow, this.options.stat, this._shouldSkipDirectory.bind(this));\n    this.paused = false;\n    this.inactive = false;\n    this.aborted = false;\n  \n    if(cb) {\n      this._matches = []; \n      this.on('match', match => this._matches.push(this.options.absolute ? match.absolute : match.relative));\n      this.on('error', err => cb(err));\n      this.on('end', () => cb(null, this._matches));\n    }\n\n    setTimeout( () => this._next(), 0);\n  }\n\n  _shouldSkipDirectory(relative) {\n    //console.log(relative, this.skipMatchers.some(m => m.match(relative)));\n    return this.skipMatchers.some(m => m.match(relative));\n  }\n\n  _fileMatches(relative, isDirectory) {\n    const file = relative + (isDirectory ? '/' : '');\n    return (this.matchers.length === 0 || this.matchers.some(m => m.match(file)))\n      && !this.ignoreMatchers.some(m => m.match(file))\n      && (!this.options.nodir || !isDirectory);\n  }\n\n  _next() {\n    if(!this.paused && !this.aborted) {\n      this.iterator.next()\n      .then((obj)=> {\n        if(!obj.done) {\n          const isDirectory = obj.value.stats.isDirectory();\n          if(this._fileMatches(obj.value.relative, isDirectory )) {\n            let relative = obj.value.relative;\n            let absolute = obj.value.absolute;\n            if(this.options.mark && isDirectory) {\n              relative += '/';\n              absolute += '/';\n            }\n            if(this.options.stat) {\n              this.emit('match', {relative, absolute, stat:obj.value.stats});\n            } else {\n              this.emit('match', {relative, absolute});\n            }\n          }\n          this._next(this.iterator);\n        } else {\n          this.emit('end');\n        }\n      })\n      .catch((err) => {\n        this.abort();\n        this.emit('error', err);\n        if(!err.code && !this.options.silent) {\n          console.error(err);\n        }\n      });\n    } else {\n      this.inactive = true;\n    }\n  }\n\n  abort() {\n    this.aborted = true;\n  }\n\n  pause() {\n    this.paused = true;\n  }\n\n  resume() {\n    this.paused = false;\n    if(this.inactive) {\n      this.inactive = false;\n      this._next();\n    }\n  }\n}\n\n\nfunction readdirGlob(pattern, options, cb) {\n  return new ReaddirGlob(pattern, options, cb);\n}\nreaddirGlob.ReaddirGlob = ReaddirGlob;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/readdir-glob/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/brace-expansion/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var balanced = __webpack_require__(/*! balanced-match */ \"(rsc)/./node_modules/balanced-match/index.js\");\n\nmodule.exports = expandTop;\n\nvar escSlash = '\\0SLASH'+Math.random()+'\\0';\nvar escOpen = '\\0OPEN'+Math.random()+'\\0';\nvar escClose = '\\0CLOSE'+Math.random()+'\\0';\nvar escComma = '\\0COMMA'+Math.random()+'\\0';\nvar escPeriod = '\\0PERIOD'+Math.random()+'\\0';\n\nfunction numeric(str) {\n  return parseInt(str, 10) == str\n    ? parseInt(str, 10)\n    : str.charCodeAt(0);\n}\n\nfunction escapeBraces(str) {\n  return str.split('\\\\\\\\').join(escSlash)\n            .split('\\\\{').join(escOpen)\n            .split('\\\\}').join(escClose)\n            .split('\\\\,').join(escComma)\n            .split('\\\\.').join(escPeriod);\n}\n\nfunction unescapeBraces(str) {\n  return str.split(escSlash).join('\\\\')\n            .split(escOpen).join('{')\n            .split(escClose).join('}')\n            .split(escComma).join(',')\n            .split(escPeriod).join('.');\n}\n\n\n// Basically just str.split(\",\"), but handling cases\n// where we have nested braced sections, which should be\n// treated as individual members, like {a,{b,c},d}\nfunction parseCommaParts(str) {\n  if (!str)\n    return [''];\n\n  var parts = [];\n  var m = balanced('{', '}', str);\n\n  if (!m)\n    return str.split(',');\n\n  var pre = m.pre;\n  var body = m.body;\n  var post = m.post;\n  var p = pre.split(',');\n\n  p[p.length-1] += '{' + body + '}';\n  var postParts = parseCommaParts(post);\n  if (post.length) {\n    p[p.length-1] += postParts.shift();\n    p.push.apply(p, postParts);\n  }\n\n  parts.push.apply(parts, p);\n\n  return parts;\n}\n\nfunction expandTop(str) {\n  if (!str)\n    return [];\n\n  // I don't know why Bash 4.3 does this, but it does.\n  // Anything starting with {} will have the first two bytes preserved\n  // but *only* at the top level, so {},a}b will not expand to anything,\n  // but a{},b}c will be expanded to [a}c,abc].\n  // One could argue that this is a bug in Bash, but since the goal of\n  // this module is to match Bash's rules, we escape a leading {}\n  if (str.substr(0, 2) === '{}') {\n    str = '\\\\{\\\\}' + str.substr(2);\n  }\n\n  return expand(escapeBraces(str), true).map(unescapeBraces);\n}\n\nfunction embrace(str) {\n  return '{' + str + '}';\n}\nfunction isPadded(el) {\n  return /^-?0\\d/.test(el);\n}\n\nfunction lte(i, y) {\n  return i <= y;\n}\nfunction gte(i, y) {\n  return i >= y;\n}\n\nfunction expand(str, isTop) {\n  var expansions = [];\n\n  var m = balanced('{', '}', str);\n  if (!m) return [str];\n\n  // no need to expand pre, since it is guaranteed to be free of brace-sets\n  var pre = m.pre;\n  var post = m.post.length\n    ? expand(m.post, false)\n    : [''];\n\n  if (/\\$$/.test(m.pre)) {    \n    for (var k = 0; k < post.length; k++) {\n      var expansion = pre+ '{' + m.body + '}' + post[k];\n      expansions.push(expansion);\n    }\n  } else {\n    var isNumericSequence = /^-?\\d+\\.\\.-?\\d+(?:\\.\\.-?\\d+)?$/.test(m.body);\n    var isAlphaSequence = /^[a-zA-Z]\\.\\.[a-zA-Z](?:\\.\\.-?\\d+)?$/.test(m.body);\n    var isSequence = isNumericSequence || isAlphaSequence;\n    var isOptions = m.body.indexOf(',') >= 0;\n    if (!isSequence && !isOptions) {\n      // {a},b}\n      if (m.post.match(/,(?!,).*\\}/)) {\n        str = m.pre + '{' + m.body + escClose + m.post;\n        return expand(str);\n      }\n      return [str];\n    }\n\n    var n;\n    if (isSequence) {\n      n = m.body.split(/\\.\\./);\n    } else {\n      n = parseCommaParts(m.body);\n      if (n.length === 1) {\n        // x{{a,b}}y ==> x{a}y x{b}y\n        n = expand(n[0], false).map(embrace);\n        if (n.length === 1) {\n          return post.map(function(p) {\n            return m.pre + n[0] + p;\n          });\n        }\n      }\n    }\n\n    // at this point, n is the parts, and we know it's not a comma set\n    // with a single entry.\n    var N;\n\n    if (isSequence) {\n      var x = numeric(n[0]);\n      var y = numeric(n[1]);\n      var width = Math.max(n[0].length, n[1].length)\n      var incr = n.length == 3\n        ? Math.abs(numeric(n[2]))\n        : 1;\n      var test = lte;\n      var reverse = y < x;\n      if (reverse) {\n        incr *= -1;\n        test = gte;\n      }\n      var pad = n.some(isPadded);\n\n      N = [];\n\n      for (var i = x; test(i, y); i += incr) {\n        var c;\n        if (isAlphaSequence) {\n          c = String.fromCharCode(i);\n          if (c === '\\\\')\n            c = '';\n        } else {\n          c = String(i);\n          if (pad) {\n            var need = width - c.length;\n            if (need > 0) {\n              var z = new Array(need + 1).join('0');\n              if (i < 0)\n                c = '-' + z + c.slice(1);\n              else\n                c = z + c;\n            }\n          }\n        }\n        N.push(c);\n      }\n    } else {\n      N = [];\n\n      for (var j = 0; j < n.length; j++) {\n        N.push.apply(N, expand(n[j], false));\n      }\n    }\n\n    for (var j = 0; j < N.length; j++) {\n      for (var k = 0; k < post.length; k++) {\n        var expansion = pre + N[j] + post[k];\n        if (!isTop || isSequence || expansion)\n          expansions.push(expansion);\n      }\n    }\n  }\n\n  return expansions;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js":
/*!**********************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/minimatch/lib/path.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("const isWindows = typeof process === 'object' &&\n  process &&\n  process.platform === 'win32'\nmodule.exports = isWindows ? { sep: '\\\\' } : { sep: '/' }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVhZGRpci1nbG9iL25vZGVfbW9kdWxlcy9taW5pbWF0Y2gvbGliL3BhdGguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLFlBQVksSUFBSSIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhZGRpci1nbG9iXFxub2RlX21vZHVsZXNcXG1pbmltYXRjaFxcbGliXFxwYXRoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGlzV2luZG93cyA9IHR5cGVvZiBwcm9jZXNzID09PSAnb2JqZWN0JyAmJlxuICBwcm9jZXNzICYmXG4gIHByb2Nlc3MucGxhdGZvcm0gPT09ICd3aW4zMidcbm1vZHVsZS5leHBvcnRzID0gaXNXaW5kb3dzID8geyBzZXA6ICdcXFxcJyB9IDogeyBzZXA6ICcvJyB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js":
/*!***********************************************************************!*\
  !*** ./node_modules/readdir-glob/node_modules/minimatch/minimatch.js ***!
  \***********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("const minimatch = module.exports = (p, pattern, options = {}) => {\n  assertValidPattern(pattern)\n\n  // shortcut: comments match nothing.\n  if (!options.nocomment && pattern.charAt(0) === '#') {\n    return false\n  }\n\n  return new Minimatch(pattern, options).match(p)\n}\n\nmodule.exports = minimatch\n\nconst path = __webpack_require__(/*! ./lib/path.js */ \"(rsc)/./node_modules/readdir-glob/node_modules/minimatch/lib/path.js\")\nminimatch.sep = path.sep\n\nconst GLOBSTAR = Symbol('globstar **')\nminimatch.GLOBSTAR = GLOBSTAR\nconst expand = __webpack_require__(/*! brace-expansion */ \"(rsc)/./node_modules/readdir-glob/node_modules/brace-expansion/index.js\")\n\nconst plTypes = {\n  '!': { open: '(?:(?!(?:', close: '))[^/]*?)'},\n  '?': { open: '(?:', close: ')?' },\n  '+': { open: '(?:', close: ')+' },\n  '*': { open: '(?:', close: ')*' },\n  '@': { open: '(?:', close: ')' }\n}\n\n// any single thing other than /\n// don't need to escape / when using new RegExp()\nconst qmark = '[^/]'\n\n// * => any number of characters\nconst star = qmark + '*?'\n\n// ** when dots are allowed.  Anything goes, except .. and .\n// not (^ or / followed by one or two dots followed by $ or /),\n// followed by anything, any number of times.\nconst twoStarDot = '(?:(?!(?:\\\\\\/|^)(?:\\\\.{1,2})($|\\\\\\/)).)*?'\n\n// not a ^ or / followed by a dot,\n// followed by anything, any number of times.\nconst twoStarNoDot = '(?:(?!(?:\\\\\\/|^)\\\\.).)*?'\n\n// \"abc\" -> { a:true, b:true, c:true }\nconst charSet = s => s.split('').reduce((set, c) => {\n  set[c] = true\n  return set\n}, {})\n\n// characters that need to be escaped in RegExp.\nconst reSpecials = charSet('().*{}+?[]^$\\\\!')\n\n// characters that indicate we have to add the pattern start\nconst addPatternStartSet = charSet('[.(')\n\n// normalizes slashes.\nconst slashSplit = /\\/+/\n\nminimatch.filter = (pattern, options = {}) =>\n  (p, i, list) => minimatch(p, pattern, options)\n\nconst ext = (a, b = {}) => {\n  const t = {}\n  Object.keys(a).forEach(k => t[k] = a[k])\n  Object.keys(b).forEach(k => t[k] = b[k])\n  return t\n}\n\nminimatch.defaults = def => {\n  if (!def || typeof def !== 'object' || !Object.keys(def).length) {\n    return minimatch\n  }\n\n  const orig = minimatch\n\n  const m = (p, pattern, options) => orig(p, pattern, ext(def, options))\n  m.Minimatch = class Minimatch extends orig.Minimatch {\n    constructor (pattern, options) {\n      super(pattern, ext(def, options))\n    }\n  }\n  m.Minimatch.defaults = options => orig.defaults(ext(def, options)).Minimatch\n  m.filter = (pattern, options) => orig.filter(pattern, ext(def, options))\n  m.defaults = options => orig.defaults(ext(def, options))\n  m.makeRe = (pattern, options) => orig.makeRe(pattern, ext(def, options))\n  m.braceExpand = (pattern, options) => orig.braceExpand(pattern, ext(def, options))\n  m.match = (list, pattern, options) => orig.match(list, pattern, ext(def, options))\n\n  return m\n}\n\n\n\n\n\n// Brace expansion:\n// a{b,c}d -> abd acd\n// a{b,}c -> abc ac\n// a{0..3}d -> a0d a1d a2d a3d\n// a{b,c{d,e}f}g -> abg acdfg acefg\n// a{b,c}d{e,f}g -> abdeg acdeg abdeg abdfg\n//\n// Invalid sets are not expanded.\n// a{2..}b -> a{2..}b\n// a{b}c -> a{b}c\nminimatch.braceExpand = (pattern, options) => braceExpand(pattern, options)\n\nconst braceExpand = (pattern, options = {}) => {\n  assertValidPattern(pattern)\n\n  // Thanks to Yeting Li <https://github.com/yetingli> for\n  // improving this regexp to avoid a ReDOS vulnerability.\n  if (options.nobrace || !/\\{(?:(?!\\{).)*\\}/.test(pattern)) {\n    // shortcut. no need to expand.\n    return [pattern]\n  }\n\n  return expand(pattern)\n}\n\nconst MAX_PATTERN_LENGTH = 1024 * 64\nconst assertValidPattern = pattern => {\n  if (typeof pattern !== 'string') {\n    throw new TypeError('invalid pattern')\n  }\n\n  if (pattern.length > MAX_PATTERN_LENGTH) {\n    throw new TypeError('pattern is too long')\n  }\n}\n\n// parse a component of the expanded set.\n// At this point, no pattern may contain \"/\" in it\n// so we're going to return a 2d array, where each entry is the full\n// pattern, split on '/', and then turned into a regular expression.\n// A regexp is made at the end which joins each array with an\n// escaped /, and another full one which joins each regexp with |.\n//\n// Following the lead of Bash 4.1, note that \"**\" only has special meaning\n// when it is the *only* thing in a path portion.  Otherwise, any series\n// of * is equivalent to a single *.  Globstar behavior is enabled by\n// default, and can be disabled by setting options.noglobstar.\nconst SUBPARSE = Symbol('subparse')\n\nminimatch.makeRe = (pattern, options) =>\n  new Minimatch(pattern, options || {}).makeRe()\n\nminimatch.match = (list, pattern, options = {}) => {\n  const mm = new Minimatch(pattern, options)\n  list = list.filter(f => mm.match(f))\n  if (mm.options.nonull && !list.length) {\n    list.push(pattern)\n  }\n  return list\n}\n\n// replace stuff like \\* with *\nconst globUnescape = s => s.replace(/\\\\(.)/g, '$1')\nconst charUnescape = s => s.replace(/\\\\([^-\\]])/g, '$1')\nconst regExpEscape = s => s.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&')\nconst braExpEscape = s => s.replace(/[[\\]\\\\]/g, '\\\\$&')\n\nclass Minimatch {\n  constructor (pattern, options) {\n    assertValidPattern(pattern)\n\n    if (!options) options = {}\n\n    this.options = options\n    this.set = []\n    this.pattern = pattern\n    this.windowsPathsNoEscape = !!options.windowsPathsNoEscape ||\n      options.allowWindowsEscape === false\n    if (this.windowsPathsNoEscape) {\n      this.pattern = this.pattern.replace(/\\\\/g, '/')\n    }\n    this.regexp = null\n    this.negate = false\n    this.comment = false\n    this.empty = false\n    this.partial = !!options.partial\n\n    // make the set of regexps etc.\n    this.make()\n  }\n\n  debug () {}\n\n  make () {\n    const pattern = this.pattern\n    const options = this.options\n\n    // empty patterns and comments match nothing.\n    if (!options.nocomment && pattern.charAt(0) === '#') {\n      this.comment = true\n      return\n    }\n    if (!pattern) {\n      this.empty = true\n      return\n    }\n\n    // step 1: figure out negation, etc.\n    this.parseNegate()\n\n    // step 2: expand braces\n    let set = this.globSet = this.braceExpand()\n\n    if (options.debug) this.debug = (...args) => console.error(...args)\n\n    this.debug(this.pattern, set)\n\n    // step 3: now we have a set, so turn each one into a series of path-portion\n    // matching patterns.\n    // These will be regexps, except in the case of \"**\", which is\n    // set to the GLOBSTAR object for globstar behavior,\n    // and will not contain any / characters\n    set = this.globParts = set.map(s => s.split(slashSplit))\n\n    this.debug(this.pattern, set)\n\n    // glob --> regexps\n    set = set.map((s, si, set) => s.map(this.parse, this))\n\n    this.debug(this.pattern, set)\n\n    // filter out everything that didn't compile properly.\n    set = set.filter(s => s.indexOf(false) === -1)\n\n    this.debug(this.pattern, set)\n\n    this.set = set\n  }\n\n  parseNegate () {\n    if (this.options.nonegate) return\n\n    const pattern = this.pattern\n    let negate = false\n    let negateOffset = 0\n\n    for (let i = 0; i < pattern.length && pattern.charAt(i) === '!'; i++) {\n      negate = !negate\n      negateOffset++\n    }\n\n    if (negateOffset) this.pattern = pattern.slice(negateOffset)\n    this.negate = negate\n  }\n\n  // set partial to true to test if, for example,\n  // \"/a/b\" matches the start of \"/*/b/*/d\"\n  // Partial means, if you run out of file before you run\n  // out of pattern, then that's fine, as long as all\n  // the parts match.\n  matchOne (file, pattern, partial) {\n    var options = this.options\n\n    this.debug('matchOne',\n      { 'this': this, file: file, pattern: pattern })\n\n    this.debug('matchOne', file.length, pattern.length)\n\n    for (var fi = 0,\n        pi = 0,\n        fl = file.length,\n        pl = pattern.length\n        ; (fi < fl) && (pi < pl)\n        ; fi++, pi++) {\n      this.debug('matchOne loop')\n      var p = pattern[pi]\n      var f = file[fi]\n\n      this.debug(pattern, p, f)\n\n      // should be impossible.\n      // some invalid regexp stuff in the set.\n      /* istanbul ignore if */\n      if (p === false) return false\n\n      if (p === GLOBSTAR) {\n        this.debug('GLOBSTAR', [pattern, p, f])\n\n        // \"**\"\n        // a/**/b/**/c would match the following:\n        // a/b/x/y/z/c\n        // a/x/y/z/b/c\n        // a/b/x/b/x/c\n        // a/b/c\n        // To do this, take the rest of the pattern after\n        // the **, and see if it would match the file remainder.\n        // If so, return success.\n        // If not, the ** \"swallows\" a segment, and try again.\n        // This is recursively awful.\n        //\n        // a/**/b/**/c matching a/b/x/y/z/c\n        // - a matches a\n        // - doublestar\n        //   - matchOne(b/x/y/z/c, b/**/c)\n        //     - b matches b\n        //     - doublestar\n        //       - matchOne(x/y/z/c, c) -> no\n        //       - matchOne(y/z/c, c) -> no\n        //       - matchOne(z/c, c) -> no\n        //       - matchOne(c, c) yes, hit\n        var fr = fi\n        var pr = pi + 1\n        if (pr === pl) {\n          this.debug('** at the end')\n          // a ** at the end will just swallow the rest.\n          // We have found a match.\n          // however, it will not swallow /.x, unless\n          // options.dot is set.\n          // . and .. are *never* matched by **, for explosively\n          // exponential reasons.\n          for (; fi < fl; fi++) {\n            if (file[fi] === '.' || file[fi] === '..' ||\n              (!options.dot && file[fi].charAt(0) === '.')) return false\n          }\n          return true\n        }\n\n        // ok, let's see if we can swallow whatever we can.\n        while (fr < fl) {\n          var swallowee = file[fr]\n\n          this.debug('\\nglobstar while', file, fr, pattern, pr, swallowee)\n\n          // XXX remove this slice.  Just pass the start index.\n          if (this.matchOne(file.slice(fr), pattern.slice(pr), partial)) {\n            this.debug('globstar found match!', fr, fl, swallowee)\n            // found a match.\n            return true\n          } else {\n            // can't swallow \".\" or \"..\" ever.\n            // can only swallow \".foo\" when explicitly asked.\n            if (swallowee === '.' || swallowee === '..' ||\n              (!options.dot && swallowee.charAt(0) === '.')) {\n              this.debug('dot detected!', file, fr, pattern, pr)\n              break\n            }\n\n            // ** swallows a segment, and continue.\n            this.debug('globstar swallow a segment, and continue')\n            fr++\n          }\n        }\n\n        // no match was found.\n        // However, in partial mode, we can't say this is necessarily over.\n        // If there's more *pattern* left, then\n        /* istanbul ignore if */\n        if (partial) {\n          // ran out of file\n          this.debug('\\n>>> no match, partial?', file, fr, pattern, pr)\n          if (fr === fl) return true\n        }\n        return false\n      }\n\n      // something other than **\n      // non-magic patterns just have to match exactly\n      // patterns with magic have been turned into regexps.\n      var hit\n      if (typeof p === 'string') {\n        hit = f === p\n        this.debug('string match', p, f, hit)\n      } else {\n        hit = f.match(p)\n        this.debug('pattern match', p, f, hit)\n      }\n\n      if (!hit) return false\n    }\n\n    // Note: ending in / means that we'll get a final \"\"\n    // at the end of the pattern.  This can only match a\n    // corresponding \"\" at the end of the file.\n    // If the file ends in /, then it can only match a\n    // a pattern that ends in /, unless the pattern just\n    // doesn't have any more for it. But, a/b/ should *not*\n    // match \"a/b/*\", even though \"\" matches against the\n    // [^/]*? pattern, except in partial mode, where it might\n    // simply not be reached yet.\n    // However, a/b/ should still satisfy a/*\n\n    // now either we fell off the end of the pattern, or we're done.\n    if (fi === fl && pi === pl) {\n      // ran out of pattern and filename at the same time.\n      // an exact hit!\n      return true\n    } else if (fi === fl) {\n      // ran out of file, but still had pattern left.\n      // this is ok if we're doing the match as part of\n      // a glob fs traversal.\n      return partial\n    } else /* istanbul ignore else */ if (pi === pl) {\n      // ran out of pattern, still have file left.\n      // this is only acceptable if we're on the very last\n      // empty segment of a file with a trailing slash.\n      // a/* should match a/b/\n      return (fi === fl - 1) && (file[fi] === '')\n    }\n\n    // should be unreachable.\n    /* istanbul ignore next */\n    throw new Error('wtf?')\n  }\n\n  braceExpand () {\n    return braceExpand(this.pattern, this.options)\n  }\n\n  parse (pattern, isSub) {\n    assertValidPattern(pattern)\n\n    const options = this.options\n\n    // shortcuts\n    if (pattern === '**') {\n      if (!options.noglobstar)\n        return GLOBSTAR\n      else\n        pattern = '*'\n    }\n    if (pattern === '') return ''\n\n    let re = ''\n    let hasMagic = false\n    let escaping = false\n    // ? => one single character\n    const patternListStack = []\n    const negativeLists = []\n    let stateChar\n    let inClass = false\n    let reClassStart = -1\n    let classStart = -1\n    let cs\n    let pl\n    let sp\n    // . and .. never match anything that doesn't start with .,\n    // even when options.dot is set.  However, if the pattern\n    // starts with ., then traversal patterns can match.\n    let dotTravAllowed = pattern.charAt(0) === '.'\n    let dotFileAllowed = options.dot || dotTravAllowed\n    const patternStart = () =>\n      dotTravAllowed\n        ? ''\n        : dotFileAllowed\n        ? '(?!(?:^|\\\\/)\\\\.{1,2}(?:$|\\\\/))'\n        : '(?!\\\\.)'\n    const subPatternStart = (p) =>\n      p.charAt(0) === '.'\n        ? ''\n        : options.dot\n        ? '(?!(?:^|\\\\/)\\\\.{1,2}(?:$|\\\\/))'\n        : '(?!\\\\.)'\n\n\n    const clearStateChar = () => {\n      if (stateChar) {\n        // we had some state-tracking character\n        // that wasn't consumed by this pass.\n        switch (stateChar) {\n          case '*':\n            re += star\n            hasMagic = true\n          break\n          case '?':\n            re += qmark\n            hasMagic = true\n          break\n          default:\n            re += '\\\\' + stateChar\n          break\n        }\n        this.debug('clearStateChar %j %j', stateChar, re)\n        stateChar = false\n      }\n    }\n\n    for (let i = 0, c; (i < pattern.length) && (c = pattern.charAt(i)); i++) {\n      this.debug('%s\\t%s %s %j', pattern, i, re, c)\n\n      // skip over any that are escaped.\n      if (escaping) {\n        /* istanbul ignore next - completely not allowed, even escaped. */\n        if (c === '/') {\n          return false\n        }\n\n        if (reSpecials[c]) {\n          re += '\\\\'\n        }\n        re += c\n        escaping = false\n        continue\n      }\n\n      switch (c) {\n        /* istanbul ignore next */\n        case '/': {\n          // Should already be path-split by now.\n          return false\n        }\n\n        case '\\\\':\n          if (inClass && pattern.charAt(i + 1) === '-') {\n            re += c\n            continue\n          }\n\n          clearStateChar()\n          escaping = true\n        continue\n\n        // the various stateChar values\n        // for the \"extglob\" stuff.\n        case '?':\n        case '*':\n        case '+':\n        case '@':\n        case '!':\n          this.debug('%s\\t%s %s %j <-- stateChar', pattern, i, re, c)\n\n          // all of those are literals inside a class, except that\n          // the glob [!a] means [^a] in regexp\n          if (inClass) {\n            this.debug('  in class')\n            if (c === '!' && i === classStart + 1) c = '^'\n            re += c\n            continue\n          }\n\n          // if we already have a stateChar, then it means\n          // that there was something like ** or +? in there.\n          // Handle the stateChar, then proceed with this one.\n          this.debug('call clearStateChar %j', stateChar)\n          clearStateChar()\n          stateChar = c\n          // if extglob is disabled, then +(asdf|foo) isn't a thing.\n          // just clear the statechar *now*, rather than even diving into\n          // the patternList stuff.\n          if (options.noext) clearStateChar()\n        continue\n\n        case '(': {\n          if (inClass) {\n            re += '('\n            continue\n          }\n\n          if (!stateChar) {\n            re += '\\\\('\n            continue\n          }\n\n          const plEntry = {\n            type: stateChar,\n            start: i - 1,\n            reStart: re.length,\n            open: plTypes[stateChar].open,\n            close: plTypes[stateChar].close,\n          }\n          this.debug(this.pattern, '\\t', plEntry)\n          patternListStack.push(plEntry)\n          // negation is (?:(?!(?:js)(?:<rest>))[^/]*)\n          re += plEntry.open\n          // next entry starts with a dot maybe?\n          if (plEntry.start === 0 && plEntry.type !== '!') {\n            dotTravAllowed = true\n            re += subPatternStart(pattern.slice(i + 1))\n          }\n          this.debug('plType %j %j', stateChar, re)\n          stateChar = false\n          continue\n        }\n\n        case ')': {\n          const plEntry = patternListStack[patternListStack.length - 1]\n          if (inClass || !plEntry) {\n            re += '\\\\)'\n            continue\n          }\n          patternListStack.pop()\n\n          // closing an extglob\n          clearStateChar()\n          hasMagic = true\n          pl = plEntry\n          // negation is (?:(?!js)[^/]*)\n          // The others are (?:<pattern>)<type>\n          re += pl.close\n          if (pl.type === '!') {\n            negativeLists.push(Object.assign(pl, { reEnd: re.length }))\n          }\n          continue\n        }\n\n        case '|': {\n          const plEntry = patternListStack[patternListStack.length - 1]\n          if (inClass || !plEntry) {\n            re += '\\\\|'\n            continue\n          }\n\n          clearStateChar()\n          re += '|'\n          // next subpattern can start with a dot?\n          if (plEntry.start === 0 && plEntry.type !== '!') {\n            dotTravAllowed = true\n            re += subPatternStart(pattern.slice(i + 1))\n          }\n          continue\n        }\n\n        // these are mostly the same in regexp and glob\n        case '[':\n          // swallow any state-tracking char before the [\n          clearStateChar()\n\n          if (inClass) {\n            re += '\\\\' + c\n            continue\n          }\n\n          inClass = true\n          classStart = i\n          reClassStart = re.length\n          re += c\n        continue\n\n        case ']':\n          //  a right bracket shall lose its special\n          //  meaning and represent itself in\n          //  a bracket expression if it occurs\n          //  first in the list.  -- POSIX.2 2.8.3.2\n          if (i === classStart + 1 || !inClass) {\n            re += '\\\\' + c\n            continue\n          }\n\n          // split where the last [ was, make sure we don't have\n          // an invalid re. if so, re-walk the contents of the\n          // would-be class to re-translate any characters that\n          // were passed through as-is\n          // TODO: It would probably be faster to determine this\n          // without a try/catch and a new RegExp, but it's tricky\n          // to do safely.  For now, this is safe and works.\n          cs = pattern.substring(classStart + 1, i)\n          try {\n            RegExp('[' + braExpEscape(charUnescape(cs)) + ']')\n            // looks good, finish up the class.\n            re += c\n          } catch (er) {\n            // out of order ranges in JS are errors, but in glob syntax,\n            // they're just a range that matches nothing.\n            re = re.substring(0, reClassStart) + '(?:$.)' // match nothing ever\n          }\n          hasMagic = true\n          inClass = false\n        continue\n\n        default:\n          // swallow any state char that wasn't consumed\n          clearStateChar()\n\n          if (reSpecials[c] && !(c === '^' && inClass)) {\n            re += '\\\\'\n          }\n\n          re += c\n          break\n\n      } // switch\n    } // for\n\n    // handle the case where we left a class open.\n    // \"[abc\" is valid, equivalent to \"\\[abc\"\n    if (inClass) {\n      // split where the last [ was, and escape it\n      // this is a huge pita.  We now have to re-walk\n      // the contents of the would-be class to re-translate\n      // any characters that were passed through as-is\n      cs = pattern.slice(classStart + 1)\n      sp = this.parse(cs, SUBPARSE)\n      re = re.substring(0, reClassStart) + '\\\\[' + sp[0]\n      hasMagic = hasMagic || sp[1]\n    }\n\n    // handle the case where we had a +( thing at the *end*\n    // of the pattern.\n    // each pattern list stack adds 3 chars, and we need to go through\n    // and escape any | chars that were passed through as-is for the regexp.\n    // Go through and escape them, taking care not to double-escape any\n    // | chars that were already escaped.\n    for (pl = patternListStack.pop(); pl; pl = patternListStack.pop()) {\n      let tail\n      tail = re.slice(pl.reStart + pl.open.length)\n      this.debug('setting tail', re, pl)\n      // maybe some even number of \\, then maybe 1 \\, followed by a |\n      tail = tail.replace(/((?:\\\\{2}){0,64})(\\\\?)\\|/g, (_, $1, $2) => {\n        /* istanbul ignore else - should already be done */\n        if (!$2) {\n          // the | isn't already escaped, so escape it.\n          $2 = '\\\\'\n        }\n\n        // need to escape all those slashes *again*, without escaping the\n        // one that we need for escaping the | character.  As it works out,\n        // escaping an even number of slashes can be done by simply repeating\n        // it exactly after itself.  That's why this trick works.\n        //\n        // I am sorry that you have to see this.\n        return $1 + $1 + $2 + '|'\n      })\n\n      this.debug('tail=%j\\n   %s', tail, tail, pl, re)\n      const t = pl.type === '*' ? star\n        : pl.type === '?' ? qmark\n        : '\\\\' + pl.type\n\n      hasMagic = true\n      re = re.slice(0, pl.reStart) + t + '\\\\(' + tail\n    }\n\n    // handle trailing things that only matter at the very end.\n    clearStateChar()\n    if (escaping) {\n      // trailing \\\\\n      re += '\\\\\\\\'\n    }\n\n    // only need to apply the nodot start if the re starts with\n    // something that could conceivably capture a dot\n    const addPatternStart = addPatternStartSet[re.charAt(0)]\n\n    // Hack to work around lack of negative lookbehind in JS\n    // A pattern like: *.!(x).!(y|z) needs to ensure that a name\n    // like 'a.xyz.yz' doesn't match.  So, the first negative\n    // lookahead, has to look ALL the way ahead, to the end of\n    // the pattern.\n    for (let n = negativeLists.length - 1; n > -1; n--) {\n      const nl = negativeLists[n]\n\n      const nlBefore = re.slice(0, nl.reStart)\n      const nlFirst = re.slice(nl.reStart, nl.reEnd - 8)\n      let nlAfter = re.slice(nl.reEnd)\n      const nlLast = re.slice(nl.reEnd - 8, nl.reEnd) + nlAfter\n\n      // Handle nested stuff like *(*.js|!(*.json)), where open parens\n      // mean that we should *not* include the ) in the bit that is considered\n      // \"after\" the negated section.\n      const closeParensBefore = nlBefore.split(')').length\n      const openParensBefore = nlBefore.split('(').length - closeParensBefore\n      let cleanAfter = nlAfter\n      for (let i = 0; i < openParensBefore; i++) {\n        cleanAfter = cleanAfter.replace(/\\)[+*?]?/, '')\n      }\n      nlAfter = cleanAfter\n\n      const dollar = nlAfter === '' && isSub !== SUBPARSE ? '(?:$|\\\\/)' : ''\n\n      re = nlBefore + nlFirst + nlAfter + dollar + nlLast\n    }\n\n    // if the re is not \"\" at this point, then we need to make sure\n    // it doesn't match against an empty path part.\n    // Otherwise a/* will match a/, which it should not.\n    if (re !== '' && hasMagic) {\n      re = '(?=.)' + re\n    }\n\n    if (addPatternStart) {\n      re = patternStart() + re\n    }\n\n    // parsing just a piece of a larger pattern.\n    if (isSub === SUBPARSE) {\n      return [re, hasMagic]\n    }\n\n    // if it's nocase, and the lcase/uppercase don't match, it's magic\n    if (options.nocase && !hasMagic) {\n      hasMagic = pattern.toUpperCase() !== pattern.toLowerCase()\n    }\n\n    // skip the regexp for non-magical patterns\n    // unescape anything in it, though, so that it'll be\n    // an exact match against a file etc.\n    if (!hasMagic) {\n      return globUnescape(pattern)\n    }\n\n    const flags = options.nocase ? 'i' : ''\n    try {\n      return Object.assign(new RegExp('^' + re + '$', flags), {\n        _glob: pattern,\n        _src: re,\n      })\n    } catch (er) /* istanbul ignore next - should be impossible */ {\n      // If it was an invalid regular expression, then it can't match\n      // anything.  This trick looks for a character after the end of\n      // the string, which is of course impossible, except in multi-line\n      // mode, but it's not a /m regex.\n      return new RegExp('$.')\n    }\n  }\n\n  makeRe () {\n    if (this.regexp || this.regexp === false) return this.regexp\n\n    // at this point, this.set is a 2d array of partial\n    // pattern strings, or \"**\".\n    //\n    // It's better to use .match().  This function shouldn't\n    // be used, really, but it's pretty convenient sometimes,\n    // when you just want to work with a regex.\n    const set = this.set\n\n    if (!set.length) {\n      this.regexp = false\n      return this.regexp\n    }\n    const options = this.options\n\n    const twoStar = options.noglobstar ? star\n      : options.dot ? twoStarDot\n      : twoStarNoDot\n    const flags = options.nocase ? 'i' : ''\n\n    // coalesce globstars and regexpify non-globstar patterns\n    // if it's the only item, then we just do one twoStar\n    // if it's the first, and there are more, prepend (\\/|twoStar\\/)? to next\n    // if it's the last, append (\\/twoStar|) to previous\n    // if it's in the middle, append (\\/|\\/twoStar\\/) to previous\n    // then filter out GLOBSTAR symbols\n    let re = set.map(pattern => {\n      pattern = pattern.map(p =>\n        typeof p === 'string' ? regExpEscape(p)\n        : p === GLOBSTAR ? GLOBSTAR\n        : p._src\n      ).reduce((set, p) => {\n        if (!(set[set.length - 1] === GLOBSTAR && p === GLOBSTAR)) {\n          set.push(p)\n        }\n        return set\n      }, [])\n      pattern.forEach((p, i) => {\n        if (p !== GLOBSTAR || pattern[i-1] === GLOBSTAR) {\n          return\n        }\n        if (i === 0) {\n          if (pattern.length > 1) {\n            pattern[i+1] = '(?:\\\\\\/|' + twoStar + '\\\\\\/)?' + pattern[i+1]\n          } else {\n            pattern[i] = twoStar\n          }\n        } else if (i === pattern.length - 1) {\n          pattern[i-1] += '(?:\\\\\\/|' + twoStar + ')?'\n        } else {\n          pattern[i-1] += '(?:\\\\\\/|\\\\\\/' + twoStar + '\\\\\\/)' + pattern[i+1]\n          pattern[i+1] = GLOBSTAR\n        }\n      })\n      return pattern.filter(p => p !== GLOBSTAR).join('/')\n    }).join('|')\n\n    // must match entire pattern\n    // ending in a * or ** will make it less strict.\n    re = '^(?:' + re + ')$'\n\n    // can match anything, as long as it's not this.\n    if (this.negate) re = '^(?!' + re + ').*$'\n\n    try {\n      this.regexp = new RegExp(re, flags)\n    } catch (ex) /* istanbul ignore next - should be impossible */ {\n      this.regexp = false\n    }\n    return this.regexp\n  }\n\n  match (f, partial = this.partial) {\n    this.debug('match', f, this.pattern)\n    // short-circuit in the case of busted things.\n    // comments, etc.\n    if (this.comment) return false\n    if (this.empty) return f === ''\n\n    if (f === '/' && partial) return true\n\n    const options = this.options\n\n    // windows: need to use /, not \\\n    if (path.sep !== '/') {\n      f = f.split(path.sep).join('/')\n    }\n\n    // treat the test path as a set of pathparts.\n    f = f.split(slashSplit)\n    this.debug(this.pattern, 'split', f)\n\n    // just ONE of the pattern sets in this.set needs to match\n    // in order for it to be valid.  If negating, then just one\n    // match means that we have failed.\n    // Either way, return on the first hit.\n\n    const set = this.set\n    this.debug(this.pattern, 'set', set)\n\n    // Find the basename of the path by looking for the last non-empty segment\n    let filename\n    for (let i = f.length - 1; i >= 0; i--) {\n      filename = f[i]\n      if (filename) break\n    }\n\n    for (let i = 0; i < set.length; i++) {\n      const pattern = set[i]\n      let file = f\n      if (options.matchBase && pattern.length === 1) {\n        file = [filename]\n      }\n      const hit = this.matchOne(file, pattern, partial)\n      if (hit) {\n        if (options.flipNegate) return true\n        return !this.negate\n      }\n    }\n\n    // didn't get any hits.  this is success if it's a negative\n    // pattern, failure otherwise.\n    if (options.flipNegate) return false\n    return this.negate\n  }\n\n  static defaults (def) {\n    return minimatch.defaults(def).Minimatch\n  }\n}\n\nminimatch.Minimatch = Minimatch\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvcmVhZGRpci1nbG9iL25vZGVfbW9kdWxlcy9taW5pbWF0Y2gvbWluaW1hdGNoLmpzIiwibWFwcGluZ3MiOiJBQUFBLDREQUE0RDtBQUM1RDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBLGFBQWEsbUJBQU8sQ0FBQywyRkFBZTtBQUNwQzs7QUFFQTtBQUNBO0FBQ0EsZUFBZSxtQkFBTyxDQUFDLGdHQUFpQjs7QUFFeEM7QUFDQSxTQUFTLHNDQUFzQztBQUMvQyxTQUFTLDBCQUEwQjtBQUNuQyxTQUFTLDBCQUEwQjtBQUNuQyxTQUFTLDBCQUEwQjtBQUNuQyxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsMkNBQTJDLElBQUk7O0FBRS9DO0FBQ0E7QUFDQTs7QUFFQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxJQUFJOztBQUVMO0FBQ0Esa0NBQWtDOztBQUVsQztBQUNBOztBQUVBO0FBQ0E7O0FBRUEseUNBQXlDO0FBQ3pDOztBQUVBLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOzs7Ozs7QUFNQTtBQUNBLEtBQUssSUFBSTtBQUNULEtBQUssR0FBRztBQUNSLEtBQUssS0FBSztBQUNWLEtBQUssSUFBSSxJQUFJLEVBQUU7QUFDZixLQUFLLElBQUksRUFBRSxJQUFJO0FBQ2Y7QUFDQTtBQUNBLEtBQUssSUFBSSxPQUFPLElBQUk7QUFDcEIsS0FBSyxFQUFFLE9BQU8sRUFBRTtBQUNoQjs7QUFFQSwwQ0FBMEM7QUFDMUM7O0FBRUE7QUFDQTtBQUNBLDZCQUE2QixRQUFRLE1BQU07QUFDM0M7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxzQ0FBc0M7O0FBRXRDLDhDQUE4QztBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEM7QUFDNUM7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsb0JBQW9CLGlEQUFpRDtBQUNyRTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsUUFBUSw0Q0FBNEM7O0FBRXBEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLFNBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixJQUFJO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsSUFBSTtBQUMvQjs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHVCQUF1QixpREFBaUQ7QUFDeEU7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCxrQkFBa0I7QUFDckU7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxRQUFRO0FBQ1IsTUFBTTs7QUFFTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLElBQUk7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsRUFBRSxFQUFFLEtBQUs7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87O0FBRVA7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyxRQUFRO0FBQ25EOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixzQkFBc0I7QUFDNUM7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsK0JBQStCLFFBQVE7QUFDdkM7QUFDQTtBQUNBOztBQUVBLG9CQUFvQixnQkFBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJEOlxcRG9jIGRhdGFiYXNlXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFkZGlyLWdsb2JcXG5vZGVfbW9kdWxlc1xcbWluaW1hdGNoXFxtaW5pbWF0Y2guanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgbWluaW1hdGNoID0gbW9kdWxlLmV4cG9ydHMgPSAocCwgcGF0dGVybiwgb3B0aW9ucyA9IHt9KSA9PiB7XG4gIGFzc2VydFZhbGlkUGF0dGVybihwYXR0ZXJuKVxuXG4gIC8vIHNob3J0Y3V0OiBjb21tZW50cyBtYXRjaCBub3RoaW5nLlxuICBpZiAoIW9wdGlvbnMubm9jb21tZW50ICYmIHBhdHRlcm4uY2hhckF0KDApID09PSAnIycpIHtcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxuXG4gIHJldHVybiBuZXcgTWluaW1hdGNoKHBhdHRlcm4sIG9wdGlvbnMpLm1hdGNoKHApXG59XG5cbm1vZHVsZS5leHBvcnRzID0gbWluaW1hdGNoXG5cbmNvbnN0IHBhdGggPSByZXF1aXJlKCcuL2xpYi9wYXRoLmpzJylcbm1pbmltYXRjaC5zZXAgPSBwYXRoLnNlcFxuXG5jb25zdCBHTE9CU1RBUiA9IFN5bWJvbCgnZ2xvYnN0YXIgKionKVxubWluaW1hdGNoLkdMT0JTVEFSID0gR0xPQlNUQVJcbmNvbnN0IGV4cGFuZCA9IHJlcXVpcmUoJ2JyYWNlLWV4cGFuc2lvbicpXG5cbmNvbnN0IHBsVHlwZXMgPSB7XG4gICchJzogeyBvcGVuOiAnKD86KD8hKD86JywgY2xvc2U6ICcpKVteL10qPyknfSxcbiAgJz8nOiB7IG9wZW46ICcoPzonLCBjbG9zZTogJyk/JyB9LFxuICAnKyc6IHsgb3BlbjogJyg/OicsIGNsb3NlOiAnKSsnIH0sXG4gICcqJzogeyBvcGVuOiAnKD86JywgY2xvc2U6ICcpKicgfSxcbiAgJ0AnOiB7IG9wZW46ICcoPzonLCBjbG9zZTogJyknIH1cbn1cblxuLy8gYW55IHNpbmdsZSB0aGluZyBvdGhlciB0aGFuIC9cbi8vIGRvbid0IG5lZWQgdG8gZXNjYXBlIC8gd2hlbiB1c2luZyBuZXcgUmVnRXhwKClcbmNvbnN0IHFtYXJrID0gJ1teL10nXG5cbi8vICogPT4gYW55IG51bWJlciBvZiBjaGFyYWN0ZXJzXG5jb25zdCBzdGFyID0gcW1hcmsgKyAnKj8nXG5cbi8vICoqIHdoZW4gZG90cyBhcmUgYWxsb3dlZC4gIEFueXRoaW5nIGdvZXMsIGV4Y2VwdCAuLiBhbmQgLlxuLy8gbm90ICheIG9yIC8gZm9sbG93ZWQgYnkgb25lIG9yIHR3byBkb3RzIGZvbGxvd2VkIGJ5ICQgb3IgLyksXG4vLyBmb2xsb3dlZCBieSBhbnl0aGluZywgYW55IG51bWJlciBvZiB0aW1lcy5cbmNvbnN0IHR3b1N0YXJEb3QgPSAnKD86KD8hKD86XFxcXFxcL3xeKSg/OlxcXFwuezEsMn0pKCR8XFxcXFxcLykpLikqPydcblxuLy8gbm90IGEgXiBvciAvIGZvbGxvd2VkIGJ5IGEgZG90LFxuLy8gZm9sbG93ZWQgYnkgYW55dGhpbmcsIGFueSBudW1iZXIgb2YgdGltZXMuXG5jb25zdCB0d29TdGFyTm9Eb3QgPSAnKD86KD8hKD86XFxcXFxcL3xeKVxcXFwuKS4pKj8nXG5cbi8vIFwiYWJjXCIgLT4geyBhOnRydWUsIGI6dHJ1ZSwgYzp0cnVlIH1cbmNvbnN0IGNoYXJTZXQgPSBzID0+IHMuc3BsaXQoJycpLnJlZHVjZSgoc2V0LCBjKSA9PiB7XG4gIHNldFtjXSA9IHRydWVcbiAgcmV0dXJuIHNldFxufSwge30pXG5cbi8vIGNoYXJhY3RlcnMgdGhhdCBuZWVkIHRvIGJlIGVzY2FwZWQgaW4gUmVnRXhwLlxuY29uc3QgcmVTcGVjaWFscyA9IGNoYXJTZXQoJygpLip7fSs/W11eJFxcXFwhJylcblxuLy8gY2hhcmFjdGVycyB0aGF0IGluZGljYXRlIHdlIGhhdmUgdG8gYWRkIHRoZSBwYXR0ZXJuIHN0YXJ0XG5jb25zdCBhZGRQYXR0ZXJuU3RhcnRTZXQgPSBjaGFyU2V0KCdbLignKVxuXG4vLyBub3JtYWxpemVzIHNsYXNoZXMuXG5jb25zdCBzbGFzaFNwbGl0ID0gL1xcLysvXG5cbm1pbmltYXRjaC5maWx0ZXIgPSAocGF0dGVybiwgb3B0aW9ucyA9IHt9KSA9PlxuICAocCwgaSwgbGlzdCkgPT4gbWluaW1hdGNoKHAsIHBhdHRlcm4sIG9wdGlvbnMpXG5cbmNvbnN0IGV4dCA9IChhLCBiID0ge30pID0+IHtcbiAgY29uc3QgdCA9IHt9XG4gIE9iamVjdC5rZXlzKGEpLmZvckVhY2goayA9PiB0W2tdID0gYVtrXSlcbiAgT2JqZWN0LmtleXMoYikuZm9yRWFjaChrID0+IHRba10gPSBiW2tdKVxuICByZXR1cm4gdFxufVxuXG5taW5pbWF0Y2guZGVmYXVsdHMgPSBkZWYgPT4ge1xuICBpZiAoIWRlZiB8fCB0eXBlb2YgZGVmICE9PSAnb2JqZWN0JyB8fCAhT2JqZWN0LmtleXMoZGVmKS5sZW5ndGgpIHtcbiAgICByZXR1cm4gbWluaW1hdGNoXG4gIH1cblxuICBjb25zdCBvcmlnID0gbWluaW1hdGNoXG5cbiAgY29uc3QgbSA9IChwLCBwYXR0ZXJuLCBvcHRpb25zKSA9PiBvcmlnKHAsIHBhdHRlcm4sIGV4dChkZWYsIG9wdGlvbnMpKVxuICBtLk1pbmltYXRjaCA9IGNsYXNzIE1pbmltYXRjaCBleHRlbmRzIG9yaWcuTWluaW1hdGNoIHtcbiAgICBjb25zdHJ1Y3RvciAocGF0dGVybiwgb3B0aW9ucykge1xuICAgICAgc3VwZXIocGF0dGVybiwgZXh0KGRlZiwgb3B0aW9ucykpXG4gICAgfVxuICB9XG4gIG0uTWluaW1hdGNoLmRlZmF1bHRzID0gb3B0aW9ucyA9PiBvcmlnLmRlZmF1bHRzKGV4dChkZWYsIG9wdGlvbnMpKS5NaW5pbWF0Y2hcbiAgbS5maWx0ZXIgPSAocGF0dGVybiwgb3B0aW9ucykgPT4gb3JpZy5maWx0ZXIocGF0dGVybiwgZXh0KGRlZiwgb3B0aW9ucykpXG4gIG0uZGVmYXVsdHMgPSBvcHRpb25zID0+IG9yaWcuZGVmYXVsdHMoZXh0KGRlZiwgb3B0aW9ucykpXG4gIG0ubWFrZVJlID0gKHBhdHRlcm4sIG9wdGlvbnMpID0+IG9yaWcubWFrZVJlKHBhdHRlcm4sIGV4dChkZWYsIG9wdGlvbnMpKVxuICBtLmJyYWNlRXhwYW5kID0gKHBhdHRlcm4sIG9wdGlvbnMpID0+IG9yaWcuYnJhY2VFeHBhbmQocGF0dGVybiwgZXh0KGRlZiwgb3B0aW9ucykpXG4gIG0ubWF0Y2ggPSAobGlzdCwgcGF0dGVybiwgb3B0aW9ucykgPT4gb3JpZy5tYXRjaChsaXN0LCBwYXR0ZXJuLCBleHQoZGVmLCBvcHRpb25zKSlcblxuICByZXR1cm4gbVxufVxuXG5cblxuXG5cbi8vIEJyYWNlIGV4cGFuc2lvbjpcbi8vIGF7YixjfWQgLT4gYWJkIGFjZFxuLy8gYXtiLH1jIC0+IGFiYyBhY1xuLy8gYXswLi4zfWQgLT4gYTBkIGExZCBhMmQgYTNkXG4vLyBhe2IsY3tkLGV9Zn1nIC0+IGFiZyBhY2RmZyBhY2VmZ1xuLy8gYXtiLGN9ZHtlLGZ9ZyAtPiBhYmRlZyBhY2RlZyBhYmRlZyBhYmRmZ1xuLy9cbi8vIEludmFsaWQgc2V0cyBhcmUgbm90IGV4cGFuZGVkLlxuLy8gYXsyLi59YiAtPiBhezIuLn1iXG4vLyBhe2J9YyAtPiBhe2J9Y1xubWluaW1hdGNoLmJyYWNlRXhwYW5kID0gKHBhdHRlcm4sIG9wdGlvbnMpID0+IGJyYWNlRXhwYW5kKHBhdHRlcm4sIG9wdGlvbnMpXG5cbmNvbnN0IGJyYWNlRXhwYW5kID0gKHBhdHRlcm4sIG9wdGlvbnMgPSB7fSkgPT4ge1xuICBhc3NlcnRWYWxpZFBhdHRlcm4ocGF0dGVybilcblxuICAvLyBUaGFua3MgdG8gWWV0aW5nIExpIDxodHRwczovL2dpdGh1Yi5jb20veWV0aW5nbGk+IGZvclxuICAvLyBpbXByb3ZpbmcgdGhpcyByZWdleHAgdG8gYXZvaWQgYSBSZURPUyB2dWxuZXJhYmlsaXR5LlxuICBpZiAob3B0aW9ucy5ub2JyYWNlIHx8ICEvXFx7KD86KD8hXFx7KS4pKlxcfS8udGVzdChwYXR0ZXJuKSkge1xuICAgIC8vIHNob3J0Y3V0LiBubyBuZWVkIHRvIGV4cGFuZC5cbiAgICByZXR1cm4gW3BhdHRlcm5dXG4gIH1cblxuICByZXR1cm4gZXhwYW5kKHBhdHRlcm4pXG59XG5cbmNvbnN0IE1BWF9QQVRURVJOX0xFTkdUSCA9IDEwMjQgKiA2NFxuY29uc3QgYXNzZXJ0VmFsaWRQYXR0ZXJuID0gcGF0dGVybiA9PiB7XG4gIGlmICh0eXBlb2YgcGF0dGVybiAhPT0gJ3N0cmluZycpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdpbnZhbGlkIHBhdHRlcm4nKVxuICB9XG5cbiAgaWYgKHBhdHRlcm4ubGVuZ3RoID4gTUFYX1BBVFRFUk5fTEVOR1RIKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcigncGF0dGVybiBpcyB0b28gbG9uZycpXG4gIH1cbn1cblxuLy8gcGFyc2UgYSBjb21wb25lbnQgb2YgdGhlIGV4cGFuZGVkIHNldC5cbi8vIEF0IHRoaXMgcG9pbnQsIG5vIHBhdHRlcm4gbWF5IGNvbnRhaW4gXCIvXCIgaW4gaXRcbi8vIHNvIHdlJ3JlIGdvaW5nIHRvIHJldHVybiBhIDJkIGFycmF5LCB3aGVyZSBlYWNoIGVudHJ5IGlzIHRoZSBmdWxsXG4vLyBwYXR0ZXJuLCBzcGxpdCBvbiAnLycsIGFuZCB0aGVuIHR1cm5lZCBpbnRvIGEgcmVndWxhciBleHByZXNzaW9uLlxuLy8gQSByZWdleHAgaXMgbWFkZSBhdCB0aGUgZW5kIHdoaWNoIGpvaW5zIGVhY2ggYXJyYXkgd2l0aCBhblxuLy8gZXNjYXBlZCAvLCBhbmQgYW5vdGhlciBmdWxsIG9uZSB3aGljaCBqb2lucyBlYWNoIHJlZ2V4cCB3aXRoIHwuXG4vL1xuLy8gRm9sbG93aW5nIHRoZSBsZWFkIG9mIEJhc2ggNC4xLCBub3RlIHRoYXQgXCIqKlwiIG9ubHkgaGFzIHNwZWNpYWwgbWVhbmluZ1xuLy8gd2hlbiBpdCBpcyB0aGUgKm9ubHkqIHRoaW5nIGluIGEgcGF0aCBwb3J0aW9uLiAgT3RoZXJ3aXNlLCBhbnkgc2VyaWVzXG4vLyBvZiAqIGlzIGVxdWl2YWxlbnQgdG8gYSBzaW5nbGUgKi4gIEdsb2JzdGFyIGJlaGF2aW9yIGlzIGVuYWJsZWQgYnlcbi8vIGRlZmF1bHQsIGFuZCBjYW4gYmUgZGlzYWJsZWQgYnkgc2V0dGluZyBvcHRpb25zLm5vZ2xvYnN0YXIuXG5jb25zdCBTVUJQQVJTRSA9IFN5bWJvbCgnc3VicGFyc2UnKVxuXG5taW5pbWF0Y2gubWFrZVJlID0gKHBhdHRlcm4sIG9wdGlvbnMpID0+XG4gIG5ldyBNaW5pbWF0Y2gocGF0dGVybiwgb3B0aW9ucyB8fCB7fSkubWFrZVJlKClcblxubWluaW1hdGNoLm1hdGNoID0gKGxpc3QsIHBhdHRlcm4sIG9wdGlvbnMgPSB7fSkgPT4ge1xuICBjb25zdCBtbSA9IG5ldyBNaW5pbWF0Y2gocGF0dGVybiwgb3B0aW9ucylcbiAgbGlzdCA9IGxpc3QuZmlsdGVyKGYgPT4gbW0ubWF0Y2goZikpXG4gIGlmIChtbS5vcHRpb25zLm5vbnVsbCAmJiAhbGlzdC5sZW5ndGgpIHtcbiAgICBsaXN0LnB1c2gocGF0dGVybilcbiAgfVxuICByZXR1cm4gbGlzdFxufVxuXG4vLyByZXBsYWNlIHN0dWZmIGxpa2UgXFwqIHdpdGggKlxuY29uc3QgZ2xvYlVuZXNjYXBlID0gcyA9PiBzLnJlcGxhY2UoL1xcXFwoLikvZywgJyQxJylcbmNvbnN0IGNoYXJVbmVzY2FwZSA9IHMgPT4gcy5yZXBsYWNlKC9cXFxcKFteLVxcXV0pL2csICckMScpXG5jb25zdCByZWdFeHBFc2NhcGUgPSBzID0+IHMucmVwbGFjZSgvWy1bXFxde30oKSorPy4sXFxcXF4kfCNcXHNdL2csICdcXFxcJCYnKVxuY29uc3QgYnJhRXhwRXNjYXBlID0gcyA9PiBzLnJlcGxhY2UoL1tbXFxdXFxcXF0vZywgJ1xcXFwkJicpXG5cbmNsYXNzIE1pbmltYXRjaCB7XG4gIGNvbnN0cnVjdG9yIChwYXR0ZXJuLCBvcHRpb25zKSB7XG4gICAgYXNzZXJ0VmFsaWRQYXR0ZXJuKHBhdHRlcm4pXG5cbiAgICBpZiAoIW9wdGlvbnMpIG9wdGlvbnMgPSB7fVxuXG4gICAgdGhpcy5vcHRpb25zID0gb3B0aW9uc1xuICAgIHRoaXMuc2V0ID0gW11cbiAgICB0aGlzLnBhdHRlcm4gPSBwYXR0ZXJuXG4gICAgdGhpcy53aW5kb3dzUGF0aHNOb0VzY2FwZSA9ICEhb3B0aW9ucy53aW5kb3dzUGF0aHNOb0VzY2FwZSB8fFxuICAgICAgb3B0aW9ucy5hbGxvd1dpbmRvd3NFc2NhcGUgPT09IGZhbHNlXG4gICAgaWYgKHRoaXMud2luZG93c1BhdGhzTm9Fc2NhcGUpIHtcbiAgICAgIHRoaXMucGF0dGVybiA9IHRoaXMucGF0dGVybi5yZXBsYWNlKC9cXFxcL2csICcvJylcbiAgICB9XG4gICAgdGhpcy5yZWdleHAgPSBudWxsXG4gICAgdGhpcy5uZWdhdGUgPSBmYWxzZVxuICAgIHRoaXMuY29tbWVudCA9IGZhbHNlXG4gICAgdGhpcy5lbXB0eSA9IGZhbHNlXG4gICAgdGhpcy5wYXJ0aWFsID0gISFvcHRpb25zLnBhcnRpYWxcblxuICAgIC8vIG1ha2UgdGhlIHNldCBvZiByZWdleHBzIGV0Yy5cbiAgICB0aGlzLm1ha2UoKVxuICB9XG5cbiAgZGVidWcgKCkge31cblxuICBtYWtlICgpIHtcbiAgICBjb25zdCBwYXR0ZXJuID0gdGhpcy5wYXR0ZXJuXG4gICAgY29uc3Qgb3B0aW9ucyA9IHRoaXMub3B0aW9uc1xuXG4gICAgLy8gZW1wdHkgcGF0dGVybnMgYW5kIGNvbW1lbnRzIG1hdGNoIG5vdGhpbmcuXG4gICAgaWYgKCFvcHRpb25zLm5vY29tbWVudCAmJiBwYXR0ZXJuLmNoYXJBdCgwKSA9PT0gJyMnKSB7XG4gICAgICB0aGlzLmNvbW1lbnQgPSB0cnVlXG4gICAgICByZXR1cm5cbiAgICB9XG4gICAgaWYgKCFwYXR0ZXJuKSB7XG4gICAgICB0aGlzLmVtcHR5ID0gdHJ1ZVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgLy8gc3RlcCAxOiBmaWd1cmUgb3V0IG5lZ2F0aW9uLCBldGMuXG4gICAgdGhpcy5wYXJzZU5lZ2F0ZSgpXG5cbiAgICAvLyBzdGVwIDI6IGV4cGFuZCBicmFjZXNcbiAgICBsZXQgc2V0ID0gdGhpcy5nbG9iU2V0ID0gdGhpcy5icmFjZUV4cGFuZCgpXG5cbiAgICBpZiAob3B0aW9ucy5kZWJ1ZykgdGhpcy5kZWJ1ZyA9ICguLi5hcmdzKSA9PiBjb25zb2xlLmVycm9yKC4uLmFyZ3MpXG5cbiAgICB0aGlzLmRlYnVnKHRoaXMucGF0dGVybiwgc2V0KVxuXG4gICAgLy8gc3RlcCAzOiBub3cgd2UgaGF2ZSBhIHNldCwgc28gdHVybiBlYWNoIG9uZSBpbnRvIGEgc2VyaWVzIG9mIHBhdGgtcG9ydGlvblxuICAgIC8vIG1hdGNoaW5nIHBhdHRlcm5zLlxuICAgIC8vIFRoZXNlIHdpbGwgYmUgcmVnZXhwcywgZXhjZXB0IGluIHRoZSBjYXNlIG9mIFwiKipcIiwgd2hpY2ggaXNcbiAgICAvLyBzZXQgdG8gdGhlIEdMT0JTVEFSIG9iamVjdCBmb3IgZ2xvYnN0YXIgYmVoYXZpb3IsXG4gICAgLy8gYW5kIHdpbGwgbm90IGNvbnRhaW4gYW55IC8gY2hhcmFjdGVyc1xuICAgIHNldCA9IHRoaXMuZ2xvYlBhcnRzID0gc2V0Lm1hcChzID0+IHMuc3BsaXQoc2xhc2hTcGxpdCkpXG5cbiAgICB0aGlzLmRlYnVnKHRoaXMucGF0dGVybiwgc2V0KVxuXG4gICAgLy8gZ2xvYiAtLT4gcmVnZXhwc1xuICAgIHNldCA9IHNldC5tYXAoKHMsIHNpLCBzZXQpID0+IHMubWFwKHRoaXMucGFyc2UsIHRoaXMpKVxuXG4gICAgdGhpcy5kZWJ1Zyh0aGlzLnBhdHRlcm4sIHNldClcblxuICAgIC8vIGZpbHRlciBvdXQgZXZlcnl0aGluZyB0aGF0IGRpZG4ndCBjb21waWxlIHByb3Blcmx5LlxuICAgIHNldCA9IHNldC5maWx0ZXIocyA9PiBzLmluZGV4T2YoZmFsc2UpID09PSAtMSlcblxuICAgIHRoaXMuZGVidWcodGhpcy5wYXR0ZXJuLCBzZXQpXG5cbiAgICB0aGlzLnNldCA9IHNldFxuICB9XG5cbiAgcGFyc2VOZWdhdGUgKCkge1xuICAgIGlmICh0aGlzLm9wdGlvbnMubm9uZWdhdGUpIHJldHVyblxuXG4gICAgY29uc3QgcGF0dGVybiA9IHRoaXMucGF0dGVyblxuICAgIGxldCBuZWdhdGUgPSBmYWxzZVxuICAgIGxldCBuZWdhdGVPZmZzZXQgPSAwXG5cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHBhdHRlcm4ubGVuZ3RoICYmIHBhdHRlcm4uY2hhckF0KGkpID09PSAnISc7IGkrKykge1xuICAgICAgbmVnYXRlID0gIW5lZ2F0ZVxuICAgICAgbmVnYXRlT2Zmc2V0KytcbiAgICB9XG5cbiAgICBpZiAobmVnYXRlT2Zmc2V0KSB0aGlzLnBhdHRlcm4gPSBwYXR0ZXJuLnNsaWNlKG5lZ2F0ZU9mZnNldClcbiAgICB0aGlzLm5lZ2F0ZSA9IG5lZ2F0ZVxuICB9XG5cbiAgLy8gc2V0IHBhcnRpYWwgdG8gdHJ1ZSB0byB0ZXN0IGlmLCBmb3IgZXhhbXBsZSxcbiAgLy8gXCIvYS9iXCIgbWF0Y2hlcyB0aGUgc3RhcnQgb2YgXCIvKi9iLyovZFwiXG4gIC8vIFBhcnRpYWwgbWVhbnMsIGlmIHlvdSBydW4gb3V0IG9mIGZpbGUgYmVmb3JlIHlvdSBydW5cbiAgLy8gb3V0IG9mIHBhdHRlcm4sIHRoZW4gdGhhdCdzIGZpbmUsIGFzIGxvbmcgYXMgYWxsXG4gIC8vIHRoZSBwYXJ0cyBtYXRjaC5cbiAgbWF0Y2hPbmUgKGZpbGUsIHBhdHRlcm4sIHBhcnRpYWwpIHtcbiAgICB2YXIgb3B0aW9ucyA9IHRoaXMub3B0aW9uc1xuXG4gICAgdGhpcy5kZWJ1ZygnbWF0Y2hPbmUnLFxuICAgICAgeyAndGhpcyc6IHRoaXMsIGZpbGU6IGZpbGUsIHBhdHRlcm46IHBhdHRlcm4gfSlcblxuICAgIHRoaXMuZGVidWcoJ21hdGNoT25lJywgZmlsZS5sZW5ndGgsIHBhdHRlcm4ubGVuZ3RoKVxuXG4gICAgZm9yICh2YXIgZmkgPSAwLFxuICAgICAgICBwaSA9IDAsXG4gICAgICAgIGZsID0gZmlsZS5sZW5ndGgsXG4gICAgICAgIHBsID0gcGF0dGVybi5sZW5ndGhcbiAgICAgICAgOyAoZmkgPCBmbCkgJiYgKHBpIDwgcGwpXG4gICAgICAgIDsgZmkrKywgcGkrKykge1xuICAgICAgdGhpcy5kZWJ1ZygnbWF0Y2hPbmUgbG9vcCcpXG4gICAgICB2YXIgcCA9IHBhdHRlcm5bcGldXG4gICAgICB2YXIgZiA9IGZpbGVbZmldXG5cbiAgICAgIHRoaXMuZGVidWcocGF0dGVybiwgcCwgZilcblxuICAgICAgLy8gc2hvdWxkIGJlIGltcG9zc2libGUuXG4gICAgICAvLyBzb21lIGludmFsaWQgcmVnZXhwIHN0dWZmIGluIHRoZSBzZXQuXG4gICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgaWYgKi9cbiAgICAgIGlmIChwID09PSBmYWxzZSkgcmV0dXJuIGZhbHNlXG5cbiAgICAgIGlmIChwID09PSBHTE9CU1RBUikge1xuICAgICAgICB0aGlzLmRlYnVnKCdHTE9CU1RBUicsIFtwYXR0ZXJuLCBwLCBmXSlcblxuICAgICAgICAvLyBcIioqXCJcbiAgICAgICAgLy8gYS8qKi9iLyoqL2Mgd291bGQgbWF0Y2ggdGhlIGZvbGxvd2luZzpcbiAgICAgICAgLy8gYS9iL3gveS96L2NcbiAgICAgICAgLy8gYS94L3kvei9iL2NcbiAgICAgICAgLy8gYS9iL3gvYi94L2NcbiAgICAgICAgLy8gYS9iL2NcbiAgICAgICAgLy8gVG8gZG8gdGhpcywgdGFrZSB0aGUgcmVzdCBvZiB0aGUgcGF0dGVybiBhZnRlclxuICAgICAgICAvLyB0aGUgKiosIGFuZCBzZWUgaWYgaXQgd291bGQgbWF0Y2ggdGhlIGZpbGUgcmVtYWluZGVyLlxuICAgICAgICAvLyBJZiBzbywgcmV0dXJuIHN1Y2Nlc3MuXG4gICAgICAgIC8vIElmIG5vdCwgdGhlICoqIFwic3dhbGxvd3NcIiBhIHNlZ21lbnQsIGFuZCB0cnkgYWdhaW4uXG4gICAgICAgIC8vIFRoaXMgaXMgcmVjdXJzaXZlbHkgYXdmdWwuXG4gICAgICAgIC8vXG4gICAgICAgIC8vIGEvKiovYi8qKi9jIG1hdGNoaW5nIGEvYi94L3kvei9jXG4gICAgICAgIC8vIC0gYSBtYXRjaGVzIGFcbiAgICAgICAgLy8gLSBkb3VibGVzdGFyXG4gICAgICAgIC8vICAgLSBtYXRjaE9uZShiL3gveS96L2MsIGIvKiovYylcbiAgICAgICAgLy8gICAgIC0gYiBtYXRjaGVzIGJcbiAgICAgICAgLy8gICAgIC0gZG91Ymxlc3RhclxuICAgICAgICAvLyAgICAgICAtIG1hdGNoT25lKHgveS96L2MsIGMpIC0+IG5vXG4gICAgICAgIC8vICAgICAgIC0gbWF0Y2hPbmUoeS96L2MsIGMpIC0+IG5vXG4gICAgICAgIC8vICAgICAgIC0gbWF0Y2hPbmUoei9jLCBjKSAtPiBub1xuICAgICAgICAvLyAgICAgICAtIG1hdGNoT25lKGMsIGMpIHllcywgaGl0XG4gICAgICAgIHZhciBmciA9IGZpXG4gICAgICAgIHZhciBwciA9IHBpICsgMVxuICAgICAgICBpZiAocHIgPT09IHBsKSB7XG4gICAgICAgICAgdGhpcy5kZWJ1ZygnKiogYXQgdGhlIGVuZCcpXG4gICAgICAgICAgLy8gYSAqKiBhdCB0aGUgZW5kIHdpbGwganVzdCBzd2FsbG93IHRoZSByZXN0LlxuICAgICAgICAgIC8vIFdlIGhhdmUgZm91bmQgYSBtYXRjaC5cbiAgICAgICAgICAvLyBob3dldmVyLCBpdCB3aWxsIG5vdCBzd2FsbG93IC8ueCwgdW5sZXNzXG4gICAgICAgICAgLy8gb3B0aW9ucy5kb3QgaXMgc2V0LlxuICAgICAgICAgIC8vIC4gYW5kIC4uIGFyZSAqbmV2ZXIqIG1hdGNoZWQgYnkgKiosIGZvciBleHBsb3NpdmVseVxuICAgICAgICAgIC8vIGV4cG9uZW50aWFsIHJlYXNvbnMuXG4gICAgICAgICAgZm9yICg7IGZpIDwgZmw7IGZpKyspIHtcbiAgICAgICAgICAgIGlmIChmaWxlW2ZpXSA9PT0gJy4nIHx8IGZpbGVbZmldID09PSAnLi4nIHx8XG4gICAgICAgICAgICAgICghb3B0aW9ucy5kb3QgJiYgZmlsZVtmaV0uY2hhckF0KDApID09PSAnLicpKSByZXR1cm4gZmFsc2VcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIG9rLCBsZXQncyBzZWUgaWYgd2UgY2FuIHN3YWxsb3cgd2hhdGV2ZXIgd2UgY2FuLlxuICAgICAgICB3aGlsZSAoZnIgPCBmbCkge1xuICAgICAgICAgIHZhciBzd2FsbG93ZWUgPSBmaWxlW2ZyXVxuXG4gICAgICAgICAgdGhpcy5kZWJ1ZygnXFxuZ2xvYnN0YXIgd2hpbGUnLCBmaWxlLCBmciwgcGF0dGVybiwgcHIsIHN3YWxsb3dlZSlcblxuICAgICAgICAgIC8vIFhYWCByZW1vdmUgdGhpcyBzbGljZS4gIEp1c3QgcGFzcyB0aGUgc3RhcnQgaW5kZXguXG4gICAgICAgICAgaWYgKHRoaXMubWF0Y2hPbmUoZmlsZS5zbGljZShmciksIHBhdHRlcm4uc2xpY2UocHIpLCBwYXJ0aWFsKSkge1xuICAgICAgICAgICAgdGhpcy5kZWJ1ZygnZ2xvYnN0YXIgZm91bmQgbWF0Y2ghJywgZnIsIGZsLCBzd2FsbG93ZWUpXG4gICAgICAgICAgICAvLyBmb3VuZCBhIG1hdGNoLlxuICAgICAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gY2FuJ3Qgc3dhbGxvdyBcIi5cIiBvciBcIi4uXCIgZXZlci5cbiAgICAgICAgICAgIC8vIGNhbiBvbmx5IHN3YWxsb3cgXCIuZm9vXCIgd2hlbiBleHBsaWNpdGx5IGFza2VkLlxuICAgICAgICAgICAgaWYgKHN3YWxsb3dlZSA9PT0gJy4nIHx8IHN3YWxsb3dlZSA9PT0gJy4uJyB8fFxuICAgICAgICAgICAgICAoIW9wdGlvbnMuZG90ICYmIHN3YWxsb3dlZS5jaGFyQXQoMCkgPT09ICcuJykpIHtcbiAgICAgICAgICAgICAgdGhpcy5kZWJ1ZygnZG90IGRldGVjdGVkIScsIGZpbGUsIGZyLCBwYXR0ZXJuLCBwcilcbiAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gKiogc3dhbGxvd3MgYSBzZWdtZW50LCBhbmQgY29udGludWUuXG4gICAgICAgICAgICB0aGlzLmRlYnVnKCdnbG9ic3RhciBzd2FsbG93IGEgc2VnbWVudCwgYW5kIGNvbnRpbnVlJylcbiAgICAgICAgICAgIGZyKytcbiAgICAgICAgICB9XG4gICAgICAgIH1cblxuICAgICAgICAvLyBubyBtYXRjaCB3YXMgZm91bmQuXG4gICAgICAgIC8vIEhvd2V2ZXIsIGluIHBhcnRpYWwgbW9kZSwgd2UgY2FuJ3Qgc2F5IHRoaXMgaXMgbmVjZXNzYXJpbHkgb3Zlci5cbiAgICAgICAgLy8gSWYgdGhlcmUncyBtb3JlICpwYXR0ZXJuKiBsZWZ0LCB0aGVuXG4gICAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBpZiAqL1xuICAgICAgICBpZiAocGFydGlhbCkge1xuICAgICAgICAgIC8vIHJhbiBvdXQgb2YgZmlsZVxuICAgICAgICAgIHRoaXMuZGVidWcoJ1xcbj4+PiBubyBtYXRjaCwgcGFydGlhbD8nLCBmaWxlLCBmciwgcGF0dGVybiwgcHIpXG4gICAgICAgICAgaWYgKGZyID09PSBmbCkgcmV0dXJuIHRydWVcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2VcbiAgICAgIH1cblxuICAgICAgLy8gc29tZXRoaW5nIG90aGVyIHRoYW4gKipcbiAgICAgIC8vIG5vbi1tYWdpYyBwYXR0ZXJucyBqdXN0IGhhdmUgdG8gbWF0Y2ggZXhhY3RseVxuICAgICAgLy8gcGF0dGVybnMgd2l0aCBtYWdpYyBoYXZlIGJlZW4gdHVybmVkIGludG8gcmVnZXhwcy5cbiAgICAgIHZhciBoaXRcbiAgICAgIGlmICh0eXBlb2YgcCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgaGl0ID0gZiA9PT0gcFxuICAgICAgICB0aGlzLmRlYnVnKCdzdHJpbmcgbWF0Y2gnLCBwLCBmLCBoaXQpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBoaXQgPSBmLm1hdGNoKHApXG4gICAgICAgIHRoaXMuZGVidWcoJ3BhdHRlcm4gbWF0Y2gnLCBwLCBmLCBoaXQpXG4gICAgICB9XG5cbiAgICAgIGlmICghaGl0KSByZXR1cm4gZmFsc2VcbiAgICB9XG5cbiAgICAvLyBOb3RlOiBlbmRpbmcgaW4gLyBtZWFucyB0aGF0IHdlJ2xsIGdldCBhIGZpbmFsIFwiXCJcbiAgICAvLyBhdCB0aGUgZW5kIG9mIHRoZSBwYXR0ZXJuLiAgVGhpcyBjYW4gb25seSBtYXRjaCBhXG4gICAgLy8gY29ycmVzcG9uZGluZyBcIlwiIGF0IHRoZSBlbmQgb2YgdGhlIGZpbGUuXG4gICAgLy8gSWYgdGhlIGZpbGUgZW5kcyBpbiAvLCB0aGVuIGl0IGNhbiBvbmx5IG1hdGNoIGFcbiAgICAvLyBhIHBhdHRlcm4gdGhhdCBlbmRzIGluIC8sIHVubGVzcyB0aGUgcGF0dGVybiBqdXN0XG4gICAgLy8gZG9lc24ndCBoYXZlIGFueSBtb3JlIGZvciBpdC4gQnV0LCBhL2IvIHNob3VsZCAqbm90KlxuICAgIC8vIG1hdGNoIFwiYS9iLypcIiwgZXZlbiB0aG91Z2ggXCJcIiBtYXRjaGVzIGFnYWluc3QgdGhlXG4gICAgLy8gW14vXSo/IHBhdHRlcm4sIGV4Y2VwdCBpbiBwYXJ0aWFsIG1vZGUsIHdoZXJlIGl0IG1pZ2h0XG4gICAgLy8gc2ltcGx5IG5vdCBiZSByZWFjaGVkIHlldC5cbiAgICAvLyBIb3dldmVyLCBhL2IvIHNob3VsZCBzdGlsbCBzYXRpc2Z5IGEvKlxuXG4gICAgLy8gbm93IGVpdGhlciB3ZSBmZWxsIG9mZiB0aGUgZW5kIG9mIHRoZSBwYXR0ZXJuLCBvciB3ZSdyZSBkb25lLlxuICAgIGlmIChmaSA9PT0gZmwgJiYgcGkgPT09IHBsKSB7XG4gICAgICAvLyByYW4gb3V0IG9mIHBhdHRlcm4gYW5kIGZpbGVuYW1lIGF0IHRoZSBzYW1lIHRpbWUuXG4gICAgICAvLyBhbiBleGFjdCBoaXQhXG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH0gZWxzZSBpZiAoZmkgPT09IGZsKSB7XG4gICAgICAvLyByYW4gb3V0IG9mIGZpbGUsIGJ1dCBzdGlsbCBoYWQgcGF0dGVybiBsZWZ0LlxuICAgICAgLy8gdGhpcyBpcyBvayBpZiB3ZSdyZSBkb2luZyB0aGUgbWF0Y2ggYXMgcGFydCBvZlxuICAgICAgLy8gYSBnbG9iIGZzIHRyYXZlcnNhbC5cbiAgICAgIHJldHVybiBwYXJ0aWFsXG4gICAgfSBlbHNlIC8qIGlzdGFuYnVsIGlnbm9yZSBlbHNlICovIGlmIChwaSA9PT0gcGwpIHtcbiAgICAgIC8vIHJhbiBvdXQgb2YgcGF0dGVybiwgc3RpbGwgaGF2ZSBmaWxlIGxlZnQuXG4gICAgICAvLyB0aGlzIGlzIG9ubHkgYWNjZXB0YWJsZSBpZiB3ZSdyZSBvbiB0aGUgdmVyeSBsYXN0XG4gICAgICAvLyBlbXB0eSBzZWdtZW50IG9mIGEgZmlsZSB3aXRoIGEgdHJhaWxpbmcgc2xhc2guXG4gICAgICAvLyBhLyogc2hvdWxkIG1hdGNoIGEvYi9cbiAgICAgIHJldHVybiAoZmkgPT09IGZsIC0gMSkgJiYgKGZpbGVbZmldID09PSAnJylcbiAgICB9XG5cbiAgICAvLyBzaG91bGQgYmUgdW5yZWFjaGFibGUuXG4gICAgLyogaXN0YW5idWwgaWdub3JlIG5leHQgKi9cbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3d0Zj8nKVxuICB9XG5cbiAgYnJhY2VFeHBhbmQgKCkge1xuICAgIHJldHVybiBicmFjZUV4cGFuZCh0aGlzLnBhdHRlcm4sIHRoaXMub3B0aW9ucylcbiAgfVxuXG4gIHBhcnNlIChwYXR0ZXJuLCBpc1N1Yikge1xuICAgIGFzc2VydFZhbGlkUGF0dGVybihwYXR0ZXJuKVxuXG4gICAgY29uc3Qgb3B0aW9ucyA9IHRoaXMub3B0aW9uc1xuXG4gICAgLy8gc2hvcnRjdXRzXG4gICAgaWYgKHBhdHRlcm4gPT09ICcqKicpIHtcbiAgICAgIGlmICghb3B0aW9ucy5ub2dsb2JzdGFyKVxuICAgICAgICByZXR1cm4gR0xPQlNUQVJcbiAgICAgIGVsc2VcbiAgICAgICAgcGF0dGVybiA9ICcqJ1xuICAgIH1cbiAgICBpZiAocGF0dGVybiA9PT0gJycpIHJldHVybiAnJ1xuXG4gICAgbGV0IHJlID0gJydcbiAgICBsZXQgaGFzTWFnaWMgPSBmYWxzZVxuICAgIGxldCBlc2NhcGluZyA9IGZhbHNlXG4gICAgLy8gPyA9PiBvbmUgc2luZ2xlIGNoYXJhY3RlclxuICAgIGNvbnN0IHBhdHRlcm5MaXN0U3RhY2sgPSBbXVxuICAgIGNvbnN0IG5lZ2F0aXZlTGlzdHMgPSBbXVxuICAgIGxldCBzdGF0ZUNoYXJcbiAgICBsZXQgaW5DbGFzcyA9IGZhbHNlXG4gICAgbGV0IHJlQ2xhc3NTdGFydCA9IC0xXG4gICAgbGV0IGNsYXNzU3RhcnQgPSAtMVxuICAgIGxldCBjc1xuICAgIGxldCBwbFxuICAgIGxldCBzcFxuICAgIC8vIC4gYW5kIC4uIG5ldmVyIG1hdGNoIGFueXRoaW5nIHRoYXQgZG9lc24ndCBzdGFydCB3aXRoIC4sXG4gICAgLy8gZXZlbiB3aGVuIG9wdGlvbnMuZG90IGlzIHNldC4gIEhvd2V2ZXIsIGlmIHRoZSBwYXR0ZXJuXG4gICAgLy8gc3RhcnRzIHdpdGggLiwgdGhlbiB0cmF2ZXJzYWwgcGF0dGVybnMgY2FuIG1hdGNoLlxuICAgIGxldCBkb3RUcmF2QWxsb3dlZCA9IHBhdHRlcm4uY2hhckF0KDApID09PSAnLidcbiAgICBsZXQgZG90RmlsZUFsbG93ZWQgPSBvcHRpb25zLmRvdCB8fCBkb3RUcmF2QWxsb3dlZFxuICAgIGNvbnN0IHBhdHRlcm5TdGFydCA9ICgpID0+XG4gICAgICBkb3RUcmF2QWxsb3dlZFxuICAgICAgICA/ICcnXG4gICAgICAgIDogZG90RmlsZUFsbG93ZWRcbiAgICAgICAgPyAnKD8hKD86XnxcXFxcLylcXFxcLnsxLDJ9KD86JHxcXFxcLykpJ1xuICAgICAgICA6ICcoPyFcXFxcLiknXG4gICAgY29uc3Qgc3ViUGF0dGVyblN0YXJ0ID0gKHApID0+XG4gICAgICBwLmNoYXJBdCgwKSA9PT0gJy4nXG4gICAgICAgID8gJydcbiAgICAgICAgOiBvcHRpb25zLmRvdFxuICAgICAgICA/ICcoPyEoPzpefFxcXFwvKVxcXFwuezEsMn0oPzokfFxcXFwvKSknXG4gICAgICAgIDogJyg/IVxcXFwuKSdcblxuXG4gICAgY29uc3QgY2xlYXJTdGF0ZUNoYXIgPSAoKSA9PiB7XG4gICAgICBpZiAoc3RhdGVDaGFyKSB7XG4gICAgICAgIC8vIHdlIGhhZCBzb21lIHN0YXRlLXRyYWNraW5nIGNoYXJhY3RlclxuICAgICAgICAvLyB0aGF0IHdhc24ndCBjb25zdW1lZCBieSB0aGlzIHBhc3MuXG4gICAgICAgIHN3aXRjaCAoc3RhdGVDaGFyKSB7XG4gICAgICAgICAgY2FzZSAnKic6XG4gICAgICAgICAgICByZSArPSBzdGFyXG4gICAgICAgICAgICBoYXNNYWdpYyA9IHRydWVcbiAgICAgICAgICBicmVha1xuICAgICAgICAgIGNhc2UgJz8nOlxuICAgICAgICAgICAgcmUgKz0gcW1hcmtcbiAgICAgICAgICAgIGhhc01hZ2ljID0gdHJ1ZVxuICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJlICs9ICdcXFxcJyArIHN0YXRlQ2hhclxuICAgICAgICAgIGJyZWFrXG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5kZWJ1ZygnY2xlYXJTdGF0ZUNoYXIgJWogJWonLCBzdGF0ZUNoYXIsIHJlKVxuICAgICAgICBzdGF0ZUNoYXIgPSBmYWxzZVxuICAgICAgfVxuICAgIH1cblxuICAgIGZvciAobGV0IGkgPSAwLCBjOyAoaSA8IHBhdHRlcm4ubGVuZ3RoKSAmJiAoYyA9IHBhdHRlcm4uY2hhckF0KGkpKTsgaSsrKSB7XG4gICAgICB0aGlzLmRlYnVnKCclc1xcdCVzICVzICVqJywgcGF0dGVybiwgaSwgcmUsIGMpXG5cbiAgICAgIC8vIHNraXAgb3ZlciBhbnkgdGhhdCBhcmUgZXNjYXBlZC5cbiAgICAgIGlmIChlc2NhcGluZykge1xuICAgICAgICAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAtIGNvbXBsZXRlbHkgbm90IGFsbG93ZWQsIGV2ZW4gZXNjYXBlZC4gKi9cbiAgICAgICAgaWYgKGMgPT09ICcvJykge1xuICAgICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgICB9XG5cbiAgICAgICAgaWYgKHJlU3BlY2lhbHNbY10pIHtcbiAgICAgICAgICByZSArPSAnXFxcXCdcbiAgICAgICAgfVxuICAgICAgICByZSArPSBjXG4gICAgICAgIGVzY2FwaW5nID0gZmFsc2VcbiAgICAgICAgY29udGludWVcbiAgICAgIH1cblxuICAgICAgc3dpdGNoIChjKSB7XG4gICAgICAgIC8qIGlzdGFuYnVsIGlnbm9yZSBuZXh0ICovXG4gICAgICAgIGNhc2UgJy8nOiB7XG4gICAgICAgICAgLy8gU2hvdWxkIGFscmVhZHkgYmUgcGF0aC1zcGxpdCBieSBub3cuXG4gICAgICAgICAgcmV0dXJuIGZhbHNlXG4gICAgICAgIH1cblxuICAgICAgICBjYXNlICdcXFxcJzpcbiAgICAgICAgICBpZiAoaW5DbGFzcyAmJiBwYXR0ZXJuLmNoYXJBdChpICsgMSkgPT09ICctJykge1xuICAgICAgICAgICAgcmUgKz0gY1xuICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjbGVhclN0YXRlQ2hhcigpXG4gICAgICAgICAgZXNjYXBpbmcgPSB0cnVlXG4gICAgICAgIGNvbnRpbnVlXG5cbiAgICAgICAgLy8gdGhlIHZhcmlvdXMgc3RhdGVDaGFyIHZhbHVlc1xuICAgICAgICAvLyBmb3IgdGhlIFwiZXh0Z2xvYlwiIHN0dWZmLlxuICAgICAgICBjYXNlICc/JzpcbiAgICAgICAgY2FzZSAnKic6XG4gICAgICAgIGNhc2UgJysnOlxuICAgICAgICBjYXNlICdAJzpcbiAgICAgICAgY2FzZSAnISc6XG4gICAgICAgICAgdGhpcy5kZWJ1ZygnJXNcXHQlcyAlcyAlaiA8LS0gc3RhdGVDaGFyJywgcGF0dGVybiwgaSwgcmUsIGMpXG5cbiAgICAgICAgICAvLyBhbGwgb2YgdGhvc2UgYXJlIGxpdGVyYWxzIGluc2lkZSBhIGNsYXNzLCBleGNlcHQgdGhhdFxuICAgICAgICAgIC8vIHRoZSBnbG9iIFshYV0gbWVhbnMgW15hXSBpbiByZWdleHBcbiAgICAgICAgICBpZiAoaW5DbGFzcykge1xuICAgICAgICAgICAgdGhpcy5kZWJ1ZygnICBpbiBjbGFzcycpXG4gICAgICAgICAgICBpZiAoYyA9PT0gJyEnICYmIGkgPT09IGNsYXNzU3RhcnQgKyAxKSBjID0gJ14nXG4gICAgICAgICAgICByZSArPSBjXG4gICAgICAgICAgICBjb250aW51ZVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIGlmIHdlIGFscmVhZHkgaGF2ZSBhIHN0YXRlQ2hhciwgdGhlbiBpdCBtZWFuc1xuICAgICAgICAgIC8vIHRoYXQgdGhlcmUgd2FzIHNvbWV0aGluZyBsaWtlICoqIG9yICs/IGluIHRoZXJlLlxuICAgICAgICAgIC8vIEhhbmRsZSB0aGUgc3RhdGVDaGFyLCB0aGVuIHByb2NlZWQgd2l0aCB0aGlzIG9uZS5cbiAgICAgICAgICB0aGlzLmRlYnVnKCdjYWxsIGNsZWFyU3RhdGVDaGFyICVqJywgc3RhdGVDaGFyKVxuICAgICAgICAgIGNsZWFyU3RhdGVDaGFyKClcbiAgICAgICAgICBzdGF0ZUNoYXIgPSBjXG4gICAgICAgICAgLy8gaWYgZXh0Z2xvYiBpcyBkaXNhYmxlZCwgdGhlbiArKGFzZGZ8Zm9vKSBpc24ndCBhIHRoaW5nLlxuICAgICAgICAgIC8vIGp1c3QgY2xlYXIgdGhlIHN0YXRlY2hhciAqbm93KiwgcmF0aGVyIHRoYW4gZXZlbiBkaXZpbmcgaW50b1xuICAgICAgICAgIC8vIHRoZSBwYXR0ZXJuTGlzdCBzdHVmZi5cbiAgICAgICAgICBpZiAob3B0aW9ucy5ub2V4dCkgY2xlYXJTdGF0ZUNoYXIoKVxuICAgICAgICBjb250aW51ZVxuXG4gICAgICAgIGNhc2UgJygnOiB7XG4gICAgICAgICAgaWYgKGluQ2xhc3MpIHtcbiAgICAgICAgICAgIHJlICs9ICcoJ1xuICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAoIXN0YXRlQ2hhcikge1xuICAgICAgICAgICAgcmUgKz0gJ1xcXFwoJ1xuICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjb25zdCBwbEVudHJ5ID0ge1xuICAgICAgICAgICAgdHlwZTogc3RhdGVDaGFyLFxuICAgICAgICAgICAgc3RhcnQ6IGkgLSAxLFxuICAgICAgICAgICAgcmVTdGFydDogcmUubGVuZ3RoLFxuICAgICAgICAgICAgb3BlbjogcGxUeXBlc1tzdGF0ZUNoYXJdLm9wZW4sXG4gICAgICAgICAgICBjbG9zZTogcGxUeXBlc1tzdGF0ZUNoYXJdLmNsb3NlLFxuICAgICAgICAgIH1cbiAgICAgICAgICB0aGlzLmRlYnVnKHRoaXMucGF0dGVybiwgJ1xcdCcsIHBsRW50cnkpXG4gICAgICAgICAgcGF0dGVybkxpc3RTdGFjay5wdXNoKHBsRW50cnkpXG4gICAgICAgICAgLy8gbmVnYXRpb24gaXMgKD86KD8hKD86anMpKD86PHJlc3Q+KSlbXi9dKilcbiAgICAgICAgICByZSArPSBwbEVudHJ5Lm9wZW5cbiAgICAgICAgICAvLyBuZXh0IGVudHJ5IHN0YXJ0cyB3aXRoIGEgZG90IG1heWJlP1xuICAgICAgICAgIGlmIChwbEVudHJ5LnN0YXJ0ID09PSAwICYmIHBsRW50cnkudHlwZSAhPT0gJyEnKSB7XG4gICAgICAgICAgICBkb3RUcmF2QWxsb3dlZCA9IHRydWVcbiAgICAgICAgICAgIHJlICs9IHN1YlBhdHRlcm5TdGFydChwYXR0ZXJuLnNsaWNlKGkgKyAxKSlcbiAgICAgICAgICB9XG4gICAgICAgICAgdGhpcy5kZWJ1ZygncGxUeXBlICVqICVqJywgc3RhdGVDaGFyLCByZSlcbiAgICAgICAgICBzdGF0ZUNoYXIgPSBmYWxzZVxuICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgIH1cblxuICAgICAgICBjYXNlICcpJzoge1xuICAgICAgICAgIGNvbnN0IHBsRW50cnkgPSBwYXR0ZXJuTGlzdFN0YWNrW3BhdHRlcm5MaXN0U3RhY2subGVuZ3RoIC0gMV1cbiAgICAgICAgICBpZiAoaW5DbGFzcyB8fCAhcGxFbnRyeSkge1xuICAgICAgICAgICAgcmUgKz0gJ1xcXFwpJ1xuICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICB9XG4gICAgICAgICAgcGF0dGVybkxpc3RTdGFjay5wb3AoKVxuXG4gICAgICAgICAgLy8gY2xvc2luZyBhbiBleHRnbG9iXG4gICAgICAgICAgY2xlYXJTdGF0ZUNoYXIoKVxuICAgICAgICAgIGhhc01hZ2ljID0gdHJ1ZVxuICAgICAgICAgIHBsID0gcGxFbnRyeVxuICAgICAgICAgIC8vIG5lZ2F0aW9uIGlzICg/Oig/IWpzKVteL10qKVxuICAgICAgICAgIC8vIFRoZSBvdGhlcnMgYXJlICg/OjxwYXR0ZXJuPik8dHlwZT5cbiAgICAgICAgICByZSArPSBwbC5jbG9zZVxuICAgICAgICAgIGlmIChwbC50eXBlID09PSAnIScpIHtcbiAgICAgICAgICAgIG5lZ2F0aXZlTGlzdHMucHVzaChPYmplY3QuYXNzaWduKHBsLCB7IHJlRW5kOiByZS5sZW5ndGggfSkpXG4gICAgICAgICAgfVxuICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgIH1cblxuICAgICAgICBjYXNlICd8Jzoge1xuICAgICAgICAgIGNvbnN0IHBsRW50cnkgPSBwYXR0ZXJuTGlzdFN0YWNrW3BhdHRlcm5MaXN0U3RhY2subGVuZ3RoIC0gMV1cbiAgICAgICAgICBpZiAoaW5DbGFzcyB8fCAhcGxFbnRyeSkge1xuICAgICAgICAgICAgcmUgKz0gJ1xcXFx8J1xuICAgICAgICAgICAgY29udGludWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjbGVhclN0YXRlQ2hhcigpXG4gICAgICAgICAgcmUgKz0gJ3wnXG4gICAgICAgICAgLy8gbmV4dCBzdWJwYXR0ZXJuIGNhbiBzdGFydCB3aXRoIGEgZG90P1xuICAgICAgICAgIGlmIChwbEVudHJ5LnN0YXJ0ID09PSAwICYmIHBsRW50cnkudHlwZSAhPT0gJyEnKSB7XG4gICAgICAgICAgICBkb3RUcmF2QWxsb3dlZCA9IHRydWVcbiAgICAgICAgICAgIHJlICs9IHN1YlBhdHRlcm5TdGFydChwYXR0ZXJuLnNsaWNlKGkgKyAxKSlcbiAgICAgICAgICB9XG4gICAgICAgICAgY29udGludWVcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIHRoZXNlIGFyZSBtb3N0bHkgdGhlIHNhbWUgaW4gcmVnZXhwIGFuZCBnbG9iXG4gICAgICAgIGNhc2UgJ1snOlxuICAgICAgICAgIC8vIHN3YWxsb3cgYW55IHN0YXRlLXRyYWNraW5nIGNoYXIgYmVmb3JlIHRoZSBbXG4gICAgICAgICAgY2xlYXJTdGF0ZUNoYXIoKVxuXG4gICAgICAgICAgaWYgKGluQ2xhc3MpIHtcbiAgICAgICAgICAgIHJlICs9ICdcXFxcJyArIGNcbiAgICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaW5DbGFzcyA9IHRydWVcbiAgICAgICAgICBjbGFzc1N0YXJ0ID0gaVxuICAgICAgICAgIHJlQ2xhc3NTdGFydCA9IHJlLmxlbmd0aFxuICAgICAgICAgIHJlICs9IGNcbiAgICAgICAgY29udGludWVcblxuICAgICAgICBjYXNlICddJzpcbiAgICAgICAgICAvLyAgYSByaWdodCBicmFja2V0IHNoYWxsIGxvc2UgaXRzIHNwZWNpYWxcbiAgICAgICAgICAvLyAgbWVhbmluZyBhbmQgcmVwcmVzZW50IGl0c2VsZiBpblxuICAgICAgICAgIC8vICBhIGJyYWNrZXQgZXhwcmVzc2lvbiBpZiBpdCBvY2N1cnNcbiAgICAgICAgICAvLyAgZmlyc3QgaW4gdGhlIGxpc3QuICAtLSBQT1NJWC4yIDIuOC4zLjJcbiAgICAgICAgICBpZiAoaSA9PT0gY2xhc3NTdGFydCArIDEgfHwgIWluQ2xhc3MpIHtcbiAgICAgICAgICAgIHJlICs9ICdcXFxcJyArIGNcbiAgICAgICAgICAgIGNvbnRpbnVlXG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gc3BsaXQgd2hlcmUgdGhlIGxhc3QgWyB3YXMsIG1ha2Ugc3VyZSB3ZSBkb24ndCBoYXZlXG4gICAgICAgICAgLy8gYW4gaW52YWxpZCByZS4gaWYgc28sIHJlLXdhbGsgdGhlIGNvbnRlbnRzIG9mIHRoZVxuICAgICAgICAgIC8vIHdvdWxkLWJlIGNsYXNzIHRvIHJlLXRyYW5zbGF0ZSBhbnkgY2hhcmFjdGVycyB0aGF0XG4gICAgICAgICAgLy8gd2VyZSBwYXNzZWQgdGhyb3VnaCBhcy1pc1xuICAgICAgICAgIC8vIFRPRE86IEl0IHdvdWxkIHByb2JhYmx5IGJlIGZhc3RlciB0byBkZXRlcm1pbmUgdGhpc1xuICAgICAgICAgIC8vIHdpdGhvdXQgYSB0cnkvY2F0Y2ggYW5kIGEgbmV3IFJlZ0V4cCwgYnV0IGl0J3MgdHJpY2t5XG4gICAgICAgICAgLy8gdG8gZG8gc2FmZWx5LiAgRm9yIG5vdywgdGhpcyBpcyBzYWZlIGFuZCB3b3Jrcy5cbiAgICAgICAgICBjcyA9IHBhdHRlcm4uc3Vic3RyaW5nKGNsYXNzU3RhcnQgKyAxLCBpKVxuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICBSZWdFeHAoJ1snICsgYnJhRXhwRXNjYXBlKGNoYXJVbmVzY2FwZShjcykpICsgJ10nKVxuICAgICAgICAgICAgLy8gbG9va3MgZ29vZCwgZmluaXNoIHVwIHRoZSBjbGFzcy5cbiAgICAgICAgICAgIHJlICs9IGNcbiAgICAgICAgICB9IGNhdGNoIChlcikge1xuICAgICAgICAgICAgLy8gb3V0IG9mIG9yZGVyIHJhbmdlcyBpbiBKUyBhcmUgZXJyb3JzLCBidXQgaW4gZ2xvYiBzeW50YXgsXG4gICAgICAgICAgICAvLyB0aGV5J3JlIGp1c3QgYSByYW5nZSB0aGF0IG1hdGNoZXMgbm90aGluZy5cbiAgICAgICAgICAgIHJlID0gcmUuc3Vic3RyaW5nKDAsIHJlQ2xhc3NTdGFydCkgKyAnKD86JC4pJyAvLyBtYXRjaCBub3RoaW5nIGV2ZXJcbiAgICAgICAgICB9XG4gICAgICAgICAgaGFzTWFnaWMgPSB0cnVlXG4gICAgICAgICAgaW5DbGFzcyA9IGZhbHNlXG4gICAgICAgIGNvbnRpbnVlXG5cbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAvLyBzd2FsbG93IGFueSBzdGF0ZSBjaGFyIHRoYXQgd2Fzbid0IGNvbnN1bWVkXG4gICAgICAgICAgY2xlYXJTdGF0ZUNoYXIoKVxuXG4gICAgICAgICAgaWYgKHJlU3BlY2lhbHNbY10gJiYgIShjID09PSAnXicgJiYgaW5DbGFzcykpIHtcbiAgICAgICAgICAgIHJlICs9ICdcXFxcJ1xuICAgICAgICAgIH1cblxuICAgICAgICAgIHJlICs9IGNcbiAgICAgICAgICBicmVha1xuXG4gICAgICB9IC8vIHN3aXRjaFxuICAgIH0gLy8gZm9yXG5cbiAgICAvLyBoYW5kbGUgdGhlIGNhc2Ugd2hlcmUgd2UgbGVmdCBhIGNsYXNzIG9wZW4uXG4gICAgLy8gXCJbYWJjXCIgaXMgdmFsaWQsIGVxdWl2YWxlbnQgdG8gXCJcXFthYmNcIlxuICAgIGlmIChpbkNsYXNzKSB7XG4gICAgICAvLyBzcGxpdCB3aGVyZSB0aGUgbGFzdCBbIHdhcywgYW5kIGVzY2FwZSBpdFxuICAgICAgLy8gdGhpcyBpcyBhIGh1Z2UgcGl0YS4gIFdlIG5vdyBoYXZlIHRvIHJlLXdhbGtcbiAgICAgIC8vIHRoZSBjb250ZW50cyBvZiB0aGUgd291bGQtYmUgY2xhc3MgdG8gcmUtdHJhbnNsYXRlXG4gICAgICAvLyBhbnkgY2hhcmFjdGVycyB0aGF0IHdlcmUgcGFzc2VkIHRocm91Z2ggYXMtaXNcbiAgICAgIGNzID0gcGF0dGVybi5zbGljZShjbGFzc1N0YXJ0ICsgMSlcbiAgICAgIHNwID0gdGhpcy5wYXJzZShjcywgU1VCUEFSU0UpXG4gICAgICByZSA9IHJlLnN1YnN0cmluZygwLCByZUNsYXNzU3RhcnQpICsgJ1xcXFxbJyArIHNwWzBdXG4gICAgICBoYXNNYWdpYyA9IGhhc01hZ2ljIHx8IHNwWzFdXG4gICAgfVxuXG4gICAgLy8gaGFuZGxlIHRoZSBjYXNlIHdoZXJlIHdlIGhhZCBhICsoIHRoaW5nIGF0IHRoZSAqZW5kKlxuICAgIC8vIG9mIHRoZSBwYXR0ZXJuLlxuICAgIC8vIGVhY2ggcGF0dGVybiBsaXN0IHN0YWNrIGFkZHMgMyBjaGFycywgYW5kIHdlIG5lZWQgdG8gZ28gdGhyb3VnaFxuICAgIC8vIGFuZCBlc2NhcGUgYW55IHwgY2hhcnMgdGhhdCB3ZXJlIHBhc3NlZCB0aHJvdWdoIGFzLWlzIGZvciB0aGUgcmVnZXhwLlxuICAgIC8vIEdvIHRocm91Z2ggYW5kIGVzY2FwZSB0aGVtLCB0YWtpbmcgY2FyZSBub3QgdG8gZG91YmxlLWVzY2FwZSBhbnlcbiAgICAvLyB8IGNoYXJzIHRoYXQgd2VyZSBhbHJlYWR5IGVzY2FwZWQuXG4gICAgZm9yIChwbCA9IHBhdHRlcm5MaXN0U3RhY2sucG9wKCk7IHBsOyBwbCA9IHBhdHRlcm5MaXN0U3RhY2sucG9wKCkpIHtcbiAgICAgIGxldCB0YWlsXG4gICAgICB0YWlsID0gcmUuc2xpY2UocGwucmVTdGFydCArIHBsLm9wZW4ubGVuZ3RoKVxuICAgICAgdGhpcy5kZWJ1Zygnc2V0dGluZyB0YWlsJywgcmUsIHBsKVxuICAgICAgLy8gbWF5YmUgc29tZSBldmVuIG51bWJlciBvZiBcXCwgdGhlbiBtYXliZSAxIFxcLCBmb2xsb3dlZCBieSBhIHxcbiAgICAgIHRhaWwgPSB0YWlsLnJlcGxhY2UoLygoPzpcXFxcezJ9KXswLDY0fSkoXFxcXD8pXFx8L2csIChfLCAkMSwgJDIpID0+IHtcbiAgICAgICAgLyogaXN0YW5idWwgaWdub3JlIGVsc2UgLSBzaG91bGQgYWxyZWFkeSBiZSBkb25lICovXG4gICAgICAgIGlmICghJDIpIHtcbiAgICAgICAgICAvLyB0aGUgfCBpc24ndCBhbHJlYWR5IGVzY2FwZWQsIHNvIGVzY2FwZSBpdC5cbiAgICAgICAgICAkMiA9ICdcXFxcJ1xuICAgICAgICB9XG5cbiAgICAgICAgLy8gbmVlZCB0byBlc2NhcGUgYWxsIHRob3NlIHNsYXNoZXMgKmFnYWluKiwgd2l0aG91dCBlc2NhcGluZyB0aGVcbiAgICAgICAgLy8gb25lIHRoYXQgd2UgbmVlZCBmb3IgZXNjYXBpbmcgdGhlIHwgY2hhcmFjdGVyLiAgQXMgaXQgd29ya3Mgb3V0LFxuICAgICAgICAvLyBlc2NhcGluZyBhbiBldmVuIG51bWJlciBvZiBzbGFzaGVzIGNhbiBiZSBkb25lIGJ5IHNpbXBseSByZXBlYXRpbmdcbiAgICAgICAgLy8gaXQgZXhhY3RseSBhZnRlciBpdHNlbGYuICBUaGF0J3Mgd2h5IHRoaXMgdHJpY2sgd29ya3MuXG4gICAgICAgIC8vXG4gICAgICAgIC8vIEkgYW0gc29ycnkgdGhhdCB5b3UgaGF2ZSB0byBzZWUgdGhpcy5cbiAgICAgICAgcmV0dXJuICQxICsgJDEgKyAkMiArICd8J1xuICAgICAgfSlcblxuICAgICAgdGhpcy5kZWJ1ZygndGFpbD0lalxcbiAgICVzJywgdGFpbCwgdGFpbCwgcGwsIHJlKVxuICAgICAgY29uc3QgdCA9IHBsLnR5cGUgPT09ICcqJyA/IHN0YXJcbiAgICAgICAgOiBwbC50eXBlID09PSAnPycgPyBxbWFya1xuICAgICAgICA6ICdcXFxcJyArIHBsLnR5cGVcblxuICAgICAgaGFzTWFnaWMgPSB0cnVlXG4gICAgICByZSA9IHJlLnNsaWNlKDAsIHBsLnJlU3RhcnQpICsgdCArICdcXFxcKCcgKyB0YWlsXG4gICAgfVxuXG4gICAgLy8gaGFuZGxlIHRyYWlsaW5nIHRoaW5ncyB0aGF0IG9ubHkgbWF0dGVyIGF0IHRoZSB2ZXJ5IGVuZC5cbiAgICBjbGVhclN0YXRlQ2hhcigpXG4gICAgaWYgKGVzY2FwaW5nKSB7XG4gICAgICAvLyB0cmFpbGluZyBcXFxcXG4gICAgICByZSArPSAnXFxcXFxcXFwnXG4gICAgfVxuXG4gICAgLy8gb25seSBuZWVkIHRvIGFwcGx5IHRoZSBub2RvdCBzdGFydCBpZiB0aGUgcmUgc3RhcnRzIHdpdGhcbiAgICAvLyBzb21ldGhpbmcgdGhhdCBjb3VsZCBjb25jZWl2YWJseSBjYXB0dXJlIGEgZG90XG4gICAgY29uc3QgYWRkUGF0dGVyblN0YXJ0ID0gYWRkUGF0dGVyblN0YXJ0U2V0W3JlLmNoYXJBdCgwKV1cblxuICAgIC8vIEhhY2sgdG8gd29yayBhcm91bmQgbGFjayBvZiBuZWdhdGl2ZSBsb29rYmVoaW5kIGluIEpTXG4gICAgLy8gQSBwYXR0ZXJuIGxpa2U6ICouISh4KS4hKHl8eikgbmVlZHMgdG8gZW5zdXJlIHRoYXQgYSBuYW1lXG4gICAgLy8gbGlrZSAnYS54eXoueXonIGRvZXNuJ3QgbWF0Y2guICBTbywgdGhlIGZpcnN0IG5lZ2F0aXZlXG4gICAgLy8gbG9va2FoZWFkLCBoYXMgdG8gbG9vayBBTEwgdGhlIHdheSBhaGVhZCwgdG8gdGhlIGVuZCBvZlxuICAgIC8vIHRoZSBwYXR0ZXJuLlxuICAgIGZvciAobGV0IG4gPSBuZWdhdGl2ZUxpc3RzLmxlbmd0aCAtIDE7IG4gPiAtMTsgbi0tKSB7XG4gICAgICBjb25zdCBubCA9IG5lZ2F0aXZlTGlzdHNbbl1cblxuICAgICAgY29uc3QgbmxCZWZvcmUgPSByZS5zbGljZSgwLCBubC5yZVN0YXJ0KVxuICAgICAgY29uc3QgbmxGaXJzdCA9IHJlLnNsaWNlKG5sLnJlU3RhcnQsIG5sLnJlRW5kIC0gOClcbiAgICAgIGxldCBubEFmdGVyID0gcmUuc2xpY2UobmwucmVFbmQpXG4gICAgICBjb25zdCBubExhc3QgPSByZS5zbGljZShubC5yZUVuZCAtIDgsIG5sLnJlRW5kKSArIG5sQWZ0ZXJcblxuICAgICAgLy8gSGFuZGxlIG5lc3RlZCBzdHVmZiBsaWtlICooKi5qc3whKCouanNvbikpLCB3aGVyZSBvcGVuIHBhcmVuc1xuICAgICAgLy8gbWVhbiB0aGF0IHdlIHNob3VsZCAqbm90KiBpbmNsdWRlIHRoZSApIGluIHRoZSBiaXQgdGhhdCBpcyBjb25zaWRlcmVkXG4gICAgICAvLyBcImFmdGVyXCIgdGhlIG5lZ2F0ZWQgc2VjdGlvbi5cbiAgICAgIGNvbnN0IGNsb3NlUGFyZW5zQmVmb3JlID0gbmxCZWZvcmUuc3BsaXQoJyknKS5sZW5ndGhcbiAgICAgIGNvbnN0IG9wZW5QYXJlbnNCZWZvcmUgPSBubEJlZm9yZS5zcGxpdCgnKCcpLmxlbmd0aCAtIGNsb3NlUGFyZW5zQmVmb3JlXG4gICAgICBsZXQgY2xlYW5BZnRlciA9IG5sQWZ0ZXJcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgb3BlblBhcmVuc0JlZm9yZTsgaSsrKSB7XG4gICAgICAgIGNsZWFuQWZ0ZXIgPSBjbGVhbkFmdGVyLnJlcGxhY2UoL1xcKVsrKj9dPy8sICcnKVxuICAgICAgfVxuICAgICAgbmxBZnRlciA9IGNsZWFuQWZ0ZXJcblxuICAgICAgY29uc3QgZG9sbGFyID0gbmxBZnRlciA9PT0gJycgJiYgaXNTdWIgIT09IFNVQlBBUlNFID8gJyg/OiR8XFxcXC8pJyA6ICcnXG5cbiAgICAgIHJlID0gbmxCZWZvcmUgKyBubEZpcnN0ICsgbmxBZnRlciArIGRvbGxhciArIG5sTGFzdFxuICAgIH1cblxuICAgIC8vIGlmIHRoZSByZSBpcyBub3QgXCJcIiBhdCB0aGlzIHBvaW50LCB0aGVuIHdlIG5lZWQgdG8gbWFrZSBzdXJlXG4gICAgLy8gaXQgZG9lc24ndCBtYXRjaCBhZ2FpbnN0IGFuIGVtcHR5IHBhdGggcGFydC5cbiAgICAvLyBPdGhlcndpc2UgYS8qIHdpbGwgbWF0Y2ggYS8sIHdoaWNoIGl0IHNob3VsZCBub3QuXG4gICAgaWYgKHJlICE9PSAnJyAmJiBoYXNNYWdpYykge1xuICAgICAgcmUgPSAnKD89LiknICsgcmVcbiAgICB9XG5cbiAgICBpZiAoYWRkUGF0dGVyblN0YXJ0KSB7XG4gICAgICByZSA9IHBhdHRlcm5TdGFydCgpICsgcmVcbiAgICB9XG5cbiAgICAvLyBwYXJzaW5nIGp1c3QgYSBwaWVjZSBvZiBhIGxhcmdlciBwYXR0ZXJuLlxuICAgIGlmIChpc1N1YiA9PT0gU1VCUEFSU0UpIHtcbiAgICAgIHJldHVybiBbcmUsIGhhc01hZ2ljXVxuICAgIH1cblxuICAgIC8vIGlmIGl0J3Mgbm9jYXNlLCBhbmQgdGhlIGxjYXNlL3VwcGVyY2FzZSBkb24ndCBtYXRjaCwgaXQncyBtYWdpY1xuICAgIGlmIChvcHRpb25zLm5vY2FzZSAmJiAhaGFzTWFnaWMpIHtcbiAgICAgIGhhc01hZ2ljID0gcGF0dGVybi50b1VwcGVyQ2FzZSgpICE9PSBwYXR0ZXJuLnRvTG93ZXJDYXNlKClcbiAgICB9XG5cbiAgICAvLyBza2lwIHRoZSByZWdleHAgZm9yIG5vbi1tYWdpY2FsIHBhdHRlcm5zXG4gICAgLy8gdW5lc2NhcGUgYW55dGhpbmcgaW4gaXQsIHRob3VnaCwgc28gdGhhdCBpdCdsbCBiZVxuICAgIC8vIGFuIGV4YWN0IG1hdGNoIGFnYWluc3QgYSBmaWxlIGV0Yy5cbiAgICBpZiAoIWhhc01hZ2ljKSB7XG4gICAgICByZXR1cm4gZ2xvYlVuZXNjYXBlKHBhdHRlcm4pXG4gICAgfVxuXG4gICAgY29uc3QgZmxhZ3MgPSBvcHRpb25zLm5vY2FzZSA/ICdpJyA6ICcnXG4gICAgdHJ5IHtcbiAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKG5ldyBSZWdFeHAoJ14nICsgcmUgKyAnJCcsIGZsYWdzKSwge1xuICAgICAgICBfZ2xvYjogcGF0dGVybixcbiAgICAgICAgX3NyYzogcmUsXG4gICAgICB9KVxuICAgIH0gY2F0Y2ggKGVyKSAvKiBpc3RhbmJ1bCBpZ25vcmUgbmV4dCAtIHNob3VsZCBiZSBpbXBvc3NpYmxlICovIHtcbiAgICAgIC8vIElmIGl0IHdhcyBhbiBpbnZhbGlkIHJlZ3VsYXIgZXhwcmVzc2lvbiwgdGhlbiBpdCBjYW4ndCBtYXRjaFxuICAgICAgLy8gYW55dGhpbmcuICBUaGlzIHRyaWNrIGxvb2tzIGZvciBhIGNoYXJhY3RlciBhZnRlciB0aGUgZW5kIG9mXG4gICAgICAvLyB0aGUgc3RyaW5nLCB3aGljaCBpcyBvZiBjb3Vyc2UgaW1wb3NzaWJsZSwgZXhjZXB0IGluIG11bHRpLWxpbmVcbiAgICAgIC8vIG1vZGUsIGJ1dCBpdCdzIG5vdCBhIC9tIHJlZ2V4LlxuICAgICAgcmV0dXJuIG5ldyBSZWdFeHAoJyQuJylcbiAgICB9XG4gIH1cblxuICBtYWtlUmUgKCkge1xuICAgIGlmICh0aGlzLnJlZ2V4cCB8fCB0aGlzLnJlZ2V4cCA9PT0gZmFsc2UpIHJldHVybiB0aGlzLnJlZ2V4cFxuXG4gICAgLy8gYXQgdGhpcyBwb2ludCwgdGhpcy5zZXQgaXMgYSAyZCBhcnJheSBvZiBwYXJ0aWFsXG4gICAgLy8gcGF0dGVybiBzdHJpbmdzLCBvciBcIioqXCIuXG4gICAgLy9cbiAgICAvLyBJdCdzIGJldHRlciB0byB1c2UgLm1hdGNoKCkuICBUaGlzIGZ1bmN0aW9uIHNob3VsZG4ndFxuICAgIC8vIGJlIHVzZWQsIHJlYWxseSwgYnV0IGl0J3MgcHJldHR5IGNvbnZlbmllbnQgc29tZXRpbWVzLFxuICAgIC8vIHdoZW4geW91IGp1c3Qgd2FudCB0byB3b3JrIHdpdGggYSByZWdleC5cbiAgICBjb25zdCBzZXQgPSB0aGlzLnNldFxuXG4gICAgaWYgKCFzZXQubGVuZ3RoKSB7XG4gICAgICB0aGlzLnJlZ2V4cCA9IGZhbHNlXG4gICAgICByZXR1cm4gdGhpcy5yZWdleHBcbiAgICB9XG4gICAgY29uc3Qgb3B0aW9ucyA9IHRoaXMub3B0aW9uc1xuXG4gICAgY29uc3QgdHdvU3RhciA9IG9wdGlvbnMubm9nbG9ic3RhciA/IHN0YXJcbiAgICAgIDogb3B0aW9ucy5kb3QgPyB0d29TdGFyRG90XG4gICAgICA6IHR3b1N0YXJOb0RvdFxuICAgIGNvbnN0IGZsYWdzID0gb3B0aW9ucy5ub2Nhc2UgPyAnaScgOiAnJ1xuXG4gICAgLy8gY29hbGVzY2UgZ2xvYnN0YXJzIGFuZCByZWdleHBpZnkgbm9uLWdsb2JzdGFyIHBhdHRlcm5zXG4gICAgLy8gaWYgaXQncyB0aGUgb25seSBpdGVtLCB0aGVuIHdlIGp1c3QgZG8gb25lIHR3b1N0YXJcbiAgICAvLyBpZiBpdCdzIHRoZSBmaXJzdCwgYW5kIHRoZXJlIGFyZSBtb3JlLCBwcmVwZW5kIChcXC98dHdvU3RhclxcLyk/IHRvIG5leHRcbiAgICAvLyBpZiBpdCdzIHRoZSBsYXN0LCBhcHBlbmQgKFxcL3R3b1N0YXJ8KSB0byBwcmV2aW91c1xuICAgIC8vIGlmIGl0J3MgaW4gdGhlIG1pZGRsZSwgYXBwZW5kIChcXC98XFwvdHdvU3RhclxcLykgdG8gcHJldmlvdXNcbiAgICAvLyB0aGVuIGZpbHRlciBvdXQgR0xPQlNUQVIgc3ltYm9sc1xuICAgIGxldCByZSA9IHNldC5tYXAocGF0dGVybiA9PiB7XG4gICAgICBwYXR0ZXJuID0gcGF0dGVybi5tYXAocCA9PlxuICAgICAgICB0eXBlb2YgcCA9PT0gJ3N0cmluZycgPyByZWdFeHBFc2NhcGUocClcbiAgICAgICAgOiBwID09PSBHTE9CU1RBUiA/IEdMT0JTVEFSXG4gICAgICAgIDogcC5fc3JjXG4gICAgICApLnJlZHVjZSgoc2V0LCBwKSA9PiB7XG4gICAgICAgIGlmICghKHNldFtzZXQubGVuZ3RoIC0gMV0gPT09IEdMT0JTVEFSICYmIHAgPT09IEdMT0JTVEFSKSkge1xuICAgICAgICAgIHNldC5wdXNoKHApXG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHNldFxuICAgICAgfSwgW10pXG4gICAgICBwYXR0ZXJuLmZvckVhY2goKHAsIGkpID0+IHtcbiAgICAgICAgaWYgKHAgIT09IEdMT0JTVEFSIHx8IHBhdHRlcm5baS0xXSA9PT0gR0xPQlNUQVIpIHtcbiAgICAgICAgICByZXR1cm5cbiAgICAgICAgfVxuICAgICAgICBpZiAoaSA9PT0gMCkge1xuICAgICAgICAgIGlmIChwYXR0ZXJuLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgIHBhdHRlcm5baSsxXSA9ICcoPzpcXFxcXFwvfCcgKyB0d29TdGFyICsgJ1xcXFxcXC8pPycgKyBwYXR0ZXJuW2krMV1cbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcGF0dGVybltpXSA9IHR3b1N0YXJcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSBpZiAoaSA9PT0gcGF0dGVybi5sZW5ndGggLSAxKSB7XG4gICAgICAgICAgcGF0dGVybltpLTFdICs9ICcoPzpcXFxcXFwvfCcgKyB0d29TdGFyICsgJyk/J1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHBhdHRlcm5baS0xXSArPSAnKD86XFxcXFxcL3xcXFxcXFwvJyArIHR3b1N0YXIgKyAnXFxcXFxcLyknICsgcGF0dGVybltpKzFdXG4gICAgICAgICAgcGF0dGVybltpKzFdID0gR0xPQlNUQVJcbiAgICAgICAgfVxuICAgICAgfSlcbiAgICAgIHJldHVybiBwYXR0ZXJuLmZpbHRlcihwID0+IHAgIT09IEdMT0JTVEFSKS5qb2luKCcvJylcbiAgICB9KS5qb2luKCd8JylcblxuICAgIC8vIG11c3QgbWF0Y2ggZW50aXJlIHBhdHRlcm5cbiAgICAvLyBlbmRpbmcgaW4gYSAqIG9yICoqIHdpbGwgbWFrZSBpdCBsZXNzIHN0cmljdC5cbiAgICByZSA9ICdeKD86JyArIHJlICsgJykkJ1xuXG4gICAgLy8gY2FuIG1hdGNoIGFueXRoaW5nLCBhcyBsb25nIGFzIGl0J3Mgbm90IHRoaXMuXG4gICAgaWYgKHRoaXMubmVnYXRlKSByZSA9ICdeKD8hJyArIHJlICsgJykuKiQnXG5cbiAgICB0cnkge1xuICAgICAgdGhpcy5yZWdleHAgPSBuZXcgUmVnRXhwKHJlLCBmbGFncylcbiAgICB9IGNhdGNoIChleCkgLyogaXN0YW5idWwgaWdub3JlIG5leHQgLSBzaG91bGQgYmUgaW1wb3NzaWJsZSAqLyB7XG4gICAgICB0aGlzLnJlZ2V4cCA9IGZhbHNlXG4gICAgfVxuICAgIHJldHVybiB0aGlzLnJlZ2V4cFxuICB9XG5cbiAgbWF0Y2ggKGYsIHBhcnRpYWwgPSB0aGlzLnBhcnRpYWwpIHtcbiAgICB0aGlzLmRlYnVnKCdtYXRjaCcsIGYsIHRoaXMucGF0dGVybilcbiAgICAvLyBzaG9ydC1jaXJjdWl0IGluIHRoZSBjYXNlIG9mIGJ1c3RlZCB0aGluZ3MuXG4gICAgLy8gY29tbWVudHMsIGV0Yy5cbiAgICBpZiAodGhpcy5jb21tZW50KSByZXR1cm4gZmFsc2VcbiAgICBpZiAodGhpcy5lbXB0eSkgcmV0dXJuIGYgPT09ICcnXG5cbiAgICBpZiAoZiA9PT0gJy8nICYmIHBhcnRpYWwpIHJldHVybiB0cnVlXG5cbiAgICBjb25zdCBvcHRpb25zID0gdGhpcy5vcHRpb25zXG5cbiAgICAvLyB3aW5kb3dzOiBuZWVkIHRvIHVzZSAvLCBub3QgXFxcbiAgICBpZiAocGF0aC5zZXAgIT09ICcvJykge1xuICAgICAgZiA9IGYuc3BsaXQocGF0aC5zZXApLmpvaW4oJy8nKVxuICAgIH1cblxuICAgIC8vIHRyZWF0IHRoZSB0ZXN0IHBhdGggYXMgYSBzZXQgb2YgcGF0aHBhcnRzLlxuICAgIGYgPSBmLnNwbGl0KHNsYXNoU3BsaXQpXG4gICAgdGhpcy5kZWJ1Zyh0aGlzLnBhdHRlcm4sICdzcGxpdCcsIGYpXG5cbiAgICAvLyBqdXN0IE9ORSBvZiB0aGUgcGF0dGVybiBzZXRzIGluIHRoaXMuc2V0IG5lZWRzIHRvIG1hdGNoXG4gICAgLy8gaW4gb3JkZXIgZm9yIGl0IHRvIGJlIHZhbGlkLiAgSWYgbmVnYXRpbmcsIHRoZW4ganVzdCBvbmVcbiAgICAvLyBtYXRjaCBtZWFucyB0aGF0IHdlIGhhdmUgZmFpbGVkLlxuICAgIC8vIEVpdGhlciB3YXksIHJldHVybiBvbiB0aGUgZmlyc3QgaGl0LlxuXG4gICAgY29uc3Qgc2V0ID0gdGhpcy5zZXRcbiAgICB0aGlzLmRlYnVnKHRoaXMucGF0dGVybiwgJ3NldCcsIHNldClcblxuICAgIC8vIEZpbmQgdGhlIGJhc2VuYW1lIG9mIHRoZSBwYXRoIGJ5IGxvb2tpbmcgZm9yIHRoZSBsYXN0IG5vbi1lbXB0eSBzZWdtZW50XG4gICAgbGV0IGZpbGVuYW1lXG4gICAgZm9yIChsZXQgaSA9IGYubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIHtcbiAgICAgIGZpbGVuYW1lID0gZltpXVxuICAgICAgaWYgKGZpbGVuYW1lKSBicmVha1xuICAgIH1cblxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2V0Lmxlbmd0aDsgaSsrKSB7XG4gICAgICBjb25zdCBwYXR0ZXJuID0gc2V0W2ldXG4gICAgICBsZXQgZmlsZSA9IGZcbiAgICAgIGlmIChvcHRpb25zLm1hdGNoQmFzZSAmJiBwYXR0ZXJuLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICBmaWxlID0gW2ZpbGVuYW1lXVxuICAgICAgfVxuICAgICAgY29uc3QgaGl0ID0gdGhpcy5tYXRjaE9uZShmaWxlLCBwYXR0ZXJuLCBwYXJ0aWFsKVxuICAgICAgaWYgKGhpdCkge1xuICAgICAgICBpZiAob3B0aW9ucy5mbGlwTmVnYXRlKSByZXR1cm4gdHJ1ZVxuICAgICAgICByZXR1cm4gIXRoaXMubmVnYXRlXG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gZGlkbid0IGdldCBhbnkgaGl0cy4gIHRoaXMgaXMgc3VjY2VzcyBpZiBpdCdzIGEgbmVnYXRpdmVcbiAgICAvLyBwYXR0ZXJuLCBmYWlsdXJlIG90aGVyd2lzZS5cbiAgICBpZiAob3B0aW9ucy5mbGlwTmVnYXRlKSByZXR1cm4gZmFsc2VcbiAgICByZXR1cm4gdGhpcy5uZWdhdGVcbiAgfVxuXG4gIHN0YXRpYyBkZWZhdWx0cyAoZGVmKSB7XG4gICAgcmV0dXJuIG1pbmltYXRjaC5kZWZhdWx0cyhkZWYpLk1pbmltYXRjaFxuICB9XG59XG5cbm1pbmltYXRjaC5NaW5pbWF0Y2ggPSBNaW5pbWF0Y2hcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/readdir-glob/node_modules/minimatch/minimatch.js\n");

/***/ })

};
;