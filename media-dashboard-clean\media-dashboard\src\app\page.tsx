'use client';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthGuard';
import { useEffect, useState } from 'react';

export default function Home() {
  const router = useRouter();
  const { user, logout, hasPermission } = useAuth();
  const [showUserInfo, setShowUserInfo] = useState(false);

  useEffect(() => {
    // إذا لم يكن المستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول
    if (!user && !localStorage.getItem('user')) {
      router.push('/login');
    }
  }, [user, router]);
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* شريط المستخدم */}
        {user && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.95)',
            borderRadius: '15px',
            padding: '20px',
            marginBottom: '20px',
            boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            backdropFilter: 'blur(10px)'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <div style={{
                width: '50px',
                height: '50px',
                borderRadius: '50%',
                overflow: 'hidden',
                background: 'white',
                border: '2px solid #667eea',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <img
                  src="/images/logo.jpeg"
                  alt="شعار النظام"
                  style={{
                    width: '40px',
                    height: '40px',
                    objectFit: 'contain'
                  }}
                  onError={(e) => {
                    // في حالة عدم وجود الصورة، عرض الحرف الأول
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.parentElement.innerHTML = `
                      <div style="
                        width: 50px;
                        height: 50px;
                        background: linear-gradient(45deg, #667eea, #764ba2);
                        borderRadius: 50%;
                        display: flex;
                        alignItems: center;
                        justifyContent: center;
                        color: white;
                        fontSize: 1.5rem;
                        fontWeight: bold;
                      ">${user.name.charAt(0)}</div>
                    `;
                  }}
                />
              </div>
              <div>
                <h3 style={{ margin: 0, color: '#333', fontSize: '1.2rem' }}>
                  مرحباً، {user.name}
                </h3>
                <p style={{ margin: 0, color: '#6c757d', fontSize: '0.9rem' }}>
                  {user.role === 'ADMIN' && '👑 مدير النظام'}
                  {user.role === 'MEDIA_MANAGER' && '📝 مدير المحتوى'}
                  {user.role === 'SCHEDULER' && '📅 مجدول البرامج'}
                  {user.role === 'VIEWER' && '👁️ مستخدم عرض'}
                </p>
              </div>
            </div>
            <div style={{ display: 'flex', gap: '10px' }}>
              {user.role === 'ADMIN' && (
                <button
                  onClick={() => router.push('/admin-dashboard')}
                  style={{
                    background: 'linear-gradient(45deg, #dc3545, #c82333)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '8px 15px',
                    cursor: 'pointer',
                    fontSize: '0.9rem'
                  }}
                >
                  👑 لوحة المدير
                </button>
              )}
              <button
                onClick={() => setShowUserInfo(!showUserInfo)}
                style={{
                  background: '#f8f9fa',
                  color: '#333',
                  border: '1px solid #dee2e6',
                  borderRadius: '8px',
                  padding: '8px 15px',
                  cursor: 'pointer',
                  fontSize: '0.9rem'
                }}
              >
                ⚙️ الإعدادات
              </button>
              <button
                onClick={logout}
                style={{
                  background: 'linear-gradient(45deg, #6c757d, #495057)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 15px',
                  cursor: 'pointer',
                  fontSize: '0.9rem'
                }}
              >
                🚪 خروج
              </button>
            </div>
          </div>
        )}

        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '40px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            color: '#333',
            textAlign: 'center',
            marginBottom: '40px',
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            🎬 نظام إدارة المحتوى الإعلامي
          </h1>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          marginBottom: '40px'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #1976d215 0%, #1976d225 100%)',
            border: '2px solid #1976d230',
            borderRadius: '15px',
            padding: '20px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '10px' }}>🎬</div>
            <h3 style={{ color: '#1976d2', margin: '10px 0', fontSize: '1.2rem' }}>إجمالي المواد</h3>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1976d2' }}>1,234</div>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #2e7d3215 0%, #2e7d3225 100%)',
            border: '2px solid #2e7d3230',
            borderRadius: '15px',
            padding: '20px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '10px' }}>📺</div>
            <h3 style={{ color: '#2e7d32', margin: '10px 0', fontSize: '1.2rem' }}>البرامج النشطة</h3>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#2e7d32' }}>89</div>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #ed6c0215 0%, #ed6c0225 100%)',
            border: '2px solid #ed6c0230',
            borderRadius: '15px',
            padding: '20px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '10px' }}>🎵</div>
            <h3 style={{ color: '#ed6c02', margin: '10px 0', fontSize: '1.2rem' }}>الأغاني</h3>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#ed6c02' }}>456</div>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #d32f2f15 0%, #d32f2f25 100%)',
            border: '2px solid #d32f2f30',
            borderRadius: '15px',
            padding: '20px',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '10px' }}>❌</div>
            <h3 style={{ color: '#d32f2f', margin: '10px 0', fontSize: '1.2rem' }}>المواد المرفوضة</h3>
            <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#d32f2f' }}>12</div>
          </div>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          gap: '20px',
          marginTop: '40px'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            borderRadius: '15px',
            padding: '30px',
            border: '1px solid #e0e0e0'
          }}>
            <h2 style={{ color: '#333', marginBottom: '20px', fontSize: '1.5rem' }}>📊 النشاط الأخير</h2>
            <div style={{ textAlign: 'center', color: '#666', padding: '40px' }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>📈</div>
              <p>لا توجد أنشطة حديثة</p>
            </div>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
            borderRadius: '15px',
            padding: '30px',
            border: '1px solid #e0e0e0'
          }}>
            <h2 style={{ color: '#333', marginBottom: '20px', fontSize: '1.5rem' }}>🔔 الإشعارات</h2>
            <div style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>🔕</div>
              <p>لا توجد إشعارات جديدة</p>
            </div>
          </div>
        </div>

        <div style={{
          marginTop: '40px',
          display: 'flex',
          justifyContent: 'center',
          gap: '20px',
          flexWrap: 'wrap'
        }}>
          {hasPermission('MEDIA_CREATE') && (
            <button
              onClick={() => router.push('/add-media')}
              style={{
              background: 'linear-gradient(45deg, #2e7d32, #2e7d32dd)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
              transition: 'transform 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              ➕ إضافة مادة إعلامية
            </button>
          )}

          {hasPermission('MEDIA_READ') && (
            <button
              onClick={() => router.push('/media-list')}
              style={{
              background: 'linear-gradient(45deg, #f093fb, #f5576c)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
              transition: 'transform 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              📚 قائمة المواد الإعلامية
            </button>
          )}

          {user?.role === 'ADMIN' && (
            <button
              onClick={() => router.push('/statistics')}
              style={{
              background: 'linear-gradient(45deg, #9c27b0, #7b1fa2)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
              transition: 'transform 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              📊 إحصائيات النظام
            </button>
          )}









          {hasPermission('SCHEDULE_READ') && (
            <button
              onClick={() => router.push('/weekly-schedule')}
              style={{
              background: 'linear-gradient(45deg, #673ab7, #512da8)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
              transition: 'transform 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              📅 الخريطة البرامجية الأسبوعية
            </button>
          )}

          {user?.role === 'ADMIN' && (
            <button
              onClick={() => router.push('/export')}
              style={{
              background: 'linear-gradient(45deg, #9c27b0, #9c27b0dd)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
              transition: 'transform 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              📤 تصدير البيانات
            </button>
          )}
        </div>

        <div style={{
          marginTop: '40px',
          textAlign: 'center',
          padding: '20px',
          background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
          borderRadius: '15px',
          border: '1px solid #e0e0e0'
        }}>
          <h3 style={{ color: '#333', marginBottom: '10px' }}>🚀 مرحباً بك في نظام إدارة المحتوى الإعلامي المتطور</h3>
          <p style={{ color: '#666', lineHeight: '1.6' }}>
            نظام شامل لإدارة المواد الإعلامية مع دعم كامل للغة العربية وواجهة مستخدم عصرية
          </p>
          <div style={{ marginTop: '20px' }}>
            <p style={{ color: '#555', fontSize: '0.9rem' }}>
              ✨ الميزات الرئيسية: إدارة المواد الإعلامية • الخريطة البرامجية الأسبوعية • جدول الإذاعة اليومية • تصدير إلى Excel مع دعم RTL
            </p>
          </div>
          </div>
        </div>
      </div>
    </div>
  );
}
