'use client';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthGuard';
import { useEffect, useState } from 'react';

export default function Home() {
  const router = useRouter();
  const { user, logout, hasPermission } = useAuth();
  const [showUserInfo, setShowUserInfo] = useState(false);

  useEffect(() => {
    // إذا لم يكن المستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول
    if (!user && !localStorage.getItem('user')) {
      router.replace('/login');
    } else {
      // إذا كان المستخدم مسجل دخول، توجيه للوحة التحكم الجديدة
      router.replace('/dashboard');
    }
  }, [user, router]);
  return (
    <div style={{
      minHeight: '100vh',
      background: '#1a1d29',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        textAlign: 'center',
        color: 'white'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '2rem',
          fontWeight: '900',
          fontFamily: 'Arial, sans-serif',
          gap: '8px',
          marginBottom: '30px'
        }}>
          <span style={{
            background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: '900',
            fontSize: '3rem'
          }}>
            X
          </span>
          <span style={{
            color: '#6c757d',
            fontSize: '2rem',
            fontWeight: '300'
          }}>
            -
          </span>
          <span style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: '800',
            letterSpacing: '1px'
          }}>
            Prime
          </span>
        </div>

        <div style={{ fontSize: '3rem', marginBottom: '20px' }}>⏳</div>
        <div style={{ fontSize: '1.5rem', marginBottom: '10px' }}>جاري التحميل...</div>
        <div style={{ color: '#a0aec0', fontSize: '1rem' }}>يتم توجيهك للوحة التحكم</div>
      </div>
    </div>
  );
}
