'use client';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthGuard';
import { useEffect, useState } from 'react';

export default function Home() {
  const router = useRouter();
  const { user, logout, hasPermission } = useAuth();
  const [showUserInfo, setShowUserInfo] = useState(false);

  useEffect(() => {
    // إذا لم يكن المستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول
    if (!user && !localStorage.getItem('user')) {
      router.replace('/login');
    } else {
      // تأخير إعادة التوجيه لإظهار الأزرار
      const timer = setTimeout(() => {
        router.replace('/dashboard');
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [user, router]);
  return (
    <div style={{
      minHeight: '100vh',
      background: '#1a1d29',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        textAlign: 'center',
        color: 'white'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '2rem',
          fontWeight: '900',
          fontFamily: 'Arial, sans-serif',
          gap: '8px',
          marginBottom: '30px'
        }}>
          <span style={{
            background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: '900',
            fontSize: '3rem'
          }}>
            X
          </span>
          <span style={{
            color: '#6c757d',
            fontSize: '2rem',
            fontWeight: '300'
          }}>
            -
          </span>
          <span style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: '800',
            letterSpacing: '1px'
          }}>
            Prime
          </span>
        </div>

        <div style={{ fontSize: '3rem', marginBottom: '20px' }}>⏳</div>
        <div style={{ fontSize: '1.5rem', marginBottom: '20px' }}>جاري التحميل...</div>

        {/* أزرار التنقل السريع */}
        {user && (
          <div style={{
            display: 'flex',
            gap: '15px',
            justifyContent: 'center',
            marginBottom: '20px',
            flexWrap: 'wrap'
          }}>
            <button
              onClick={() => router.push('/dashboard')}
              style={{
                background: 'linear-gradient(45deg, #667eea, #764ba2)',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '12px 20px',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: 'bold',
                transition: 'all 0.3s ease'
              }}
            >
              📊 لوحة التحكم
            </button>

            {hasPermission('SCHEDULE_READ') && (
              <button
                onClick={() => router.push('/daily-schedule')}
                style={{
                  background: 'linear-gradient(45deg, #10b981, #059669)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '12px 20px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease'
                }}
              >
                📊 الجدول الإذاعي اليومي
              </button>
            )}

            {hasPermission('SCHEDULE_READ') && (
              <button
                onClick={() => router.push('/weekly-schedule')}
                style={{
                  background: 'linear-gradient(45deg, #f59e0b, #d97706)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '12px 20px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease'
                }}
              >
                📅 الخريطة البرامجية
              </button>
            )}

            {hasPermission('MEDIA_READ') && (
              <button
                onClick={() => router.push('/media-list')}
                style={{
                  background: 'linear-gradient(45deg, #8b5cf6, #7c3aed)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '12px 20px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease'
                }}
              >
                🎬 المواد الإعلامية
              </button>
            )}
          </div>
        )}

        <div style={{ color: '#a0aec0', fontSize: '1rem' }}>أو انتظر للتوجه التلقائي للوحة التحكم</div>
      </div>
    </div>
  );
}
