"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin-dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin-dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddUser, setShowAddUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        role: 'VIEWER'\n    });\n    const roles = {\n        'ADMIN': {\n            name: 'مدير النظام',\n            color: '#dc3545',\n            icon: '👑'\n        },\n        'MEDIA_MANAGER': {\n            name: 'مدير المحتوى',\n            color: '#28a745',\n            icon: '📝'\n        },\n        'SCHEDULER': {\n            name: 'مجدول البرامج',\n            color: '#007bff',\n            icon: '📅'\n        },\n        'VIEWER': {\n            name: 'مستخدم عرض',\n            color: '#6c757d',\n            icon: '👁️'\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchUsers();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch('/api/users');\n            const result = await response.json();\n            if (result.success) {\n                setUsers(result.users);\n            } else {\n                showToast('خطأ في جلب بيانات المستخدمين', 'error');\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddUser = async (e)=>{\n        e.preventDefault();\n        if (!newUser.username || !newUser.password || !newUser.name) {\n            showToast('يرجى ملء جميع الحقول المطلوبة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch('/api/users', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newUser)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم إنشاء المستخدم بنجاح!', 'success');\n                setUsers([\n                    ...users,\n                    result.user\n                ]);\n                setShowAddUser(false);\n                setNewUser({\n                    username: '',\n                    password: '',\n                    name: '',\n                    email: '',\n                    role: 'VIEWER'\n                });\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error adding user:', error);\n            showToast('خطأ في إنشاء المستخدم', 'error');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;\n        try {\n            const response = await fetch(\"/api/users?id=\".concat(userId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حذف المستخدم بنجاح!', 'success');\n                setUsers(users.filter((u)=>u.id !== userId));\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error deleting user:', error);\n            showToast('خطأ في حذف المستخدم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredRole: \"ADMIN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                padding: '20px',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'white',\n                                borderRadius: '20px',\n                                padding: '30px',\n                                marginBottom: '20px',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.1)',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '150px',\n                                                height: '60px',\n                                                borderRadius: '15px',\n                                                overflow: 'hidden',\n                                                background: 'white',\n                                                border: '2px solid #667eea',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                padding: '8px'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/images/logo.jpeg\",\n                                                alt: \"شعار النظام\",\n                                                style: {\n                                                    width: '100%',\n                                                    height: '100%',\n                                                    objectFit: 'contain'\n                                                },\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = 'none';\n                                                    e.currentTarget.parentElement.innerHTML = '\\n                      <div style=\"\\n                        width: 140px;\\n                        height: 50px;\\n                        background: linear-gradient(45deg, #667eea, #764ba2);\\n                        borderRadius: 15px;\\n                        display: flex;\\n                        alignItems: center;\\n                        justifyContent: center;\\n                        fontSize: 1rem;\\n                        color: white;\\n                        fontWeight: bold;\\n                        textAlign: center;\\n                      \">\\uD83D\\uDC51 لوحة المدير</div>\\n                    ';\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        color: '#333',\n                                                        margin: '0 0 10px 0',\n                                                        background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: \"\\uD83D\\uDC51 لوحة تحكم المدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#6c757d',\n                                                        margin: 0\n                                                    },\n                                                    children: [\n                                                        \"مرحباً \",\n                                                        user === null || user === void 0 ? void 0 : user.name,\n                                                        \" - إدارة المستخدمين والصلاحيات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '10px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push('/'),\n                                            style: {\n                                                background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '10px',\n                                                padding: '10px 20px',\n                                                cursor: 'pointer',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"\\uD83C\\uDFE0 الرئيسية\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: logout,\n                                            style: {\n                                                background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '10px',\n                                                padding: '10px 20px',\n                                                cursor: 'pointer',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"\\uD83D\\uDEAA تسجيل الخروج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: Object.entries(roles).map((param)=>{\n                                let [roleKey, roleInfo] = param;\n                                const count = users.filter((u)=>u.role === roleKey).length;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'white',\n                                        borderRadius: '15px',\n                                        padding: '25px',\n                                        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',\n                                        textAlign: 'center',\n                                        border: \"3px solid \".concat(roleInfo.color, \"20\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: roleInfo.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: roleInfo.color,\n                                                margin: '0 0 5px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: roleInfo.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#333'\n                                            },\n                                            children: count\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '0.9rem',\n                                                margin: 0\n                                            },\n                                            children: \"مستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, roleKey, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'white',\n                                borderRadius: '20px',\n                                padding: '30px',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.1)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '25px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: '#333',\n                                                fontSize: '1.5rem',\n                                                margin: 0\n                                            },\n                                            children: [\n                                                \"\\uD83D\\uDC65 إدارة المستخدمين (\",\n                                                users.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAddUser(true),\n                                            style: {\n                                                background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '10px',\n                                                padding: '12px 20px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: \"➕ إضافة مستخدم جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this),\n                                showAddUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#f8f9fa',\n                                        borderRadius: '15px',\n                                        padding: '25px',\n                                        marginBottom: '25px',\n                                        border: '2px solid #e9ecef'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#333',\n                                                marginBottom: '20px'\n                                            },\n                                            children: \"➕ إضافة مستخدم جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleAddUser,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                                        gap: '15px',\n                                                        marginBottom: '20px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"اسم المستخدم *\",\n                                                            value: newUser.username,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    username: e.target.value\n                                                                }),\n                                                            style: inputStyle,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            placeholder: \"كلمة المرور *\",\n                                                            value: newUser.password,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    password: e.target.value\n                                                                }),\n                                                            style: inputStyle,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"الاسم الكامل *\",\n                                                            value: newUser.name,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    name: e.target.value\n                                                                }),\n                                                            style: inputStyle,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            placeholder: \"البريد الإلكتروني\",\n                                                            value: newUser.email,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    email: e.target.value\n                                                                }),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: newUser.role,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    role: e.target.value\n                                                                }),\n                                                            style: inputStyle,\n                                                            children: Object.entries(roles).map((param)=>{\n                                                                let [key, role] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: role.name\n                                                                }, key, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        gap: '10px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            style: {\n                                                                background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '8px',\n                                                                padding: '10px 20px',\n                                                                cursor: 'pointer'\n                                                            },\n                                                            children: \"✅ إنشاء المستخدم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowAddUser(false),\n                                                            style: {\n                                                                background: '#6c757d',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '8px',\n                                                                padding: '10px 20px',\n                                                                cursor: 'pointer'\n                                                            },\n                                                            children: \"❌ إلغاء\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        overflowX: 'auto',\n                                        borderRadius: '10px',\n                                        border: '1px solid #e9ecef'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        style: {\n                                            width: '100%',\n                                            borderCollapse: 'collapse',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    style: {\n                                                        background: '#f8f9fa'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'right',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"المستخدم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"الدور\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"الحالة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"تاريخ الإنشاء\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"آخر دخول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: users.map((user)=>{\n                                                    var _roles_user_role, _roles_user_role1, _roles_user_role2, _roles_user_role3;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        style: {\n                                                            borderBottom: '1px solid #e9ecef',\n                                                            transition: 'background-color 0.2s'\n                                                        },\n                                                        onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f8f9fa',\n                                                        onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                fontWeight: 'bold',\n                                                                                color: '#333',\n                                                                                marginBottom: '5px'\n                                                                            },\n                                                                            children: user.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                color: '#6c757d',\n                                                                                fontSize: '0.85rem'\n                                                                            },\n                                                                            children: [\n                                                                                \"@\",\n                                                                                user.username\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 484,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        user.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                color: '#6c757d',\n                                                                                fontSize: '0.8rem'\n                                                                            },\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCE7 \",\n                                                                                user.email\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 488,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 479,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        background: ((_roles_user_role = roles[user.role]) === null || _roles_user_role === void 0 ? void 0 : _roles_user_role.color) + '20',\n                                                                        color: (_roles_user_role1 = roles[user.role]) === null || _roles_user_role1 === void 0 ? void 0 : _roles_user_role1.color,\n                                                                        padding: '5px 12px',\n                                                                        borderRadius: '20px',\n                                                                        fontSize: '0.85rem',\n                                                                        fontWeight: 'bold',\n                                                                        display: 'inline-flex',\n                                                                        alignItems: 'center',\n                                                                        gap: '5px'\n                                                                    },\n                                                                    children: [\n                                                                        (_roles_user_role2 = roles[user.role]) === null || _roles_user_role2 === void 0 ? void 0 : _roles_user_role2.icon,\n                                                                        (_roles_user_role3 = roles[user.role]) === null || _roles_user_role3 === void 0 ? void 0 : _roles_user_role3.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        background: user.isActive ? '#28a74520' : '#dc354520',\n                                                                        color: user.isActive ? '#28a745' : '#dc3545',\n                                                                        padding: '5px 12px',\n                                                                        borderRadius: '20px',\n                                                                        fontSize: '0.85rem',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: user.isActive ? '✅ نشط' : '❌ معطل'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center',\n                                                                    color: '#6c757d'\n                                                                },\n                                                                children: new Date(user.createdAt).toLocaleDateString('ar-EG')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center',\n                                                                    color: '#6c757d'\n                                                                },\n                                                                children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-EG') : 'لم يدخل بعد'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: 'flex',\n                                                                        gap: '5px',\n                                                                        justifyContent: 'center'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setEditingUser(user),\n                                                                            style: {\n                                                                                background: '#007bff',\n                                                                                color: 'white',\n                                                                                border: 'none',\n                                                                                borderRadius: '5px',\n                                                                                padding: '5px 10px',\n                                                                                cursor: 'pointer',\n                                                                                fontSize: '0.8rem'\n                                                                            },\n                                                                            title: \"تعديل\",\n                                                                            children: \"✏️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        user.id !== '1' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleDeleteUser(user.id),\n                                                                            style: {\n                                                                                background: '#dc3545',\n                                                                                color: 'white',\n                                                                                border: 'none',\n                                                                                borderRadius: '5px',\n                                                                                padding: '5px 10px',\n                                                                                cursor: 'pointer',\n                                                                                fontSize: '0.8rem'\n                                                                            },\n                                                                            title: \"حذف\",\n                                                                            children: \"\\uD83D\\uDDD1️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, user.id, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this),\n                                users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '40px',\n                                        color: '#6c757d'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"\\uD83D\\uDC65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"لا توجد مستخدمين\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"ابدأ بإضافة مستخدم جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"QUoxQpgLP3uCmSEx0ZC3Aom6ph4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/page.tsx\n"));

/***/ })

});