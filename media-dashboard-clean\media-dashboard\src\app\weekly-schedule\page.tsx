'use client';
import React, { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { AuthGuard } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  duration: string;
  description?: string;
  isTemporary?: boolean;
  temporaryType?: string;
  episodeNumber?: number;
  seasonNumber?: number;
  partNumber?: number;
  isFromSchedule?: boolean;
  originalScheduleItem?: any;
}

interface ScheduleItem {
  id: string;
  mediaItemId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isRerun?: boolean;
  isTemporary?: boolean;
  mediaItem?: MediaItem;
  weekStart: string;
  episodeNumber?: number;
  seasonNumber?: number;
  partNumber?: number;
  createdAt?: string;
}

export default function WeeklySchedulePage() {
  const [loading, setLoading] = useState(true);
  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([]);
  const [availableMedia, setAvailableMedia] = useState<MediaItem[]>([]);
  const [selectedWeek, setSelectedWeek] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [draggedItem, setDraggedItem] = useState<MediaItem | null>(null);
  const scrollPositionRef = useRef<number>(0);
  const shouldRestoreScroll = useRef<boolean>(false);

  // حالات المواد المؤقتة
  const [tempMediaName, setTempMediaName] = useState('');
  const [tempMediaType, setTempMediaType] = useState('PROGRAM');
  const [tempMediaDuration, setTempMediaDuration] = useState('01:00:00');
  const [tempMediaNotes, setTempMediaNotes] = useState('');
  const [tempMediaItems, setTempMediaItems] = useState<MediaItem[]>([]);

  // أيام الأسبوع
  const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];





  // حساب تواريخ الأسبوع بالأرقام العربية العادية
  const getWeekDates = () => {
    if (!selectedWeek) return ['--/--', '--/--', '--/--', '--/--', '--/--', '--/--', '--/--'];

    const startDate = new Date(selectedWeek + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية
    const dates = [];

    console.log('📅 حساب تواريخ الأسبوع من:', selectedWeek);

    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      // استخدام الأرقام العربية العادية (1234567890)
      const dateStr = date.toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit'
      });
      dates.push(dateStr);

      console.log(`  يوم ${i} (${days[i]}): ${date.toISOString().split('T')[0]} → ${dateStr}`);
    }
    return dates;
  };

  const weekDates = getWeekDates();

  // الساعات من 08:00 إلى 07:00 (24 ساعة)
  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = (i + 8) % 24;
    return `${hour.toString().padStart(2, '0')}:00`;
  });

  // حساب المدة الإجمالية للمادة
  const calculateTotalDuration = (segments: any[]) => {
    if (!segments || segments.length === 0) return '01:00:00';

    let totalSeconds = 0;
    segments.forEach(segment => {
      const [hours, minutes, seconds] = segment.duration.split(':').map(Number);
      totalSeconds += hours * 3600 + minutes * 60 + seconds;
    });

    const hours_calc = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    return `${hours_calc.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // إنشاء نص عرض المادة مع التفاصيل
  const getMediaDisplayText = (item: MediaItem) => {
    let displayText = item.name || 'غير معروف';

    // إضافة تفاصيل الحلقات والأجزاء
    if (item.type === 'SERIES') {
      if (item.seasonNumber && item.episodeNumber) {
        displayText += ` - الموسم ${item.seasonNumber} الحلقة ${item.episodeNumber}`;
      } else if (item.episodeNumber) {
        displayText += ` - الحلقة ${item.episodeNumber}`;
      }
    } else if (item.type === 'PROGRAM') {
      if (item.seasonNumber && item.episodeNumber) {
        displayText += ` - الموسم ${item.seasonNumber} الحلقة ${item.episodeNumber}`;
      } else if (item.episodeNumber) {
        displayText += ` - الحلقة ${item.episodeNumber}`;
      }
    } else if (item.type === 'MOVIE' && item.partNumber) {
      displayText += ` - الجزء ${item.partNumber}`;
    }

    return displayText;
  };

  // تحديد الأسبوع الحالي
  useEffect(() => {
    const today = new Date();

    // إضافة تسجيل للتحقق
    console.log('🔍 حساب الأسبوع الحالي:');
    console.log('  📅 اليوم:', today.toISOString().split('T')[0]);
    console.log('  📊 يوم الأسبوع:', today.getDay(), '(0=أحد)');

    const sunday = new Date(today);
    sunday.setDate(today.getDate() - today.getDay());
    const weekStart = sunday.toISOString().split('T')[0];

    console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);

    setSelectedWeek(weekStart);
  }, []);

  // جلب البيانات
  useEffect(() => {
    if (selectedWeek) {
      fetchScheduleData();
    }
  }, [selectedWeek]);

  // استعادة موضع التمرير بعد تحديث البيانات
  useLayoutEffect(() => {
    if (shouldRestoreScroll.current && scrollPositionRef.current > 0) {
      window.scrollTo(0, scrollPositionRef.current);
      shouldRestoreScroll.current = false;
      console.log('📍 تم استعادة موضع التمرير:', scrollPositionRef.current);
    }
  }, [scheduleItems]);

  const fetchScheduleData = async () => {
    try {
      setLoading(true);
      console.log('🔄 بدء تحديث البيانات...');

      console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');

      const url = `/api/weekly-schedule?weekStart=${selectedWeek}`;
      console.log('🌐 إرسال طلب إلى:', url);

      const response = await fetch(url);
      console.log('📡 تم استلام الاستجابة:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📊 تم تحليل البيانات:', result.success);

      if (result.success) {
        // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)
        const allItems = result.data.scheduleItems || [];
        const apiTempItems = result.data.tempItems || [];

        // تحديث المواد المؤقتة في القائمة الجانبية
        console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map(item => ({ id: item.id, name: item.name })));
        setTempMediaItems(apiTempItems);

        setScheduleItems(allItems);
        setAvailableMedia(result.data.availableMedia || []);

        const regularItems = allItems.filter(item => !item.isTemporary && !item.isRerun);
        const tempItems = allItems.filter(item => item.isTemporary && !item.isRerun);
        const reruns = allItems.filter(item => item.isRerun);

        console.log(`📊 تم تحديث الجدول: ${regularItems.length} مادة عادية + ${tempItems.length} مؤقتة + ${reruns.length} إعادة = ${allItems.length} إجمالي`);
        console.log(`📦 تم تحديث القائمة الجانبية: ${apiTempItems.length} مادة مؤقتة`);
      } else {
        console.error('❌ خطأ في الاستجابة:', result.error);
        alert(`خطأ في تحديث البيانات: ${result.error}`);

        // الحفاظ على المواد المؤقتة حتى في حالة الخطأ
        setScheduleItems(currentTempItems);
        setAvailableMedia([]);
      }
    } catch (error) {
      console.error('❌ خطأ في جلب البيانات:', error);
      alert(`خطأ في الاتصال: ${error.message}`);

      // الحفاظ على المواد المؤقتة حتى في حالة الخطأ
      const currentTempItemsError = scheduleItems.filter(item => item.isTemporary);
      setScheduleItems(currentTempItemsError);
      setAvailableMedia([]);
    } finally {
      console.log('✅ انتهاء تحديث البيانات');
      setLoading(false);
    }
  };

  // إضافة مادة مؤقتة
  const addTempMedia = async () => {
    if (!tempMediaName.trim()) {
      alert('يرجى إدخال اسم المادة');
      return;
    }

    const newTempMedia: MediaItem = {
      id: `temp_${Date.now()}`,
      name: tempMediaName.trim(),
      type: tempMediaType,
      duration: tempMediaDuration,
      description: tempMediaNotes.trim() || undefined,
      isTemporary: true,
      temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' :
                   tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'
    };

    try {
      // حفظ المادة المؤقتة في القائمة الجانبية عبر API
      const response = await fetch('/api/weekly-schedule', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'saveTempToSidebar',
          tempMedia: newTempMedia,
          weekStart: selectedWeek
        })
      });

      const result = await response.json();
      if (result.success) {
        setTempMediaItems(prev => [...prev, newTempMedia]);
        console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');
      } else {
        alert(result.error || 'فشل في حفظ المادة المؤقتة');
        return;
      }
    } catch (error) {
      console.error('❌ خطأ في حفظ المادة المؤقتة:', error);
      alert('خطأ في حفظ المادة المؤقتة');
      return;
    }

    // إعادة تعيين النموذج
    setTempMediaName('');
    setTempMediaNotes('');
    setTempMediaDuration('01:00:00');

    console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);
  };

  // حذف مادة مؤقتة من القائمة الجانبية
  const deleteTempMedia = async (tempMediaId: string) => {
    if (!confirm('هل تريد حذف هذه المادة المؤقتة؟')) {
      return;
    }

    try {
      const response = await fetch('/api/weekly-schedule', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'deleteTempFromSidebar',
          tempMediaId,
          weekStart: selectedWeek
        })
      });

      const result = await response.json();
      if (result.success) {
        setTempMediaItems(prev => prev.filter(item => item.id !== tempMediaId));
        console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');
      } else {
        alert(result.error || 'فشل في حذف المادة المؤقتة');
      }
    } catch (error) {
      console.error('❌ خطأ في حذف المادة المؤقتة:', error);
      alert('خطأ في حذف المادة المؤقتة');
    }
  };

  // حذف مادة مؤقتة
  const removeTempMedia = (id: string) => {
    setTempMediaItems(prev => prev.filter(item => item.id !== id));
  };

  // دمج المواد العادية والمؤقتة
  const allAvailableMedia = [...availableMedia, ...tempMediaItems];

  // فلترة المواد حسب النوع والبحث
  const filteredMedia = allAvailableMedia.filter(item => {
    const matchesType = selectedType === '' || item.type === selectedType;
    const itemName = item.name || '';
    const itemType = item.type || '';
    const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         itemType.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesType && matchesSearch;
  });

  // التحقق من البرايم تايم
  const isPrimeTimeSlot = (dayOfWeek: number, timeStr: string) => {
    const hour = parseInt(timeStr.split(':')[0]);

    // الأحد-الأربعاء: 18:00-23:59
    if (dayOfWeek >= 0 && dayOfWeek <= 3) {
      return hour >= 18;
    }

    // الخميس-السبت: 18:00-23:59 أو 00:00-01:59
    if (dayOfWeek >= 4 && dayOfWeek <= 6) {
      return hour >= 18 || hour < 2;
    }

    return false;
  };

  // دالة توليد الإعادات تم إيقافها نهائياً
  const generateLocalTempReruns = (originalItem: any) => {
    console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');
    return [];
  };

  // دالة توليد الإعادات تم إيقافها نهائياً
  const generateLocalRerunsWithItems = (tempItems: any[], checkItems: any[]) => {
    console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');
    return [];
  };

  // دالة توليد الإعادات تم إيقافها نهائياً
  const generateLocalReruns = (tempItems: any[]) => {
    console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');
    return [];
  };

  // دالة توليد الإعادات تم إيقافها نهائياً
  const generateTempReruns = (originalItem: any) => {
    console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');
    return [];
  };

  // التحقق من التداخل في الأوقات
  const checkTimeConflict = (newItem: any, existingItems: any[]) => {
    try {
      const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');
      const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');

      const conflict = existingItems.some(item => {
        if (item.dayOfWeek !== newItem.dayOfWeek) return false;

        const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');
        const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');

        return (newStart < itemEnd && newEnd > itemStart);
      });

      if (conflict) {
        console.log('⚠️ تم اكتشاف تداخل:', newItem);
      }

      return conflict;
    } catch (error) {
      console.error('خطأ في فحص التداخل:', error);
      return false; // في حالة الخطأ، اسمح بالإضافة
    }
  };

  // إضافة مادة للجدول
  const addItemToSchedule = async (mediaItem: MediaItem, dayOfWeek: number, hour: string) => {
    try {
      // حفظ موضع التمرير الحالي
      scrollPositionRef.current = window.scrollY;
      shouldRestoreScroll.current = true;

      console.log('🎯 محاولة إضافة مادة:', {
        name: mediaItem.name,
        isTemporary: mediaItem.isTemporary,
        dayOfWeek,
        hour,
        scrollPosition: scrollPositionRef.current
      });

      const startTime = hour;
      const endTime = `${(parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0')}:00`;

      // التحقق من التداخل
      const newItem = {
        dayOfWeek,
        startTime,
        endTime
      };

      if (checkTimeConflict(newItem, scheduleItems)) {
        alert('⚠️ يوجد تداخل في الأوقات! اختر وقت آخر.');
        console.log('❌ تم منع الإضافة بسبب التداخل');
        return;
      }

      // التحقق من المواد المؤقتة
      if (mediaItem.isTemporary) {
        console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');
        console.log('📋 بيانات المادة:', {
          id: mediaItem.id,
          name: mediaItem.name,
          type: mediaItem.type,
          duration: mediaItem.duration,
          fullItem: mediaItem
        });

        // التحقق من صحة البيانات
        if (!mediaItem.name || mediaItem.name === 'undefined') {
          console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);
          alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');
          shouldRestoreScroll.current = false;
          return;
        }

        // حذف المادة من القائمة الجانبية محلياً أولاً
        setTempMediaItems(prev => {
          const filtered = prev.filter(item => item.id !== mediaItem.id);
          console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);
          console.log('📊 المواد المتبقية في القائمة:', filtered.length);
          return filtered;
        });

        // التأكد من صحة البيانات قبل الإرسال
        const cleanMediaItem = {
          ...mediaItem,
          name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',
          id: mediaItem.id || `temp_${Date.now()}`,
          type: mediaItem.type || 'PROGRAM',
          duration: mediaItem.duration || '01:00:00'
        };

        console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);

        // إرسال المادة المؤقتة إلى API
        const response = await fetch('/api/weekly-schedule', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            mediaItemId: cleanMediaItem.id,
            dayOfWeek,
            startTime,
            endTime,
            weekStart: selectedWeek,
            isTemporary: true,
            mediaItem: cleanMediaItem
          })
        });

        const result = await response.json();
        if (result.success) {
          console.log('✅ تم نقل المادة المؤقتة إلى الجدول');

          // حذف المادة من القائمة الجانبية في الخادم بعد النجاح
          try {
            await fetch('/api/weekly-schedule', {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                action: 'deleteTempFromSidebar',
                tempMediaId: mediaItem.id,
                weekStart: selectedWeek
              })
            });
            console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');
          } catch (error) {
            console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);
          }

          // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل
          const newScheduleItem = {
            id: result.data.id,
            mediaItemId: mediaItem.id,
            dayOfWeek,
            startTime,
            endTime,
            weekStart: selectedWeek,
            isTemporary: true,
            mediaItem: mediaItem
          };

          setScheduleItems(prev => [...prev, newScheduleItem]);
          console.log('✅ تم إضافة المادة للجدول محلياً');
        } else {
          // في حالة الفشل، أعد المادة للقائمة الجانبية
          setTempMediaItems(prev => [...prev, mediaItem]);
          alert(result.error);
          shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ
        }

        return;
      }

      // للمواد العادية - استخدام API
      const response = await fetch('/api/weekly-schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          mediaItemId: mediaItem.id,
          dayOfWeek,
          startTime,
          endTime,
          weekStart: selectedWeek,
          // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة
          episodeNumber: mediaItem.episodeNumber,
          seasonNumber: mediaItem.seasonNumber,
          partNumber: mediaItem.partNumber
        })
      });

      const result = await response.json();
      if (result.success) {
        await fetchScheduleData();
      } else {
        alert(result.error);
        shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ
      }
    } catch (error) {
      console.error('خطأ في إضافة المادة:', error);
    }
  };

  // حذف مادة مع تأكيد
  const deleteItem = async (item: ScheduleItem) => {
    const itemName = item.mediaItem?.name || 'المادة';
    const itemType = item.isRerun ? 'إعادة' :
                    item.isTemporary ? 'مادة مؤقتة' : 'مادة أصلية';

    const confirmed = window.confirm(
      `هل أنت متأكد من حذف ${itemType}: "${itemName}"؟\n\n` +
      `الوقت: ${item.startTime} - ${item.endTime}\n` +
      (item.isRerun ? 'تحذير: حذف الإعادة لن يؤثر على المادة الأصلية' :
       item.isTemporary ? 'سيتم حذف المادة المؤقتة من الجدول' :
       'تحذير: حذف المادة الأصلية سيحذف جميع إعاداتها')
    );

    if (!confirmed) return;

    try {
      // للمواد المؤقتة - حذف محلي
      if (item.isTemporary) {
        if (item.isRerun) {
          // حذف إعادة مؤقتة فقط
          setScheduleItems(prev => prev.filter(scheduleItem => scheduleItem.id !== item.id));
          console.log(`✅ تم حذف إعادة مؤقتة: ${itemName}`);
        } else {
          // حذف المادة الأصلية وجميع إعاداتها المؤقتة
          setScheduleItems(prev => prev.filter(scheduleItem =>
            scheduleItem.id !== item.id &&
            !(scheduleItem.isTemporary && scheduleItem.originalId === item.id)
          ));
          console.log(`✅ تم حذف المادة المؤقتة وإعاداتها: ${itemName}`);
        }
        return;
      }

      // حفظ موضع التمرير الحالي
      scrollPositionRef.current = window.scrollY;
      shouldRestoreScroll.current = true;

      // للمواد العادية - استخدام API
      const response = await fetch(`/api/weekly-schedule?id=${item.id}&weekStart=${selectedWeek}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await fetchScheduleData();
        console.log(`✅ تم حذف ${itemType}: ${itemName}`);
      } else {
        const result = await response.json();
        alert(`خطأ في الحذف: ${result.error}`);
      }
    } catch (error) {
      console.error('خطأ في حذف المادة:', error);
      alert('حدث خطأ أثناء حذف المادة');
    }
  };

  // الحصول على المواد في خلية معينة
  const getItemsForCell = (dayOfWeek: number, hour: string) => {
    return scheduleItems.filter(item => 
      item.dayOfWeek === dayOfWeek && 
      item.startTime <= hour && 
      item.endTime > hour
    );
  };

  // معالجة السحب والإفلات
  const handleDragStart = (e: React.DragEvent, mediaItem: MediaItem) => {
    console.log('🖱️ بدء السحب:', {
      id: mediaItem.id,
      name: mediaItem.name,
      isTemporary: mediaItem.isTemporary,
      type: mediaItem.type,
      duration: mediaItem.duration,
      fullItem: mediaItem
    });

    // التأكد من أن جميع البيانات موجودة
    const itemToSet = {
      ...mediaItem,
      name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',
      id: mediaItem.id || `temp_${Date.now()}`
    };

    console.log('📦 المادة المحفوظة للسحب:', itemToSet);
    setDraggedItem(itemToSet);
  };

  // سحب مادة من الجدول نفسه (نسخ)
  const handleScheduleItemDragStart = (e: React.DragEvent, scheduleItem: any) => {
    // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء
    const itemToCopy = {
      ...scheduleItem.mediaItem,
      // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول
      episodeNumber: scheduleItem.episodeNumber || scheduleItem.mediaItem?.episodeNumber,
      seasonNumber: scheduleItem.seasonNumber || scheduleItem.mediaItem?.seasonNumber,
      partNumber: scheduleItem.partNumber || scheduleItem.mediaItem?.partNumber,
      isFromSchedule: true,
      originalScheduleItem: scheduleItem
    };
    setDraggedItem(itemToCopy);
    console.log('🔄 سحب مادة من الجدول:', scheduleItem.mediaItem?.name);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dayOfWeek: number, hour: string) => {
    e.preventDefault();
    console.log('📍 إفلات في:', { dayOfWeek, hour });

    if (draggedItem) {
      console.log('📦 المادة المسحوبة:', {
        id: draggedItem.id,
        name: draggedItem.name,
        isTemporary: draggedItem.isTemporary,
        type: draggedItem.type,
        fullItem: draggedItem
      });

      // التأكد من أن البيانات سليمة قبل الإرسال
      if (!draggedItem.name || draggedItem.name === 'undefined') {
        console.error('⚠️ اسم المادة غير صحيح:', draggedItem);
        alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');
        setDraggedItem(null);
        return;
      }

      addItemToSchedule(draggedItem, dayOfWeek, hour);
      setDraggedItem(null);
    } else {
      console.log('❌ لا توجد مادة مسحوبة');
    }
  };

  // تغيير الأسبوع
  const changeWeek = (direction: number) => {
    const currentDate = new Date(selectedWeek);
    currentDate.setDate(currentDate.getDate() + (direction * 7));
    setSelectedWeek(currentDate.toISOString().split('T')[0]);
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <div style={{ fontSize: '1.5rem' }}>⏳ جاري تحميل الجدول الأسبوعي...</div>
      </div>
    );
  }

  // إذا لم يتم تحديد الأسبوع بعد
  if (!selectedWeek) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <div style={{ fontSize: '1.5rem' }}>📅 جاري تحديد التاريخ...</div>
      </div>
    );
  }

  return (
    <AuthGuard requiredPermissions={['SCHEDULE_READ']}>
      <DashboardLayout title="الخريطة البرامجية الأسبوعية" subtitle="جدولة البرامج الأسبوعية" icon="📅" fullWidth={true}>
        <div style={{
          display: 'flex',
          height: 'calc(100vh - 120px)',
          fontFamily: 'Arial, sans-serif',
          direction: 'rtl',
          gap: '20px'
        }}>
          {/* قائمة المواد - اليمين */}
          <div style={{
            width: '320px',
            background: '#4a5568',
            borderRadius: '15px',
            border: '1px solid #6b7280',
            padding: '20px',
            overflowY: 'auto'
          }}>
            <h3 style={{ margin: '0 0 15px 0', color: '#f3f4f6' }}>📚 قائمة المواد</h3>

            {/* نموذج إضافة مادة مؤقتة */}
            <div style={{
              background: '#1f2937',
              border: '2px solid #f59e0b',
              borderRadius: '8px',
              padding: '12px',
              marginBottom: '15px'
            }}>
              <h4 style={{ margin: '0 0 10px 0', color: '#fbbf24', fontSize: '0.9rem' }}>
                ⚡ إضافة مادة مؤقتة
              </h4>

              <input
                type="text"
                placeholder="اسم المادة..."
                value={tempMediaName}
                onChange={(e) => setTempMediaName(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #6b7280',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  fontSize: '13px',
                  color: '#333',
                  background: 'white'
                }}
              />

              <select
                value={tempMediaType}
                onChange={(e) => setTempMediaType(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #6b7280',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  fontSize: '13px',
                  color: '#333',
                  background: 'white'
                }}
              >
                <option value="PROGRAM">📻 برنامج</option>
                <option value="SERIES">📺 مسلسل</option>
                <option value="MOVIE">🎥 فيلم</option>
                <option value="LIVE">🔴 برنامج هواء مباشر</option>
                <option value="PENDING">🟡 مادة قيد التسليم</option>
              </select>

              <input
                type="text"
                placeholder="المدة (مثل: 01:30:00)"
                value={tempMediaDuration}
                onChange={(e) => setTempMediaDuration(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #6b7280',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  fontSize: '13px',
                  color: '#333',
                  background: 'white'
                }}
              />

              <input
                type="text"
                placeholder="ملاحظات (اختياري)..."
                value={tempMediaNotes}
                onChange={(e) => setTempMediaNotes(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #6b7280',
                  borderRadius: '4px',
                  marginBottom: '10px',
                  fontSize: '13px',
                  color: '#333',
                  background: 'white'
                }}
              />

              <button
                onClick={addTempMedia}
                style={{
                  width: '100%',
                  padding: '8px',
                  background: '#ff9800',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  fontSize: '13px',
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  marginBottom: '8px'
                }}
              >
                ➕ إضافة
              </button>

              <button
                onClick={async () => {
                  console.log('🔄 تحديث الإعادات...');
                  scrollPositionRef.current = window.scrollY;
                  shouldRestoreScroll.current = true;
                  await fetchScheduleData();
                }}
                style={{
                  width: '100%',
                  padding: '6px',
                  background: '#4caf50',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                ♻️ تحديث الإعادات
              </button>
            </div>

            {/* فلتر نوع المادة */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #6b7280',
                borderRadius: '5px',
                marginBottom: '10px',
                fontSize: '14px',
                backgroundColor: 'white',
                color: '#333'
              }}
            >
              <option value="">🎬 جميع الأنواع</option>
              <option value="SERIES">📺 مسلسل</option>
              <option value="MOVIE">🎥 فيلم</option>
              <option value="PROGRAM">📻 برنامج</option>
              <option value="PROMO">📢 إعلان</option>
              <option value="STING">⚡ ستينج</option>
              <option value="FILL_IN">🔄 فيل إن</option>
              <option value="FILLER">⏸️ فيلر</option>
            </select>

            {/* البحث */}
            <input
              type="text"
              placeholder="🔍 البحث في المواد..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #6b7280',
                borderRadius: '5px',
                marginBottom: '15px',
                fontSize: '14px',
                color: '#333',
                background: 'white'
              }}
            />

            {/* عداد النتائج */}
            <div style={{
              fontSize: '12px',
              color: '#d1d5db',
              marginBottom: '10px',
              textAlign: 'center',
              padding: '5px',
              background: '#1f2937',
              borderRadius: '4px',
              border: '1px solid #6b7280'
            }}>
              📊 {filteredMedia.length} من {allAvailableMedia.length} مادة
            </div>

            {/* قائمة المواد */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {filteredMedia.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '20px',
                  color: '#666',
                  background: '#f8f9fa',
                  borderRadius: '8px',
                  border: '2px dashed #dee2e6'
                }}>
                  <div style={{ fontSize: '2rem', marginBottom: '10px' }}>🔍</div>
                  <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>لا توجد مواد</div>
                  <div style={{ fontSize: '12px' }}>
                    {searchTerm || selectedType ? 'جرب تغيير الفلتر أو البحث' : 'أضف مواد جديدة من صفحة المستخدم'}
                  </div>
                </div>
              ) : (
                filteredMedia.map(item => {
                  // تحديد لون المادة حسب النوع
                  const getItemStyle = () => {
                    if (item.isTemporary) {
                      switch (item.type) {
                        case 'LIVE':
                          return {
                            background: '#ffebee',
                            border: '2px solid #f44336',
                            borderLeft: '5px solid #f44336'
                          };
                        case 'PENDING':
                          return {
                            background: '#fff8e1',
                            border: '2px solid #ffc107',
                            borderLeft: '5px solid #ffc107'
                          };
                        default:
                          return {
                            background: '#f3e5f5',
                            border: '2px solid #9c27b0',
                            borderLeft: '5px solid #9c27b0'
                          };
                      }
                    }
                    return {
                      background: '#fff',
                      border: '1px solid #ddd'
                    };
                  };

                  return (
                    <div
                      key={item.id}
                      draggable
                      onDragStart={(e) => handleDragStart(e, item)}
                      style={{
                        ...getItemStyle(),
                        borderRadius: '8px',
                        padding: '12px',
                        cursor: 'grab',
                        transition: 'all 0.2s',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                      }}
                    >
                      {/* زر حذف للمواد المؤقتة */}
                      {item.isTemporary && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteTempMedia(item.id);
                          }}
                          style={{
                            position: 'absolute',
                            top: '5px',
                            left: '5px',
                            background: '#f44336',
                            color: 'white',
                            border: 'none',
                            borderRadius: '50%',
                            width: '20px',
                            height: '20px',
                            fontSize: '12px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                          title="حذف المادة المؤقتة"
                        >
                          ×
                        </button>
                      )}

                      <div style={{ fontWeight: 'bold', color: '#333', marginBottom: '4px' }}>
                        {item.isTemporary && (
                          <span style={{
                            fontSize: '10px',
                            background: item.type === 'LIVE' ? '#f44336' :
                                       item.type === 'PENDING' ? '#ffc107' : '#9c27b0',
                            color: 'white',
                            padding: '2px 6px',
                            borderRadius: '10px',
                            marginLeft: '5px'
                          }}>
                            {item.type === 'LIVE' ? '🔴 هواء' :
                             item.type === 'PENDING' ? '🟡 قيد التسليم' : '🟣 مؤقت'}
                          </span>
                        )}
                        {getMediaDisplayText(item)}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {item.type} • {item.duration}
                      </div>
                      {item.description && (
                        <div style={{ fontSize: '11px', color: '#888', marginTop: '4px' }}>
                          {item.description}
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </div>
          </div>

          {/* الجدول الرئيسي - اليسار */}
          <div style={{ flex: 1, overflowY: 'auto' }}>
            {/* العنوان والتحكم في التاريخ */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px',
              background: '#4a5568',
              padding: '15px',
              borderRadius: '15px',
              border: '1px solid #6b7280'
            }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => window.location.href = '/daily-schedule'}
              style={{
                background: 'linear-gradient(45deg, #007bff, #0056b3)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '10px 20px',
                fontSize: '0.9rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
                transition: 'transform 0.2s ease',
                marginLeft: '10px'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              📋 الجدول الإذاعي
            </button>





            <button
              onClick={async () => {
                try {
                  console.log('📊 بدء تصدير الخريطة الأسبوعية...');
                  const response = await fetch(`/api/export-schedule?weekStart=${selectedWeek}`);

                  if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                  }

                  const blob = await response.blob();
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `Weekly_Schedule_${selectedWeek}.xlsx`;
                  document.body.appendChild(a);
                  a.click();
                  window.URL.revokeObjectURL(url);
                  document.body.removeChild(a);

                  console.log('✅ تم تصدير الخريطة بنجاح');
                } catch (error) {
                  console.error('❌ خطأ في تصدير الخريطة:', error);
                  alert('فشل في تصدير الخريطة: ' + error.message);
                }
              }}
              style={{
                background: 'linear-gradient(45deg, #28a745, #20c997)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '10px 20px',
                fontSize: '0.9rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
                transition: 'transform 0.2s ease',
                marginLeft: '10px'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              📊 تصدير الخريطة
            </button>



            <h2 style={{ margin: 0, color: '#f3f4f6', fontSize: '1.2rem' }}>📅 الأسبوع المحدد</h2>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => changeWeek(-1)}
              style={{
                padding: '8px 15px',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              ← الأسبوع السابق
            </button>

            <input
              type="date"
              value={selectedWeek}
              onChange={(e) => {
                const selectedDate = new Date(e.target.value + 'T12:00:00');
                const dayOfWeek = selectedDate.getDay();

                console.log('📅 تغيير التاريخ من التقويم:', {
                  selectedDate: e.target.value,
                  dayOfWeek: dayOfWeek,
                  dayName: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][dayOfWeek]
                });

                // حساب بداية الأسبوع (الأحد)
                const sunday = new Date(selectedDate);
                sunday.setDate(selectedDate.getDate() - dayOfWeek);
                const weekStart = sunday.toISOString().split('T')[0];

                console.log('📅 بداية الأسبوع المحسوبة:', weekStart);

                setSelectedWeek(weekStart);
              }}
              style={{
                padding: '8px',
                border: '1px solid #6b7280',
                borderRadius: '5px',
                color: '#333',
                background: 'white'
              }}
            />

            <button
              onClick={() => changeWeek(1)}
              style={{
                padding: '8px 15px',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              الأسبوع التالي →
            </button>
          </div>
        </div>

            {/* الجدول */}
            <div style={{
              background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',
              borderRadius: '10px',
              overflow: 'hidden',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              minHeight: '80vh'
            }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            {/* رأس الجدول */}
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{
                  padding: '12px',
                  border: '1px solid #dee2e6',
                  fontWeight: 'bold',
                  width: '80px',
                  color: '#000'
                }}>
                  الوقت
                </th>
                {days.map((day, index) => (
                  <th key={index} style={{
                    padding: '12px',
                    border: '1px solid #dee2e6',
                    fontWeight: 'bold',
                    width: `${100/7}%`,
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '1rem', marginBottom: '4px', color: '#000', fontWeight: 'bold' }}>{day}</div>
                    <div style={{ fontSize: '1rem', color: '#000', fontWeight: 'bold' }}>
                      {new Date(weekDates[index]).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                      })}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>

            {/* جسم الجدول */}
            <tbody>
              {hours.map((hour, hourIndex) => (
                <tr key={hourIndex}>
                  <td style={{
                    background: '#f8f9fa',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    padding: '8px',
                    border: '1px solid #dee2e6',
                    color: '#000'
                  }}>
                    {hour}
                  </td>
                  
                  {days.map((_, dayIndex) => {
                    const cellItems = getItemsForCell(dayIndex, hour);
                    return (
                      <td
                        key={dayIndex}
                        onDragOver={handleDragOver}
                        onDrop={(e) => handleDrop(e, dayIndex, hour)}
                        style={{
                          border: '1px solid #dee2e6',
                          padding: '8px',
                          height: '150px',
                          cursor: 'pointer',
                          background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',
                          verticalAlign: 'top'
                        }}
                      >
                        {cellItems.map(item => (
                          <div
                            key={item.id}
                            draggable
                            onDragStart={(e) => handleScheduleItemDragStart(e, item)}
                            onClick={() => deleteItem(item)}
                            style={{
                              background: item.isRerun ? '#f0f0f0' :
                                         item.isTemporary ? (
                                           item.mediaItem?.type === 'LIVE' ? '#ffebee' :
                                           item.mediaItem?.type === 'PENDING' ? '#fff8e1' : '#f3e5f5'
                                         ) : '#fff3e0',
                              border: `2px solid ${
                                item.isRerun ? '#888888' :
                                item.isTemporary ? (
                                  item.mediaItem?.type === 'LIVE' ? '#f44336' :
                                  item.mediaItem?.type === 'PENDING' ? '#ffc107' : '#9c27b0'
                                ) : '#ff9800'
                              }`,
                              borderRadius: '4px',
                              padding: '8px 6px',
                              marginBottom: '4px',
                              fontSize: '1rem',
                              cursor: 'grab',
                              transition: 'all 0.2s ease',
                              minHeight: '60px',
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'center'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.transform = 'scale(1.02)';
                              e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.transform = 'scale(1)';
                              e.currentTarget.style.boxShadow = 'none';
                            }}
                          >
                            <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '4px', color: '#000' }}>
                              {item.isRerun ? (
                                <span style={{ color: '#666' }}>
                                  ♻️ {item.rerunPart ? `ج${item.rerunPart}` : ''}{item.rerunCycle ? `(${item.rerunCycle})` : ''}
                                </span>
                              ) : item.isTemporary ? (
                                <span style={{
                                  color: item.mediaItem?.type === 'LIVE' ? '#f44336' :
                                         item.mediaItem?.type === 'PENDING' ? '#ffc107' : '#9c27b0'
                                }}>
                                  {item.mediaItem?.type === 'LIVE' ? '🔴' :
                                   item.mediaItem?.type === 'PENDING' ? '🟡' : '🟣'}
                                </span>
                              ) : (
                                <span style={{ color: '#ff9800' }}>🌟</span>
                              )}
                              <span style={{ color: '#000' }}>
                                {item.mediaItem ? getMediaDisplayText(item.mediaItem) : 'غير معروف'}
                              </span>
                            </div>
                            <div style={{ fontSize: '0.8rem', color: '#000', marginTop: '4px' }}>
                              {item.startTime} - {item.endTime}
                            </div>
                            {item.isRerun && (
                              <div style={{ fontSize: '0.5rem', color: '#888', fontStyle: 'italic' }}>
                                إعادة - يمكن الحذف للتعديل
                              </div>
                            )}
                          </div>
                        ))}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
            </div>

            {/* تعليمات */}
            <div style={{
              background: '#4a5568',
              padding: '20px',
              borderRadius: '15px',
              marginTop: '20px',
              border: '1px solid #6b7280'
            }}>
          <h4 style={{ color: '#f3f4f6', margin: '0 0 20px 0', fontSize: '1.3rem', textAlign: 'center' }}>📋 تعليمات الاستخدام:</h4>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
            <div>
              <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>🎯 إضافة المواد:</strong>
              <ul style={{ margin: '10px 0', paddingRight: '20px', fontSize: '1rem', color: '#d1d5db' }}>
                <li style={{ marginBottom: '8px' }}>اسحب المواد من القائمة اليمنى إلى الجدول</li>
                <li style={{ marginBottom: '8px' }}>🔄 اسحب المواد من الجدول نفسه لنسخها لمواعيد أخرى</li>
                <li style={{ marginBottom: '8px' }}>🎬 استخدم فلتر النوع للتصفية حسب نوع المادة</li>
                <li style={{ marginBottom: '8px' }}>🔍 استخدم البحث للعثور على المواد بسرعة</li>
              </ul>
            </div>
            <div>
              <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>🗑️ حذف المواد:</strong>
              <ul style={{ margin: '10px 0', paddingRight: '20px', fontSize: '1rem', color: '#d1d5db' }}>
                <li style={{ marginBottom: '8px' }}><strong>المواد الأصلية:</strong> حذف نهائي مع جميع إعاداتها</li>
                <li style={{ marginBottom: '8px' }}><strong>الإعادات:</strong> حذف مع ترك الحقل فارغ للتعديل</li>
                <li style={{ marginBottom: '8px' }}>سيظهر تأكيد قبل الحذف</li>
              </ul>
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div>
              <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>🌟 المواد الأصلية (البرايم تايم):</strong>
              <ul style={{ margin: '10px 0', paddingRight: '20px', fontSize: '1rem', color: '#d1d5db' }}>
                <li style={{ marginBottom: '8px' }}>الأحد-الأربعاء: 18:00-00:00</li>
                <li style={{ marginBottom: '8px' }}>الخميس-السبت: 18:00-02:00</li>
                <li style={{ marginBottom: '8px' }}>🟡 <strong style={{ color: '#fbbf24' }}>لون ذهبي في الجدول</strong></li>
              </ul>
            </div>
            <div>
              <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>♻️ الإعادات التلقائية (جزئين):</strong>
              <ul style={{ margin: '10px 0', paddingRight: '20px', fontSize: '1rem', color: '#d1d5db' }}>
                <li style={{ marginBottom: '8px' }}><strong>الأحد-الأربعاء:</strong></li>
                <ul style={{ margin: '5px 0', paddingRight: '15px', fontSize: '0.9rem' }}>
                  <li style={{ marginBottom: '5px' }}>ج1: نفس العمود 00:00-07:59</li>
                  <li style={{ marginBottom: '5px' }}>ج2: العمود التالي 08:00-17:59</li>
                </ul>
                <li style={{ marginBottom: '8px' }}><strong>الخميس-السبت:</strong></li>
                <ul style={{ margin: '5px 0', paddingRight: '15px', fontSize: '0.9rem' }}>
                  <li style={{ marginBottom: '5px' }}>ج1: نفس العمود 02:00-07:59</li>
                  <li style={{ marginBottom: '5px' }}>ج2: العمود التالي 08:00-17:59</li>
                </ul>
                <li style={{ marginBottom: '8px' }}>🔘 <strong style={{ color: '#9ca3af' }}>لون رمادي - يمكن حذفها للتعديل</strong></li>
              </ul>
            </div>
          </div>

          <div style={{ marginTop: '15px', padding: '15px', background: '#1f2937', borderRadius: '10px', border: '1px solid #f59e0b' }}>
            <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>📅 إدارة التواريخ:</strong>
            <span style={{ color: '#d1d5db', fontSize: '1rem' }}> استخدم الكالندر والأزرار للتنقل بين الأسابيع • كل أسبوع يُحفظ بشكل منفصل</span>
          </div>

          <div style={{ marginTop: '15px', padding: '15px', background: '#1f2937', borderRadius: '10px', border: '1px solid #3b82f6' }}>
            <strong style={{ color: '#60a5fa', fontSize: '1.1rem' }}>💡 ملاحظة مهمة:</strong>
            <span style={{ color: '#d1d5db', fontSize: '1rem' }}> عند إضافة مواد قليلة (١-٣ مواد) في البرايم، ستظهر مع فواصل زمنية في الإعادات لتجنب التكرار المفرط. أضف المزيد من المواد للحصول على تنوع أكبر.</span>
          </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </AuthGuard>
  );
}
