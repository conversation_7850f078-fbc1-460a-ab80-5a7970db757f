"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/media-list/page",{

/***/ "(app-pages-browser)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MediaListPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction MediaListPage() {\n    _s();\n    const [mediaItems, setMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            fetchMediaItems();\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            filterAndSortItems();\n        }\n    }[\"MediaListPage.useEffect\"], [\n        mediaItems,\n        searchTerm,\n        selectedType,\n        selectedStatus,\n        sortBy\n    ]);\n    const fetchMediaItems = async ()=>{\n        try {\n            const response = await fetch('/api/media');\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(result.data);\n            } else {\n                setError(result.error);\n            }\n        } catch (error) {\n            console.error('Error fetching media items:', error);\n            setError('فشل في جلب المواد الإعلامية');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterAndSortItems = ()=>{\n        let filtered = [\n            ...mediaItems\n        ];\n        // البحث بالاسم\n        if (searchTerm) {\n            filtered = filtered.filter((item)=>item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // فلترة بالنوع\n        if (selectedType !== 'ALL') {\n            filtered = filtered.filter((item)=>item.type === selectedType);\n        }\n        // فلترة بالحالة\n        if (selectedStatus !== 'ALL') {\n            filtered = filtered.filter((item)=>item.status === selectedStatus);\n        }\n        // الترتيب\n        switch(sortBy){\n            case 'newest':\n                filtered.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                break;\n            case 'oldest':\n                filtered.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n                break;\n            case 'name':\n                filtered.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n                break;\n            case 'type':\n                filtered.sort((a, b)=>a.type.localeCompare(b.type));\n                break;\n        }\n        setFilteredItems(filtered);\n    };\n    const deleteMediaItem = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذه المادة؟')) return;\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(mediaItems.filter((item)=>item.id !== id));\n                alert('تم حذف المادة بنجاح');\n            } else {\n                alert('فشل في حذف المادة: ' + result.error);\n            }\n        } catch (error) {\n            console.error('Error deleting media item:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    const exportToExcel = async ()=>{\n        setIsExporting(true);\n        try {\n            console.log('🚀 بدء تصدير قاعدة البيانات...');\n            const response = await fetch('/api/export');\n            if (!response.ok) {\n                throw new Error('فشل في تصدير البيانات');\n            }\n            // الحصول على الملف كـ blob\n            const blob = await response.blob();\n            // إنشاء رابط التحميل\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            // تحديد اسم الملف\n            const fileName = \"Media_Database_\".concat(new Date().toISOString().split('T')[0], \".xlsx\");\n            link.download = fileName;\n            // تحميل الملف\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            // تنظيف الذاكرة\n            window.URL.revokeObjectURL(url);\n            console.log('✅ تم تصدير قاعدة البيانات بنجاح');\n            alert('✅ تم تصدير قاعدة البيانات بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في التصدير:', error);\n            alert('❌ فشل في تصدير قاعدة البيانات. يرجى المحاولة مرة أخرى.');\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const getTypeLabel = (type)=>{\n        const types = {\n            PROGRAM: 'برنامج',\n            SERIES: 'مسلسل',\n            MOVIE: 'فيلم',\n            SONG: 'أغنية',\n            STING: 'Sting',\n            FILL_IN: 'Fill IN',\n            FILLER: 'Filler',\n            PROMO: 'Promo'\n        };\n        return types[type] || type;\n    };\n    const getStatusLabel = (status)=>{\n        const statuses = {\n            VALID: 'صالح',\n            REJECTED_CENSORSHIP: 'مرفوض رقابي',\n            REJECTED_TECHNICAL: 'مرفوض هندسي',\n            WAITING: 'في الانتظار'\n        };\n        return statuses[status] || status;\n    };\n    const getChannelLabel = (channel)=>{\n        const channels = {\n            DOCUMENTARY: 'الوثائقية',\n            NEWS: 'الأخبار',\n            OTHER: 'أخرى'\n        };\n        return channels[channel] || channel;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: \"⏳ جاري التحميل...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"❌ خطأ: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: '#1a1d29',\n            padding: '20px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '30px',\n                            marginBottom: '30px',\n                            boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: '#2c3e50',\n                                    marginBottom: '20px',\n                                    fontSize: '2.5rem',\n                                    fontWeight: 'bold'\n                                },\n                                children: \"\\uD83D\\uDCDA قائمة المواد الإعلامية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center',\n                                    flexWrap: 'wrap'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/add-media\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            textDecoration: 'none',\n                                            fontWeight: 'bold',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)'\n                                        },\n                                        children: \"➕ إضافة مادة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: exportToExcel,\n                                        disabled: isExporting,\n                                        style: {\n                                            background: isExporting ? 'linear-gradient(45deg, #6c757d, #5a6268)' : 'linear-gradient(45deg, #17a2b8, #138496)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            border: 'none',\n                                            fontWeight: 'bold',\n                                            cursor: isExporting ? 'not-allowed' : 'pointer',\n                                            boxShadow: '0 4px 15px rgba(23,162,184,0.3)',\n                                            fontSize: '1rem'\n                                        },\n                                        children: isExporting ? '⏳ جاري التصدير...' : '📊 تصدير Excel'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            textDecoration: 'none',\n                                            fontWeight: 'bold',\n                                            boxShadow: '0 4px 15px rgba(0,123,255,0.3)'\n                                        },\n                                        children: \"\\uD83C\\uDFE0 الرئيسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            boxShadow: '0 8px 25px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#2c3e50',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: \"\\uD83D\\uDD0D البحث والفلترة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"البحث بالاسم أو الوصف:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"ابحث عن مادة إعلامية...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"نوع المادة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedType,\n                                                onChange: (e)=>setSelectedType(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ALL\",\n                                                        children: \"جميع الأنواع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"PROGRAM\",\n                                                        children: \"برنامج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SERIES\",\n                                                        children: \"مسلسل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"MOVIE\",\n                                                        children: \"فيلم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SONG\",\n                                                        children: \"أغنية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"STING\",\n                                                        children: \"Sting\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FILL_IN\",\n                                                        children: \"Fill IN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FILLER\",\n                                                        children: \"Filler\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"PROMO\",\n                                                        children: \"Promo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"الحالة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ALL\",\n                                                        children: \"جميع الحالات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"VALID\",\n                                                        children: \"صالح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_CENSORSHIP\",\n                                                        children: \"مرفوض رقابي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_TECHNICAL\",\n                                                        children: \"مرفوض هندسي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"WAITING\",\n                                                        children: \"في الانتظار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"ترتيب حسب:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"newest\",\n                                                        children: \"الأحدث أولاً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"oldest\",\n                                                        children: \"الأقدم أولاً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: \"الاسم (أ-ي)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"type\",\n                                                        children: \"النوع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '15px',\n                                    padding: '10px',\n                                    background: '#f8f9fa',\n                                    borderRadius: '8px',\n                                    textAlign: 'center',\n                                    color: '#6c757d'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA عرض \",\n                                    filteredItems.length,\n                                    \" من أصل \",\n                                    mediaItems.length,\n                                    \" مادة إعلامية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this),\n                    filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '50px',\n                            textAlign: 'center',\n                            boxShadow: '0 10px 30px rgba(0,0,0,0.2)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#6c757d',\n                                    fontSize: '1.5rem'\n                                },\n                                children: \"\\uD83D\\uDCED لا توجد مواد إعلامية محفوظة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: '#6c757d',\n                                    marginTop: '10px'\n                                },\n                                children: \"ابدأ بإضافة مادة إعلامية جديدة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gap: '20px'\n                        },\n                        children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'rgba(255,255,255,0.95)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    boxShadow: '0 8px 25px rgba(0,0,0,0.1)',\n                                    border: '1px solid #e9ecef'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr auto',\n                                        gap: '20px',\n                                        alignItems: 'start'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#2c3e50',\n                                                        marginBottom: '15px',\n                                                        fontSize: '1.4rem'\n                                                    },\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                        gap: '15px',\n                                                        marginBottom: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"النوع:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getTypeLabel(item.type)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"القناة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getChannelLabel(item.channel)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الحالة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getStatusLabel(item.status)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"عدد السيجمانت:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                item.segments.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#6c757d',\n                                                        marginBottom: '10px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الوصف:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \" \",\n                                                        item.description\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.segments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"السيجمانت:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'grid',\n                                                                gap: '8px',\n                                                                marginTop: '8px'\n                                                            },\n                                                            children: item.segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        background: '#f8f9fa',\n                                                                        padding: '8px 12px',\n                                                                        borderRadius: '8px',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: [\n                                                                                \"#\",\n                                                                                segment.segmentNumber\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \" -\",\n                                                                        segment.code && \" \".concat(segment.code, \" - \"),\n                                                                        segment.timeIn,\n                                                                        \" → \",\n                                                                        segment.timeOut,\n                                                                        \" (\",\n                                                                        segment.duration,\n                                                                        \")\"\n                                                                    ]\n                                                                }, segment.id, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                gap: '10px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        // توجيه لصفحة التعديل مع معرف المادة\n                                                        window.location.href = \"/edit-media?id=\".concat(item.id);\n                                                    },\n                                                    style: {\n                                                        background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '8px 16px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '0.9rem',\n                                                        marginBottom: '5px'\n                                                    },\n                                                    children: \"✏️ تعديل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>deleteMediaItem(item.id),\n                                                    style: {\n                                                        background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '8px 16px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDDD1️ حذف\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 17\n                                }, this)\n                            }, item.id, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    bottom: '20px',\n                    left: '20px',\n                    color: '#6c757d',\n                    fontSize: '0.75rem',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: 'ltr'\n                },\n                children: \"Powered By Mahmoud Ismail\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 556,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n_s(MediaListPage, \"EP5OjVEHjSPUFe6kTv2ugGTcOAs=\");\n_c = MediaListPage;\nvar _c;\n$RefreshReg$(_c, \"MediaListPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/media-list/page.tsx\n"));

/***/ })

});