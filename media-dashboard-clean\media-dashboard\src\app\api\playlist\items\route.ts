import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // إذا كان الطلب لحفظ جدول كامل
    if (body.items && Array.isArray(body.items)) {
      const { date, items } = body;

      if (!date || !items.length) {
        return NextResponse.json({
          success: false,
          error: 'التاريخ والعناصر مطلوبة'
        }, { status: 400 });
      }

      // حذف العناصر الموجودة لهذا التاريخ
      await prisma.playlistItem.deleteMany({
        where: {
          date: new Date(date)
        }
      });

      // إضافة العناصر الجديدة
      const createdItems = await Promise.all(
        items.map((item: any) =>
          prisma.playlistItem.create({
            data: {
              name: item.name || '',
              type: item.type || '',
              startTime: item.startTime,
              endTime: item.endTime,
              duration: item.duration,
              code: item.code || null,
              date: new Date(date),
              status: item.status || 'قادم',
              order: item.order,
              mediaItemId: item.mediaItemId || null
            }
          })
        )
      );

      return NextResponse.json({
        success: true,
        items: createdItems,
        message: 'تم حفظ الجدول بنجاح'
      });
    }

    // إضافة عنصر واحد
    const { name, type, startTime, endTime, duration, code, date, status = 'قادم', mediaItemId } = body;

    if (!startTime || !duration || !date) {
      return NextResponse.json({
        success: false,
        error: 'الوقت والمدة والتاريخ مطلوبة'
      }, { status: 400 });
    }

    // إنشاء عنصر جديد في قائمة التشغيل
    const newItem = await prisma.playlistItem.create({
      data: {
        name: name || '',
        type: type || '',
        startTime,
        endTime,
        duration,
        code: code || null,
        date: new Date(date),
        status,
        order: await getNextOrder(date),
        mediaItemId: mediaItemId || null
      }
    });

    return NextResponse.json({
      success: true,
      item: newItem
    });

  } catch (error) {
    console.error('Error creating playlist item:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء إضافة العنصر'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { itemId, updates, date } = body;

    if (!itemId || !updates) {
      return NextResponse.json({
        success: false,
        error: 'معرف العنصر والتحديثات مطلوبة'
      }, { status: 400 });
    }

    // تحديث العنصر
    const updatedItem = await prisma.playlistItem.update({
      where: { id: itemId },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    });

    return NextResponse.json({
      success: true,
      item: updatedItem
    });

  } catch (error) {
    console.error('Error updating playlist item:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تحديث العنصر'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const itemId = searchParams.get('itemId');
    const date = searchParams.get('date');

    if (!itemId) {
      return NextResponse.json({
        success: false,
        error: 'معرف العنصر مطلوب'
      }, { status: 400 });
    }

    // حذف العنصر
    await prisma.playlistItem.delete({
      where: { id: itemId }
    });

    return NextResponse.json({
      success: true,
      message: 'تم حذف العنصر بنجاح'
    });

  } catch (error) {
    console.error('Error deleting playlist item:', error);
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء حذف العنصر'
    }, { status: 500 });
  }
}

async function getNextOrder(date: string): Promise<number> {
  try {
    const lastItem = await prisma.playlistItem.findFirst({
      where: {
        date: new Date(date)
      },
      orderBy: {
        order: 'desc'
      }
    });

    return (lastItem?.order || 0) + 1;
  } catch (error) {
    console.error('Error getting next order:', error);
    return 1;
  }
}
