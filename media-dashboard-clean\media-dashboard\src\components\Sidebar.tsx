'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from './AuthGuard';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

export default function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, hasPermission } = useAuth();

  const menuItems = [
    {
      name: 'لوحة التحكم',
      icon: '📊',
      path: '/dashboard',
      permission: null
    },
    {
      name: 'المواد الإعلامية',
      icon: '🎬',
      path: '/media-list',
      permission: 'MEDIA_READ'
    },
    {
      name: 'إضافة مادة',
      icon: '➕',
      path: '/add-media',
      permission: 'MEDIA_CREATE'
    },
    {
      name: 'الخريطة البرامجية',
      icon: '📅',
      path: '/weekly-schedule',
      permission: 'SCHEDULE_READ'
    },
    {
      name: 'إدارة المستخدمين',
      icon: '👥',
      path: '/admin-dashboard',
      permission: null,
      adminOnly: true
    },
    {
      name: 'الإحصائيات',
      icon: '📈',
      path: '/statistics',
      permission: null,
      adminOnly: true
    },
    {
      name: 'تصدير البيانات',
      icon: '📤',
      path: '/export',
      permission: null,
      adminOnly: true
    }
  ];

  const filteredMenuItems = menuItems.filter(item => {
    if (item.adminOnly && user?.role !== 'ADMIN') return false;
    if (item.permission && !hasPermission(item.permission)) return false;
    return true;
  });

  return (
    <>
      {/* خلفية شفافة للموبايل */}
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            zIndex: 998,
            display: window.innerWidth <= 768 ? 'block' : 'none'
          }}
          onClick={onToggle}
        />
      )}

      {/* الشريط الجانبي */}
      <div
        style={{
          position: 'fixed',
          top: 0,
          right: isOpen ? 0 : '-280px',
          width: '280px',
          height: '100vh',
          background: '#1a1d29',
          borderLeft: '1px solid #2d3748',
          transition: 'right 0.3s ease',
          zIndex: 999,
          display: 'flex',
          flexDirection: 'column',
          fontFamily: 'Cairo, Arial, sans-serif'
        }}
      >
        {/* رأس الشريط الجانبي */}
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #2d3748',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '10px',
              overflow: 'hidden',
              background: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '5px'
            }}>
              <img 
                src="/images/logo.jpeg" 
                alt="شعار النظام"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.parentElement.innerHTML = `
                    <div style="
                      width: 40px;
                      height: 40px;
                      background: linear-gradient(45deg, #667eea, #764ba2);
                      borderRadius: 10px;
                      display: flex;
                      alignItems: center;
                      justifyContent: center;
                      fontSize: 1.2rem;
                      color: white;
                      fontWeight: bold;
                    ">📺</div>
                  `;
                }}
              />
            </div>
            <div>
              <h3 style={{
                color: 'white',
                margin: 0,
                fontSize: '1.1rem',
                fontWeight: 'bold'
              }}>
                نظام الإدارة
              </h3>
              <p style={{
                color: '#a0aec0',
                margin: 0,
                fontSize: '0.8rem'
              }}>
                المحتوى الإعلامي
              </p>
            </div>
          </div>
          <button
            onClick={onToggle}
            style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer',
              padding: '5px'
            }}
          >
            ✕
          </button>
        </div>

        {/* معلومات المستخدم */}
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #2d3748'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            marginBottom: '10px'
          }}>
            <div style={{
              width: '35px',
              height: '35px',
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '1rem',
              fontWeight: 'bold'
            }}>
              {user?.name?.charAt(0) || 'U'}
            </div>
            <div>
              <p style={{
                color: 'white',
                margin: 0,
                fontSize: '0.9rem',
                fontWeight: 'bold'
              }}>
                {user?.name || 'مستخدم'}
              </p>
              <p style={{
                color: '#a0aec0',
                margin: 0,
                fontSize: '0.8rem'
              }}>
                {user?.role === 'ADMIN' && '👑 مدير النظام'}
                {user?.role === 'MEDIA_MANAGER' && '📝 مدير المحتوى'}
                {user?.role === 'SCHEDULER' && '📅 مجدول البرامج'}
                {user?.role === 'VIEWER' && '👁️ مستخدم عرض'}
              </p>
            </div>
          </div>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '5px',
            color: '#68d391',
            fontSize: '0.8rem'
          }}>
            <div style={{
              width: '6px',
              height: '6px',
              background: '#68d391',
              borderRadius: '50%'
            }}></div>
            متصل
          </div>
        </div>

        {/* قائمة التنقل */}
        <div style={{
          flex: 1,
          padding: '20px 0',
          overflowY: 'auto'
        }}>
          {filteredMenuItems.map((item, index) => {
            const isActive = pathname === item.path;
            return (
              <button
                key={index}
                onClick={() => {
                  router.push(item.path);
                  if (window.innerWidth <= 768) {
                    onToggle();
                  }
                }}
                style={{
                  width: '100%',
                  background: isActive ? '#4299e1' : 'transparent',
                  color: isActive ? 'white' : '#a0aec0',
                  border: 'none',
                  padding: '12px 20px',
                  textAlign: 'right',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  fontSize: '0.9rem',
                  transition: 'all 0.2s',
                  borderRight: isActive ? '3px solid #4299e1' : '3px solid transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.target.style.background = '#2d3748';
                    e.target.style.color = 'white';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.target.style.background = 'transparent';
                    e.target.style.color = '#a0aec0';
                  }
                }}
              >
                <span style={{ fontSize: '1.1rem' }}>{item.icon}</span>
                {item.name}
              </button>
            );
          })}
        </div>

        {/* أسفل الشريط الجانبي */}
        <div style={{
          padding: '20px',
          borderTop: '1px solid #2d3748'
        }}>
          <button
            onClick={() => {
              // إضافة وظيفة تسجيل الخروج هنا
              localStorage.removeItem('user');
              localStorage.removeItem('token');
              router.push('/login');
            }}
            style={{
              width: '100%',
              background: 'linear-gradient(45deg, #f56565, #e53e3e)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '10px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              fontSize: '0.9rem',
              fontWeight: 'bold'
            }}
          >
            🚪 تسجيل الخروج
          </button>
        </div>
      </div>
    </>
  );
}
