import { NextResponse } from 'next/server';

// استيراد البيانات المشتركة
import { getAllMediaItems } from '../shared-data';


// GET - جلب إحصائيات النظام
export async function GET() {
  try {
    const mediaItems = getAllMediaItems();

    // حساب الإحصائيات من البيانات المحفوظة
    const statistics = {
      totalItems: mediaItems.length,
      byType: {},
      byStatus: {},
      totalSegments: 0,
      recentItems: mediaItems.slice(-5).reverse(),
      dailyStats: generateDailyStats(),
      weeklyStats: generateWeeklyStats()
    };

    // إحصائيات الأنواع والحالات
    mediaItems.forEach((item: any) => {
      // إحصائيات الأنواع
      statistics.byType[item.type] = (statistics.byType[item.type] || 0) + 1;

      // إحصائيات الحالات
      statistics.byStatus[item.status] = (statistics.byStatus[item.status] || 0) + 1;

      // إجمالي السيجمانت
      statistics.totalSegments += item.segments?.length || 0;
    });

    return NextResponse.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الإحصائيات' },
      { status: 500 }
    );
  }
}

// دالة لتوليد إحصائيات يومية تجريبية
function generateDailyStats() {
  const days = [];
  const today = new Date();
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    days.push({
      date: date.toISOString().split('T')[0],
      items: Math.floor(Math.random() * 10) + 1,
      segments: Math.floor(Math.random() * 50) + 10
    });
  }
  
  return days;
}

// دالة لتوليد إحصائيات أسبوعية تجريبية
function generateWeeklyStats() {
  const weeks = [];
  const today = new Date();
  
  for (let i = 3; i >= 0; i--) {
    const weekStart = new Date(today);
    weekStart.setDate(weekStart.getDate() - (i * 7));
    
    weeks.push({
      week: `الأسبوع ${4 - i}`,
      items: Math.floor(Math.random() * 50) + 20,
      segments: Math.floor(Math.random() * 200) + 100,
      validItems: Math.floor(Math.random() * 30) + 15,
      rejectedItems: Math.floor(Math.random() * 10) + 2
    });
  }
  
  return weeks;
}

// تصدير دالة للحصول على البيانات للاستخدام في API endpoints أخرى
export { getAllMediaItems };
