'use client';
import React, { useState, useEffect } from 'react';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  duration: string;
  description?: string;
  isTemporary?: boolean;
}

export default function WeeklyScheduleSimplePage() {
  const [loading, setLoading] = useState(true);
  const [availableMedia, setAvailableMedia] = useState<MediaItem[]>([]);
  const [tempMediaItems, setTempMediaItems] = useState<MediaItem[]>([]);
  const [tempMediaName, setTempMediaName] = useState('');
  const [tempMediaType, setTempMediaType] = useState('PROGRAM');

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setAvailableMedia([
        { id: '1', name: 'برنامج تجريبي', type: 'PROGRAM', duration: '01:00:00' }
      ]);
      setLoading(false);
    }, 1000);
  }, []);

  const addTempMedia = () => {
    if (!tempMediaName.trim()) {
      alert('يرجى إدخال اسم المادة');
      return;
    }

    const newTempMedia: MediaItem = {
      id: `temp_${Date.now()}`,
      name: tempMediaName.trim(),
      type: tempMediaType,
      duration: '01:00:00',
      isTemporary: true
    };

    setTempMediaItems(prev => [...prev, newTempMedia]);
    setTempMediaName('');
  };

  const allMedia = [...availableMedia, ...tempMediaItems];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <div style={{ fontSize: '1.5rem' }}>⏳ جاري التحميل...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif', direction: 'rtl' }}>
      <h1>📅 الخريطة البرامجية المبسطة</h1>
      
      <div style={{ 
        background: '#fff3e0', 
        border: '2px solid #ff9800', 
        borderRadius: '8px', 
        padding: '15px', 
        marginBottom: '20px',
        maxWidth: '400px'
      }}>
        <h3>⚡ إضافة مادة مؤقتة</h3>
        
        <input
          type="text"
          placeholder="اسم المادة..."
          value={tempMediaName}
          onChange={(e) => setTempMediaName(e.target.value)}
          style={{
            width: '100%',
            padding: '8px',
            marginBottom: '10px',
            border: '1px solid #ddd',
            borderRadius: '4px'
          }}
        />
        
        <select
          value={tempMediaType}
          onChange={(e) => setTempMediaType(e.target.value)}
          style={{
            width: '100%',
            padding: '8px',
            marginBottom: '10px',
            border: '1px solid #ddd',
            borderRadius: '4px'
          }}
        >
          <option value="PROGRAM">📻 برنامج</option>
          <option value="LIVE">🔴 برنامج هواء مباشر</option>
          <option value="PENDING">🟡 مادة قيد التسليم</option>
        </select>
        
        <button
          onClick={addTempMedia}
          style={{
            width: '100%',
            padding: '10px',
            background: '#ff9800',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontWeight: 'bold'
          }}
        >
          ➕ إضافة
        </button>
      </div>

      <div>
        <h3>📚 قائمة المواد ({allMedia.length})</h3>
        {allMedia.map(item => (
          <div
            key={item.id}
            style={{
              background: item.isTemporary ? '#f3e5f5' : '#fff',
              border: item.isTemporary ? '2px solid #9c27b0' : '1px solid #ddd',
              borderRadius: '8px',
              padding: '10px',
              marginBottom: '10px',
              maxWidth: '400px'
            }}
          >
            <div style={{ fontWeight: 'bold' }}>
              {item.isTemporary && <span style={{ color: '#9c27b0' }}>🟣 </span>}
              {item.name}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {item.type} • {item.duration}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
