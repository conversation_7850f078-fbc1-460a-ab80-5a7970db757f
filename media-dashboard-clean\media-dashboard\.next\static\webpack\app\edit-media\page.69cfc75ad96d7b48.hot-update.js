"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit-media/page",{

/***/ "(app-pages-browser)/./src/app/edit-media/page.tsx":
/*!*************************************!*\
  !*** ./src/app/edit-media/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EditMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const mediaId = searchParams.get('id');\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: '',\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    // تحميل بيانات المادة للتعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditMediaPage.useEffect\": ()=>{\n            if (!mediaId) {\n                router.push('/media-list');\n                return;\n            }\n            const fetchMediaItem = {\n                \"EditMediaPage.useEffect.fetchMediaItem\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/media?id=\".concat(mediaId));\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            var _item_episodeNumber, _item_seasonNumber, _item_partNumber, _item_segments;\n                            const item = result.data;\n                            setFormData({\n                                name: item.name || '',\n                                type: item.type || 'PROGRAM',\n                                description: item.description || '',\n                                channel: item.channel || 'DOCUMENTARY',\n                                source: item.source || '',\n                                status: item.status || 'WAITING',\n                                startDate: item.startDate ? item.startDate.split('T')[0] : '',\n                                endDate: item.endDate ? item.endDate.split('T')[0] : '',\n                                notes: item.notes || '',\n                                episodeNumber: ((_item_episodeNumber = item.episodeNumber) === null || _item_episodeNumber === void 0 ? void 0 : _item_episodeNumber.toString()) || '',\n                                seasonNumber: ((_item_seasonNumber = item.seasonNumber) === null || _item_seasonNumber === void 0 ? void 0 : _item_seasonNumber.toString()) || '',\n                                partNumber: ((_item_partNumber = item.partNumber) === null || _item_partNumber === void 0 ? void 0 : _item_partNumber.toString()) || '',\n                                hardDiskNumber: item.hardDiskNumber || 'SERVER'\n                            });\n                            console.log('📋 البيانات المحملة للتعديل:', {\n                                name: item.name,\n                                status: item.status,\n                                segments: ((_item_segments = item.segments) === null || _item_segments === void 0 ? void 0 : _item_segments.length) || 0\n                            });\n                            if (item.segments && item.segments.length > 0) {\n                                setSegments(item.segments.map({\n                                    \"EditMediaPage.useEffect.fetchMediaItem\": (seg, index)=>({\n                                            id: index + 1,\n                                            segmentCode: seg.segmentCode || \"SEG\".concat(String(index + 1).padStart(3, '0')),\n                                            timeIn: seg.timeIn || '00:00:00',\n                                            timeOut: seg.timeOut || '00:00:00',\n                                            duration: seg.duration || '00:00:00'\n                                        })\n                                }[\"EditMediaPage.useEffect.fetchMediaItem\"]));\n                                setSegmentCount(item.segments.length);\n                            }\n                        } else {\n                            showToast('فشل في تحميل بيانات المادة', 'error');\n                            router.push('/media-list');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching media item:', error);\n                        showToast('خطأ في تحميل البيانات', 'error');\n                        router.push('/media-list');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditMediaPage.useEffect.fetchMediaItem\"];\n            fetchMediaItem();\n        }\n    }[\"EditMediaPage.useEffect\"], [\n        mediaId,\n        router,\n        showToast\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const addSegment = ()=>{\n        const newSegmentCount = segmentCount + 1;\n        setSegmentCount(newSegmentCount);\n        setSegments((prev)=>[\n                ...prev,\n                {\n                    id: newSegmentCount,\n                    segmentCode: \"SEG\".concat(String(newSegmentCount).padStart(3, '0')),\n                    timeIn: '00:00:00',\n                    timeOut: '00:00:00',\n                    duration: '00:00:00'\n                }\n            ]);\n    };\n    const removeSegment = (segmentId)=>{\n        if (segments.length > 1) {\n            setSegments((prev)=>prev.filter((segment)=>segment.id !== segmentId));\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.name.trim()) {\n            showToast('يرجى إدخال اسم المادة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(mediaId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم تحديث المادة الإعلامية بنجاح!', 'success');\n                router.push('/media-list');\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error updating media item:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        transition: 'border-color 0.3s',\n        direction: 'rtl'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '800px',\n                    margin: '0 auto',\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    fontWeight: 'bold',\n                                    color: '#333',\n                                    margin: 0,\n                                    background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent'\n                                },\n                                children: \"✏️ تعديل المادة الإعلامية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/media-list'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '10px 20px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDD19 العودة للقائمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #dee2e6'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#495057',\n                                            marginBottom: '20px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '200px 1fr',\n                                                    gap: '15px',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            color: '#495057',\n                                                            fontWeight: 'bold',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDCBE رقم الهارد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"رقم الهارد\",\n                                                        value: formData.hardDiskNumber,\n                                                        onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            maxWidth: '200px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة *\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: inputStyle,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '80px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"المصدر\",\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"WAITING\",\n                                                                children: \"في الانتظار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"APPROVED\",\n                                                                children: \"مقبول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED\",\n                                                                children: \"مرفوض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: \"منتهي الصلاحية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ البداية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ النهاية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الجزء\",\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الموسم\",\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات إضافية\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '60px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #90caf9'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-between',\n                                            alignItems: 'center',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    color: '#1976d2',\n                                                    fontSize: '1.3rem',\n                                                    margin: 0\n                                                },\n                                                children: \"\\uD83C\\uDFAC إدارة السيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #4caf50, #45a049)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"➕ إضافة سيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this),\n                                    segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: 'white',\n                                                borderRadius: '10px',\n                                                padding: '20px',\n                                                marginBottom: '15px',\n                                                border: '1px solid #e0e0e0'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            style: {\n                                                                color: '#333',\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"سيجمانت \",\n                                                                segment.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        segments.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removeSegment(segment.id),\n                                                            style: {\n                                                                background: '#ff4444',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '15px',\n                                                                padding: '5px 10px',\n                                                                cursor: 'pointer',\n                                                                fontSize: '0.8rem'\n                                                            },\n                                                            children: \"❌ حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                        gap: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"كود السيجمانت\",\n                                                            value: segment.segmentCode,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"وقت البداية (00:00:00)\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"وقت النهاية (00:00:00)\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"المدة (00:00:00)\",\n                                                            value: segment.duration,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'duration', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, segment.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)'\n                                        },\n                                        children: \"✅ حفظ التعديلات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.push('/media-list'),\n                                        style: {\n                                            background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(108,117,125,0.3)'\n                                        },\n                                        children: \"❌ إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 551,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, this);\n}\n_s(EditMediaPage, \"U/Mp9ns9pODrm6O21lgz1y/hFgg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = EditMediaPage;\nvar _c;\n$RefreshReg$(_c, \"EditMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/edit-media/page.tsx\n"));

/***/ })

});