'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/Toast';

export default function LoginPage() {
  const router = useRouter();
  const { showToast, ToastContainer } = useToast();
  
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.username.trim() || !formData.password.trim()) {
      showToast('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        showToast('تم تسجيل الدخول بنجاح!', 'success');
        
        // حفظ بيانات المستخدم في localStorage
        localStorage.setItem('user', JSON.stringify(result.user));
        localStorage.setItem('token', result.token);
        
        // توجيه جميع المستخدمين للوحة التحكم الجديدة
        setTimeout(() => {
          router.push('/dashboard');
        }, 1500);
      } else {
        showToast('خطأ: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Login error:', error);
      showToast('خطأ في الاتصال بالخادم', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: '#1a1d29',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl',
      padding: '20px'
    }}>
      <div style={{
        background: '#2d3748',
        borderRadius: '24px',
        padding: '60px 50px',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        border: '1px solid #4a5568',
        width: '100%',
        maxWidth: '450px',
        textAlign: 'center'
      }}>
        {/* منطقة اللوجو */}
        <div style={{
          marginBottom: '40px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}>
          {/* لوجو Prime X الاحترافي */}
          <div style={{
            marginBottom: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px'
          }}>
            <div style={{
              fontSize: '3.5rem',
              fontWeight: '900',
              fontFamily: 'Arial, sans-serif',
              display: 'flex',
              alignItems: 'center',
              gap: '15px',
              textShadow: '0 4px 8px rgba(0,0,0,0.3)'
            }}>
              <span style={{
                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '900',
                fontSize: '4rem',
                textShadow: '0 0 20px rgba(255, 215, 0, 0.5)',
                filter: 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.3))'
              }}>
                X
              </span>
              <span style={{
                color: '#a0aec0',
                fontSize: '2.5rem',
                fontWeight: '300'
              }}>
                -
              </span>
              <span style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '800',
                letterSpacing: '2px'
              }}>
                Prime
              </span>
            </div>
          </div>
          
          <h1 style={{
            fontSize: '2.2rem',
            fontWeight: 'bold',
            color: '#f3f4f6',
            margin: '0 0 10px 0'
          }}>
            نظام إدارة المحتوى
          </h1>

          <p style={{
            color: '#a0aec0',
            fontSize: '1rem',
            margin: 0
          }}>
            مرحباً بك في نظام إدارة المحتوى الإذاعي
          </p>
        </div>

        {/* نموذج تسجيل الدخول */}
        <form onSubmit={handleSubmit} style={{ width: '100%' }}>
          <div style={{ marginBottom: '25px' }}>
            <input
              type="text"
              placeholder="اسم المستخدم"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              style={{
                width: '100%',
                padding: '16px 20px',
                border: '2px solid #e9ecef',
                borderRadius: '12px',
                fontSize: '1rem',
                fontFamily: 'Cairo, Arial, sans-serif',
                direction: 'rtl',
                outline: 'none',
                transition: 'all 0.3s ease',
                background: 'rgba(255, 255, 255, 0.8)',
                color: '#000',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#667eea';
                e.target.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e9ecef';
                e.target.style.boxShadow = 'none';
              }}
            />
          </div>

          <div style={{ marginBottom: '30px' }}>
            <input
              type="password"
              placeholder="كلمة المرور"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              style={{
                width: '100%',
                padding: '16px 20px',
                border: '2px solid #e9ecef',
                borderRadius: '12px',
                fontSize: '1rem',
                fontFamily: 'Cairo, Arial, sans-serif',
                direction: 'rtl',
                outline: 'none',
                transition: 'all 0.3s ease',
                background: 'rgba(255, 255, 255, 0.8)',
                color: '#000',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#667eea';
                e.target.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e9ecef';
                e.target.style.boxShadow = 'none';
              }}
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '16px',
              background: isLoading 
                ? 'linear-gradient(45deg, #adb5bd, #6c757d)' 
                : 'linear-gradient(45deg, #667eea, #764ba2)',
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 8px 20px rgba(102, 126, 234, 0.3)',
              fontFamily: 'Cairo, Arial, sans-serif'
            }}
            onMouseEnter={(e) => {
              if (!isLoading) {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 12px 25px rgba(102, 126, 234, 0.4)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading) {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 20px rgba(102, 126, 234, 0.3)';
              }
            }}
          >
            {isLoading ? '⏳ جاري تسجيل الدخول...' : '🔐 تسجيل الدخول'}
          </button>
        </form>

        {/* معلومات إضافية */}
        <div style={{
          marginTop: '30px',
          padding: '20px',
          background: 'rgba(102, 126, 234, 0.1)',
          borderRadius: '12px',
          border: '1px solid rgba(102, 126, 234, 0.2)'
        }}>
          <h3 style={{
            color: '#667eea',
            fontSize: '1rem',
            margin: '0 0 10px 0'
          }}>
            🔑 أدوار المستخدمين
          </h3>
          <div style={{
            fontSize: '0.85rem',
            color: '#6c757d',
            textAlign: 'right'
          }}>
            <div>👑 <strong>مدير النظام</strong>: صلاحيات كاملة</div>
            <div>📝 <strong>مدير المحتوى</strong>: إدارة المواد الإعلامية</div>
            <div>📅 <strong>مجدول البرامج</strong>: إدارة الجداول الإذاعية</div>
            <div>👁️ <strong>مستخدم عرض</strong>: تصفح فقط</div>
          </div>
        </div>
      </div>

      <ToastContainer />

      {/* النص السفلي */}
      <div style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        color: '#6c757d',
        fontSize: '0.75rem',
        fontFamily: 'Arial, sans-serif',
        direction: 'ltr'
      }}>
        Powered By Mahmoud Ismail
      </div>
    </div>
  );
}
