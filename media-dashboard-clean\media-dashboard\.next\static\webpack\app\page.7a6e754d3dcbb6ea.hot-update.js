"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [showUserInfo, setShowUserInfo] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // إذا لم يكن المستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول\n            if (!user && !localStorage.getItem('user')) {\n                router.push('/login');\n            } else if (user) {\n                // إذا كان المستخدم مسجل دخول، توجيه للوحة التحكم الجديدة\n                router.push('/dashboard');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: '1200px',\n                margin: '0 auto'\n            },\n            children: [\n                user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '15px',\n                        padding: '20px',\n                        marginBottom: '20px',\n                        boxShadow: '0 10px 30px rgba(0,0,0,0.1)',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        backdropFilter: 'blur(10px)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: '120px',\n                                        height: '50px',\n                                        borderRadius: '12px',\n                                        overflow: 'hidden',\n                                        background: 'white',\n                                        border: '2px solid #667eea',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        padding: '5px'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"/images/logo.jpeg\",\n                                        alt: \"شعار النظام\",\n                                        style: {\n                                            width: '100%',\n                                            height: '100%',\n                                            objectFit: 'contain'\n                                        },\n                                        onError: (e)=>{\n                                            // في حالة عدم وجود الصورة، عرض الحرف الأول\n                                            e.currentTarget.style.display = 'none';\n                                            e.currentTarget.parentElement.innerHTML = '\\n                      <div style=\"\\n                        width: 110px;\\n                        height: 40px;\\n                        background: linear-gradient(45deg, #667eea, #764ba2);\\n                        borderRadius: 12px;\\n                        display: flex;\\n                        alignItems: center;\\n                        justifyContent: center;\\n                        color: white;\\n                        fontSize: 0.9rem;\\n                        fontWeight: bold;\\n                        textAlign: center;\\n                      \">\\uD83D\\uDCFA النظام</div>\\n                    ';\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                margin: 0,\n                                                color: '#333',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: [\n                                                \"مرحباً، \",\n                                                user.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                margin: 0,\n                                                color: '#6c757d',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                user.role === 'ADMIN' && '👑 مدير النظام',\n                                                user.role === 'MEDIA_MANAGER' && '📝 مدير المحتوى',\n                                                user.role === 'SCHEDULER' && '📅 مجدول البرامج',\n                                                user.role === 'VIEWER' && '👁️ مستخدم عرض'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '10px'\n                            },\n                            children: [\n                                user.role === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/admin-dashboard'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 15px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDC51 لوحة المدير\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUserInfo(!showUserInfo),\n                                    style: {\n                                        background: '#f8f9fa',\n                                        color: '#333',\n                                        border: '1px solid #dee2e6',\n                                        borderRadius: '8px',\n                                        padding: '8px 15px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"⚙️ الإعدادات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 15px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDEAA خروج\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'white',\n                        borderRadius: '20px',\n                        padding: '40px',\n                        boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            style: {\n                                fontSize: '2.5rem',\n                                fontWeight: 'bold',\n                                color: '#333',\n                                textAlign: 'center',\n                                marginBottom: '40px',\n                                background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                WebkitBackgroundClip: 'text',\n                                WebkitTextFillColor: 'transparent'\n                            },\n                            children: \"\\uD83C\\uDFAC نظام إدارة المحتوى الإعلامي\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                gap: '20px',\n                                marginBottom: '40px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #1976d215 0%, #1976d225 100%)',\n                                        border: '2px solid #1976d230',\n                                        borderRadius: '15px',\n                                        padding: '20px',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83C\\uDFAC\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#1976d2',\n                                                margin: '10px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: \"إجمالي المواد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#1976d2'\n                                            },\n                                            children: \"1,234\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #2e7d3215 0%, #2e7d3225 100%)',\n                                        border: '2px solid #2e7d3230',\n                                        borderRadius: '15px',\n                                        padding: '20px',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDCFA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#2e7d32',\n                                                margin: '10px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: \"البرامج النشطة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#2e7d32'\n                                            },\n                                            children: \"89\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #ed6c0215 0%, #ed6c0225 100%)',\n                                        border: '2px solid #ed6c0230',\n                                        borderRadius: '15px',\n                                        padding: '20px',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83C\\uDFB5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#ed6c02',\n                                                margin: '10px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: \"الأغاني\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#ed6c02'\n                                            },\n                                            children: \"456\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #d32f2f15 0%, #d32f2f25 100%)',\n                                        border: '2px solid #d32f2f30',\n                                        borderRadius: '15px',\n                                        padding: '20px',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#d32f2f',\n                                                margin: '10px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: \"المواد المرفوضة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#d32f2f'\n                                            },\n                                            children: \"12\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: '2fr 1fr',\n                                gap: '20px',\n                                marginTop: '40px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n                                        borderRadius: '15px',\n                                        padding: '30px',\n                                        border: '1px solid #e0e0e0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: '#333',\n                                                marginBottom: '20px',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA النشاط الأخير\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                textAlign: 'center',\n                                                color: '#666',\n                                                padding: '40px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '3rem',\n                                                        marginBottom: '10px'\n                                                    },\n                                                    children: \"\\uD83D\\uDCC8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد أنشطة حديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',\n                                        borderRadius: '15px',\n                                        padding: '30px',\n                                        border: '1px solid #e0e0e0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: '#333',\n                                                marginBottom: '20px',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDD14 الإشعارات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                textAlign: 'center',\n                                                color: '#666',\n                                                padding: '20px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '3rem',\n                                                        marginBottom: '10px'\n                                                    },\n                                                    children: \"\\uD83D\\uDD15\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد إشعارات جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '40px',\n                                display: 'flex',\n                                justifyContent: 'center',\n                                gap: '20px',\n                                flexWrap: 'wrap'\n                            },\n                            children: [\n                                hasPermission('MEDIA_CREATE') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/add-media'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #2e7d32, #2e7d32dd)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"➕ إضافة مادة إعلامية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                hasPermission('MEDIA_READ') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/media-list'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #f093fb, #f5576c)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"\\uD83D\\uDCDA قائمة المواد الإعلامية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/statistics'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #9c27b0, #7b1fa2)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"\\uD83D\\uDCCA إحصائيات النظام\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 13\n                                }, this),\n                                hasPermission('SCHEDULE_READ') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/weekly-schedule'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #673ab7, #512da8)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"\\uD83D\\uDCC5 الخريطة البرامجية الأسبوعية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/export'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #9c27b0, #9c27b0dd)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"\\uD83D\\uDCE4 تصدير البيانات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '40px',\n                                textAlign: 'center',\n                                padding: '20px',\n                                background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',\n                                borderRadius: '15px',\n                                border: '1px solid #e0e0e0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#333',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDE80 مرحباً بك في نظام إدارة المحتوى الإعلامي المتطور\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#666',\n                                        lineHeight: '1.6'\n                                    },\n                                    children: \"نظام شامل لإدارة المواد الإعلامية مع دعم كامل للغة العربية وواجهة مستخدم عصرية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: '20px'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#555',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: \"✨ الميزات الرئيسية: إدارة المواد الإعلامية • الخريطة البرامجية الأسبوعية • جدول الإذاعة اليومية • تصدير إلى Excel مع دعم RTL\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"SlaB3zud4Dn3sgLC32VA8bZxTTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});