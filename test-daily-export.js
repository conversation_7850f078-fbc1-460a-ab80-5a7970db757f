const XLSX = require('xlsx');
const fs = require('fs');

// قراءة البيانات المحفوظة
const savedData = JSON.parse(fs.readFileSync('./saved-schedules/daily-schedule-2025-06-20.json', 'utf8'));
const scheduleRows = savedData.scheduleRows || [];

console.log('📊 عدد الصفوف:', scheduleRows.length);

// إنشاء مصفوفة البيانات للتصدير
const exportData = [];

// إضافة تاريخ اليوم أعلى الجدول
exportData.push(['تاريخ الإذاعة: 2025-06-20', '', '', '', '', '', '', '', '']);
exportData.push(['', '', '', '', '', '', '', '', '']); // صف فارغ

// إضافة رأس الجدول (من اليمين لليسار)
const headers = [
  'DURATION',
  'OUT', 
  'IN',
  'رقم الهارد',
  'DESCRIPTION',
  'TITLE',
  'TYPE',
  'وقت الإذاعة',
  'ID CODE'
];
exportData.push(headers);

// تنسيق الوقت
function formatTimeWithSeconds(time) {
  if (time && time.includes(':')) {
    const parts = time.split(':');
    if (parts.length === 2) {
      return `${parts[0]}:${parts[1]}:00`;
    }
    return time;
  }
  return '00:00:00';
}

// معالجة كل صف في الجدول
scheduleRows.forEach((row, index) => {
  if (row.type === 'segment' && row.mediaItemId) {
    // حساب الأوقات
    const broadcastTime = formatTimeWithSeconds(row.time || '00:00:00');
    const duration = row.duration || '00:00:00';
    
    // حساب وقت النهاية
    const [hours, minutes, seconds] = broadcastTime.split(':').map(Number);
    const [durHours, durMinutes, durSeconds] = duration.split(':').map(Number);
    const totalSeconds = (hours * 3600 + minutes * 60 + seconds) + (durHours * 3600 + durMinutes * 60 + durSeconds);
    const endHours = Math.floor(totalSeconds / 3600) % 24;
    const endMinutes = Math.floor((totalSeconds % 3600) / 60);
    const endSecs = totalSeconds % 60;
    const endTime = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}:${endSecs.toString().padStart(2, '0')}`;
    
    // تحديد النوع
    let type = 'Program';
    if (row.content.includes('STING')) type = 'Sting';
    else if (row.content.includes('PROMO')) type = 'Promo';
    else if (row.content.includes('FILL_IN')) type = 'Fill IN';
    else if (row.content.includes('FILLER')) type = 'Filler';
    else if (row.content.includes('إعادة')) type = 'Rerun';
    
    // تحديد العنوان والوصف
    let title = row.content || 'غير محدد';
    let description = `2025-06-20 ${broadcastTime} ${title}`;
    
    // كود المادة
    const idCode = row.segmentCode || `DPR${String(index + 1).padStart(5, '0')}`;
    
    // إضافة الصف (من اليمين لليسار)
    const rowData = [
      duration,           // DURATION
      endTime,            // OUT
      broadcastTime,      // IN
      'SERVER',           // رقم الهارد
      description,        // DESCRIPTION
      title,              // TITLE
      type,               // TYPE
      broadcastTime,      // وقت الإذاعة
      idCode              // ID CODE
    ];
    
    exportData.push(rowData);
  } else if (row.type === 'filler' && row.content) {
    // صف فيلر
    const broadcastTime = formatTimeWithSeconds(row.time || '00:00:00');
    const duration = row.duration || '00:11:07';
    
    // حساب وقت النهاية
    const [hours, minutes, seconds] = broadcastTime.split(':').map(Number);
    const [durHours, durMinutes, durSeconds] = duration.split(':').map(Number);
    const totalSeconds = (hours * 3600 + minutes * 60 + seconds) + (durHours * 3600 + durMinutes * 60 + durSeconds);
    const endHours = Math.floor(totalSeconds / 3600) % 24;
    const endMinutes = Math.floor((totalSeconds % 3600) / 60);
    const endSecs = totalSeconds % 60;
    const endTime = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}:${endSecs.toString().padStart(2, '0')}`;
    
    // تحديد النوع
    let type = 'Filler';
    if (row.content.includes('STING')) type = 'Sting';
    else if (row.content.includes('PROMO')) type = 'Promo';
    else if (row.content.includes('FILL_IN')) type = 'Fill IN';
    
    const rowData = [
      duration,           // DURATION
      endTime,            // OUT
      broadcastTime,      // IN
      'SERVER',           // رقم الهارد
      `2025-06-20 ${broadcastTime} ${row.content}`,  // DESCRIPTION
      row.content,        // TITLE
      type,               // TYPE
      broadcastTime,      // وقت الإذاعة
      row.segmentCode || `DPR${String(index + 1).padStart(5, '0')}`  // ID CODE
    ];
    
    exportData.push(rowData);
  }
});

console.log('📋 تم تحضير البيانات للتصدير');
console.log('📊 عدد الصفوف المصدرة:', exportData.length - 3);

// إنشاء مصنف Excel
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.aoa_to_sheet(exportData);

// تطبيق التنسيق
const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

for (let row = range.s.r; row <= range.e.r; row++) {
  for (let col = range.s.c; col <= range.e.c; col++) {
    const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
    const cell = worksheet[cellAddress];
    
    if (cell) {
      // تحديد الألوان حسب نوع المحتوى
      let backgroundColor = 'FFFFFFFF'; // أبيض افتراضي
      let textColor = 'FF000000'; // نص أسود
      
      // تنسيق خاص لصف التاريخ
      if (row === 0) {
        cell.s = {
          font: { name: 'Arial', sz: 12, bold: true, color: { rgb: textColor } },
          alignment: { horizontal: 'right', vertical: 'center', wrapText: true },
          fill: { fgColor: { rgb: backgroundColor } },
          border: {
            top: { style: 'thin', color: { rgb: 'FF000000' } },
            bottom: { style: 'thin', color: { rgb: 'FF000000' } },
            left: { style: 'thin', color: { rgb: 'FF000000' } },
            right: { style: 'thin', color: { rgb: 'FF000000' } }
          }
        };
      }
      // تنسيق خاص لرأس الجدول
      else if (row === 2) {
        backgroundColor = 'FF4CAF50'; // أخضر للرأس
        textColor = 'FFFFFFFF'; // نص أبيض
        
        cell.s = {
          font: { name: 'Arial', sz: 10, bold: true, color: { rgb: textColor } },
          alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
          fill: { fgColor: { rgb: backgroundColor } },
          border: {
            top: { style: 'thin', color: { rgb: 'FF000000' } },
            bottom: { style: 'thin', color: { rgb: 'FF000000' } },
            left: { style: 'thin', color: { rgb: 'FF000000' } },
            right: { style: 'thin', color: { rgb: 'FF000000' } }
          }
        };
      }
      // تنسيق المحتوى حسب النوع
      else if (row > 2) {
        const rowData = exportData[row];
        if (rowData && rowData.length > 6) {
          const type = rowData[6]; // عمود TYPE
          
          // تحديد اللون حسب النوع
          switch (type) {
            case 'Program':
            case 'Rerun':
              backgroundColor = 'FF90EE90'; // أخضر فاتح للمحتوى الأساسي
              break;
            case 'Promo':
              backgroundColor = 'FFFFE4B5'; // بيج للبرومو
              break;
            case 'Sting':
              backgroundColor = 'FFFF6B6B'; // أحمر فاتح للستينغ
              break;
            case 'Filler':
              backgroundColor = 'FFFFA500'; // برتقالي للفيلر
              break;
            case 'Fill IN':
              backgroundColor = 'FF87CEEB'; // أزرق فاتح للـ Fill IN
              break;
            default:
              backgroundColor = 'FFFFFFFF'; // أبيض للباقي
          }
        }
        
        cell.s = {
          font: { name: 'Arial', sz: 10, color: { rgb: textColor } },
          alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
          fill: { fgColor: { rgb: backgroundColor } },
          border: {
            top: { style: 'thin', color: { rgb: 'FF000000' } },
            bottom: { style: 'thin', color: { rgb: 'FF000000' } },
            left: { style: 'thin', color: { rgb: 'FF000000' } },
            right: { style: 'thin', color: { rgb: 'FF000000' } }
          }
        };
      }
    }
  }
}

// تحديد عرض الأعمدة
const columnWidths = [
  { wch: 12 }, // DURATION
  { wch: 10 }, // OUT
  { wch: 10 }, // IN
  { wch: 12 }, // رقم الهارد
  { wch: 35 }, // DESCRIPTION
  { wch: 25 }, // TITLE
  { wch: 12 }, // TYPE
  { wch: 15 }, // وقت الإذاعة
  { wch: 15 }  // ID CODE
];
worksheet['!cols'] = columnWidths;

// إعداد اتجاه الشيت من اليمين لليسار مع زووم 70%
if (!worksheet['!views']) worksheet['!views'] = [{}];
worksheet['!views'][0] = {
  rightToLeft: true,
  zoomScale: 70,
  zoomScaleNormal: 70,
  zoomScalePageLayoutView: 70
};

// إضافة الورقة للمصنف
XLSX.utils.book_append_sheet(workbook, worksheet, 'جدول إذاعي 2025-06-20');

// حفظ الملف
XLSX.writeFile(workbook, 'Daily_Schedule_2025-06-20_Manual_RTL_Colored.xlsx');

console.log('✅ تم إنشاء الملف بنجاح: Daily_Schedule_2025-06-20_Manual_RTL_Colored.xlsx');
