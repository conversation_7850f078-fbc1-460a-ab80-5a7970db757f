import { NextRequest, NextResponse } from 'next/server';
import { DynamicScheduler, TimelineItem } from '@/lib/dynamicScheduler';

// تخزين مؤقت للجداول المعدلة
let customTimelines: { [dateKey: string]: TimelineItem[] } = {};

// PUT - تحديث الجدول الزمني
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { date, timeline } = body;

    if (!date || !Array.isArray(timeline)) {
      return NextResponse.json(
        { success: false, error: 'التاريخ والجدول الزمني مطلوبان' },
        { status: 400 }
      );
    }

    // حفظ الجدول المعدل
    customTimelines[date] = timeline;

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الجدول الزمني بنجاح',
      timeline
    });

  } catch (error) {
    console.error('Error updating timeline:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث الجدول الزمني' },
      { status: 500 }
    );
  }
}

// GET - جلب الجدول المعدل
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json(
        { success: false, error: 'التاريخ مطلوب' },
        { status: 400 }
      );
    }

    const timeline = customTimelines[date] || [];

    return NextResponse.json({
      success: true,
      data: timeline,
      hasCustomTimeline: timeline.length > 0
    });

  } catch (error) {
    console.error('Error fetching custom timeline:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الجدول المعدل' },
      { status: 500 }
    );
  }
}

// DELETE - حذف الجدول المعدل والعودة للافتراضي
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json(
        { success: false, error: 'التاريخ مطلوب' },
        { status: 400 }
      );
    }

    delete customTimelines[date];

    return NextResponse.json({
      success: true,
      message: 'تم حذف الجدول المعدل والعودة للجدول الافتراضي'
    });

  } catch (error) {
    console.error('Error deleting custom timeline:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في حذف الجدول المعدل' },
      { status: 500 }
    );
  }
}

// تصدير البيانات للاستخدام في API endpoints أخرى
export { customTimelines };
