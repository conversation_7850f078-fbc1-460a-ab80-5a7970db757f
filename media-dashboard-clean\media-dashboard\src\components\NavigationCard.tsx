import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './AuthGuard';

interface NavigationCardProps {
  icon: string;
  title: string;
  subtitle: string;
  path: string;
  permission?: string;
  adminOnly?: boolean;
}

// ألوان زاهية للأيقونات المختلفة
const getIconColors = (icon: string) => {
  switch (icon) {
    case '🎬':
      return {
        background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
        shadow: 'rgba(255, 107, 107, 0.4)',
        border: '#ff6b6b'
      };
    case '➕':
      return {
        background: 'linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%)',
        shadow: 'rgba(0, 210, 211, 0.4)',
        border: '#00d2d3'
      };
    case '📅':
      return {
        background: 'linear-gradient(135deg, #5f27cd 0%, #341f97 100%)',
        shadow: 'rgba(95, 39, 205, 0.4)',
        border: '#5f27cd'
      };
    case '📊':
      return {
        background: 'linear-gradient(135deg, #00d2d3 0%, #01a3a4 100%)',
        shadow: 'rgba(0, 210, 211, 0.4)',
        border: '#00d2d3'
      };
    case '👥':
      return {
        background: 'linear-gradient(135deg, #feca57 0%, #ff9ff3 100%)',
        shadow: 'rgba(254, 202, 87, 0.4)',
        border: '#feca57'
      };
    case '📈':
      return {
        background: 'linear-gradient(135deg, #48dbfb 0%, #0abde3 100%)',
        shadow: 'rgba(72, 219, 251, 0.4)',
        border: '#48dbfb'
      };
    default:
      return {
        background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 100%)',
        shadow: 'rgba(255, 215, 0, 0.4)',
        border: '#ffd700'
      };
  }
};

export default function NavigationCard({ 
  icon, 
  title, 
  subtitle, 
  path,
  permission,
  adminOnly = false
}: NavigationCardProps) {
  const router = useRouter();
  const { user, hasPermission } = useAuth();

  // التحقق من الصلاحيات
  if (adminOnly && user?.role !== 'ADMIN') {
    return null;
  }
  
  if (permission && !hasPermission(permission)) {
    return null;
  }

  const handleClick = () => {
    router.push(path);
  };

  const iconColors = getIconColors(icon);

  return (
    <div 
      onClick={handleClick}
      style={{
        background: '#1a1d29',
        borderRadius: '12px',
        padding: '25px',
        border: '1px solid #4a5568',
        position: 'relative',
        overflow: 'hidden',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        transform: 'translateZ(0)',
        textAlign: 'center'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-8px) scale(1.02)';
        e.currentTarget.style.boxShadow = '0 20px 40px rgba(255, 215, 0, 0.3), 0 0 0 2px rgba(255, 215, 0, 0.5)';
        e.currentTarget.style.borderColor = '#ffd700';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0) scale(1)';
        e.currentTarget.style.boxShadow = 'none';
        e.currentTarget.style.borderColor = '#4a5568';
      }}
    >
      <div style={{
        width: '60px',
        height: '60px',
        background: 'linear-gradient(135deg, #2d3748 0%, #4a5568 100%)',
        borderRadius: '15px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '2rem',
        marginBottom: '20px',
        margin: '0 auto 20px auto',
        border: '2px solid #6b7280'
      }}>
        {icon}
      </div>
      
      <div style={{
        fontSize: '1.3rem',
        fontWeight: 'bold',
        color: 'white',
        marginBottom: '8px'
      }}>
        {title}
      </div>
      
      <div style={{
        color: '#a0aec0',
        fontSize: '0.9rem',
        lineHeight: '1.4'
      }}>
        {subtitle}
      </div>

      {/* تأثير الضوء */}
      <div style={{
        position: 'absolute',
        top: '0',
        left: '0',
        right: '0',
        height: '2px',
        background: 'linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.5), transparent)',
        transform: 'translateX(-100%)',
        transition: 'transform 0.6s ease'
      }}></div>
    </div>
  );
}
