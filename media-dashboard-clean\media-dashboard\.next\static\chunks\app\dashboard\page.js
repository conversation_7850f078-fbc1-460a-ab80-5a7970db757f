/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSxrTEFBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_StatsCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/StatsCard */ \"(app-pages-browser)/./src/components/StatsCard.tsx\");\n/* harmony import */ var _components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/NavigationCard */ \"(app-pages-browser)/./src/components/NavigationCard.tsx\");\n/* harmony import */ var _styles_dashboard_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/styles/dashboard.css */ \"(app-pages-browser)/./src/styles/dashboard.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Dashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Dashboard.useEffect.timer\"], 1000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // بيانات الإحصائيات الحقيقية\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMedia: 0,\n        validMedia: 0,\n        rejectedMedia: 0,\n        expiredMedia: 0,\n        pendingMedia: 0,\n        activeUsers: 0,\n        onlineUsers: 0,\n        todayAdded: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // جلب البيانات الحقيقية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchRealStats();\n            // تحديث البيانات كل 30 ثانية\n            const interval = setInterval(fetchRealStats, 30000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(interval)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchRealStats = async ()=>{\n        try {\n            setLoading(true);\n            // جلب البيانات من API مع timeout\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 ثواني timeout\n            const [mediaResponse, usersResponse] = await Promise.all([\n                fetch('/api/media', {\n                    signal: controller.signal\n                }),\n                fetch('/api/users', {\n                    signal: controller.signal\n                })\n            ]);\n            clearTimeout(timeoutId);\n            let mediaData = [];\n            let userData = [];\n            if (mediaResponse.ok) {\n                const mediaResult = await mediaResponse.json();\n                mediaData = mediaResult.success ? mediaResult.data : [];\n            }\n            if (usersResponse.ok) {\n                const usersResult = await usersResponse.json();\n                userData = usersResult.success ? usersResult.users : [];\n            }\n            // حساب الإحصائيات الحقيقية\n            const totalMedia = mediaData.length;\n            const validMedia = mediaData.filter((item)=>item.status === 'VALID').length;\n            const rejectedMedia = mediaData.filter((item)=>item.status === 'REJECTED_CENSORSHIP' || item.status === 'REJECTED_TECHNICAL').length;\n            const expiredMedia = mediaData.filter((item)=>item.status === 'EXPIRED').length;\n            const pendingMedia = mediaData.filter((item)=>item.status === 'PENDING').length;\n            // حساب المواد المضافة اليوم\n            const today = new Date().toDateString();\n            const todayAdded = mediaData.filter((item)=>{\n                if (!item.createdAt) return false;\n                return new Date(item.createdAt).toDateString() === today;\n            }).length;\n            setStats({\n                totalMedia,\n                validMedia,\n                rejectedMedia,\n                expiredMedia,\n                pendingMedia,\n                activeUsers: userData.length,\n                onlineUsers: userData.filter((u)=>u.isActive).length,\n                todayAdded\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // بيانات افتراضية في حالة الخطأ\n            setStats({\n                totalMedia: 0,\n                validMedia: 0,\n                rejectedMedia: 0,\n                expiredMedia: 0,\n                pendingMedia: 0,\n                activeUsers: 4,\n                onlineUsers: 1,\n                todayAdded: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    color: 'white'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            fontSize: '2rem',\n                            fontWeight: '900',\n                            fontFamily: 'Arial, sans-serif',\n                            gap: '8px',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    fontWeight: '900',\n                                    fontSize: '3rem'\n                                },\n                                children: \"X\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    color: '#6c757d',\n                                    fontSize: '2rem',\n                                    fontWeight: '300'\n                                },\n                                children: \"-\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent',\n                                    fontWeight: '800',\n                                    letterSpacing: '1px'\n                                },\n                                children: \"Prime\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '3rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.5rem',\n                            marginBottom: '10px'\n                        },\n                        children: \"جاري تحميل البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#a0aec0',\n                            fontSize: '1rem'\n                        },\n                        children: \"يرجى الانتظار\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        fontSize: '1.2rem',\n                                        fontWeight: '900',\n                                        fontFamily: 'Arial, sans-serif',\n                                        gap: '5px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '900',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"X\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '1rem',\n                                                fontWeight: '300'\n                                            },\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '800',\n                                                letterSpacing: '1px'\n                                            },\n                                            children: \"Prime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        marginRight: sidebarOpen ? '280px' : '0',\n                        transition: 'margin-right 0.3s ease'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: \"لوحة التحكم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: \"رؤية عامة للعملية في الوقت الفعلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#68d391',\n                                                        margin: '5px 0 0 0',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"مراقبة \",\n                                                        stats.totalMedia,\n                                                        \" مادة إعلامية و \",\n                                                        stats.activeUsers,\n                                                        \" مستخدمين نشطين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"البيانات المحلية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"المزامنة: \",\n                                                        currentTime.toLocaleTimeString('ar-EG')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83C\\uDFAC\",\n                                    title: \"المواد الإعلامية\",\n                                    subtitle: \"عرض وإدارة المحتوى\",\n                                    path: \"/media-list\",\n                                    permission: \"MEDIA_READ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"➕\",\n                                    title: \"إضافة مادة\",\n                                    subtitle: \"إضافة محتوى جديد\",\n                                    path: \"/add-media\",\n                                    permission: \"MEDIA_CREATE\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    title: \"الخريطة البرامجية\",\n                                    subtitle: \"جدولة البرامج الأسبوعية\",\n                                    path: \"/weekly-schedule\",\n                                    permission: \"SCHEDULE_READ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCA\",\n                                    title: \"جدول الإذاعة اليومي\",\n                                    subtitle: \"البرامج المجدولة اليوم\",\n                                    path: \"/daily-schedule\",\n                                    permission: \"SCHEDULE_READ\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83D\\uDC65\",\n                                    title: \"إدارة المستخدمين\",\n                                    subtitle: \"إضافة وتعديل المستخدمين\",\n                                    path: \"/admin-dashboard\",\n                                    adminOnly: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC8\",\n                                    title: \"الإحصائيات\",\n                                    subtitle: \"تقارير وإحصائيات مفصلة\",\n                                    path: \"/statistics\",\n                                    adminOnly: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StatsCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    title: \"التاريخ الحالي\",\n                                    value: new Date().toLocaleDateString('en-GB', {\n                                        weekday: 'long',\n                                        day: 'numeric',\n                                        month: 'short'\n                                    }),\n                                    subtitle: currentTime.toLocaleTimeString('ar-EG', {\n                                        hour: '2-digit',\n                                        minute: '2-digit'\n                                    }),\n                                    badge: \"مباشر\",\n                                    badgeColor: \"#68d391\",\n                                    gradient: \"linear-gradient(135deg, #1a1d29 0%, #2d3748 100%)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StatsCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCA\",\n                                    title: \"إجمالي المواد\",\n                                    value: stats.totalMedia,\n                                    subtitle: \"جميع المواد المسجلة\",\n                                    badge: \"+\".concat(stats.todayAdded, \" اليوم\"),\n                                    badgeColor: stats.todayAdded > 0 ? '#68d391' : '#6c757d',\n                                    gradient: \"linear-gradient(135deg, #1a1d29 0%, #2d3748 100%)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"uzZWCdRhffBS169JAH9V1+bLGc8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction AuthGuard(param) {\n    let { children, requiredPermissions = [], requiredRole, fallbackComponent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        // المدير له صلاحيات كاملة\n        if (user.role === 'ADMIN') {\n            return true;\n        }\n        // التحقق من الدور المطلوب\n        if (role && user.role !== role) {\n            return false;\n        }\n        // التحقق من الصلاحيات المطلوبة\n        if (permissions.length > 0) {\n            const userPermissions = getUserPermissions(user.role);\n            return permissions.every((permission)=>userPermissions.includes(permission) || userPermissions.includes('ALL'));\n        }\n        return true;\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user === null || user === void 0 ? void 0 : user.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user === null || user === void 0 ? void 0 : user.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AuthGuard, \"fqF8YvhaHbrPIfzSUHTCm0cPUfQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthGuard;\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        if (user.role === 'ADMIN') return true;\n        const userPermissions = getUserPermissions(user.role);\n        return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN',\n        isMediaManager: (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER',\n        isScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER',\n        isViewer: (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER'\n    };\n}\n_s1(useAuth, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AuthGuard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/NavigationCard.tsx":
/*!*******************************************!*\
  !*** ./src/components/NavigationCard.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n// ألوان زاهية للأيقونات المختلفة\nconst getIconColors = (icon)=>{\n    switch(icon){\n        case '🎬':\n            return {\n                background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',\n                shadow: 'rgba(255, 107, 107, 0.4)',\n                border: '#ff6b6b'\n            };\n        case '➕':\n            return {\n                background: 'linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%)',\n                shadow: 'rgba(0, 210, 211, 0.4)',\n                border: '#00d2d3'\n            };\n        case '📅':\n            return {\n                background: 'linear-gradient(135deg, #5f27cd 0%, #341f97 100%)',\n                shadow: 'rgba(95, 39, 205, 0.4)',\n                border: '#5f27cd'\n            };\n        case '📊':\n            return {\n                background: 'linear-gradient(135deg, #00d2d3 0%, #01a3a4 100%)',\n                shadow: 'rgba(0, 210, 211, 0.4)',\n                border: '#00d2d3'\n            };\n        case '👥':\n            return {\n                background: 'linear-gradient(135deg, #feca57 0%, #ff9ff3 100%)',\n                shadow: 'rgba(254, 202, 87, 0.4)',\n                border: '#feca57'\n            };\n        case '📈':\n            return {\n                background: 'linear-gradient(135deg, #48dbfb 0%, #0abde3 100%)',\n                shadow: 'rgba(72, 219, 251, 0.4)',\n                border: '#48dbfb'\n            };\n        default:\n            return {\n                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 100%)',\n                shadow: 'rgba(255, 215, 0, 0.4)',\n                border: '#ffd700'\n            };\n    }\n};\nfunction NavigationCard(param) {\n    let { icon, title, subtitle, path, permission, adminOnly = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    // التحقق من الصلاحيات\n    if (adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') {\n        return null;\n    }\n    if (permission && !hasPermission(permission)) {\n        return null;\n    }\n    const handleClick = ()=>{\n        router.push(path);\n    };\n    const iconColors = getIconColors(icon);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: handleClick,\n        style: {\n            background: '#1a1d29',\n            borderRadius: '12px',\n            padding: '25px',\n            border: '1px solid #4a5568',\n            position: 'relative',\n            overflow: 'hidden',\n            cursor: 'pointer',\n            transition: 'all 0.3s ease',\n            transform: 'translateZ(0)',\n            textAlign: 'center'\n        },\n        onMouseEnter: (e)=>{\n            e.currentTarget.style.transform = 'translateY(-8px) scale(1.02)';\n            e.currentTarget.style.boxShadow = \"0 20px 40px \".concat(iconColors.shadow, \", 0 0 0 2px \").concat(iconColors.border);\n            e.currentTarget.style.borderColor = iconColors.border;\n        },\n        onMouseLeave: (e)=>{\n            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n            e.currentTarget.style.boxShadow = 'none';\n            e.currentTarget.style.borderColor = '#4a5568';\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: '60px',\n                    height: '60px',\n                    background: iconColors.background,\n                    borderRadius: '15px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '2rem',\n                    marginBottom: '20px',\n                    margin: '0 auto 20px auto',\n                    border: \"2px solid \".concat(iconColors.border),\n                    boxShadow: \"0 8px 20px \".concat(iconColors.shadow),\n                    transition: 'all 0.3s ease'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        filter: 'brightness(1.2) contrast(1.1)',\n                        textShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                    },\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.3rem',\n                    fontWeight: 'bold',\n                    color: 'white',\n                    marginBottom: '8px'\n                },\n                children: title\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: '#a0aec0',\n                    fontSize: '0.9rem',\n                    lineHeight: '1.4'\n                },\n                children: subtitle\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: '0',\n                    left: '0',\n                    right: '0',\n                    height: '2px',\n                    background: \"linear-gradient(90deg, transparent, \".concat(iconColors.border, \"80, transparent)\"),\n                    transform: 'translateX(-100%)',\n                    transition: 'transform 0.6s ease'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationCard, \"APkMxzqj6Cvn9EuTA4qSPdE9DRs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = NavigationCard;\nvar _c;\n$RefreshReg$(_c, \"NavigationCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/NavigationCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Sidebar(param) {\n    let { isOpen, onToggle } = param;\n    var _user_name;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const menuItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'جدول الإذاعة اليومي',\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'إدارة المستخدمين',\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null,\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            path: '/statistics',\n            permission: null,\n            adminOnly: true\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    right: isOpen ? 0 : '-280px',\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    borderLeft: '1px solid #2d3748',\n                    transition: 'right 0.3s ease',\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            fontSize: '1.5rem',\n                                            fontWeight: '900',\n                                            fontFamily: 'Arial, sans-serif',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '900',\n                                                    fontSize: '1.8rem'\n                                                },\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#6c757d',\n                                                    fontSize: '1.2rem',\n                                                    fontWeight: '300'\n                                                },\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '800',\n                                                    letterSpacing: '1px'\n                                                },\n                                                children: \"Prime\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '1.1rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: \"نظام الإدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: \"المحتوى الإعلامي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                style: {\n                                    background: 'transparent',\n                                    border: 'none',\n                                    color: '#a0aec0',\n                                    fontSize: '1.2rem',\n                                    cursor: 'pointer',\n                                    padding: '5px'\n                                },\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    marginBottom: '10px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '35px',\n                                            height: '35px',\n                                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                            borderRadius: '50%',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            color: 'white',\n                                            fontSize: '1rem',\n                                            fontWeight: 'bold'\n                                        },\n                                        children: (user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0)) || 'U'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: (user === null || user === void 0 ? void 0 : user.name) || 'مستخدم'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: [\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && '👑 مدير النظام',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER' && '📝 مدير المحتوى',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER' && '📅 مجدول البرامج',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER' && '👁️ مستخدم عرض'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '5px',\n                                    color: '#68d391',\n                                    fontSize: '0.8rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '6px',\n                                            height: '6px',\n                                            background: '#68d391',\n                                            borderRadius: '50%'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"متصل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#4299e1' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    padding: '12px 20px',\n                                    textAlign: 'right',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    fontSize: '0.9rem',\n                                    transition: 'all 0.2s',\n                                    borderRight: isActive ? '3px solid #4299e1' : '3px solid transparent'\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = '#2d3748';\n                                        e.target.style.color = 'white';\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '1.1rem'\n                                        },\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    // إضافة وظيفة تسجيل الخروج هنا\n                                    localStorage.removeItem('user');\n                                    localStorage.removeItem('token');\n                                    router.push('/login');\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    padding: '10px',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    gap: '8px',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '15px'\n                                },\n                                children: \"\\uD83D\\uDEAA تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    color: '#6c757d',\n                                    fontSize: '0.75rem',\n                                    fontFamily: 'Arial, sans-serif',\n                                    direction: 'ltr'\n                                },\n                                children: \"Powered By Mahmoud Ismail\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"F0XCIKCOXO7Aj/2LRHqPM/x8tuo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/StatsCard.tsx":
/*!**************************************!*\
  !*** ./src/components/StatsCard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction StatsCard(param) {\n    let { icon, title, value, subtitle, badge, badgeColor = '#68d391', gradient } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            background: '#2d3748',\n            borderRadius: '12px',\n            padding: '25px',\n            border: '1px solid #4a5568',\n            position: 'relative',\n            overflow: 'hidden',\n            cursor: 'pointer',\n            transition: 'all 0.3s ease',\n            transform: 'translateZ(0)'\n        },\n        onMouseEnter: (e)=>{\n            e.currentTarget.style.transform = 'translateY(-8px) scale(1.02)';\n            e.currentTarget.style.boxShadow = '0 20px 40px rgba(255, 215, 0, 0.3), 0 0 0 2px rgba(255, 215, 0, 0.5)';\n            e.currentTarget.style.borderColor = '#ffd700';\n        },\n        onMouseLeave: (e)=>{\n            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n            e.currentTarget.style.boxShadow = 'none';\n            e.currentTarget.style.borderColor = '#4a5568';\n        },\n        children: [\n            badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: '15px',\n                    left: '15px',\n                    background: badgeColor,\n                    color: 'white',\n                    padding: '4px 8px',\n                    borderRadius: '6px',\n                    fontSize: '0.8rem',\n                    fontWeight: 'bold'\n                },\n                children: badge\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\StatsCard.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: '50px',\n                    height: '50px',\n                    background: gradient,\n                    borderRadius: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '1.5rem',\n                    marginBottom: '15px',\n                    margin: badge ? '0 auto 15px auto' : '0 0 15px 0'\n                },\n                children: icon\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\StatsCard.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '2.5rem',\n                    fontWeight: 'bold',\n                    color: 'white',\n                    marginBottom: '5px',\n                    textAlign: badge ? 'center' : 'left'\n                },\n                children: typeof value === 'number' ? value.toLocaleString() : value\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\StatsCard.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontWeight: 'bold',\n                    fontSize: '1rem',\n                    marginBottom: '5px',\n                    textAlign: badge ? 'center' : 'left'\n                },\n                children: title\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\StatsCard.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: '#a0aec0',\n                    fontSize: '0.9rem',\n                    textAlign: badge ? 'center' : 'left'\n                },\n                children: subtitle\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\StatsCard.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\StatsCard.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_c = StatsCard;\nvar _c;\n$RefreshReg$(_c, \"StatsCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/StatsCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/dashboard.css":
/*!**********************************!*\
  !*** ./src/styles/dashboard.css ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a68ed52e2e72\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZGFzaGJvYXJkLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxzcmNcXHN0eWxlc1xcZGFzaGJvYXJkLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImE2OGVkNTJlMmU3MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/dashboard.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);