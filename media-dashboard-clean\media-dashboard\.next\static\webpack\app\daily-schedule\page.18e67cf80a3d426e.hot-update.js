"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/daily-schedule/page",{

/***/ "(app-pages-browser)/./src/app/daily-schedule/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/daily-schedule/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DailySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _daily_schedule_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./daily-schedule.css */ \"(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DailySchedulePage() {\n    _s();\n    const { user } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gridRows, setGridRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [weeklySchedule, setWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWeeklySchedule, setShowWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تهيئة التاريخ الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            setSelectedDate(today.toISOString().split('T')[0]);\n        }\n    }[\"DailySchedulePage.useEffect\"], []);\n    // جلب البيانات عند تغيير التاريخ\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (selectedDate) {\n                fetchScheduleData();\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        selectedDate\n    ]);\n    // جلب بيانات الجدول الإذاعي\n    const fetchScheduleData = async ()=>{\n        setLoading(true);\n        try {\n            console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);\n            const response = await fetch(\"/api/daily-schedule?date=\".concat(selectedDate));\n            const data = await response.json();\n            if (data.success) {\n                var _data_data_availableMedia;\n                setScheduleItems(data.data.scheduleItems);\n                setAvailableMedia(data.data.availableMedia || []);\n                setGridRows(data.data.scheduleRows || []);\n                if (data.fromSavedFile) {\n                    console.log('📂 تم تحميل جدول محفوظ مسبقاً');\n                    console.log('💾 تاريخ الحفظ:', data.savedAt);\n                    console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);\n                } else {\n                    console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');\n                    console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');\n                }\n                console.log('📦 المواد المتاحة:', ((_data_data_availableMedia = data.data.availableMedia) === null || _data_data_availableMedia === void 0 ? void 0 : _data_data_availableMedia.length) || 0);\n                // عرض عينة من المواد المتاحة\n                if (data.data.availableMedia && data.data.availableMedia.length > 0) {\n                    console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));\n                }\n            }\n            // جلب الجدول الأسبوعي للمراجعة\n            await fetchWeeklySchedule();\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // جلب الجدول الأسبوعي للمراجعة\n    const fetchWeeklySchedule = async ()=>{\n        try {\n            const date = new Date(selectedDate);\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const data = await response.json();\n            console.log('📊 استجابة API للجدول الأسبوعي:', data);\n            if (data.success && data.data) {\n                // استخراج مواد اليوم المحدد من scheduleItems\n                const dayOfWeek = new Date(selectedDate).getDay();\n                const daySchedule = [];\n                console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);\n                console.log('📦 البيانات المتاحة:', data.data);\n                // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)\n                if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {\n                    console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);\n                    const dayItems = data.data.scheduleItems.filter((item)=>{\n                        var _item_mediaItem;\n                        console.log('🔍 فحص المادة:', (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);\n                        return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء\n                    }).sort((a, b)=>{\n                        // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59\n                        const timeA = a.startTime;\n                        const timeB = b.startTime;\n                        const getTimeOrder = (time)=>{\n                            const hour = parseInt(time.split(':')[0]);\n                            if (hour >= 8 && hour < 18) return 1; // صباح ومساء\n                            if (hour >= 18) return 2; // برايم تايم\n                            return 3; // منتصف الليل والفجر\n                        };\n                        const orderA = getTimeOrder(timeA);\n                        const orderB = getTimeOrder(timeB);\n                        if (orderA !== orderB) return orderA - orderB;\n                        return timeA.localeCompare(timeB);\n                    });\n                    console.log('✅ مواد اليوم المفلترة:', dayItems.length);\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3;\n                        const scheduleItem = {\n                            time: item.startTime,\n                            name: ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'مادة غير محددة',\n                            episodeNumber: item.episodeNumber || ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.episodeNumber),\n                            partNumber: item.partNumber || ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.partNumber),\n                            seasonNumber: item.seasonNumber || ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.seasonNumber),\n                            isRerun: item.isRerun || false\n                        };\n                        daySchedule.push(scheduleItem);\n                        console.log('📺 إضافة مادة للخريطة:', scheduleItem);\n                    });\n                } else {\n                    console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');\n                }\n                setWeeklySchedule(daySchedule);\n                console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');\n            } else {\n                console.log('❌ فشل في جلب البيانات أو البيانات فارغة');\n                setWeeklySchedule([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);\n            setWeeklySchedule([]);\n        }\n    };\n    // فلترة المواد المتاحة\n    const filteredMedia = availableMedia.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesType = filterType === 'ALL' || item.type === filterType;\n        return matchesSearch && matchesType;\n    });\n    // أنواع المواد للفلترة\n    const mediaTypes = [\n        'ALL',\n        'PROGRAM',\n        'SERIES',\n        'MOVIE',\n        'PROMO',\n        'STING',\n        'FILL_IN',\n        'FILLER'\n    ];\n    // إضافة صف فارغ\n    const addEmptyRow = (afterIndex)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        const newRow = {\n            id: \"empty_\".concat(Date.now()),\n            type: 'empty',\n            canDelete: true\n        };\n        newRows.splice(afterIndex + 1, 0, newRow);\n        setGridRows(newRows);\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // إضافة صفوف فارغة متعددة\n    const addMultipleEmptyRows = function(afterIndex) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        for(let i = 0; i < count; i++){\n            const newRow = {\n                id: \"empty_\".concat(Date.now(), \"_\").concat(i),\n                type: 'empty',\n                canDelete: true\n            };\n            newRows.splice(afterIndex + 1 + i, 0, newRow);\n        }\n        setGridRows(newRows);\n        console.log(\"✅ تم إضافة \".concat(count, \" صف فارغ\"));\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // التحقق من الحاجة لإضافة صفوف فارغة\n    const checkAndAddEmptyRows = (currentIndex)=>{\n        const currentRows = gridRows;\n        const nextEmptyIndex = currentRows.findIndex((row, index)=>index > currentIndex && row.type === 'empty');\n        // إذا لم توجد صفوف فارغة بعد الفاصل الحالي\n        if (nextEmptyIndex === -1) {\n            console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 5 صفوف');\n            addMultipleEmptyRows(currentIndex, 5);\n        } else {\n            console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);\n        }\n    };\n    // حذف صف فارغ\n    const deleteRow = (rowId)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        // استعادة موضع التمرير بعد الحذف\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 100);\n    };\n    // حذف سيجمنت\n    const deleteSegment = (rowId)=>{\n        if (confirm('هل أنت متأكد من حذف هذا السيجمنت؟')) {\n            const newRows = gridRows.filter((row)=>row.id !== rowId);\n            recalculateTimes(newRows);\n            console.log('🗑️ تم حذف السيجمنت');\n        }\n    };\n    // حذف فاصل بدون تأكيد\n    const deleteFiller = (rowId)=>{\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        console.log('🗑️ تم حذف الفاصل');\n    };\n    // تحريك صف لأعلى\n    const moveRowUp = (index)=>{\n        if (index <= 0) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index - 1], newRows[index]] = [\n            newRows[index],\n            newRows[index - 1]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬆️ تم تحريك الصف لأعلى');\n    };\n    // تحريك صف لأسفل\n    const moveRowDown = (index)=>{\n        if (index >= gridRows.length - 1) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index], newRows[index + 1]] = [\n            newRows[index + 1],\n            newRows[index]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬇️ تم تحريك الصف لأسفل');\n    };\n    // معالجة سحب الصفوف داخل الجدول\n    const handleRowDragStart = (e, index)=>{\n        e.dataTransfer.setData('text/plain', index.toString());\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    // معالجة إسقاط الصفوف داخل الجدول\n    const handleRowDrop = (e, targetIndex)=>{\n        e.preventDefault();\n        const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));\n        if (sourceIndex === targetIndex) return;\n        const newRows = [\n            ...gridRows\n        ];\n        const [movedRow] = newRows.splice(sourceIndex, 1);\n        newRows.splice(targetIndex, 0, movedRow);\n        recalculateTimes(newRows);\n        console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);\n    };\n    // معالجة إسقاط المواد\n    const handleDrop = (e, rowIndex)=>{\n        e.preventDefault();\n        try {\n            var _mediaData_segments, _mediaData_segments1;\n            // محاولة الحصول على البيانات بطرق مختلفة\n            let mediaData;\n            const jsonData = e.dataTransfer.getData('application/json');\n            const textData = e.dataTransfer.getData('text/plain');\n            if (jsonData) {\n                mediaData = JSON.parse(jsonData);\n            } else if (textData) {\n                try {\n                    mediaData = JSON.parse(textData);\n                } catch (e) {\n                    console.error('❌ لا يمكن تحليل البيانات المسحوبة');\n                    return;\n                }\n            } else {\n                console.error('❌ لا توجد بيانات مسحوبة');\n                return;\n            }\n            // التحقق من أن الصف فارغ\n            const targetRow = gridRows[rowIndex];\n            if (targetRow.type !== 'empty') {\n                alert('يمكن إسقاط المواد في الصفوف الفارغة فقط');\n                return;\n            }\n            console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);\n            // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر\n            if (!mediaData || typeof mediaData !== 'object') {\n                console.error('❌ بيانات المادة غير صحيحة:', mediaData);\n                console.error('❌ نوع البيانات:', typeof mediaData);\n                return;\n            }\n            // التأكد من وجود الاسم\n            const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';\n            const itemType = mediaData.type || 'UNKNOWN';\n            const itemId = mediaData.id || Date.now().toString();\n            console.log('📥 معلومات المادة:', {\n                name: itemName,\n                type: itemType,\n                id: itemId,\n                segments: ((_mediaData_segments = mediaData.segments) === null || _mediaData_segments === void 0 ? void 0 : _mediaData_segments.length) || 0\n            });\n            // تحديد نوع المادة المسحوبة\n            let dragItemType = 'filler';\n            let itemContent = itemName;\n            // إضافة تفاصيل المادة\n            const details = [];\n            if (mediaData.episodeNumber) details.push(\"ح\".concat(mediaData.episodeNumber));\n            if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(\"م\".concat(mediaData.seasonNumber));\n            if (mediaData.partNumber) details.push(\"ج\".concat(mediaData.partNumber));\n            const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n            // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات\n            if ([\n                'PROMO',\n                'STING',\n                'FILLER',\n                'FILL_IN'\n            ].includes(itemType)) {\n                dragItemType = 'filler';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n            } else {\n                dragItemType = 'segment';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" (مادة إضافية)\");\n            }\n            // حساب المدة الحقيقية للمادة\n            let itemDuration = '00:01:00'; // مدة افتراضية\n            console.log('🔍 تحليل مدة المادة:', {\n                name: itemName,\n                hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),\n                segmentsCount: ((_mediaData_segments1 = mediaData.segments) === null || _mediaData_segments1 === void 0 ? void 0 : _mediaData_segments1.length) || 0,\n                hasDuration: !!mediaData.duration,\n                directDuration: mediaData.duration\n            });\n            if (mediaData.segments && mediaData.segments.length > 0) {\n                // حساب إجمالي مدة جميع السيجمنتات\n                let totalSeconds = 0;\n                mediaData.segments.forEach((segment, index)=>{\n                    if (segment.duration) {\n                        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                        const segmentSeconds = hours * 3600 + minutes * 60 + seconds;\n                        totalSeconds += segmentSeconds;\n                        console.log(\"  \\uD83D\\uDCFA سيجمنت \".concat(index + 1, \": \").concat(segment.duration, \" (\").concat(segmentSeconds, \" ثانية)\"));\n                    }\n                });\n                if (totalSeconds > 0) {\n                    const hours = Math.floor(totalSeconds / 3600);\n                    const minutes = Math.floor(totalSeconds % 3600 / 60);\n                    const secs = totalSeconds % 60;\n                    itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                }\n                console.log('📊 حساب مدة المادة من السيجمنتات:', {\n                    name: itemName,\n                    segments: mediaData.segments.length,\n                    totalSeconds,\n                    finalDuration: itemDuration\n                });\n            } else if (mediaData.duration) {\n                // استخدام المدة المباشرة إذا كانت موجودة\n                itemDuration = mediaData.duration;\n                console.log('📊 استخدام مدة مباشرة:', itemDuration);\n            } else {\n                console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);\n            }\n            // إنشاء كود للمادة المسحوبة\n            let itemCode = '';\n            if (mediaData.segmentCode) {\n                itemCode = mediaData.segmentCode;\n            } else if (mediaData.id) {\n                itemCode = \"\".concat(itemType, \"_\").concat(mediaData.id);\n            } else {\n                itemCode = \"\".concat(itemType, \"_\").concat(Date.now().toString().slice(-6));\n            }\n            // إنشاء صف جديد مع المدة الحقيقية\n            const newRow = {\n                id: \"dropped_\".concat(Date.now()),\n                type: dragItemType,\n                content: itemContent,\n                mediaItemId: itemId,\n                segmentCode: itemCode,\n                duration: itemDuration,\n                canDelete: true\n            };\n            // التأكد من أن الصف المستهدف فارغ\n            if (gridRows[rowIndex].type !== 'empty') {\n                console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);\n                return;\n            }\n            // استبدال الصف الفارغ مباشرة\n            const newRows = [\n                ...gridRows\n            ];\n            newRows[rowIndex] = newRow;\n            console.log('✅ تم إضافة المادة:', {\n                name: itemName,\n                type: dragItemType,\n                duration: itemDuration,\n                position: rowIndex,\n                content: itemContent,\n                beforeType: gridRows[rowIndex].type,\n                afterType: newRow.type\n            });\n            // حفظ موضع التمرير الحالي\n            const gridBody = document.querySelector('.grid-body');\n            const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n            // تحديث الصفوف مباشرة\n            setGridRows(newRows);\n            // إعادة حساب الأوقات بعد تأخير قصير\n            setTimeout(()=>{\n                recalculateTimes(newRows);\n                // التحقق من الحاجة لإضافة صفوف فارغة\n                checkAndAddEmptyRows(rowIndex);\n                // استعادة موضع التمرير بعد التحديث\n                setTimeout(()=>{\n                    if (gridBody) {\n                        gridBody.scrollTop = currentScrollTop;\n                        console.log('📍 تم استعادة موضع التمرير:', currentScrollTop);\n                    }\n                }, 100);\n            }, 50);\n        } catch (error) {\n            console.error('❌ خطأ في إسقاط المادة:', error);\n        }\n    };\n    // إعادة حساب الأوقات مثل Excel\n    const recalculateTimes = (rows)=>{\n        const newRows = [\n            ...rows\n        ];\n        let currentTime = '08:00:00'; // نقطة البداية بالثواني\n        let hasFillers = false; // هل تم إضافة فواصل؟\n        // التحقق من وجود فواصل\n        hasFillers = rows.some((row)=>row.type === 'filler');\n        console.log('🔄 بدء إعادة حساب الأوقات من 08:00:00', hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');\n        for(let i = 0; i < newRows.length; i++){\n            const row = newRows[i];\n            if (row.type === 'segment' || row.type === 'filler') {\n                // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل\n                if (i === 0 || hasFillers) {\n                    newRows[i] = {\n                        ...row,\n                        time: currentTime\n                    };\n                } else {\n                    newRows[i] = {\n                        ...row,\n                        time: undefined\n                    };\n                }\n                if (row.duration) {\n                    // حساب الوقت التالي بناءً على المدة\n                    const nextTime = calculateNextTime(currentTime, row.duration);\n                    console.log(\"⏰ \".concat(row.type, ': \"').concat(row.content, '\" - من ').concat(currentTime, \" إلى \").concat(nextTime, \" (مدة: \").concat(row.duration, \")\"));\n                    currentTime = nextTime;\n                }\n            } else if (row.type === 'empty') {\n                // الصفوف الفارغة لا تؤثر على الوقت\n                newRows[i] = {\n                    ...row,\n                    time: undefined\n                };\n            }\n            // التحقق من الوصول لوقت مادة أساسية\n            if (row.originalStartTime && hasFillers) {\n                const targetMinutes = timeToMinutes(row.originalStartTime);\n                const currentMinutes = timeToMinutes(currentTime);\n                const difference = targetMinutes - currentMinutes;\n                if (Math.abs(difference) > 5) {\n                    console.log('⚠️ انحراف زمني: المادة \"'.concat(row.content, '\" مجدولة في ').concat(row.originalStartTime, \" لكن ستدخل في \").concat(currentTime, \" (فرق: \").concat(difference, \" دقيقة)\"));\n                } else {\n                    console.log('✅ توقيت صحيح: المادة \"'.concat(row.content, '\" ستدخل في ').concat(currentTime, \" (مجدولة: \").concat(row.originalStartTime, \")\"));\n                }\n            }\n        }\n        console.log(\"\\uD83C\\uDFC1 انتهاء الحساب - الوقت النهائي: \".concat(currentTime));\n        // حفظ موضع التمرير قبل التحديث\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        // تحديث الصفوف دائماً لضمان التحديث الصحيح\n        setGridRows(newRows);\n        // استعادة موضع التمرير بعد التحديث\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // تحويل الوقت إلى دقائق\n    const timeToMinutes = (time)=>{\n        const [hours, minutes] = time.split(':').map(Number);\n        return hours * 60 + minutes;\n    };\n    // حساب المدة الإجمالية للمادة بدقة\n    const calculateTotalDuration = (item)=>{\n        if (item.segments && item.segments.length > 0) {\n            let totalSeconds = 0;\n            item.segments.forEach((segment)=>{\n                if (segment.duration) {\n                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                }\n            });\n            if (totalSeconds > 0) {\n                const hours = Math.floor(totalSeconds / 3600);\n                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                const secs = totalSeconds % 60;\n                return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n            }\n        }\n        return item.duration || '00:01:00';\n    };\n    // حفظ تعديلات الجدول\n    const saveScheduleChanges = async ()=>{\n        try {\n            console.log('💾 بدء حفظ التعديلات...');\n            console.log('📅 التاريخ:', selectedDate);\n            console.log('📝 عدد الصفوف:', gridRows.length);\n            const response = await fetch('/api/daily-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    date: selectedDate,\n                    scheduleRows: gridRows\n                })\n            });\n            const result = await response.json();\n            if (response.ok && result.success) {\n                console.log('✅ تم حفظ تعديلات الجدول الإذاعي بنجاح');\n                alert('✅ تم حفظ التعديلات بنجاح!');\n            } else {\n                console.error('❌ فشل في حفظ التعديلات:', result.error);\n                alert('❌ فشل في حفظ التعديلات: ' + result.error);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ التعديلات:', error);\n            alert('❌ خطأ في الاتصال بالخادم');\n        }\n    };\n    // تصدير الجدول الإذاعي إلى Excel\n    const exportDailySchedule = async ()=>{\n        try {\n            console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');\n            if (!selectedDate) {\n                alert('يرجى تحديد التاريخ أولاً');\n                return;\n            }\n            const response = await fetch(\"/api/export-daily-schedule-new?date=\".concat(selectedDate));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"Daily_Schedule_\".concat(selectedDate, \".xlsx\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('✅ تم تصدير الجدول الإذاعي بنجاح');\n            alert('✅ تم تصدير الجدول بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في تصدير الجدول:', error);\n            alert('❌ فشل في تصدير الجدول: ' + error.message);\n        }\n    };\n    // حساب الوقت التالي بناءً على المدة (بدقة الثواني)\n    const calculateNextTime = (startTime, duration)=>{\n        // تحليل وقت البداية\n        const startParts = startTime.split(':');\n        const startHours = parseInt(startParts[0]);\n        const startMins = parseInt(startParts[1]);\n        const startSecs = parseInt(startParts[2] || '0');\n        // تحليل المدة\n        const [durHours, durMins, durSecs] = duration.split(':').map(Number);\n        // حساب إجمالي الثواني\n        let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;\n        totalSeconds += durHours * 3600 + durMins * 60 + durSecs;\n        // تحويل إلى ساعات ودقائق وثواني\n        const hours = Math.floor(totalSeconds / 3600) % 24;\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"جدول الإذاعة اليومي\",\n            subtitle: \"البرامج المجدولة اليوم\",\n            icon: \"\\uD83D\\uDCCA\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"schedule-controls\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"date-selector\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"schedule-date\",\n                                    children: \"اختر التاريخ:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"schedule-date\",\n                                    type: \"date\",\n                                    value: selectedDate,\n                                    onChange: (e)=>setSelectedDate(e.target.value),\n                                    className: \"glass-input\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"header-buttons\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: saveScheduleChanges,\n                                    className: \"glass-button primary\",\n                                    children: \"\\uD83D\\uDCBE حفظ التعديلات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: exportDailySchedule,\n                                    className: \"glass-button export\",\n                                    style: {\n                                        background: 'linear-gradient(45deg, #17a2b8, #138496)',\n                                        color: 'white'\n                                    },\n                                    children: \"\\uD83D\\uDCCA تصدير Excel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowWeeklySchedule(!showWeeklySchedule),\n                                    className: \"glass-button\",\n                                    children: showWeeklySchedule ? '📋 إخفاء الخريطة' : '📅 عرض الخريطة'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                    lineNumber: 726,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"schedule-content\",\n                    children: [\n                        showWeeklySchedule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"weekly-sidebar\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"sidebar-title\",\n                                    children: \"الخريطة البرامجية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"weekly-schedule-list\",\n                                    children: Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? weeklySchedule.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"weekly-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"weekly-time\",\n                                                    children: item.time\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"weekly-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"weekly-name\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"weekly-details\",\n                                                            children: [\n                                                                \"ح\",\n                                                                item.episodeNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"weekly-details\",\n                                                            children: [\n                                                                \"ج\",\n                                                                item.partNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"weekly-status\",\n                                                    children: item.isRerun ? '🔄' : '🎯'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"no-data\",\n                                        children: \"لا توجد بيانات للخريطة البرامجية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"media-sidebar\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"sidebar-title\",\n                                    children: \"المواد المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-controls\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterType,\n                                            onChange: (e)=>setFilterType(e.target.value),\n                                            className: \"filter-select\",\n                                            children: mediaTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: type,\n                                                    children: type === 'ALL' ? 'جميع الأنواع' : type\n                                                }, type, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"بحث في المواد...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"search-input\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 804,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"media-list\",\n                                    children: filteredMedia.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"media-item \".concat(item.type.toLowerCase()),\n                                            draggable: true,\n                                            onDragStart: (e)=>{\n                                                e.dataTransfer.setData('application/json', JSON.stringify(item));\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"media-name\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"media-details\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"media-type\",\n                                                            children: item.type\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"media-duration\",\n                                                            children: calculateTotalDuration(item)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"media-info\",\n                                                    children: [\n                                                        item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"info-tag\",\n                                                            children: [\n                                                                \"ح\",\n                                                                item.episodeNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 42\n                                                        }, this),\n                                                        item.seasonNumber && item.seasonNumber > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"info-tag\",\n                                                            children: [\n                                                                \"م\",\n                                                                item.seasonNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 66\n                                                        }, this),\n                                                        item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"info-tag\",\n                                                            children: [\n                                                                \"ج\",\n                                                                item.partNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 39\n                                                        }, this),\n                                                        item.segments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"info-tag\",\n                                                            children: [\n                                                                item.segments.length,\n                                                                \" سيجمنت\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 846,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 15\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"schedule-grid\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"code-column\",\n                                            children: \"الكود\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"time-column\",\n                                            children: \"الوقت\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"content-column\",\n                                            children: \"المحتوى\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"duration-column\",\n                                            children: \"المدة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"status-column\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"actions-column\",\n                                            children: \"إجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 855,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid-body\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading\",\n                                        children: \"جاري التحميل...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 866,\n                                        columnNumber: 15\n                                    }, this) : gridRows.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid-row \".concat(row.type, \" \").concat(row.isRerun ? 'rerun' : '', \" \").concat(row.isTemporary ? 'temporary' : ''),\n                                            draggable: row.type === 'filler' || row.type === 'empty',\n                                            onDragStart: (e)=>handleRowDragStart(e, index),\n                                            onDrop: (e)=>handleRowDrop(e, index),\n                                            onDragOver: (e)=>e.preventDefault(),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"code-cell\",\n                                                    children: row.type === 'segment' || row.type === 'filler' ? row.segmentCode || row.mediaItemId || \"\".concat(row.type.toUpperCase(), \"_\").concat(row.id.slice(-6)) : ''\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"time-cell\",\n                                                    children: row.time || ''\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"content-cell\",\n                                                    onDrop: (e)=>handleDrop(e, index),\n                                                    onDragOver: (e)=>e.preventDefault(),\n                                                    children: row.content || ''\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"duration-cell\",\n                                                    children: row.duration || ''\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"status-cell\",\n                                                    children: [\n                                                        row.type === 'segment' && row.isTemporary && '🟣 مؤقت',\n                                                        row.type === 'segment' && !row.isRerun && !row.isTemporary && (row.originalStartTime ? Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ? '⚠️ انحراف' : '✅ دقيق' : '🎯 أساسي'),\n                                                        row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة',\n                                                        row.type === 'filler' && '📺 فاصل',\n                                                        row.type === 'empty' && '⚪ فارغ'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"actions-cell\",\n                                                    children: [\n                                                        row.type === 'empty' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn add-row\",\n                                                                    title: \"إضافة صف\",\n                                                                    onClick: ()=>addEmptyRow(index),\n                                                                    children: \"➕\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn add-multiple-rows\",\n                                                                    title: \"إضافة 5 صفوف\",\n                                                                    onClick: ()=>addMultipleEmptyRows(index, 5),\n                                                                    children: \"➕➕\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                row.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn delete-row\",\n                                                                    title: \"حذف صف\",\n                                                                    onClick: ()=>deleteRow(row.id),\n                                                                    children: \"➖\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true),\n                                                        row.type === 'filler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn move-up\",\n                                                                    title: \"تحريك لأعلى\",\n                                                                    onClick: ()=>moveRowUp(index),\n                                                                    disabled: index === 0,\n                                                                    children: \"⬆️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn move-down\",\n                                                                    title: \"تحريك لأسفل\",\n                                                                    onClick: ()=>moveRowDown(index),\n                                                                    disabled: index === gridRows.length - 1,\n                                                                    children: \"⬇️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn delete-row\",\n                                                                    title: \"حذف فاصل\",\n                                                                    onClick: ()=>{\n                                                                        // تحويل الفاصل إلى صف فارغ\n                                                                        const newRows = [\n                                                                            ...gridRows\n                                                                        ];\n                                                                        newRows[index] = {\n                                                                            id: \"empty_\".concat(Date.now()),\n                                                                            type: 'empty',\n                                                                            canDelete: true\n                                                                        };\n                                                                        // إعادة حساب الأوقات\n                                                                        recalculateTimes(newRows);\n                                                                    },\n                                                                    children: \"\\uD83D\\uDDD1️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true),\n                                                        row.type === 'segment' && row.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"action-btn replace-temp\",\n                                                            title: \"استبدال بمادة حقيقية\",\n                                                            onClick: ()=>{\n                                                                alert('💡 لاستبدال المادة المؤقتة:\\n\\n1. أضف المادة الحقيقية لقاعدة البيانات\\n2. احذف المادة المؤقتة\\n3. اسحب المادة الجديدة من القائمة الجانبية');\n                                                            },\n                                                            style: {\n                                                                color: '#9c27b0'\n                                                            },\n                                                            children: \"\\uD83D\\uDD04\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        row.type === 'segment' && row.canDelete && !row.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"action-btn delete-row\",\n                                                            title: \"حذف سيجمنت\",\n                                                            onClick: ()=>deleteSegment(row.id),\n                                                            children: \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 987,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, row.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                    lineNumber: 768,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n            lineNumber: 723,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n        lineNumber: 722,\n        columnNumber: 5\n    }, this);\n}\n_s(DailySchedulePage, \"l78MCoamIEbt5T/8CCdwPLkgwyk=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DailySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"DailySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/page.tsx\n"));

/***/ })

});