'use client';
import { useState } from 'react';
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Switch,
  FormControlLabel,
  FormGroup,
  Divider,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
} from '@mui/material';
import {
  Save,
  Restore,
  Backup,
  Security,
  Notifications,
  Language,
  Palette,
  Storage,
  Edit,
  Delete,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';

export default function SettingsPage() {
  const [settings, setSettings] = useState({
    general: {
      language: 'ar',
      theme: 'light',
      autoSave: true,
      notifications: true,
      rtlSupport: true,
    },
    media: {
      defaultChannel: 'DOCUMENTARY',
      defaultStatus: 'WAITING',
      autoGenerateCode: true,
      segmentLimit: 10,
    },
    schedule: {
      timeFormat: '24h',
      weekStartDay: 'saturday',
      autoRefresh: true,
      conflictWarning: true,
    },
    export: {
      defaultFormat: 'xlsx',
      includeMetadata: true,
      compressFiles: false,
      rtlExport: true,
    },
    backup: {
      autoBackup: true,
      backupInterval: 'daily',
      retentionDays: 30,
    },
  });

  const [users, setUsers] = useState([
    { id: 1, name: 'أحمد محمد', role: 'مدير', email: '<EMAIL>', active: true },
    { id: 2, name: 'فاطمة علي', role: 'محرر', email: '<EMAIL>', active: true },
    { id: 3, name: 'محمد حسن', role: 'مشاهد', email: '<EMAIL>', active: false },
  ]);

  const handleSettingChange = (category: string, setting: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [setting]: value,
      },
    }));
  };

  const handleSaveSettings = () => {
    // حفظ الإعدادات
    console.log('Saving settings:', settings);
    alert('تم حفظ الإعدادات بنجاح!');
  };

  const handleResetSettings = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
      // إعادة تعيين الإعدادات للقيم الافتراضية
      alert('تم إعادة تعيين الإعدادات!');
    }
  };

  const handleBackup = () => {
    alert('سيتم تطبيق ميزة النسخ الاحتياطي قريباً');
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Typography variant="h4" gutterBottom sx={{ mb: 4, fontWeight: 'bold' }}>
            إعدادات النظام
          </Typography>

          <Grid container spacing={3}>
            {/* الإعدادات العامة */}
            <Grid item xs={12} md={6}>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Language color="primary" />
                    الإعدادات العامة
                  </Typography>
                  
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.general.autoSave}
                          onChange={(e) => handleSettingChange('general', 'autoSave', e.target.checked)}
                        />
                      }
                      label="الحفظ التلقائي"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.general.notifications}
                          onChange={(e) => handleSettingChange('general', 'notifications', e.target.checked)}
                        />
                      }
                      label="الإشعارات"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.general.rtlSupport}
                          onChange={(e) => handleSettingChange('general', 'rtlSupport', e.target.checked)}
                        />
                      }
                      label="دعم RTL"
                    />
                  </FormGroup>

                  <Box sx={{ mt: 2 }}>
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel>اللغة</InputLabel>
                      <Select
                        value={settings.general.language}
                        onChange={(e) => handleSettingChange('general', 'language', e.target.value)}
                        label="اللغة"
                      >
                        <MenuItem value="ar">العربية</MenuItem>
                        <MenuItem value="en">English</MenuItem>
                      </Select>
                    </FormControl>

                    <FormControl fullWidth>
                      <InputLabel>المظهر</InputLabel>
                      <Select
                        value={settings.general.theme}
                        onChange={(e) => handleSettingChange('general', 'theme', e.target.value)}
                        label="المظهر"
                      >
                        <MenuItem value="light">فاتح</MenuItem>
                        <MenuItem value="dark">داكن</MenuItem>
                        <MenuItem value="auto">تلقائي</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* إعدادات المواد الإعلامية */}
            <Grid item xs={12} md={6}>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Storage color="primary" />
                    إعدادات المواد الإعلامية
                  </Typography>
                  
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.media.autoGenerateCode}
                          onChange={(e) => handleSettingChange('media', 'autoGenerateCode', e.target.checked)}
                        />
                      }
                      label="توليد الكود تلقائياً"
                    />
                  </FormGroup>

                  <Box sx={{ mt: 2 }}>
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel>القناة الافتراضية</InputLabel>
                      <Select
                        value={settings.media.defaultChannel}
                        onChange={(e) => handleSettingChange('media', 'defaultChannel', e.target.value)}
                        label="القناة الافتراضية"
                      >
                        <MenuItem value="DOCUMENTARY">الوثائقية</MenuItem>
                        <MenuItem value="NEWS">الأخبار</MenuItem>
                        <MenuItem value="OTHER">أخرى</MenuItem>
                      </Select>
                    </FormControl>

                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel>الحالة الافتراضية</InputLabel>
                      <Select
                        value={settings.media.defaultStatus}
                        onChange={(e) => handleSettingChange('media', 'defaultStatus', e.target.value)}
                        label="الحالة الافتراضية"
                      >
                        <MenuItem value="VALID">صالح</MenuItem>
                        <MenuItem value="WAITING">في الانتظار</MenuItem>
                      </Select>
                    </FormControl>

                    <TextField
                      label="الحد الأقصى للسيجمانت"
                      type="number"
                      value={settings.media.segmentLimit}
                      onChange={(e) => handleSettingChange('media', 'segmentLimit', parseInt(e.target.value))}
                      fullWidth
                      inputProps={{ min: 1, max: 20 }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* إعدادات الخريطة البرامجية */}
            <Grid item xs={12} md={6}>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Palette color="primary" />
                    إعدادات الخريطة البرامجية
                  </Typography>
                  
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.schedule.autoRefresh}
                          onChange={(e) => handleSettingChange('schedule', 'autoRefresh', e.target.checked)}
                        />
                      }
                      label="التحديث التلقائي"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.schedule.conflictWarning}
                          onChange={(e) => handleSettingChange('schedule', 'conflictWarning', e.target.checked)}
                        />
                      }
                      label="تحذير التعارض"
                    />
                  </FormGroup>

                  <Box sx={{ mt: 2 }}>
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel>تنسيق الوقت</InputLabel>
                      <Select
                        value={settings.schedule.timeFormat}
                        onChange={(e) => handleSettingChange('schedule', 'timeFormat', e.target.value)}
                        label="تنسيق الوقت"
                      >
                        <MenuItem value="12h">12 ساعة</MenuItem>
                        <MenuItem value="24h">24 ساعة</MenuItem>
                      </Select>
                    </FormControl>

                    <FormControl fullWidth>
                      <InputLabel>بداية الأسبوع</InputLabel>
                      <Select
                        value={settings.schedule.weekStartDay}
                        onChange={(e) => handleSettingChange('schedule', 'weekStartDay', e.target.value)}
                        label="بداية الأسبوع"
                      >
                        <MenuItem value="saturday">السبت</MenuItem>
                        <MenuItem value="sunday">الأحد</MenuItem>
                        <MenuItem value="monday">الاثنين</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* إعدادات النسخ الاحتياطي */}
            <Grid item xs={12} md={6}>
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Backup color="primary" />
                    النسخ الاحتياطي
                  </Typography>
                  
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.backup.autoBackup}
                          onChange={(e) => handleSettingChange('backup', 'autoBackup', e.target.checked)}
                        />
                      }
                      label="النسخ الاحتياطي التلقائي"
                    />
                  </FormGroup>

                  <Box sx={{ mt: 2 }}>
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <InputLabel>فترة النسخ الاحتياطي</InputLabel>
                      <Select
                        value={settings.backup.backupInterval}
                        onChange={(e) => handleSettingChange('backup', 'backupInterval', e.target.value)}
                        label="فترة النسخ الاحتياطي"
                      >
                        <MenuItem value="hourly">كل ساعة</MenuItem>
                        <MenuItem value="daily">يومياً</MenuItem>
                        <MenuItem value="weekly">أسبوعياً</MenuItem>
                      </Select>
                    </FormControl>

                    <TextField
                      label="مدة الاحتفاظ (أيام)"
                      type="number"
                      value={settings.backup.retentionDays}
                      onChange={(e) => handleSettingChange('backup', 'retentionDays', parseInt(e.target.value))}
                      fullWidth
                      inputProps={{ min: 1, max: 365 }}
                    />
                  </Box>

                  <Button
                    variant="outlined"
                    startIcon={<Backup />}
                    onClick={handleBackup}
                    fullWidth
                    sx={{ mt: 2 }}
                  >
                    إنشاء نسخة احتياطية الآن
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            {/* إدارة المستخدمين */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Security color="primary" />
                    إدارة المستخدمين
                  </Typography>
                  
                  <List>
                    {users.map((user) => (
                      <ListItem key={user.id} divider>
                        <ListItemText
                          primary={user.name}
                          secondary={`${user.role} - ${user.email}`}
                        />
                        <ListItemSecondaryAction>
                          <Switch checked={user.active} />
                          <IconButton edge="end" sx={{ ml: 1 }}>
                            <Edit />
                          </IconButton>
                          <IconButton edge="end">
                            <Delete />
                          </IconButton>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* أزرار الحفظ */}
          <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<Save />}
              onClick={handleSaveSettings}
              sx={{
                px: 4,
                py: 1.5,
                background: 'linear-gradient(45deg, #2e7d32 30%, #4caf50 90%)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #1b5e20 30%, #2e7d32 90%)',
                },
              }}
            >
              حفظ الإعدادات
            </Button>
            <Button
              variant="outlined"
              size="large"
              startIcon={<Restore />}
              onClick={handleResetSettings}
              sx={{
                px: 4,
                py: 1.5,
                borderColor: '#d32f2f',
                color: '#d32f2f',
                '&:hover': {
                  borderColor: '#c62828',
                  backgroundColor: '#ffebee',
                },
              }}
            >
              إعادة تعيين
            </Button>
          </Box>

          <Alert severity="info" sx={{ mt: 3 }}>
            <Typography variant="body2">
              <strong>ملاحظة:</strong> بعض الإعدادات قد تتطلب إعادة تشغيل التطبيق لتصبح فعالة.
            </Typography>
          </Alert>
        </motion.div>
      </Container>
    </Layout>
  );
}
