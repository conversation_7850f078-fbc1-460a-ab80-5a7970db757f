'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import Sidebar from '@/components/Sidebar';
import '@/styles/dashboard.css';

export default function Dashboard() {
  const router = useRouter();
  const { user, logout, hasPermission } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // بيانات وهمية للإحصائيات
  const stats = {
    totalMedia: 1234,
    validMedia: 1156,
    rejectedMedia: 12,
    expiredMedia: 66,
    activeUsers: 8,
    onlineUsers: 3,
    systemEfficiency: 95,
    avgProcessingTime: 2.5
  };

  const navigationItems = [
    { name: 'لوحة التحكم', icon: '📊', active: true, path: '/dashboard' },
    { name: 'المواد الإعلامية', icon: '🎬', active: false, path: '/media-list' },
    { name: 'المستخدمين', icon: '👥', active: false, path: '/admin-dashboard' },
    { name: 'العمليات', icon: '⚙️', active: false, path: '/operations' },
    { name: 'الصيانة', icon: '🔧', active: false, path: '/maintenance' },
    { name: 'التوريد', icon: '📦', active: false, path: '/supply' },
    { name: 'التقارير', icon: '📋', active: false, path: '/reports' }
  ];

  return (
    <AuthGuard>
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        color: 'white',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        {/* الشريط الجانبي */}
        <Sidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />

        {/* شريط التنقل العلوي */}
        <div style={{
          background: '#1a1d29',
          padding: '15px 30px',
          borderBottom: '1px solid #2d3748',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          {/* زر القائمة واللوجو */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.5rem',
                cursor: 'pointer',
                padding: '5px'
              }}
            >
              ☰
            </button>
            <div style={{
              width: '40px',
              height: '40px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '10px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1.2rem'
            }}>
              📺
            </div>
          </div>

          {/* عناصر التنقل */}
          <div style={{ display: 'flex', gap: '5px' }}>
            {navigationItems.map((item, index) => (
              <button
                key={index}
                onClick={() => router.push(item.path)}
                style={{
                  background: item.active ? '#4299e1' : 'transparent',
                  color: item.active ? 'white' : '#a0aec0',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!item.active) {
                    e.target.style.background = '#2d3748';
                    e.target.style.color = 'white';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.active) {
                    e.target.style.background = 'transparent';
                    e.target.style.color = '#a0aec0';
                  }
                }}
              >
                <span>{item.icon}</span>
                {item.name}
              </button>
            ))}
          </div>

          {/* أدوات المستخدم */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}>
              🔍
            </button>
            <button style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}>
              ⚙️
            </button>
            <button
              onClick={logout}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.2rem',
                cursor: 'pointer'
              }}
            >
              🚪
            </button>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div style={{
          padding: '30px',
          marginRight: sidebarOpen ? '280px' : '0',
          transition: 'margin-right 0.3s ease'
        }}>
          {/* رأس الصفحة */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '30px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem'
              }}>
                📊
              </div>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  margin: '0 0 5px 0',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  لوحة التحكم
                </h1>
                <p style={{
                  color: '#a0aec0',
                  margin: 0,
                  fontSize: '1rem'
                }}>
                  رؤية عامة للعملية في الوقت الفعلي
                </p>
                <p style={{
                  color: '#68d391',
                  margin: '5px 0 0 0',
                  fontSize: '0.9rem'
                }}>
                  مراقبة {stats.totalMedia} مادة إعلامية و {stats.activeUsers} مستخدمين نشطين
                </p>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '20px',
              color: '#a0aec0'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  background: '#68d391',
                  borderRadius: '50%'
                }}></div>
                <span style={{ fontSize: '0.9rem' }}>البيانات المحلية</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>🔄</span>
                <span style={{ fontSize: '0.9rem' }}>
                  المزامنة: {currentTime.toLocaleTimeString('ar-EG')}
                </span>
              </div>
            </div>
          </div>

          {/* بطاقات الإحصائيات */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
            gap: '20px',
            marginBottom: '30px'
          }}>
            {/* إجمالي المواد */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative',
              overflow: 'hidden'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#68d391',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                12%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #9f7aea 0%, #667eea 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                📈
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.totalMedia.toLocaleString()}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                إجمالي المواد
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                المواد المسجلة
              </div>
            </div>

            {/* المواد الصالحة */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#68d391',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                94%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                ✅
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.validMedia.toLocaleString()}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                المواد الصالحة
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                المواد النشطة
              </div>
            </div>

            {/* المواد المرفوضة */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#f56565',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                1%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                ⚠️
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.rejectedMedia}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                قيد الصيانة
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                تحتاج انتباه
              </div>
            </div>

            {/* المستخدمين النشطين */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#4299e1',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                8%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #4299e1 0%, #3182ce 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                👥
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.activeUsers}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                المستخدمين النشطين
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                فريق العمليات
              </div>
            </div>

            {/* كفاءة النظام */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#9f7aea',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                5%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #9f7aea 0%, #805ad5 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                ⚡
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.systemEfficiency}%
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                الكفاءة العامة
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                الأداء المتوسط
              </div>
            </div>

            {/* التكلفة التشغيلية */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568'
            }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #ed64a6 0%, #d53f8c 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                💰
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                ر.س 0,00
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                التكلفة التشغيلية
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                التكلفة اليومية المتوسطة
              </div>
            </div>

            {/* متوسط وقت المعالجة */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568'
            }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                ⏱️
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.avgProcessingTime}دق
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                الوقت المتوسط/العملية
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                المدة المتوسطة
              </div>
            </div>

            {/* العمليات النشطة */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#38b2ac',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                تحميل البيانات الوهمية
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #38b2ac 0%, #319795 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                🔄
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.onlineUsers}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                العمليات النشطة
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                قيد التنفيذ الآن
              </div>
            </div>
          </div>
        </div>
      </div>
    </AuthGuard>
  );
}
