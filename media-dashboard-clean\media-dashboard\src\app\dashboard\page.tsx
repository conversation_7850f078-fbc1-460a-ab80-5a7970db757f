'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import Sidebar from '@/components/Sidebar';
import StatsCard from '@/components/StatsCard';
import NavigationCard from '@/components/NavigationCard';
import '@/styles/dashboard.css';

export default function Dashboard() {
  const router = useRouter();
  const { user, logout, hasPermission } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // بيانات الإحصائيات الحقيقية
  const [stats, setStats] = useState({
    totalMedia: 0,
    validMedia: 0,
    rejectedMedia: 0,
    expiredMedia: 0,
    pendingMedia: 0,
    activeUsers: 0,
    onlineUsers: 0,
    todayAdded: 0
  });

  // جلب البيانات الحقيقية
  useEffect(() => {
    fetchRealStats();
  }, []);

  const fetchRealStats = async () => {
    try {
      // محاكاة جلب البيانات من localStorage أو API
      const mediaData = JSON.parse(localStorage.getItem('mediaItems') || '[]');
      const userData = JSON.parse(localStorage.getItem('users') || '[]');

      // حساب الإحصائيات الحقيقية
      const totalMedia = mediaData.length;
      const validMedia = mediaData.filter(item => item.status === 'approved').length;
      const rejectedMedia = mediaData.filter(item => item.status === 'rejected').length;
      const expiredMedia = mediaData.filter(item => {
        if (!item.endDate) return false;
        return new Date(item.endDate) < new Date();
      }).length;
      const pendingMedia = mediaData.filter(item => item.status === 'pending').length;

      // حساب المواد المضافة اليوم
      const today = new Date().toDateString();
      const todayAdded = mediaData.filter(item => {
        if (!item.createdAt) return false;
        return new Date(item.createdAt).toDateString() === today;
      }).length;

      setStats({
        totalMedia,
        validMedia,
        rejectedMedia,
        expiredMedia,
        pendingMedia,
        activeUsers: userData.length,
        onlineUsers: userData.filter(u => u.isActive).length,
        todayAdded
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
      // بيانات افتراضية في حالة الخطأ
      setStats({
        totalMedia: 0,
        validMedia: 0,
        rejectedMedia: 0,
        expiredMedia: 0,
        pendingMedia: 0,
        activeUsers: 4,
        onlineUsers: 1,
        todayAdded: 0
      });
    }
  };

  const navigationItems = [
    { name: 'لوحة التحكم', icon: '📊', active: true, path: '/dashboard' },
    { name: 'المواد الإعلامية', icon: '🎬', active: false, path: '/media-list', permission: 'MEDIA_READ' },
    { name: 'إضافة مادة', icon: '➕', active: false, path: '/add-media', permission: 'MEDIA_CREATE' },
    { name: 'الخريطة البرامجية', icon: '📅', active: false, path: '/weekly-schedule', permission: 'SCHEDULE_READ' },
    { name: 'المستخدمين', icon: '👥', active: false, path: '/admin-dashboard', adminOnly: true },
    { name: 'الإحصائيات', icon: '📈', active: false, path: '/statistics', adminOnly: true },

  ].filter(item => {
    if (item.adminOnly && user?.role !== 'ADMIN') return false;
    if (item.permission && !hasPermission(item.permission)) return false;
    return true;
  });

  return (
    <AuthGuard>
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        color: 'white',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        {/* الشريط الجانبي */}
        <Sidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />

        {/* شريط التنقل العلوي */}
        <div style={{
          background: '#1a1d29',
          padding: '15px 30px',
          borderBottom: '1px solid #2d3748',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          {/* زر القائمة واللوجو */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.5rem',
                cursor: 'pointer',
                padding: '5px'
              }}
            >
              ☰
            </button>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              fontSize: '1.2rem',
              fontWeight: '900',
              fontFamily: 'Arial, sans-serif',
              gap: '5px'
            }}>
              <span style={{
                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '900',
                fontSize: '1.5rem'
              }}>
                X
              </span>
              <span style={{
                color: '#6c757d',
                fontSize: '1rem',
                fontWeight: '300'
              }}>
                -
              </span>
              <span style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '800',
                letterSpacing: '1px'
              }}>
                Prime
              </span>
            </div>
          </div>

          {/* عناصر التنقل */}
          <div style={{ display: 'flex', gap: '5px' }}>
            {navigationItems.map((item, index) => (
              <button
                key={index}
                onClick={() => router.push(item.path)}
                style={{
                  background: item.active ? '#4299e1' : 'transparent',
                  color: item.active ? 'white' : '#a0aec0',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!item.active) {
                    e.target.style.background = '#2d3748';
                    e.target.style.color = 'white';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.active) {
                    e.target.style.background = 'transparent';
                    e.target.style.color = '#a0aec0';
                  }
                }}
              >
                <span>{item.icon}</span>
                {item.name}
              </button>
            ))}
          </div>

          {/* أدوات المستخدم */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}>
              🔍
            </button>
            <button style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}>
              ⚙️
            </button>
            <button
              onClick={logout}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.2rem',
                cursor: 'pointer'
              }}
            >
              🚪
            </button>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div style={{
          padding: '30px',
          marginRight: sidebarOpen ? '280px' : '0',
          transition: 'margin-right 0.3s ease'
        }}>
          {/* رأس الصفحة */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '30px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem'
              }}>
                📊
              </div>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  margin: '0 0 5px 0',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  لوحة التحكم
                </h1>
                <p style={{
                  color: '#a0aec0',
                  margin: 0,
                  fontSize: '1rem'
                }}>
                  رؤية عامة للعملية في الوقت الفعلي
                </p>
                <p style={{
                  color: '#68d391',
                  margin: '5px 0 0 0',
                  fontSize: '0.9rem'
                }}>
                  مراقبة {stats.totalMedia} مادة إعلامية و {stats.activeUsers} مستخدمين نشطين
                </p>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '20px',
              color: '#a0aec0'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  background: '#68d391',
                  borderRadius: '50%'
                }}></div>
                <span style={{ fontSize: '0.9rem' }}>البيانات المحلية</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>🔄</span>
                <span style={{ fontSize: '0.9rem' }}>
                  المزامنة: {currentTime.toLocaleTimeString('ar-EG')}
                </span>
              </div>
            </div>
          </div>

          {/* بطاقات الإحصائيات - الصف الأول (4 بطاقات) */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '20px',
            marginBottom: '20px'
          }}>
            <NavigationCard
              icon="🎬"
              title="المواد الإعلامية"
              subtitle="عرض وإدارة المحتوى"
              path="/media-list"
              permission="MEDIA_READ"
            />

            <NavigationCard
              icon="➕"
              title="إضافة مادة"
              subtitle="إضافة محتوى جديد"
              path="/add-media"
              permission="MEDIA_CREATE"
            />

            <NavigationCard
              icon="📅"
              title="الخريطة البرامجية"
              subtitle="جدولة البرامج الأسبوعية"
              path="/weekly-schedule"
              permission="SCHEDULE_READ"
            />

            <NavigationCard
              icon="📊"
              title="جدول الإذاعة اليومي"
              subtitle="البرامج المجدولة اليوم"
              path="/daily-schedule"
              permission="SCHEDULE_READ"
            />
          </div>

          {/* بطاقات الإحصائيات - الصف الثاني (4 بطاقات) */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '20px',
            marginBottom: '30px'
          }}>
            <NavigationCard
              icon="👥"
              title="إدارة المستخدمين"
              subtitle="إضافة وتعديل المستخدمين"
              path="/admin-dashboard"
              adminOnly={true}
            />

            <NavigationCard
              icon="📈"
              title="الإحصائيات"
              subtitle="تقارير وإحصائيات مفصلة"
              path="/statistics"
              adminOnly={true}
            />

            <StatsCard
              icon="📅"
              title="التاريخ الحالي"
              value={new Date().toLocaleDateString('ar-EG', {
                weekday: 'long',
                day: 'numeric',
                month: 'short'
              })}
              subtitle={currentTime.toLocaleTimeString('ar-EG', {
                hour: '2-digit',
                minute: '2-digit'
              })}
              badge="مباشر"
              badgeColor="#68d391"
              gradient="linear-gradient(135deg, #1a1d29 0%, #2d3748 100%)"
            />

            <StatsCard
              icon="📊"
              title="إجمالي المواد"
              value={stats.totalMedia}
              subtitle="جميع المواد المسجلة"
              badge={`+${stats.todayAdded} اليوم`}
              badgeColor={stats.todayAdded > 0 ? '#68d391' : '#6c757d'}
              gradient="linear-gradient(135deg, #1a1d29 0%, #2d3748 100%)"
            />


          </div>
        </div>

        {/* النص السفلي */}
        <div style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          color: '#6c757d',
          fontSize: '0.75rem',
          fontFamily: 'Arial, sans-serif',
          direction: 'ltr'
        }}>
          Powered By Mahmoud Ismail
        </div>
      </div>
    </AuthGuard>
  );
}
