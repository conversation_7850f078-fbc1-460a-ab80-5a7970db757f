'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import Sidebar from '@/components/Sidebar';
import '@/styles/dashboard.css';

export default function Dashboard() {
  const router = useRouter();
  const { user, logout, hasPermission } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // بيانات الإحصائيات الحقيقية
  const [stats, setStats] = useState({
    totalMedia: 0,
    validMedia: 0,
    rejectedMedia: 0,
    expiredMedia: 0,
    pendingMedia: 0,
    activeUsers: 0,
    onlineUsers: 0,
    todayAdded: 0
  });

  // جلب البيانات الحقيقية
  useEffect(() => {
    fetchRealStats();
  }, []);

  const fetchRealStats = async () => {
    try {
      // محاكاة جلب البيانات من localStorage أو API
      const mediaData = JSON.parse(localStorage.getItem('mediaItems') || '[]');
      const userData = JSON.parse(localStorage.getItem('users') || '[]');

      // حساب الإحصائيات الحقيقية
      const totalMedia = mediaData.length;
      const validMedia = mediaData.filter(item => item.status === 'approved').length;
      const rejectedMedia = mediaData.filter(item => item.status === 'rejected').length;
      const expiredMedia = mediaData.filter(item => {
        if (!item.endDate) return false;
        return new Date(item.endDate) < new Date();
      }).length;
      const pendingMedia = mediaData.filter(item => item.status === 'pending').length;

      // حساب المواد المضافة اليوم
      const today = new Date().toDateString();
      const todayAdded = mediaData.filter(item => {
        if (!item.createdAt) return false;
        return new Date(item.createdAt).toDateString() === today;
      }).length;

      setStats({
        totalMedia,
        validMedia,
        rejectedMedia,
        expiredMedia,
        pendingMedia,
        activeUsers: userData.length,
        onlineUsers: userData.filter(u => u.isActive).length,
        todayAdded
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
      // بيانات افتراضية في حالة الخطأ
      setStats({
        totalMedia: 0,
        validMedia: 0,
        rejectedMedia: 0,
        expiredMedia: 0,
        pendingMedia: 0,
        activeUsers: 4,
        onlineUsers: 1,
        todayAdded: 0
      });
    }
  };

  const navigationItems = [
    { name: 'لوحة التحكم', icon: '📊', active: true, path: '/dashboard' },
    { name: 'المواد الإعلامية', icon: '🎬', active: false, path: '/media-list', permission: 'MEDIA_READ' },
    { name: 'إضافة مادة', icon: '➕', active: false, path: '/add-media', permission: 'MEDIA_CREATE' },
    { name: 'الخريطة البرامجية', icon: '📅', active: false, path: '/weekly-schedule', permission: 'SCHEDULE_READ' },
    { name: 'المستخدمين', icon: '👥', active: false, path: '/admin-dashboard', adminOnly: true },
    { name: 'الإحصائيات', icon: '📈', active: false, path: '/statistics', adminOnly: true },
    { name: 'تصدير البيانات', icon: '📤', active: false, path: '/export', adminOnly: true }
  ].filter(item => {
    if (item.adminOnly && user?.role !== 'ADMIN') return false;
    if (item.permission && !hasPermission(item.permission)) return false;
    return true;
  });

  return (
    <AuthGuard>
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        color: 'white',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        {/* الشريط الجانبي */}
        <Sidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />

        {/* شريط التنقل العلوي */}
        <div style={{
          background: '#1a1d29',
          padding: '15px 30px',
          borderBottom: '1px solid #2d3748',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          {/* زر القائمة واللوجو */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.5rem',
                cursor: 'pointer',
                padding: '5px'
              }}
            >
              ☰
            </button>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              fontSize: '1.2rem',
              fontWeight: '900',
              fontFamily: 'Arial, sans-serif'
            }}>
              <span style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '800',
                letterSpacing: '1px'
              }}>
                Prime
              </span>
              <span style={{
                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '900',
                fontSize: '1.5rem',
                marginLeft: '3px'
              }}>
                X
              </span>
            </div>
          </div>

          {/* عناصر التنقل */}
          <div style={{ display: 'flex', gap: '5px' }}>
            {navigationItems.map((item, index) => (
              <button
                key={index}
                onClick={() => router.push(item.path)}
                style={{
                  background: item.active ? '#4299e1' : 'transparent',
                  color: item.active ? 'white' : '#a0aec0',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!item.active) {
                    e.target.style.background = '#2d3748';
                    e.target.style.color = 'white';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.active) {
                    e.target.style.background = 'transparent';
                    e.target.style.color = '#a0aec0';
                  }
                }}
              >
                <span>{item.icon}</span>
                {item.name}
              </button>
            ))}
          </div>

          {/* أدوات المستخدم */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}>
              🔍
            </button>
            <button style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}>
              ⚙️
            </button>
            <button
              onClick={logout}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.2rem',
                cursor: 'pointer'
              }}
            >
              🚪
            </button>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div style={{
          padding: '30px',
          marginRight: sidebarOpen ? '280px' : '0',
          transition: 'margin-right 0.3s ease'
        }}>
          {/* رأس الصفحة */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '30px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem'
              }}>
                📊
              </div>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  margin: '0 0 5px 0',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  لوحة التحكم
                </h1>
                <p style={{
                  color: '#a0aec0',
                  margin: 0,
                  fontSize: '1rem'
                }}>
                  رؤية عامة للعملية في الوقت الفعلي
                </p>
                <p style={{
                  color: '#68d391',
                  margin: '5px 0 0 0',
                  fontSize: '0.9rem'
                }}>
                  مراقبة {stats.totalMedia} مادة إعلامية و {stats.activeUsers} مستخدمين نشطين
                </p>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '20px',
              color: '#a0aec0'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  background: '#68d391',
                  borderRadius: '50%'
                }}></div>
                <span style={{ fontSize: '0.9rem' }}>البيانات المحلية</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>🔄</span>
                <span style={{ fontSize: '0.9rem' }}>
                  المزامنة: {currentTime.toLocaleTimeString('ar-EG')}
                </span>
              </div>
            </div>
          </div>

          {/* بطاقات الإحصائيات - الصف الأول (3 بطاقات) */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '20px',
            marginBottom: '20px',
            justifyContent: 'center'
          }}>
            {/* إجمالي المواد */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative',
              overflow: 'hidden'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: stats.todayAdded > 0 ? '#68d391' : '#6c757d',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                +{stats.todayAdded} اليوم
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #9f7aea 0%, #667eea 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                📊
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.totalMedia.toLocaleString()}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                إجمالي المواد
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                جميع المواد المسجلة
              </div>
            </div>

            {/* المواد المعتمدة */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#68d391',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                {stats.totalMedia > 0 ? Math.round((stats.validMedia / stats.totalMedia) * 100) : 0}%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                ✅
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.validMedia.toLocaleString()}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                المواد المعتمدة
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                جاهزة للبث
              </div>
            </div>

            {/* المواد المرفوضة */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#f56565',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                {stats.totalMedia > 0 ? Math.round((stats.rejectedMedia / stats.totalMedia) * 100) : 0}%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                ❌
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.rejectedMedia}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                المواد المرفوضة
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                مرفوضة تقنياً
              </div>
            </div>
          </div>

          {/* بطاقات الإحصائيات - الصف الثاني (4 بطاقات) */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '20px',
            marginBottom: '30px'
          }}>
            {/* المواد المنتهية الصلاحية */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#ed8936',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                {stats.totalMedia > 0 ? Math.round((stats.expiredMedia / stats.totalMedia) * 100) : 0}%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                ⏰
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.expiredMedia}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                المواد المنتهية
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                انتهت صلاحيتها
              </div>
            </div>

            {/* المواد قيد المراجعة */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#9f7aea',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                {stats.totalMedia > 0 ? Math.round((stats.pendingMedia / stats.totalMedia) * 100) : 0}%
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #9f7aea 0%, #805ad5 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                ⏳
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.pendingMedia}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                قيد المراجعة
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                في انتظار الموافقة
              </div>
            </div>

            {/* المستخدمين النشطين */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568'
            }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #4299e1 0%, #3182ce 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                👥
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.activeUsers}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                المستخدمين المسجلين
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                إجمالي المستخدمين
              </div>
            </div>

            {/* المستخدمين المتصلين */}
            <div style={{
              background: '#2d3748',
              borderRadius: '12px',
              padding: '25px',
              border: '1px solid #4a5568',
              position: 'relative'
            }}>
              <div style={{
                position: 'absolute',
                top: '15px',
                left: '15px',
                background: '#68d391',
                color: 'white',
                padding: '4px 8px',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold'
              }}>
                متصل الآن
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #38b2ac 0%, #319795 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem',
                marginBottom: '15px'
              }}>
                🟢
              </div>
              <div style={{
                fontSize: '2.5rem',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '5px'
              }}>
                {stats.onlineUsers}
              </div>
              <div style={{
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1rem',
                marginBottom: '5px'
              }}>
                المستخدمين المتصلين
              </div>
              <div style={{
                color: '#a0aec0',
                fontSize: '0.9rem'
              }}>
                نشط حالياً
              </div>
            </div>


          </div>
        </div>
      </div>
    </AuthGuard>
  );
}
