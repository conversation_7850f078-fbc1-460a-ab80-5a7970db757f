"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/weekly-schedule/page",{

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WeeklySchedulePage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع\n    const days = [\n        'الأحد',\n        'الاثنين',\n        'الثلاثاء',\n        'الأربعاء',\n        'الخميس',\n        'الجمعة',\n        'السبت'\n    ];\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        const startDate = new Date(selectedWeek + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n        const dates = [];\n        console.log('📅 حساب تواريخ الأسبوع من:', selectedWeek);\n        for(let i = 0; i < 7; i++){\n            const date = new Date(startDate);\n            date.setDate(startDate.getDate() + i);\n            // استخدام الأرقام العربية العادية (1234567890) مع السنة\n            const dateStr = date.toLocaleDateString('en-GB', {\n                day: '2-digit',\n                month: '2-digit',\n                year: 'numeric'\n            });\n            dates.push(dateStr);\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(date.toISOString().split('T')[0], \" → \").concat(dateStr));\n        }\n        return dates;\n    };\n    const weekDates = getWeekDates();\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || 'غير معروف';\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - الجزء \".concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم:', today.toISOString().split('T')[0]);\n            console.log('  📊 يوم الأسبوع:', today.getDay(), '(0=أحد)');\n            const sunday = new Date(today);\n            sunday.setDate(today.getDate() - today.getDay());\n            const weekStart = sunday.toISOString().split('T')[0];\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current > 0) {\n                // تأخير أطول للتأكد من اكتمال الرندر\n                const timer = setTimeout({\n                    \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                        const targetPosition = scrollPositionRef.current;\n                        window.scrollTo({\n                            top: targetPosition,\n                            behavior: 'instant'\n                        });\n                        // التحقق من أن التمرير تم بنجاح\n                        setTimeout({\n                            \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                                if (Math.abs(window.scrollY - targetPosition) > 10) {\n                                    window.scrollTo({\n                                        top: targetPosition,\n                                        behavior: 'instant'\n                                    });\n                                }\n                            }\n                        }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 50);\n                        shouldRestoreScroll.current = false;\n                        console.log('📍 تم استعادة موضع التمرير:', targetPosition);\n                    }\n                }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 200);\n                return ({\n                    \"WeeklySchedulePage.useLayoutEffect\": ()=>clearTimeout(timer)\n                })[\"WeeklySchedulePage.useLayoutEffect\"];\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"خطأ في تحديث البيانات: \".concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"خطأ في الاتصال: \".concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert('يرجى إدخال اسم المادة');\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حفظ المادة المؤقتة');\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert('خطأ في حفظ المادة المؤقتة');\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm('هل تريد حذف هذه المادة المؤقتة؟')) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حذف المادة المؤقتة');\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert('خطأ في حذف المادة المؤقتة');\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ يوجد تداخل في الأوقات! اختر وقت آخر.');\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                await fetchScheduleData();\n            } else {\n                alert(result.error);\n                shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'المادة';\n        const itemType = item.isRerun ? 'إعادة' : item.isTemporary ? 'مادة مؤقتة' : 'مادة أصلية';\n        const confirmed = window.confirm(\"هل أنت متأكد من حذف \".concat(itemType, ': \"').concat(itemName, '\"؟\\n\\n') + \"الوقت: \".concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + (item.isRerun ? 'تحذير: حذف الإعادة لن يؤثر على المادة الأصلية' : item.isTemporary ? 'سيتم حذف المادة المؤقتة من الجدول' : 'تحذير: حذف المادة الأصلية سيحذف جميع إعاداتها'));\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف محلي\n            if (item.isTemporary) {\n                if (item.isRerun) {\n                    // حذف إعادة مؤقتة فقط\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                    console.log(\"✅ تم حذف إعادة مؤقتة: \".concat(itemName));\n                } else {\n                    // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isTemporary && scheduleItem.originalId === item.id)));\n                    console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها: \".concat(itemName));\n                }\n                return;\n            }\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        const currentDate = new Date(selectedWeek);\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        setSelectedWeek(currentDate.toISOString().split('T')[0]);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"⏳ جاري تحميل الجدول الأسبوعي...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 720,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 719,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"\\uD83D\\uDCC5 جاري تحديد التاريخ...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 729,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 728,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"الخريطة البرامجية الأسبوعية\",\n            subtitle: \"جدولة البرامج الأسبوعية\",\n            icon: \"\\uD83D\\uDCC5\",\n            fullWidth: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    height: 'calc(100vh - 120px)',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: 'rtl',\n                    gap: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '320px',\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            border: '1px solid #6b7280',\n                            padding: '20px',\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: '0 0 15px 0',\n                                    color: '#f3f4f6'\n                                },\n                                children: \"\\uD83D\\uDCDA قائمة المواد\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    border: '2px solid #f59e0b',\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            margin: '0 0 10px 0',\n                                            color: '#fbbf24',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: \"⚡ إضافة مادة مؤقتة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"اسم المادة...\",\n                                        value: tempMediaName,\n                                        onChange: (e)=>setTempMediaName(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: tempMediaType,\n                                        onChange: (e)=>setTempMediaType(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROGRAM\",\n                                                children: \"\\uD83D\\uDCFB برنامج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SERIES\",\n                                                children: \"\\uD83D\\uDCFA مسلسل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 799,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MOVIE\",\n                                                children: \"\\uD83C\\uDFA5 فيلم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 800,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"LIVE\",\n                                                children: \"\\uD83D\\uDD34 برنامج هواء مباشر\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PENDING\",\n                                                children: \"\\uD83D\\uDFE1 مادة قيد التسليم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"المدة (مثل: 01:30:00)\",\n                                        value: tempMediaDuration,\n                                        onChange: (e)=>setTempMediaDuration(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 805,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"ملاحظات (اختياري)...\",\n                                        value: tempMediaNotes,\n                                        onChange: (e)=>setTempMediaNotes(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '10px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: addTempMedia,\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            background: '#ff9800',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '13px',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            marginBottom: '8px'\n                                        },\n                                        children: \"➕ إضافة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            console.log('🔄 تحديث الإعادات...');\n                                            scrollPositionRef.current = window.scrollY;\n                                            shouldRestoreScroll.current = true;\n                                            await fetchScheduleData();\n                                        },\n                                        style: {\n                                            width: '100%',\n                                            padding: '6px',\n                                            background: '#4caf50',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"♻️ تحديث الإعادات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 857,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedType,\n                                onChange: (e)=>setSelectedType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '10px',\n                                    fontSize: '14px',\n                                    backgroundColor: 'white',\n                                    color: '#333'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"\\uD83C\\uDFAC جميع الأنواع\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: \"\\uD83D\\uDCFA مسلسل\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 895,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MOVIE\",\n                                        children: \"\\uD83C\\uDFA5 فيلم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: \"\\uD83D\\uDCFB برنامج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROMO\",\n                                        children: \"\\uD83D\\uDCE2 إعلان\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"STING\",\n                                        children: \"⚡ ستينج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 899,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILL_IN\",\n                                        children: \"\\uD83D\\uDD04 فيل إن\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILLER\",\n                                        children: \"⏸️ فيلر\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 901,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"\\uD83D\\uDD0D البحث في المواد...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '15px',\n                                    fontSize: '14px',\n                                    color: '#333',\n                                    background: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 905,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '12px',\n                                    color: '#d1d5db',\n                                    marginBottom: '10px',\n                                    textAlign: 'center',\n                                    padding: '5px',\n                                    background: '#1f2937',\n                                    borderRadius: '4px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    filteredMedia.length,\n                                    \" من \",\n                                    allAvailableMedia.length,\n                                    \" مادة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 923,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '8px'\n                                },\n                                children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '20px',\n                                        color: '#666',\n                                        background: '#f8f9fa',\n                                        borderRadius: '8px',\n                                        border: '2px dashed #dee2e6'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: 'bold',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"لا توجد مواد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: searchTerm || selectedType ? 'جرب تغيير الفلتر أو البحث' : 'أضف مواد جديدة من صفحة المستخدم'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 949,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 939,\n                                    columnNumber: 17\n                                }, this) : filteredMedia.map((item)=>{\n                                    // تحديد لون المادة حسب النوع\n                                    const getItemStyle = ()=>{\n                                        if (item.isTemporary) {\n                                            switch(item.type){\n                                                case 'LIVE':\n                                                    return {\n                                                        background: '#ffebee',\n                                                        border: '2px solid #f44336',\n                                                        borderLeft: '5px solid #f44336'\n                                                    };\n                                                case 'PENDING':\n                                                    return {\n                                                        background: '#fff8e1',\n                                                        border: '2px solid #ffc107',\n                                                        borderLeft: '5px solid #ffc107'\n                                                    };\n                                                default:\n                                                    return {\n                                                        background: '#f3e5f5',\n                                                        border: '2px solid #9c27b0',\n                                                        borderLeft: '5px solid #9c27b0'\n                                                    };\n                                            }\n                                        }\n                                        return {\n                                            background: '#fff',\n                                            border: '1px solid #ddd'\n                                        };\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>handleDragStart(e, item),\n                                        style: {\n                                            ...getItemStyle(),\n                                            borderRadius: '8px',\n                                            padding: '12px',\n                                            cursor: 'grab',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                            position: 'relative'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(-2px)';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(0)';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteTempMedia(item.id);\n                                                },\n                                                style: {\n                                                    position: 'absolute',\n                                                    top: '5px',\n                                                    left: '5px',\n                                                    background: '#f44336',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '50%',\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    fontSize: '12px',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                title: \"حذف المادة المؤقتة\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1010,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: 'bold',\n                                                    color: '#333',\n                                                    marginBottom: '4px'\n                                                },\n                                                children: [\n                                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '10px',\n                                                            background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                            color: 'white',\n                                                            padding: '2px 6px',\n                                                            borderRadius: '10px',\n                                                            marginLeft: '5px'\n                                                        },\n                                                        children: item.type === 'LIVE' ? '🔴 هواء' : item.type === 'PENDING' ? '🟡 قيد التسليم' : '🟣 مؤقت'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1039,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    getMediaDisplayText(item)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '12px',\n                                                    color: '#666'\n                                                },\n                                                children: [\n                                                    item.type,\n                                                    \" • \",\n                                                    item.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1054,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '11px',\n                                                    color: '#888',\n                                                    marginTop: '4px'\n                                                },\n                                                children: item.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1058,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 937,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 745,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px',\n                                    background: '#4a5568',\n                                    padding: '15px',\n                                    borderRadius: '15px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/daily-schedule',\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: \"\\uD83D\\uDCCB الجدول الإذاعي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1083,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                        const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                        if (!response.ok) {\n                                                            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                        }\n                                                        const blob = await response.blob();\n                                                        const url = window.URL.createObjectURL(blob);\n                                                        const a = document.createElement('a');\n                                                        a.href = url;\n                                                        a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                        document.body.appendChild(a);\n                                                        a.click();\n                                                        window.URL.revokeObjectURL(url);\n                                                        document.body.removeChild(a);\n                                                        console.log('✅ تم تصدير الخريطة بنجاح');\n                                                    } catch (error) {\n                                                        console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                        alert('فشل في تصدير الخريطة: ' + error.message);\n                                                    }\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: \"\\uD83D\\uDCCA تصدير الخريطة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#f3f4f6',\n                                                    fontSize: '1.2rem'\n                                                },\n                                                children: \"\\uD83D\\uDCC5 الأسبوع المحدد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1155,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1082,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(-1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: \"← الأسبوع السابق\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1159,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedWeek,\n                                                onChange: (e)=>{\n                                                    const selectedDate = new Date(e.target.value + 'T12:00:00');\n                                                    const dayOfWeek = selectedDate.getDay();\n                                                    console.log('📅 تغيير التاريخ من التقويم:', {\n                                                        selectedDate: e.target.value,\n                                                        dayOfWeek: dayOfWeek,\n                                                        dayName: [\n                                                            'الأحد',\n                                                            'الاثنين',\n                                                            'الثلاثاء',\n                                                            'الأربعاء',\n                                                            'الخميس',\n                                                            'الجمعة',\n                                                            'السبت'\n                                                        ][dayOfWeek]\n                                                    });\n                                                    // حساب بداية الأسبوع (الأحد)\n                                                    const sunday = new Date(selectedDate);\n                                                    sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                                    const weekStart = sunday.toISOString().split('T')[0];\n                                                    console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                                    setSelectedWeek(weekStart);\n                                                },\n                                                style: {\n                                                    padding: '8px',\n                                                    border: '1px solid #6b7280',\n                                                    borderRadius: '5px',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1173,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: \"الأسبوع التالي →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1204,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1158,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1072,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',\n                                    borderRadius: '10px',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                    minHeight: '80vh'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#f8f9fa'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '12px',\n                                                            border: '1px solid #dee2e6',\n                                                            fontWeight: 'bold',\n                                                            width: '80px',\n                                                            color: '#000'\n                                                        },\n                                                        children: \"الوقت\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1232,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '12px',\n                                                                border: '1px solid #dee2e6',\n                                                                fontWeight: 'bold',\n                                                                width: \"\".concat(100 / 7, \"%\"),\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        marginBottom: '4px',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: day\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1249,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: weekDates[index]\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1250,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1242,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1231,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1230,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                background: '#f8f9fa',\n                                                                fontWeight: 'bold',\n                                                                textAlign: 'center',\n                                                                padding: '8px',\n                                                                border: '1px solid #dee2e6',\n                                                                color: '#000'\n                                                            },\n                                                            children: hour\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1262,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        days.map((_, dayIndex)=>{\n                                                            const cellItems = getItemsForCell(dayIndex, hour);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                onDragOver: handleDragOver,\n                                                                onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                                style: {\n                                                                    border: '1px solid #dee2e6',\n                                                                    padding: '8px',\n                                                                    height: '150px',\n                                                                    cursor: 'pointer',\n                                                                    background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',\n                                                                    verticalAlign: 'top'\n                                                                },\n                                                                children: cellItems.map((item)=>{\n                                                                    var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        draggable: true,\n                                                                        onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                        onClick: ()=>deleteItem(item),\n                                                                        style: {\n                                                                            background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                            border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                            borderRadius: '4px',\n                                                                            padding: '8px 6px',\n                                                                            marginBottom: '4px',\n                                                                            fontSize: '1rem',\n                                                                            cursor: 'grab',\n                                                                            transition: 'all 0.2s ease',\n                                                                            minHeight: '60px',\n                                                                            display: 'flex',\n                                                                            flexDirection: 'column',\n                                                                            justifyContent: 'center'\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1.02)';\n                                                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1)';\n                                                                            e.currentTarget.style.boxShadow = 'none';\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    gap: '4px',\n                                                                                    color: '#000'\n                                                                                },\n                                                                                children: [\n                                                                                    item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#666'\n                                                                                        },\n                                                                                        children: [\n                                                                                            \"♻️ \",\n                                                                                            item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                            item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1330,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                        },\n                                                                                        children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1334,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#ff9800'\n                                                                                        },\n                                                                                        children: \"\\uD83C\\uDF1F\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1342,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#000'\n                                                                                        },\n                                                                                        children: item.mediaItem ? getMediaDisplayText(item.mediaItem) : 'غير معروف'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1344,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1328,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.8rem',\n                                                                                    color: '#000',\n                                                                                    marginTop: '4px'\n                                                                                },\n                                                                                children: [\n                                                                                    item.startTime,\n                                                                                    \" - \",\n                                                                                    item.endTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1348,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.5rem',\n                                                                                    color: '#888',\n                                                                                    fontStyle: 'italic'\n                                                                                },\n                                                                                children: \"إعادة - يمكن الحذف للتعديل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1352,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, item.id, true, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1290,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, dayIndex, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1276,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, hourIndex, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1261,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1259,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1228,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    padding: '20px',\n                                    borderRadius: '15px',\n                                    marginTop: '20px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 20px 0',\n                                            fontSize: '1.3rem',\n                                            textAlign: 'center'\n                                        },\n                                        children: \"\\uD83D\\uDCCB تعليمات الاستخدام:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1375,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDFAF إضافة المواد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1379,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"اسحب المواد من القائمة اليمنى إلى الجدول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1381,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83D\\uDD04 اسحب المواد من الجدول نفسه لنسخها لمواعيد أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1382,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83C\\uDFAC استخدم فلتر النوع للتصفية حسب نوع المادة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1383,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83D\\uDD0D استخدم البحث للعثور على المواد بسرعة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1384,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1380,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1378,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDDD1️ حذف المواد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1388,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"المواد الأصلية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1390,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" حذف نهائي مع جميع إعاداتها\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1390,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"الإعادات:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" حذف مع ترك الحقل فارغ للتعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1391,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"سيظهر تأكيد قبل الحذف\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1392,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1387,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1377,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDF1F المواد الأصلية (البرايم تايم):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1399,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"الأحد-الأربعاء: 18:00-00:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1401,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"الخميس-السبت: 18:00-02:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1402,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    \"\\uD83D\\uDFE1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        style: {\n                                                                            color: '#fbbf24'\n                                                                        },\n                                                                        children: \"لون ذهبي في الجدول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1403,\n                                                                        columnNumber: 56\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1403,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1400,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1398,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"♻️ الإعادات التلقائية (جزئين):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1407,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الأحد-الأربعاء:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1409,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1409,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج1: نفس العمود 00:00-07:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1411,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج2: العمود التالي 08:00-17:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1410,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الخميس-السبت:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1414,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج1: نفس العمود 02:00-07:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1416,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج2: العمود التالي 08:00-17:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1417,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1415,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    \"\\uD83D\\uDD18 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        style: {\n                                                                            color: '#9ca3af'\n                                                                        },\n                                                                        children: \"لون رمادي - يمكن حذفها للتعديل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1419,\n                                                                        columnNumber: 56\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1419,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1408,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1406,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #f59e0b'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: \"\\uD83D\\uDCC5 إدارة التواريخ:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1425,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \" استخدم الكالندر والأزرار للتنقل بين الأسابيع • كل أسبوع يُحفظ بشكل منفصل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1426,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1424,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #3b82f6'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: \"\\uD83D\\uDCA1 ملاحظة مهمة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1430,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \" عند إضافة مواد قليلة (١-٣ مواد) في البرايم، ستظهر مع فواصل زمنية في الإعادات لتجنب التكرار المفرط. أضف المزيد من المواد للحصول على تنوع أكبر.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1431,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1429,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1368,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1070,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 737,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 736,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 735,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"v2z1JHXlWVqbyv5DN3U+nYUgV3s=\");\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ })

});