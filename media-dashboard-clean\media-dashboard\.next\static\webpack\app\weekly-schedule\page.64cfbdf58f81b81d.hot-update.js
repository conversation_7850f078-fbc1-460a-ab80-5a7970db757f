"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/weekly-schedule/page",{

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WeeklySchedulePage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع\n    const days = [\n        'الأحد',\n        'الاثنين',\n        'الثلاثاء',\n        'الأربعاء',\n        'الخميس',\n        'الجمعة',\n        'السبت'\n    ];\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        const startDate = new Date(selectedWeek + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n        const dates = [];\n        console.log('📅 حساب تواريخ الأسبوع من:', selectedWeek);\n        for(let i = 0; i < 7; i++){\n            const date = new Date(startDate);\n            date.setDate(startDate.getDate() + i);\n            // استخدام الأرقام العربية العادية (1234567890)\n            const dateStr = date.toLocaleDateString('en-US', {\n                day: '2-digit',\n                month: '2-digit'\n            });\n            dates.push(dateStr);\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(date.toISOString().split('T')[0], \" → \").concat(dateStr));\n        }\n        return dates;\n    };\n    const weekDates = getWeekDates();\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || 'غير معروف';\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - الجزء \".concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم:', today.toISOString().split('T')[0]);\n            console.log('  📊 يوم الأسبوع:', today.getDay(), '(0=أحد)');\n            const sunday = new Date(today);\n            sunday.setDate(today.getDate() - today.getDay());\n            const weekStart = sunday.toISOString().split('T')[0];\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current > 0) {\n                window.scrollTo(0, scrollPositionRef.current);\n                shouldRestoreScroll.current = false;\n                console.log('📍 تم استعادة موضع التمرير:', scrollPositionRef.current);\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"خطأ في تحديث البيانات: \".concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"خطأ في الاتصال: \".concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert('يرجى إدخال اسم المادة');\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حفظ المادة المؤقتة');\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert('خطأ في حفظ المادة المؤقتة');\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm('هل تريد حذف هذه المادة المؤقتة؟')) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حذف المادة المؤقتة');\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert('خطأ في حذف المادة المؤقتة');\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ يوجد تداخل في الأوقات! اختر وقت آخر.');\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                await fetchScheduleData();\n            } else {\n                alert(result.error);\n                shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'المادة';\n        const itemType = item.isRerun ? 'إعادة' : item.isTemporary ? 'مادة مؤقتة' : 'مادة أصلية';\n        const confirmed = window.confirm(\"هل أنت متأكد من حذف \".concat(itemType, ': \"').concat(itemName, '\"؟\\n\\n') + \"الوقت: \".concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + (item.isRerun ? 'تحذير: حذف الإعادة لن يؤثر على المادة الأصلية' : item.isTemporary ? 'سيتم حذف المادة المؤقتة من الجدول' : 'تحذير: حذف المادة الأصلية سيحذف جميع إعاداتها'));\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف محلي\n            if (item.isTemporary) {\n                if (item.isRerun) {\n                    // حذف إعادة مؤقتة فقط\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                    console.log(\"✅ تم حذف إعادة مؤقتة: \".concat(itemName));\n                } else {\n                    // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isTemporary && scheduleItem.originalId === item.id)));\n                    console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها: \".concat(itemName));\n                }\n                return;\n            }\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        const currentDate = new Date(selectedWeek);\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        setSelectedWeek(currentDate.toISOString().split('T')[0]);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"⏳ جاري تحميل الجدول الأسبوعي...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 699,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 698,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"\\uD83D\\uDCC5 جاري تحديد التاريخ...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 708,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 707,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"الخريطة البرامجية الأسبوعية\",\n            subtitle: \"جدولة البرامج الأسبوعية\",\n            icon: \"\\uD83D\\uDCC5\",\n            fullWidth: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    height: 'calc(100vh - 120px)',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: 'rtl',\n                    gap: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '320px',\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            border: '1px solid #6b7280',\n                            padding: '20px',\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: '0 0 15px 0',\n                                    color: '#f3f4f6'\n                                },\n                                children: \"\\uD83D\\uDCDA قائمة المواد\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    border: '2px solid #f59e0b',\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            margin: '0 0 10px 0',\n                                            color: '#fbbf24',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: \"⚡ إضافة مادة مؤقتة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"اسم المادة...\",\n                                        value: tempMediaName,\n                                        onChange: (e)=>setTempMediaName(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: tempMediaType,\n                                        onChange: (e)=>setTempMediaType(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROGRAM\",\n                                                children: \"\\uD83D\\uDCFB برنامج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SERIES\",\n                                                children: \"\\uD83D\\uDCFA مسلسل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MOVIE\",\n                                                children: \"\\uD83C\\uDFA5 فيلم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"LIVE\",\n                                                children: \"\\uD83D\\uDD34 برنامج هواء مباشر\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PENDING\",\n                                                children: \"\\uD83D\\uDFE1 مادة قيد التسليم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 781,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"المدة (مثل: 01:30:00)\",\n                                        value: tempMediaDuration,\n                                        onChange: (e)=>setTempMediaDuration(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"ملاحظات (اختياري)...\",\n                                        value: tempMediaNotes,\n                                        onChange: (e)=>setTempMediaNotes(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '10px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 801,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: addTempMedia,\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            background: '#ff9800',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '13px',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            marginBottom: '8px'\n                                        },\n                                        children: \"➕ إضافة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            console.log('🔄 تحديث الإعادات...');\n                                            scrollPositionRef.current = window.scrollY;\n                                            shouldRestoreScroll.current = true;\n                                            await fetchScheduleData();\n                                        },\n                                        style: {\n                                            width: '100%',\n                                            padding: '6px',\n                                            background: '#4caf50',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"♻️ تحديث الإعادات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 735,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedType,\n                                onChange: (e)=>setSelectedType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '10px',\n                                    fontSize: '14px',\n                                    backgroundColor: 'white',\n                                    color: '#333'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"\\uD83C\\uDFAC جميع الأنواع\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: \"\\uD83D\\uDCFA مسلسل\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MOVIE\",\n                                        children: \"\\uD83C\\uDFA5 فيلم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: \"\\uD83D\\uDCFB برنامج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROMO\",\n                                        children: \"\\uD83D\\uDCE2 إعلان\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"STING\",\n                                        children: \"⚡ ستينج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILL_IN\",\n                                        children: \"\\uD83D\\uDD04 فيل إن\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILLER\",\n                                        children: \"⏸️ فيلر\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 880,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 859,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"\\uD83D\\uDD0D البحث في المواد...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '15px',\n                                    fontSize: '14px',\n                                    color: '#333',\n                                    background: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 884,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '12px',\n                                    color: '#d1d5db',\n                                    marginBottom: '10px',\n                                    textAlign: 'center',\n                                    padding: '5px',\n                                    background: '#1f2937',\n                                    borderRadius: '4px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    filteredMedia.length,\n                                    \" من \",\n                                    allAvailableMedia.length,\n                                    \" مادة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '8px'\n                                },\n                                children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '20px',\n                                        color: '#666',\n                                        background: '#f8f9fa',\n                                        borderRadius: '8px',\n                                        border: '2px dashed #dee2e6'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 926,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: 'bold',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"لا توجد مواد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 927,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: searchTerm || selectedType ? 'جرب تغيير الفلتر أو البحث' : 'أضف مواد جديدة من صفحة المستخدم'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 918,\n                                    columnNumber: 17\n                                }, this) : filteredMedia.map((item)=>{\n                                    // تحديد لون المادة حسب النوع\n                                    const getItemStyle = ()=>{\n                                        if (item.isTemporary) {\n                                            switch(item.type){\n                                                case 'LIVE':\n                                                    return {\n                                                        background: '#ffebee',\n                                                        border: '2px solid #f44336',\n                                                        borderLeft: '5px solid #f44336'\n                                                    };\n                                                case 'PENDING':\n                                                    return {\n                                                        background: '#fff8e1',\n                                                        border: '2px solid #ffc107',\n                                                        borderLeft: '5px solid #ffc107'\n                                                    };\n                                                default:\n                                                    return {\n                                                        background: '#f3e5f5',\n                                                        border: '2px solid #9c27b0',\n                                                        borderLeft: '5px solid #9c27b0'\n                                                    };\n                                            }\n                                        }\n                                        return {\n                                            background: '#fff',\n                                            border: '1px solid #ddd'\n                                        };\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>handleDragStart(e, item),\n                                        style: {\n                                            ...getItemStyle(),\n                                            borderRadius: '8px',\n                                            padding: '12px',\n                                            cursor: 'grab',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                            position: 'relative'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(-2px)';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(0)';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteTempMedia(item.id);\n                                                },\n                                                style: {\n                                                    position: 'absolute',\n                                                    top: '5px',\n                                                    left: '5px',\n                                                    background: '#f44336',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '50%',\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    fontSize: '12px',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                title: \"حذف المادة المؤقتة\",\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 989,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: 'bold',\n                                                    color: '#333',\n                                                    marginBottom: '4px'\n                                                },\n                                                children: [\n                                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '10px',\n                                                            background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                            color: 'white',\n                                                            padding: '2px 6px',\n                                                            borderRadius: '10px',\n                                                            marginLeft: '5px'\n                                                        },\n                                                        children: item.type === 'LIVE' ? '🔴 هواء' : item.type === 'PENDING' ? '🟡 قيد التسليم' : '🟣 مؤقت'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1018,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    getMediaDisplayText(item)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1016,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '12px',\n                                                    color: '#666'\n                                                },\n                                                children: [\n                                                    item.type,\n                                                    \" • \",\n                                                    item.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '11px',\n                                                    color: '#888',\n                                                    marginTop: '4px'\n                                                },\n                                                children: item.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 916,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 724,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px',\n                                    background: '#4a5568',\n                                    padding: '15px',\n                                    borderRadius: '15px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/daily-schedule',\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: \"\\uD83D\\uDCCB الجدول الإذاعي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1062,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                        const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                        if (!response.ok) {\n                                                            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                        }\n                                                        const blob = await response.blob();\n                                                        const url = window.URL.createObjectURL(blob);\n                                                        const a = document.createElement('a');\n                                                        a.href = url;\n                                                        a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                        document.body.appendChild(a);\n                                                        a.click();\n                                                        window.URL.revokeObjectURL(url);\n                                                        document.body.removeChild(a);\n                                                        console.log('✅ تم تصدير الخريطة بنجاح');\n                                                    } catch (error) {\n                                                        console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                        alert('فشل في تصدير الخريطة: ' + error.message);\n                                                    }\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: \"\\uD83D\\uDCCA تصدير الخريطة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1087,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#f3f4f6',\n                                                    fontSize: '1.2rem'\n                                                },\n                                                children: \"\\uD83D\\uDCC5 الأسبوع المحدد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1134,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1061,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(-1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: \"← الأسبوع السابق\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1138,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedWeek,\n                                                onChange: (e)=>{\n                                                    const selectedDate = new Date(e.target.value + 'T12:00:00');\n                                                    const dayOfWeek = selectedDate.getDay();\n                                                    console.log('📅 تغيير التاريخ من التقويم:', {\n                                                        selectedDate: e.target.value,\n                                                        dayOfWeek: dayOfWeek,\n                                                        dayName: [\n                                                            'الأحد',\n                                                            'الاثنين',\n                                                            'الثلاثاء',\n                                                            'الأربعاء',\n                                                            'الخميس',\n                                                            'الجمعة',\n                                                            'السبت'\n                                                        ][dayOfWeek]\n                                                    });\n                                                    // حساب بداية الأسبوع (الأحد)\n                                                    const sunday = new Date(selectedDate);\n                                                    sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                                    const weekStart = sunday.toISOString().split('T')[0];\n                                                    console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                                    setSelectedWeek(weekStart);\n                                                },\n                                                style: {\n                                                    padding: '8px',\n                                                    border: '1px solid #6b7280',\n                                                    borderRadius: '5px',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1152,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: \"الأسبوع التالي →\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1183,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1137,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1051,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',\n                                    borderRadius: '10px',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                    minHeight: '80vh'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#f8f9fa'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '12px',\n                                                            border: '1px solid #dee2e6',\n                                                            fontWeight: 'bold',\n                                                            width: '80px',\n                                                            color: '#000'\n                                                        },\n                                                        children: \"الوقت\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1211,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '12px',\n                                                                border: '1px solid #dee2e6',\n                                                                fontWeight: 'bold',\n                                                                width: \"\".concat(100 / 7, \"%\"),\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        marginBottom: '4px',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: day\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1228,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: new Date(weekDates[index]).toLocaleDateString('en-GB', {\n                                                                        day: '2-digit',\n                                                                        month: '2-digit',\n                                                                        year: 'numeric'\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1229,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1221,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1210,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1209,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                background: '#f8f9fa',\n                                                                fontWeight: 'bold',\n                                                                textAlign: 'center',\n                                                                padding: '8px',\n                                                                border: '1px solid #dee2e6',\n                                                                color: '#000'\n                                                            },\n                                                            children: hour\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1245,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        days.map((_, dayIndex)=>{\n                                                            const cellItems = getItemsForCell(dayIndex, hour);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                onDragOver: handleDragOver,\n                                                                onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                                style: {\n                                                                    border: '1px solid #dee2e6',\n                                                                    padding: '8px',\n                                                                    minHeight: '120px',\n                                                                    cursor: 'pointer',\n                                                                    background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',\n                                                                    verticalAlign: 'top'\n                                                                },\n                                                                children: cellItems.map((item)=>{\n                                                                    var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        draggable: true,\n                                                                        onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                        onClick: ()=>deleteItem(item),\n                                                                        style: {\n                                                                            background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                            border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                            borderRadius: '4px',\n                                                                            padding: '2px 4px',\n                                                                            marginBottom: '2px',\n                                                                            fontSize: '0.9rem',\n                                                                            cursor: 'grab',\n                                                                            transition: 'all 0.2s ease'\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1.02)';\n                                                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1)';\n                                                                            e.currentTarget.style.boxShadow = 'none';\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    gap: '4px',\n                                                                                    color: '#000'\n                                                                                },\n                                                                                children: [\n                                                                                    item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#666'\n                                                                                        },\n                                                                                        children: [\n                                                                                            \"♻️ \",\n                                                                                            item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                            item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1309,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                        },\n                                                                                        children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1313,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#ff9800'\n                                                                                        },\n                                                                                        children: \"\\uD83C\\uDF1F\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1321,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#000'\n                                                                                        },\n                                                                                        children: item.mediaItem ? getMediaDisplayText(item.mediaItem) : 'غير معروف'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1323,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1307,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.6rem',\n                                                                                    color: '#000'\n                                                                                },\n                                                                                children: [\n                                                                                    item.startTime,\n                                                                                    \" - \",\n                                                                                    item.endTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1327,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.5rem',\n                                                                                    color: '#888',\n                                                                                    fontStyle: 'italic'\n                                                                                },\n                                                                                children: \"إعادة - يمكن الحذف للتعديل\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1331,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, item.id, true, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1273,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, dayIndex, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1259,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, hourIndex, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1242,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1207,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    padding: '20px',\n                                    borderRadius: '15px',\n                                    marginTop: '20px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 20px 0',\n                                            fontSize: '1.3rem',\n                                            textAlign: 'center'\n                                        },\n                                        children: \"\\uD83D\\uDCCB تعليمات الاستخدام:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1354,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDFAF إضافة المواد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"اسحب المواد من القائمة اليمنى إلى الجدول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1360,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83D\\uDD04 اسحب المواد من الجدول نفسه لنسخها لمواعيد أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1361,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83C\\uDFAC استخدم فلتر النوع للتصفية حسب نوع المادة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"\\uD83D\\uDD0D استخدم البحث للعثور على المواد بسرعة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1359,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1357,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDDD1️ حذف المواد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1367,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"المواد الأصلية:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1369,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" حذف نهائي مع جميع إعاداتها\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1369,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"الإعادات:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1370,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" حذف مع ترك الحقل فارغ للتعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1370,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"سيظهر تأكيد قبل الحذف\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1371,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1368,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1366,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1356,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"\\uD83C\\uDF1F المواد الأصلية (البرايم تايم):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1378,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"الأحد-الأربعاء: 18:00-00:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1380,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: \"الخميس-السبت: 18:00-02:00\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1381,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    \"\\uD83D\\uDFE1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        style: {\n                                                                            color: '#fbbf24'\n                                                                        },\n                                                                        children: \"لون ذهبي في الجدول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1382,\n                                                                        columnNumber: 56\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1382,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1379,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1377,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: \"♻️ الإعادات التلقائية (جزئين):\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1386,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الأحد-الأربعاء:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1388,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1388,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج1: نفس العمود 00:00-07:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1390,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج2: العمود التالي 08:00-17:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1391,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1389,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الخميس-السبت:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1393,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1393,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج1: نفس العمود 02:00-07:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1395,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: \"ج2: العمود التالي 08:00-17:59\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1396,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1394,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    \"\\uD83D\\uDD18 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        style: {\n                                                                            color: '#9ca3af'\n                                                                        },\n                                                                        children: \"لون رمادي - يمكن حذفها للتعديل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1398,\n                                                                        columnNumber: 56\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1398,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1387,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1385,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1376,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #f59e0b'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: \"\\uD83D\\uDCC5 إدارة التواريخ:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1404,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \" استخدم الكالندر والأزرار للتنقل بين الأسابيع • كل أسبوع يُحفظ بشكل منفصل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1403,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #3b82f6'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: \"\\uD83D\\uDCA1 ملاحظة مهمة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1409,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \" عند إضافة مواد قليلة (١-٣ مواد) في البرايم، ستظهر مع فواصل زمنية في الإعادات لتجنب التكرار المفرط. أضف المزيد من المواد للحصول على تنوع أكبر.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1410,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1347,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1049,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 716,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 715,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 714,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"v2z1JHXlWVqbyv5DN3U+nYUgV3s=\");\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ })

});