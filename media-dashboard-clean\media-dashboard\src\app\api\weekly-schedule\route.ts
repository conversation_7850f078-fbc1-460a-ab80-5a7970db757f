import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

// استيراد البيانات المشتركة
import { getAllMediaItems, getMediaItemById } from '../shared-data';

// بيانات الجداول الأسبوعية مع التواريخ
let weeklySchedules: Map<string, any[]> = new Map();

// بيانات المواد المؤقتة لكل أسبوع
let tempItems: Map<string, any[]> = new Map();

// مسار ملف الحفظ
const SCHEDULES_FILE = path.join(process.cwd(), 'data', 'weekly-schedules.json');
const TEMP_ITEMS_FILE = path.join(process.cwd(), 'data', 'temp-items.json');

// التأكد من وجود مجلد البيانات
async function ensureDataDir() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// حفظ البيانات في ملف
async function saveSchedulesToFile() {
  try {
    await ensureDataDir();
    const schedulesData = Object.fromEntries(weeklySchedules);
    await fs.writeFile(SCHEDULES_FILE, JSON.stringify(schedulesData, null, 2));
    console.log('💾 تم حفظ الجداول في الملف');
  } catch (error) {
    console.error('❌ خطأ في حفظ الجداول:', error);
  }
}

// حفظ المواد المؤقتة في ملف
async function saveTempItemsToFile() {
  try {
    await ensureDataDir();
    const tempData = Object.fromEntries(tempItems);
    await fs.writeFile(TEMP_ITEMS_FILE, JSON.stringify(tempData, null, 2));
    console.log('💾 تم حفظ المواد المؤقتة في الملف');
  } catch (error) {
    console.error('❌ خطأ في حفظ المواد المؤقتة:', error);
  }
}

// تحميل البيانات من الملف
async function loadSchedulesFromFile() {
  try {
    const data = await fs.readFile(SCHEDULES_FILE, 'utf8');
    const schedulesData = JSON.parse(data);
    weeklySchedules = new Map(Object.entries(schedulesData));
    console.log('📂 تم تحميل الجداول من الملف');
  } catch (error) {
    console.log('📂 لا يوجد ملف جداول محفوظ، بدء جديد');
  }
}

// تحميل المواد المؤقتة من الملف
async function loadTempItemsFromFile() {
  try {
    const data = await fs.readFile(TEMP_ITEMS_FILE, 'utf8');
    const tempData = JSON.parse(data);
    tempItems = new Map(Object.entries(tempData));
    console.log('📂 تم تحميل المواد المؤقتة من الملف');
  } catch (error) {
    console.log('📂 لا يوجد ملف مواد مؤقتة محفوظ، بدء جديد');
  }
}

// تحميل البيانات عند بدء التشغيل
let dataLoaded = false;
async function initializeData() {
  if (!dataLoaded) {
    await loadSchedulesFromFile();
    await loadTempItemsFromFile();
    dataLoaded = true;
  }
}

// Helper functions
function getWeekStart(date: Date): Date {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day;
  return new Date(d.setDate(diff));
}

function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

function timeToMinutes(time: string): number {
  if (!time || typeof time !== 'string') {
    console.warn(`⚠️ وقت غير صحيح: ${time}`);
    return 0;
  }
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
}

// حساب المدة الإجمالية للمادة
function calculateTotalDuration(segments: any[]): string {
  if (!segments || segments.length === 0) return '01:00:00';

  let totalSeconds = 0;
  segments.forEach(segment => {
    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);
    totalSeconds += hours * 3600 + minutes * 60 + seconds;
  });

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const secs = totalSeconds % 60;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

function addMinutesToTime(timeStr: string, minutes: number): string {
  const totalMinutes = timeToMinutes(timeStr) + minutes;
  const hours = Math.floor(totalMinutes / 60) % 24;
  const mins = totalMinutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

function calculateDuration(startTime: string, endTime: string): number {
  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);
  let duration = endMinutes - startMinutes;
  if (duration < 0) duration += 24 * 60;
  return duration;
}

function isPrimeTime(startTime: string, dayOfWeek: number): boolean {
  const startMinutes = timeToMinutes(startTime);

  if ([0, 1, 2, 3].includes(dayOfWeek)) {
    // الأحد-الأربعاء: 18:00-00:00
    return startMinutes >= timeToMinutes('18:00');
  } else if ([4, 5, 6].includes(dayOfWeek)) {
    // الخميس-السبت: 18:00-02:00 (اليوم التالي)
    return startMinutes >= timeToMinutes('18:00') || startMinutes < timeToMinutes('02:00');
  }
  return false;
}

// دالة توليد الإعادات التلقائية (متتالية)
function generateReruns(scheduleItems: any[], weekStart: string, tempItems: any[] = []): any[] {
  const reruns: any[] = [];

  console.log(`🔄 توليد الإعادات للأسبوع: ${weekStart}`);

  // دمج المواد العادية والمؤقتة مع فلترة المواد المحذوفة
  const permanentDeletedSet = deletedReruns.get('permanent_temp_deletions') || new Set();

  const allItems = [...scheduleItems, ...tempItems];
  const regularItems = allItems.filter(item =>
    !item.isRerun &&
    !item.isTemporary &&
    !permanentDeletedSet.has(item.mediaItemId || item.id)
  );
  const temporaryItems = allItems.filter(item =>
    !item.isRerun &&
    item.isTemporary &&
    !permanentDeletedSet.has(item.mediaItemId || item.id)
  );

  console.log(`📊 المواد: ${regularItems.length} عادية + ${temporaryItems.length} مؤقتة (بعد فلترة المحذوفة)`);

  // التحقق من وجود مواد للإعادة
  if (regularItems.length === 0 && temporaryItems.length === 0) {
    console.log('⚠️ لا توجد مواد لتوليد إعادات منها');
    return reruns;
  }

  // تجميع المواد حسب اليوم لتوليد إعادات متتالية
  const itemsByDay = new Map<number, any[]>();

  // إضافة المواد العادية في البرايم تايم
  regularItems.filter(item => isPrimeTime(item.startTime, item.dayOfWeek))
    .forEach(item => {
      if (!itemsByDay.has(item.dayOfWeek)) {
        itemsByDay.set(item.dayOfWeek, []);
      }
      itemsByDay.get(item.dayOfWeek)!.push(item);
    });

  // إضافة المواد المؤقتة في البرايم تايم (تجاهل المواد المؤقتة في القائمة الجانبية)
  temporaryItems.filter(item =>
    item.startTime &&
    item.dayOfWeek !== undefined &&
    item.startTime !== undefined &&
    isPrimeTime(item.startTime, item.dayOfWeek)
  ).forEach(item => {
    if (!itemsByDay.has(item.dayOfWeek)) {
      itemsByDay.set(item.dayOfWeek, []);
    }
    itemsByDay.get(item.dayOfWeek)!.push({...item, isTemporary: true});
  });

  // توليد إعادات متتالية لكل يوم
  itemsByDay.forEach((dayItems, dayOfWeek) => {
    console.log(`📅 اليوم ${dayOfWeek}: ${dayItems.length} مادة في البرايم`);

    // ترتيب المواد حسب وقت البداية في البرايم (مع مراعاة انتقال اليوم)
    dayItems.sort((a, b) => {
      let timeA = timeToMinutes(a.startTime);
      let timeB = timeToMinutes(b.startTime);

      // للخميس-السبت: إذا كان الوقت أقل من 02:00، فهو في اليوم التالي
      if ([4, 5, 6].includes(dayOfWeek)) {
        if (timeA < timeToMinutes('02:00')) timeA += 24 * 60; // إضافة 24 ساعة
        if (timeB < timeToMinutes('02:00')) timeB += 24 * 60;
      }

      return timeA - timeB;
    });

    console.log(`📋 ترتيب المواد حسب وقت البث: ${dayItems.map((item, i) => `${i+1}:${item.mediaItem?.name || 'مؤقت'}(${item.startTime})`).join(', ')}`);

    const dayReruns = generateSequentialReruns(dayItems, weekStart, dayOfWeek);
    reruns.push(...dayReruns);
  });

  console.log(`✅ تم توليد ${reruns.length} إعادة إجمالية`);
  return reruns;
}

// دالة توليد إعادات متتالية لمواد يوم واحد (ترتيب مستمر مضمون)
function generateSequentialReruns(dayItems: any[], weekStart: string, dayOfWeek: number): any[] {
  const reruns: any[] = [];

  if (dayItems.length === 0) return reruns;

  console.log(`🔄 توليد إعادات لـ ${dayItems.length} مادة من اليوم ${dayOfWeek}`);
  console.log(`📋 ترتيب المواد: ${dayItems.map((item, i) => `${i+1}:${item.mediaItem?.name || 'مؤقت'}`).join(', ')}`);

  // تحديد أوقات الإعادات حسب اليوم
  let rerunStartTime: string;
  let rerunDay: number;

  if ([0, 1, 2, 3].includes(dayOfWeek)) {
    // الأحد-الأربعاء: الإعادات تبدأ من 00:00 في نفس اليوم
    rerunStartTime = '00:00';
    rerunDay = dayOfWeek;
  } else if ([4, 5, 6].includes(dayOfWeek)) {
    // الخميس-السبت: الإعادات تبدأ من 02:00 في نفس اليوم (بعد انتهاء البرايم)
    rerunStartTime = '02:00';
    rerunDay = dayOfWeek;
  } else {
    return reruns;
  }

  // إنشاء قائمة مستمرة من المواد (تكرار لانهائي)
  function getNextItem(index: number) {
    return dayItems[index % dayItems.length];
  }

  let currentTime = rerunStartTime;
  let itemSequenceIndex = 0; // فهرس التسلسل المستمر - يبدأ من 0 (المادة الأولى)

  console.log(`🚀 بدء الجزء الأول من ${rerunStartTime} حتى 08:00 - بدءاً من المادة الأولى: ${dayItems[0].mediaItem?.name || 'مؤقت'}`);

  // الجزء الأول: حتى 08:00
  while (timeToMinutes(currentTime) < timeToMinutes('08:00')) {
    const item = getNextItem(itemSequenceIndex);
    const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);
    const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);

    if (timeToMinutes(rerunEndTime) <= timeToMinutes('08:00')) {
      reruns.push({
        id: `rerun_${item.id}_seq${itemSequenceIndex}_${rerunDay}`,
        mediaItemId: item.mediaItemId,
        dayOfWeek: rerunDay,
        startTime: currentTime,
        endTime: rerunEndTime,
        weekStart: weekStart,
        isRerun: true,
        isTemporary: item.isTemporary || false,
        rerunPart: 1,
        rerunCycle: Math.floor(itemSequenceIndex / dayItems.length) + 1,
        mediaItem: item.mediaItem,
        originalId: item.id
      });

      console.log(`✅ جزء 1: [${itemSequenceIndex}] ${item.mediaItem?.name || 'مؤقت'} (مادة ${(itemSequenceIndex % dayItems.length) + 1}/${dayItems.length}, دورة ${Math.floor(itemSequenceIndex / dayItems.length) + 1}) ${currentTime}-${rerunEndTime}`);

      currentTime = rerunEndTime;
      itemSequenceIndex++;
    } else {
      console.log(`⏰ توقف الجزء الأول عند ${currentTime} - المادة التالية: ${item.mediaItem?.name || 'مؤقت'}`);
      break;
    }
  }

  console.log(`📊 انتهى الجزء الأول: فهرس التسلسل ${itemSequenceIndex}, المادة التالية: ${getNextItem(itemSequenceIndex).mediaItem?.name || 'مؤقت'}`);

  // حساب اليوم التالي مع مراعاة الأسبوع الجديد
  let nextDay: number;
  let nextWeekStart = weekStart;

  if (dayOfWeek === 6) { // السبت
    nextDay = 0; // الأحد في الأسبوع التالي
    const nextWeekDate = new Date(weekStart);
    nextWeekDate.setDate(nextWeekDate.getDate() + 7);
    nextWeekStart = formatDate(nextWeekDate);
    console.log(`🔄 إعادات السبت تذهب للأحد في الأسبوع التالي: ${nextWeekStart}`);
  } else {
    nextDay = (rerunDay + 1) % 7;
  }

  currentTime = '08:00';

  console.log(`🚀 بدء الجزء الثاني: العمود ${nextDay} - استكمال من المادة ${getNextItem(itemSequenceIndex).mediaItem?.name || 'مؤقت'} (فهرس ${itemSequenceIndex})`);

  // الجزء الثاني: استكمال من حيث توقف الجزء الأول
  while (timeToMinutes(currentTime) < timeToMinutes('18:00')) {
    const item = getNextItem(itemSequenceIndex);
    const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);
    const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);

    if (timeToMinutes(rerunEndTime) <= timeToMinutes('18:00')) {
      reruns.push({
        id: `rerun_${item.id}_seq${itemSequenceIndex}_${nextDay}`,
        mediaItemId: item.mediaItemId,
        dayOfWeek: nextDay,
        startTime: currentTime,
        endTime: rerunEndTime,
        weekStart: nextWeekStart,
        isRerun: true,
        isTemporary: item.isTemporary || false,
        rerunPart: 2,
        rerunCycle: Math.floor(itemSequenceIndex / dayItems.length) + 1,
        mediaItem: item.mediaItem,
        originalId: item.id,
        isNextWeekRerun: dayOfWeek === 6
      });

      console.log(`✅ جزء 2: [${itemSequenceIndex}] ${item.mediaItem?.name || 'مؤقت'} (مادة ${(itemSequenceIndex % dayItems.length) + 1}/${dayItems.length}, دورة ${Math.floor(itemSequenceIndex / dayItems.length) + 1}) ${currentTime}-${rerunEndTime}`);

      currentTime = rerunEndTime;
      itemSequenceIndex++;
    } else {
      console.log(`⏰ توقف الجزء الثاني عند ${currentTime} لتجاوز 18:00`);
      break;
    }
  }

  return reruns;
}





// قائمة الإعادات المحذوفة (لترك الحقول فارغة)
const deletedReruns = new Map<string, Set<string>>(); // weekStart -> Set of rerun IDs

// GET - جلب الجدول الأسبوعي
export async function GET(request: NextRequest) {
  try {
    // تحميل البيانات من الملفات
    await initializeData();

    const { searchParams } = new URL(request.url);
    const weekStartParam = searchParams.get('weekStart');
    const tempItemsParam = searchParams.get('tempItems');

    const weekStart = weekStartParam ? new Date(weekStartParam) : getWeekStart(new Date());
    const weekStartStr = formatDate(weekStart);

    console.log('📅 جلب الجدول الأسبوعي لتاريخ:', weekStartStr);

    // الحصول على الجدول أو إنشاء جدول فارغ
    let scheduleItems = weeklySchedules.get(weekStartStr) || [];

    // الحصول على المواد المؤقتة المحفوظة
    let weekTempItems = tempItems.get(weekStartStr) || [];
    console.log(`📦 المواد المؤقتة المحفوظة: ${weekTempItems.length}`);

    // توليد الإعادات التلقائية
    console.log('🚀 بدء توليد الإعادات...');
    const reruns = generateReruns(scheduleItems, weekStartStr, weekTempItems);
    console.log(`📊 تم توليد ${reruns.length} إعادة`);

    // فلترة الإعادات المحذوفة
    const deletedSet = deletedReruns.get(weekStartStr) || new Set();

    // فصل الإعادات حسب الأسبوع وفلترة المحذوفة
    const currentWeekReruns = reruns.filter(rerun => {
      if (rerun.weekStart !== weekStartStr) return false;
      if (deletedSet.has(rerun.id)) return false;

      // فلترة إضافية: التحقق من عدم وجود مادة أخرى في نفس المكان
      const hasConflict = [...scheduleItems, ...weekTempItems].some(item =>
        !item.isRerun &&
        item.dayOfWeek === rerun.dayOfWeek &&
        item.startTime === rerun.startTime
      );

      if (hasConflict) {
        console.log(`🚫 تم تجاهل إعادة ${rerun.mediaItem.name} في ${rerun.startTime} بسبب وجود مادة أخرى`);
        return false;
      }

      return true;
    });

    // إضافة إعادات السبت من الأسبوع السابق (تظهر في الأحد)
    const prevWeekDate = new Date(weekStartStr);
    prevWeekDate.setDate(prevWeekDate.getDate() - 7);
    const prevWeekStr = formatDate(prevWeekDate);

    // توليد إعادات الأسبوع السابق للحصول على إعادات السبت
    const prevWeekScheduleItems = weeklySchedules.get(prevWeekStr) || [];
    const prevWeekTempItems = tempItems.get(prevWeekStr) || [];

    // التحقق من وجود مواد في السبت من الأسبوع السابق
    const saturdayItems = [...prevWeekScheduleItems, ...prevWeekTempItems].filter(item =>
      !item.isRerun && item.dayOfWeek === 6
    );

    let saturdayReruns: any[] = [];

    if (saturdayItems.length > 0) {
      console.log(`📅 وجدت ${saturdayItems.length} مادة في السبت من الأسبوع السابق`);
      const prevWeekReruns = generateReruns(prevWeekScheduleItems, prevWeekStr, prevWeekTempItems);

      // فلترة إعادات السبت التي تذهب للأسبوع التالي (الأحد)
      saturdayReruns = prevWeekReruns.filter(rerun =>
        rerun.isNextWeekRerun &&
        rerun.weekStart === weekStartStr &&
        rerun.dayOfWeek === 0 &&
        !deletedSet.has(rerun.id)
      );

      console.log(`📅 إعادات السبت من الأسبوع السابق: ${saturdayReruns.length}`);
    } else {
      console.log(`📅 لا توجد مواد في السبت من الأسبوع السابق - لن يتم توليد إعادات`);
    }

    // دمج جميع المواد: عادية + مؤقتة + إعادات
    const allItems = [...scheduleItems, ...weekTempItems, ...currentWeekReruns, ...saturdayReruns];
    
    // فلترة المواد الكبيرة للعرض مع إضافة المدة المحسوبة
    const mediaItems = getAllMediaItems();
    const bigMediaTypes = ['PROGRAM', 'SERIES', 'MOVIE'];
    const availableMedia = mediaItems
      .filter(item => bigMediaTypes.includes(item.type))
      .map(item => ({
        ...item,
        duration: calculateTotalDuration(item.segments || [])
      }));
    
    // إضافة المواد المؤقتة للاستجابة (فقط المواد في القائمة الجانبية، ليس في الجدول)
    const currentTempItems = tempItems.get(weekStartStr) || [];
    const sidebarTempItems = currentTempItems.filter(item =>
      !item.dayOfWeek && !item.startTime && !item.endTime
    );
    console.log(`📦 إرسال ${sidebarTempItems.length} مادة مؤقتة في الاستجابة (من أصل ${currentTempItems.length})`);

    return NextResponse.json({
      success: true,
      data: {
        weekStart: weekStartStr,
        scheduleItems: allItems,
        availableMedia,
        tempItems: sidebarTempItems
      }
    });
    
  } catch (error) {
    console.error('❌ خطأ في جلب الجدول:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الجدول الأسبوعي' },
      { status: 500 }
    );
  }
}

// POST - إضافة مادة للجدول أو حفظ مادة مؤقتة
export async function POST(request: NextRequest) {
  try {
    // تحميل البيانات من الملفات
    await initializeData();

    const body = await request.json();



    const { mediaItemId, dayOfWeek, startTime, endTime, weekStart, episodeNumber, seasonNumber, partNumber, isTemporary, mediaItem: tempMediaItem } = body;

    // التعامل مع المواد المؤقتة
    if (isTemporary && tempMediaItem) {
      // تنظيف وتصحيح البيانات
      const cleanTempItem = {
        ...tempMediaItem,
        name: tempMediaItem.name || tempMediaItem.title || 'مادة مؤقتة',
        id: tempMediaItem.id || `temp_${Date.now()}`,
        type: tempMediaItem.type || 'PROGRAM',
        duration: tempMediaItem.duration || '01:00:00'
      };

      console.log('💾 حفظ مادة مؤقتة في API:', cleanTempItem.name);
      console.log('📋 بيانات المادة المؤقتة المنظفة:', JSON.stringify(cleanTempItem, null, 2));

      // الحصول على المواد المؤقتة الحالية
      let currentTempItems = tempItems.get(weekStart) || [];

      // البحث عن المواد الموجودة في نفس الوقت والمكان لحذف إعاداتها
      const tempItemsToRemove = currentTempItems.filter(item =>
        item.dayOfWeek === dayOfWeek && item.startTime === startTime
      );

      let scheduleItems = weeklySchedules.get(weekStart) || [];
      const regularItemsToRemove = scheduleItems.filter(item =>
        item.dayOfWeek === dayOfWeek && item.startTime === startTime
      );

      // حذف الإعادات المرتبطة بجميع المواد التي سيتم استبدالها
      const allItemsToRemove = [...tempItemsToRemove, ...regularItemsToRemove];

      for (const itemToRemove of allItemsToRemove) {
        if (!deletedReruns.has(weekStart)) {
          deletedReruns.set(weekStart, new Set());
        }
        const deletedSet = deletedReruns.get(weekStart)!;

        // البحث عن جميع الإعادات المرتبطة بهذه المادة
        const allItems = [...scheduleItems, ...currentTempItems];
        const relatedReruns = allItems.filter(item =>
          item.isRerun &&
          (item.mediaItemId === itemToRemove.mediaItemId ||
           item.id.includes(itemToRemove.id) ||
           item.originalStartTime === itemToRemove.startTime)
        );

        relatedReruns.forEach(rerun => deletedSet.add(rerun.id));
        console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة ${itemToRemove.mediaItem?.name || 'مؤقتة'}`);
      }

      // حذف أي مواد موجودة في نفس الوقت والمكان
      currentTempItems = currentTempItems.filter(item =>
        !(item.dayOfWeek === dayOfWeek && item.startTime === startTime)
      );

      // حذف المواد العادية من نفس الوقت والمكان
      scheduleItems = scheduleItems.filter(item =>
        !(item.dayOfWeek === dayOfWeek && item.startTime === startTime)
      );
      weeklySchedules.set(weekStart, scheduleItems);

      console.log(`🔄 تم تنظيف المكان للمادة المؤقتة في اليوم ${dayOfWeek} الوقت ${startTime}`);

      // إضافة المادة المؤقتة الجديدة مع معرف فريد
      const tempItem = {
        id: `temp_${Date.now()}`,
        mediaItemId: cleanTempItem.id,
        dayOfWeek,
        startTime,
        endTime,
        weekStart,
        isRerun: false,
        isTemporary: true,
        mediaItem: cleanTempItem,
        createdAt: new Date().toISOString()
      };

      console.log('✅ تم إنشاء المادة المؤقتة:', {
        id: tempItem.id,
        name: tempItem.mediaItem.name,
        type: tempItem.mediaItem.type,
        duration: tempItem.mediaItem.duration
      });

      currentTempItems.push(tempItem);
      tempItems.set(weekStart, currentTempItems);

      // حفظ في الملفات
      await saveTempItemsToFile();
      await saveSchedulesToFile();

      console.log(`✅ تم حفظ المادة المؤقتة. إجمالي المواد المؤقتة: ${currentTempItems.length}`);

      return NextResponse.json({
        success: true,
        data: tempItem
      });
    }
    
    if (!mediaItemId || dayOfWeek === undefined || !startTime || !endTime || !weekStart) {
      return NextResponse.json(
        { success: false, error: 'جميع البيانات مطلوبة' },
        { status: 400 }
      );
    }
    
    // التحقق من وجود المادة
    const mediaItem = getMediaItemById(mediaItemId);
    if (!mediaItem) {
      return NextResponse.json(
        { success: false, error: 'المادة غير موجودة' },
        { status: 404 }
      );
    }
    
    // الحصول على الجدول الحالي
    let scheduleItems = weeklySchedules.get(weekStart) || [];
    
    // التحقق من التداخل
    const startMinutes = timeToMinutes(startTime);
    const endMinutes = timeToMinutes(endTime);
    
    const conflict = scheduleItems.find(item => {
      if (item.dayOfWeek !== dayOfWeek) return false;
      const itemStart = timeToMinutes(item.startTime);
      const itemEnd = timeToMinutes(item.endTime);
      return (startMinutes < itemEnd && endMinutes > itemStart);
    });
    
    if (conflict) {
      return NextResponse.json(
        { success: false, error: 'يوجد تداخل في الأوقات' },
        { status: 400 }
      );
    }
    
    // البحث عن المواد الموجودة في نفس الوقت والمكان لحذف إعاداتها
    const itemsToRemove = scheduleItems.filter(item =>
      item.dayOfWeek === dayOfWeek && item.startTime === startTime
    );

    // حذف الإعادات المرتبطة بالمواد التي سيتم استبدالها
    for (const itemToRemove of itemsToRemove) {
      // إضافة جميع الإعادات المرتبطة بهذه المادة للقائمة المحذوفة
      if (!deletedReruns.has(weekStart)) {
        deletedReruns.set(weekStart, new Set());
      }
      const deletedSet = deletedReruns.get(weekStart)!;

      // البحث عن جميع الإعادات المرتبطة بهذه المادة
      const allItems = [...scheduleItems, ...(tempItems.get(weekStart) || [])];
      const relatedReruns = allItems.filter(item =>
        item.isRerun &&
        (item.mediaItemId === itemToRemove.mediaItemId ||
         item.id.includes(itemToRemove.id) ||
         item.originalStartTime === itemToRemove.startTime)
      );

      relatedReruns.forEach(rerun => deletedSet.add(rerun.id));
      console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة ${itemToRemove.mediaItem?.name}`);
    }

    // حذف أي مواد موجودة في نفس الوقت والمكان (بما في ذلك المواد المؤقتة)
    scheduleItems = scheduleItems.filter(item =>
      !(item.dayOfWeek === dayOfWeek && item.startTime === startTime)
    );

    // حذف المواد المؤقتة من نفس الوقت والمكان
    let currentTempItems = tempItems.get(weekStart) || [];
    const tempItemsToRemove = currentTempItems.filter(item =>
      item.dayOfWeek === dayOfWeek && item.startTime === startTime
    );

    console.log(`🔍 البحث عن مواد مؤقتة للحذف في اليوم ${dayOfWeek} الوقت ${startTime}: وجد ${tempItemsToRemove.length} مادة`);

    // حذف إعادات المواد المؤقتة أيضاً
    for (const tempItem of tempItemsToRemove) {
      console.log(`🗑️ حذف المادة المؤقتة: ${tempItem.mediaItem?.name} (ID: ${tempItem.id})`);

      if (!deletedReruns.has(weekStart)) {
        deletedReruns.set(weekStart, new Set());
      }

      // البحث عن إعادات المادة المؤقتة في جميع الأسابيع وحذفها نهائياً
      const allWeeks = Array.from(weeklySchedules.keys());
      for (const week of allWeeks) {
        const weekItems = weeklySchedules.get(week) || [];
        const relatedReruns = weekItems.filter(item =>
          item.isRerun &&
          item.isTemporary &&
          (item.mediaItemId === tempItem.mediaItemId ||
           item.id.includes(tempItem.id) ||
           item.originalId === tempItem.id ||
           (item.mediaItem && item.mediaItem.name === tempItem.mediaItem?.name))
        );

        // إضافة الإعادات للقائمة المحذوفة
        relatedReruns.forEach(rerun => {
          if (!deletedReruns.has(week)) {
            deletedReruns.set(week, new Set());
          }
          deletedReruns.get(week)!.add(rerun.id);
        });

        // حذف الإعادات من الجدول نهائياً
        const updatedWeekItems = weekItems.filter(item =>
          !relatedReruns.some(rerun => rerun.id === item.id)
        );
        weeklySchedules.set(week, updatedWeekItems);

        console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة المؤقتة ${tempItem.mediaItem?.name} في الأسبوع ${week} نهائياً`);
      }

      // إضافة معرف المادة المؤقتة لقائمة المحذوفات الدائمة
      if (!deletedReruns.has('permanent_temp_deletions')) {
        deletedReruns.set('permanent_temp_deletions', new Set());
      }
      deletedReruns.get('permanent_temp_deletions')!.add(tempItem.mediaItemId || tempItem.id);
    }

    // حذف المواد المؤقتة من tempItems نهائياً
    const beforeCount = currentTempItems.length;
    const tempItemsToDelete = currentTempItems.filter(item =>
      item.dayOfWeek === dayOfWeek && item.startTime === startTime
    );

    // طباعة تفاصيل المواد التي سيتم حذفها
    tempItemsToDelete.forEach(item => {
      console.log(`🗑️ سيتم حذف المادة المؤقتة نهائياً: ${item.mediaItem?.name} (ID: ${item.id})`);
    });

    currentTempItems = currentTempItems.filter(item =>
      !(item.dayOfWeek === dayOfWeek && item.startTime === startTime)
    );
    tempItems.set(weekStart, currentTempItems);

    // حذف المواد المؤقتة من weeklySchedules أيضاً
    scheduleItems = scheduleItems.filter(item =>
      !(item.isTemporary && item.dayOfWeek === dayOfWeek && item.startTime === startTime)
    );
    weeklySchedules.set(weekStart, scheduleItems);

    // حذف المواد المؤقتة من جميع الأسابيع في weeklySchedules
    const allWeeks = Array.from(weeklySchedules.keys());
    for (const week of allWeeks) {
      let weekItems = weeklySchedules.get(week) || [];
      const beforeWeekCount = weekItems.length;

      // حذف المواد المؤقتة والإعادات المرتبطة بها
      weekItems = weekItems.filter(item => {
        // حذف المواد المؤقتة التي تطابق المواد المحذوفة
        if (item.isTemporary) {
          const shouldDelete = tempItemsToDelete.some(deletedItem =>
            (item.mediaItem?.name === deletedItem.mediaItem?.name &&
             item.dayOfWeek === deletedItem.dayOfWeek &&
             item.startTime === deletedItem.startTime) ||
            item.id === deletedItem.id ||
            item.mediaItemId === deletedItem.mediaItemId
          );

          if (shouldDelete) {
            console.log(`🗑️ حذف مادة مؤقتة من weeklySchedules: ${item.mediaItem?.name} (${item.startTime})`);
            return false;
          }
        }

        // حذف الإعادات المرتبطة بالمواد المؤقتة المحذوفة
        if (item.isRerun && item.isTemporary) {
          const shouldDelete = tempItemsToDelete.some(deletedItem =>
            item.mediaItem?.name === deletedItem.mediaItem?.name ||
            item.originalId === deletedItem.id ||
            item.mediaItemId === deletedItem.mediaItemId
          );

          if (shouldDelete) {
            console.log(`🗑️ حذف إعادة مؤقتة من weeklySchedules: ${item.mediaItem?.name} (${item.startTime})`);
            return false;
          }
        }

        return true;
      });

      weeklySchedules.set(week, weekItems);

      if (beforeWeekCount !== weekItems.length) {
        console.log(`🗑️ تم حذف ${beforeWeekCount - weekItems.length} مادة/إعادة مؤقتة من الأسبوع ${week}`);
      }
    }

    console.log(`🗑️ تم حذف ${beforeCount - currentTempItems.length} مادة مؤقتة من tempItems نهائياً`);

    // حفظ المواد المؤقتة المحدثة
    await saveTempItemsToFile();
    await saveSchedulesToFile();

    console.log(`💾 تم حفظ الملفات بعد حذف المواد المؤقتة. المواد المتبقية: ${currentTempItems.length}`);

    console.log(`🔄 تم تنظيف المكان في اليوم ${dayOfWeek} الوقت ${startTime}`);

    // إضافة المادة الجديدة مع تفاصيل الحلقة/الجزء
    const newItem = {
      id: `schedule_${Date.now()}`,
      mediaItemId,
      dayOfWeek,
      startTime,
      endTime,
      weekStart,
      isRerun: false,
      // حفظ تفاصيل الحلقة/الجزء على مستوى العنصر
      episodeNumber,
      seasonNumber,
      partNumber,
      // إنشاء نسخة محدثة من المادة مع التفاصيل
      mediaItem: {
        ...mediaItem,
        episodeNumber: episodeNumber || mediaItem.episodeNumber,
        seasonNumber: seasonNumber || mediaItem.seasonNumber,
        partNumber: partNumber || mediaItem.partNumber
      },
      createdAt: new Date().toISOString()
    };

    scheduleItems.push(newItem);
    weeklySchedules.set(weekStart, scheduleItems);

    // حفظ في الملف
    await saveSchedulesToFile();

    console.log('✅ تم إضافة المادة:', mediaItem.name);

    return NextResponse.json({
      success: true,
      data: newItem
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة المادة:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المادة' },
      { status: 500 }
    );
  }
}

// دعم إضافي للمواد المؤقتة في القائمة الجانبية
export async function PATCH(request: NextRequest) {
  try {
    await initializeData();

    const body = await request.json();
    const { action } = body;

    // حفظ مادة مؤقتة في القائمة الجانبية
    if (action === 'saveTempToSidebar') {
      const { tempMedia, weekStart: weekStartParam } = body;
      const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));

      console.log(`💾 حفظ مادة مؤقتة في القائمة الجانبية: ${tempMedia.name}`);

      // إضافة المادة المؤقتة إلى tempItems
      let currentTempItems = tempItems.get(weekStart) || [];
      currentTempItems.push(tempMedia);
      tempItems.set(weekStart, currentTempItems);

      // حفظ في الملف
      await saveTempItemsToFile();

      console.log(`✅ تم حفظ المادة المؤقتة في القائمة الجانبية. إجمالي: ${currentTempItems.length}`);

      return NextResponse.json({
        success: true,
        message: 'تم حفظ المادة المؤقتة في القائمة الجانبية'
      });
    }

    // حذف مادة مؤقتة من القائمة الجانبية
    if (action === 'deleteTempFromSidebar') {
      const { tempMediaId, weekStart: weekStartParam } = body;
      const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));

      console.log(`🗑️ حذف مادة مؤقتة من القائمة الجانبية: ${tempMediaId}`);

      // حذف المادة المؤقتة من tempItems
      let currentTempItems = tempItems.get(weekStart) || [];
      const beforeCount = currentTempItems.length;
      currentTempItems = currentTempItems.filter(item => item.id !== tempMediaId);
      tempItems.set(weekStart, currentTempItems);

      // حفظ في الملف
      await saveTempItemsToFile();

      console.log(`✅ تم حذف المادة المؤقتة من القائمة الجانبية. المحذوف: ${beforeCount - currentTempItems.length}`);

      return NextResponse.json({
        success: true,
        message: 'تم حذف المادة المؤقتة من القائمة الجانبية'
      });
    }

    return NextResponse.json(
      { success: false, error: 'إجراء غير مدعوم' },
      { status: 400 }
    );

  } catch (error) {
    console.error('❌ خطأ في PATCH:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في العملية' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مادة
export async function DELETE(request: NextRequest) {
  try {
    // تحميل البيانات من الملفات
    await initializeData();

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const weekStart = searchParams.get('weekStart');

    if (!id || !weekStart) {
      return NextResponse.json(
        { success: false, error: 'المعرف والتاريخ مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق إذا كانت المادة مؤقتة
    if (id.startsWith('temp_')) {
      console.log('🗑️ حذف مادة مؤقتة:', id);

      // حذف من المواد المؤقتة
      let currentTempItems = tempItems.get(weekStart) || [];
      const originalLength = currentTempItems.length;
      currentTempItems = currentTempItems.filter(item =>
        item.id !== id && item.originalId !== id
      );
      tempItems.set(weekStart, currentTempItems);

      // حذف من الجدول الأساسي أيضاً
      let scheduleItems = weeklySchedules.get(weekStart) || [];
      scheduleItems = scheduleItems.filter(item =>
        item.id !== id &&
        item.mediaItemId !== id &&
        !(item.mediaItemId && item.mediaItemId.startsWith('temp_') && item.mediaItemId === id)
      );
      weeklySchedules.set(weekStart, scheduleItems);

      console.log(`🗑️ حذف المادة المؤقتة من tempItems: ${id}`);
      console.log(`🗑️ حذف المادة المؤقتة من weeklySchedules: ${id}`);

      // حفظ في الملفات
      await saveTempItemsToFile();
      await saveSchedulesToFile();

      console.log(`✅ تم حذف المادة المؤقتة نهائياً. المواد المتبقية: ${currentTempItems.length} (كان ${originalLength})`);

      return NextResponse.json({ success: true });
    }

    // التحقق إذا كانت المادة إعادة
    if (id.startsWith('rerun_')) {
      // إضافة الإعادة للقائمة المحذوفة
      if (!deletedReruns.has(weekStart)) {
        deletedReruns.set(weekStart, new Set());
      }
      deletedReruns.get(weekStart)!.add(id);

      console.log('🗑️ تم حذف الإعادة (ترك فارغ):', id);

      return NextResponse.json({
        success: true,
        message: 'تم حذف الإعادة بنجاح'
      });
    } else {
      // حذف المادة الأصلية
      let scheduleItems = weeklySchedules.get(weekStart) || [];
      const index = scheduleItems.findIndex(item => item.id === id);

      if (index === -1) {
        return NextResponse.json(
          { success: false, error: 'المادة غير موجودة' },
          { status: 404 }
        );
      }

      scheduleItems.splice(index, 1);
      weeklySchedules.set(weekStart, scheduleItems);

      // حفظ في الملف
      await saveSchedulesToFile();

      console.log('🗑️ تم حذف المادة الأصلية:', id);

      return NextResponse.json({
        success: true,
        message: 'تم حذف المادة بنجاح'
      });
    }

  } catch (error) {
    console.error('❌ خطأ في حذف المادة:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المادة' },
      { status: 500 }
    );
  }
}


