/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/edit-media/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cedit-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cedit-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/edit-media/page.tsx */ \"(app-pages-browser)/./src/app/edit-media/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZWRpdC1tZWRpYSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGVkaXQtbWVkaWFcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cedit-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/edit-media/page.tsx":
/*!*************************************!*\
  !*** ./src/app/edit-media/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EditMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const mediaId = searchParams.get('id');\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: '',\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    // تحميل بيانات المادة للتعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditMediaPage.useEffect\": ()=>{\n            if (!mediaId) {\n                router.push('/media-list');\n                return;\n            }\n            const fetchMediaItem = {\n                \"EditMediaPage.useEffect.fetchMediaItem\": async ()=>{\n                    try {\n                        console.log('📡 جاري تحميل بيانات المادة:', mediaId);\n                        const response = await fetch(\"/api/media?id=\".concat(mediaId));\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            var _item_episodeNumber, _item_seasonNumber, _item_partNumber;\n                            const item = result.data;\n                            console.log('📋 البيانات المستلمة:', item);\n                            setFormData({\n                                name: item.name || '',\n                                type: item.type || 'PROGRAM',\n                                description: item.description || '',\n                                channel: item.channel || 'DOCUMENTARY',\n                                source: item.source || '',\n                                status: item.status || 'WAITING',\n                                startDate: item.startDate ? item.startDate.split('T')[0] : '',\n                                endDate: item.endDate ? item.endDate.split('T')[0] : '',\n                                notes: item.notes || '',\n                                episodeNumber: ((_item_episodeNumber = item.episodeNumber) === null || _item_episodeNumber === void 0 ? void 0 : _item_episodeNumber.toString()) || '',\n                                seasonNumber: ((_item_seasonNumber = item.seasonNumber) === null || _item_seasonNumber === void 0 ? void 0 : _item_seasonNumber.toString()) || '',\n                                partNumber: ((_item_partNumber = item.partNumber) === null || _item_partNumber === void 0 ? void 0 : _item_partNumber.toString()) || '',\n                                hardDiskNumber: item.hardDiskNumber || 'SERVER'\n                            });\n                            console.log('✅ تم تعيين بيانات النموذج بنجاح');\n                            if (item.segments && item.segments.length > 0) {\n                                const loadedSegments = item.segments.map({\n                                    \"EditMediaPage.useEffect.fetchMediaItem.loadedSegments\": (seg, index)=>({\n                                            id: index + 1,\n                                            segmentCode: seg.code || seg.segmentCode || \"SEG\".concat(String(index + 1).padStart(3, '0')),\n                                            timeIn: seg.timeIn || '00:00:00',\n                                            timeOut: seg.timeOut || '00:00:00',\n                                            duration: seg.duration || '00:00:00'\n                                        })\n                                }[\"EditMediaPage.useEffect.fetchMediaItem.loadedSegments\"]);\n                                setSegments(loadedSegments);\n                                setSegmentCount(item.segments.length);\n                                console.log('🎬 تم تحميل السيجمانت:', loadedSegments);\n                            }\n                        } else {\n                            console.error('❌ فشل في تحميل البيانات:', result);\n                            showToast('فشل في تحميل بيانات المادة', 'error');\n                            router.push('/media-list');\n                        }\n                    } catch (error) {\n                        console.error('❌ خطأ في تحميل البيانات:', error);\n                        showToast('خطأ في تحميل البيانات', 'error');\n                        router.push('/media-list');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditMediaPage.useEffect.fetchMediaItem\"];\n            fetchMediaItem();\n        }\n    }[\"EditMediaPage.useEffect\"], [\n        mediaId\n    ]); // تحميل البيانات مرة واحدة فقط عند تغيير mediaId\n    // دوال مساعدة لحساب المدة\n    const validateTimeFormat = (time)=>{\n        const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;\n        return timeRegex.test(time);\n    };\n    const formatTimeInput = (value)=>{\n        // إزالة أي أحرف غير رقمية أو نقطتين\n        const cleaned = value.replace(/[^\\d:]/g, '');\n        // تقسيم النص إلى أجزاء\n        const parts = cleaned.split(':');\n        // تنسيق كل جزء\n        const hours = parts[0] ? parts[0].padStart(2, '0').slice(0, 2) : '00';\n        const minutes = parts[1] ? parts[1].padStart(2, '0').slice(0, 2) : '00';\n        const seconds = parts[2] ? parts[2].padStart(2, '0').slice(0, 2) : '00';\n        return \"\".concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    };\n    const calculateDuration = (timeIn, timeOut)=>{\n        if (!timeIn || !timeOut || !validateTimeFormat(timeIn) || !validateTimeFormat(timeOut)) {\n            return '00:00:00';\n        }\n        const [inHours, inMinutes, inSeconds] = timeIn.split(':').map(Number);\n        const [outHours, outMinutes, outSeconds] = timeOut.split(':').map(Number);\n        const inTotalSeconds = inHours * 3600 + inMinutes * 60 + inSeconds;\n        const outTotalSeconds = outHours * 3600 + outMinutes * 60 + outSeconds;\n        const durationSeconds = outTotalSeconds - inTotalSeconds;\n        if (durationSeconds <= 0) return '00:00:00';\n        const hours = Math.floor(durationSeconds / 3600);\n        const minutes = Math.floor(durationSeconds % 3600 / 60);\n        const seconds = durationSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const handleInputChange = (field, value)=>{\n        console.log(\"\\uD83D\\uDD04 تغيير \".concat(field, \":\"), value);\n        setFormData((prev)=>{\n            const updated = {\n                ...prev,\n                [field]: value\n            };\n            console.log('📝 حالة النموذج الجديدة:', updated);\n            return updated;\n        });\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const addSegment = ()=>{\n        console.log('🔄 محاولة إضافة سيجمانت جديد...');\n        const newSegmentCount = segmentCount + 1;\n        setSegmentCount(newSegmentCount);\n        const newSegment = {\n            id: newSegmentCount,\n            segmentCode: \"SEG\".concat(String(newSegmentCount).padStart(3, '0')),\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        };\n        setSegments((prev)=>{\n            const updated = [\n                ...prev,\n                newSegment\n            ];\n            console.log('➕ تم إضافة سيجمانت جديد. العدد الجديد:', updated.length);\n            return updated;\n        });\n    };\n    const removeSegment = (segmentId)=>{\n        if (segments.length > 1) {\n            setSegments((prev)=>prev.filter((segment)=>segment.id !== segmentId));\n            console.log('🗑️ تم حذف السيجمانت:', segmentId);\n        } else {\n            alert('لا يمكن حذف السيجمانت الوحيد المتبقي');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.name.trim()) {\n            showToast('يرجى إدخال اسم المادة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(mediaId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم تحديث المادة الإعلامية بنجاح!', 'success');\n                router.push('/media-list');\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error updating media item:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        transition: 'border-color 0.3s',\n        direction: 'rtl'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '800px',\n                    margin: '0 auto',\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    fontWeight: 'bold',\n                                    color: '#333',\n                                    margin: 0,\n                                    background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent'\n                                },\n                                children: \"✏️ تعديل المادة الإعلامية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/media-list'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '10px 20px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDD19 العودة للقائمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #dee2e6'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#495057',\n                                            marginBottom: '20px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '200px 1fr',\n                                                    gap: '15px',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            color: '#495057',\n                                                            fontWeight: 'bold',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDCBE رقم الهارد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"رقم الهارد\",\n                                                        value: formData.hardDiskNumber,\n                                                        onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            maxWidth: '200px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة *\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: inputStyle,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '80px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"المصدر\",\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"WAITING\",\n                                                                children: \"في الانتظار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: \"صالح\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"APPROVED\",\n                                                                children: \"مقبول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 399,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED\",\n                                                                children: \"مرفوض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: \"منتهي الصلاحية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ البداية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ النهاية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الجزء\",\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الموسم\",\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات إضافية\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '60px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #90caf9'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-between',\n                                            alignItems: 'center',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    color: '#1976d2',\n                                                    fontSize: '1.3rem',\n                                                    margin: 0\n                                                },\n                                                children: \"\\uD83C\\uDFAC إدارة السيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #4caf50, #45a049)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"➕ إضافة سيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, this),\n                                    segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: 'white',\n                                                borderRadius: '10px',\n                                                padding: '20px',\n                                                marginBottom: '15px',\n                                                border: '1px solid #e0e0e0'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            style: {\n                                                                color: '#333',\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"سيجمانت \",\n                                                                segment.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        segments.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removeSegment(segment.id),\n                                                            style: {\n                                                                background: '#ff4444',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '15px',\n                                                                padding: '5px 10px',\n                                                                cursor: 'pointer',\n                                                                fontSize: '0.8rem'\n                                                            },\n                                                            children: \"❌ حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                        gap: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1976d2',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"Time In (HH:MM:SS)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"00:00:00\",\n                                                                    value: segment.timeIn,\n                                                                    onChange: (e)=>{\n                                                                        let newValue = e.target.value;\n                                                                        // السماح فقط بالأرقام والنقطتين\n                                                                        newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                        // تحديد الطول الأقصى\n                                                                        if (newValue.length <= 8) {\n                                                                            handleSegmentChange(segment.id, 'timeIn', newValue);\n                                                                            // حساب المدة إذا كان التنسيق صحيح\n                                                                            if (validateTimeFormat(newValue)) {\n                                                                                const duration = calculateDuration(newValue, segment.timeOut);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            }\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        // تنسيق القيمة عند فقدان التركيز\n                                                                        const formatted = formatTimeInput(e.target.value);\n                                                                        handleSegmentChange(segment.id, 'timeIn', formatted);\n                                                                        const duration = calculateDuration(formatted, segment.timeOut);\n                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                    },\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        borderColor: validateTimeFormat(segment.timeIn) ? '#28a745' : '#e0e0e0'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1976d2',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"Time Out (HH:MM:SS)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"00:00:00\",\n                                                                    value: segment.timeOut,\n                                                                    onChange: (e)=>{\n                                                                        let newValue = e.target.value;\n                                                                        // السماح فقط بالأرقام والنقطتين\n                                                                        newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                        // تحديد الطول الأقصى\n                                                                        if (newValue.length <= 8) {\n                                                                            handleSegmentChange(segment.id, 'timeOut', newValue);\n                                                                            // حساب المدة إذا كان التنسيق صحيح\n                                                                            if (validateTimeFormat(newValue)) {\n                                                                                const duration = calculateDuration(segment.timeIn, newValue);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            }\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        // تنسيق القيمة عند فقدان التركيز\n                                                                        const formatted = formatTimeInput(e.target.value);\n                                                                        handleSegmentChange(segment.id, 'timeOut', formatted);\n                                                                        const duration = calculateDuration(segment.timeIn, formatted);\n                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                    },\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        borderColor: validateTimeFormat(segment.timeOut) ? '#28a745' : '#e0e0e0'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1976d2',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"المدة (HH:MM:SS)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                    lineNumber: 620,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: segment.duration,\n                                                                    readOnly: true,\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: '#f8f9fa',\n                                                                        color: '#6c757d'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                    lineNumber: 623,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 619,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1976d2',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"كود السيجمانت\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                    lineNumber: 636,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"SEG001\",\n                                                                    value: segment.segmentCode,\n                                                                    onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                    style: inputStyle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 635,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, segment.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)'\n                                        },\n                                        children: \"✅ حفظ التعديلات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 653,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.push('/media-list'),\n                                        style: {\n                                            background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(108,117,125,0.3)'\n                                        },\n                                        children: \"❌ إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 690,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, this);\n}\n_s(EditMediaPage, \"U/Mp9ns9pODrm6O21lgz1y/hFgg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = EditMediaPage;\nvar _c;\n$RefreshReg$(_c, \"EditMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/edit-media/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nfunction Toast(param) {\n    let { message, type, duration = 3000, onClose } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن\n                }\n            }[\"Toast.useEffect.timer\"], duration);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        duration,\n        onClose\n    ]);\n    const getToastStyles = ()=>{\n        const baseStyles = {\n            position: 'relative',\n            padding: '15px 20px',\n            borderRadius: '10px',\n            color: 'white',\n            fontWeight: 'bold',\n            fontSize: '1rem',\n            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n            transform: isVisible ? 'translateX(0)' : 'translateX(100%)',\n            transition: 'transform 0.3s ease, opacity 0.3s ease',\n            opacity: isVisible ? 1 : 0,\n            minWidth: '300px',\n            maxWidth: '500px',\n            direction: 'rtl',\n            fontFamily: 'Cairo, Arial, sans-serif'\n        };\n        const typeStyles = {\n            success: {\n                background: 'linear-gradient(45deg, #28a745, #20c997)'\n            },\n            error: {\n                background: 'linear-gradient(45deg, #dc3545, #c82333)'\n            },\n            warning: {\n                background: 'linear-gradient(45deg, #ffc107, #e0a800)'\n            },\n            info: {\n                background: 'linear-gradient(45deg, #007bff, #0056b3)'\n            }\n        };\n        return {\n            ...baseStyles,\n            ...typeStyles[type]\n        };\n    };\n    const getIcon = ()=>{\n        const icons = {\n            success: '✅',\n            error: '❌',\n            warning: '⚠️',\n            info: 'ℹ️'\n        };\n        return icons[type];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: getToastStyles(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '10px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        fontSize: '1.2rem'\n                    },\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setIsVisible(false);\n                        setTimeout(onClose, 300);\n                    },\n                    style: {\n                        background: 'rgba(255,255,255,0.2)',\n                        border: 'none',\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '25px',\n                        height: '25px',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        marginLeft: 'auto'\n                    },\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(Toast, \"m22S9IQwDfEe/fCJY7LYj8YPDMo=\");\n_c = Toast;\n// Hook لاستخدام Toast\nfunction useToast() {\n    _s1();\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        // التحقق من عدم وجود رسالة مشابهة\n        const existingToast = toasts.find((toast)=>toast.message === message && toast.type === type);\n        if (existingToast) {\n            return; // لا تضيف رسالة مكررة\n        }\n        // إنشاء ID فريد باستخدام timestamp + random number\n        const id = \"toast_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        // حد أقصى 5 رسائل في نفس الوقت\n        setToasts((prev)=>prev.slice(-4)); // احتفظ بآخر 4 + الجديدة = 5\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const ToastContainer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: 'fixed',\n                top: '20px',\n                right: '20px',\n                zIndex: 1000,\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '10px'\n            },\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                    message: toast.message,\n                    type: toast.type,\n                    onClose: ()=>removeToast(toast.id)\n                }, toast.id, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 129,\n            columnNumber: 5\n        }, this);\n    return {\n        showToast,\n        ToastContainer\n    };\n}\n_s1(useToast, \"nD8TBOiFYf9ajstmZpZK2DP4rNo=\");\nvar _c;\n$RefreshReg$(_c, \"Toast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Toast.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cedit-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);