"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardLayout(param) {\n    let { children, title = 'لوحة التحكم', subtitle = 'إدارة النظام', icon = '📊', requiredPermissions, requiredRole } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const timer = setInterval({\n                \"DashboardLayout.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"DashboardLayout.useEffect.timer\"], 1000);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>clearInterval(timer)\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: false,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: requiredPermissions,\n        requiredRole: requiredRole,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        fontSize: '1.2rem',\n                                        fontWeight: '900',\n                                        fontFamily: 'Arial, sans-serif',\n                                        gap: '5px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '900',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"X\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '1rem',\n                                                fontWeight: '300'\n                                            },\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '800',\n                                                letterSpacing: '1px'\n                                            },\n                                            children: \"Prime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        marginRight: sidebarOpen ? '280px' : '0',\n                        transition: 'margin-right 0.3s ease'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"متصل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: currentTime.toLocaleTimeString('ar-EG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '20px',\n                                padding: '25px',\n                                border: '1px solid #4a5568',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                                maxWidth: '1000px',\n                                margin: '0 auto'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"KNYedtH3V/oElqzTesyis3gRu2I=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DashboardLayout.tsx\n"));

/***/ })

});