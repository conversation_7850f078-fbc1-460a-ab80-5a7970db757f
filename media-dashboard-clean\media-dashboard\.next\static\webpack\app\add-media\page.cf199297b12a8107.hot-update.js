"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const validateTimeFormat = (time)=>{\n        const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;\n        return timeRegex.test(time);\n    };\n    const formatTimeInput = (value)=>{\n        // إزالة أي أحرف غير رقمية أو نقطتين\n        const cleaned = value.replace(/[^\\d:]/g, '');\n        // تقسيم النص إلى أجزاء\n        const parts = cleaned.split(':');\n        // تنسيق كل جزء\n        const hours = parts[0] ? parts[0].padStart(2, '0').slice(0, 2) : '00';\n        const minutes = parts[1] ? parts[1].padStart(2, '0').slice(0, 2) : '00';\n        const seconds = parts[2] ? parts[2].padStart(2, '0').slice(0, 2) : '00';\n        return \"\".concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    };\n    const calculateDuration = (timeIn, timeOut)=>{\n        if (!timeIn || !timeOut || !validateTimeFormat(timeIn) || !validateTimeFormat(timeOut)) {\n            return '00:00:00';\n        }\n        const [inHours, inMinutes, inSeconds] = timeIn.split(':').map(Number);\n        const [outHours, outMinutes, outSeconds] = timeOut.split(':').map(Number);\n        const inTotalSeconds = inHours * 3600 + inMinutes * 60 + inSeconds;\n        const outTotalSeconds = outHours * 3600 + outMinutes * 60 + outSeconds;\n        const durationSeconds = outTotalSeconds - inTotalSeconds;\n        if (durationSeconds <= 0) return '00:00:00';\n        const hours = Math.floor(durationSeconds / 3600);\n        const minutes = Math.floor(durationSeconds % 3600 / 60);\n        const seconds = durationSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const updateSegmentCount = (count)=>{\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            const existingSegment = segments.find((s)=>s.id === i);\n            newSegments.push(existingSegment || {\n                id: i,\n                segmentCode: \"SEG\".concat(i.toString().padStart(3, '0')),\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حفظ المادة الإعلامية بنجاح!', 'success');\n                // إعادة تعيين النموذج\n                setFormData({\n                    name: '',\n                    type: 'PROGRAM',\n                    description: '',\n                    channel: 'DOCUMENTARY',\n                    source: '',\n                    status: 'WAITING',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: 'SERVER'\n                });\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: 'SEG001',\n                        timeIn: '00:00:00',\n                        timeOut: '00:00:00',\n                        duration: '00:00:00'\n                    }\n                ]);\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            showToast('حدث خطأ أثناء حفظ البيانات', 'error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '800px',\n                    margin: '0 auto',\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    fontWeight: 'bold',\n                                    color: '#333',\n                                    margin: 0,\n                                    background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent'\n                                },\n                                children: \"➕ إضافة مادة إعلامية جديدة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/'),\n                                style: {\n                                    background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '10px',\n                                    padding: '10px 20px',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem'\n                                },\n                                children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #dee2e6'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#495057',\n                                            marginBottom: '20px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '200px 1fr',\n                                                    gap: '15px',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            color: '#495057',\n                                                            fontWeight: 'bold',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDCBE رقم الهارد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"رقم الهارد\",\n                                                        value: formData.hardDiskNumber,\n                                                        onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            maxWidth: '200px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة *\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: inputStyle,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '80px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"المصدر\",\n                                                value: formData.source,\n                                                onChange: (e)=>handleInputChange('source', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.status,\n                                                onChange: (e)=>handleInputChange('status', e.target.value),\n                                                style: inputStyle,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"VALID\",\n                                                        children: \"صالح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_CENSORSHIP\",\n                                                        children: \"مرفوض رقابي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_TECHNICAL\",\n                                                        children: \"مرفوض هندسي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"WAITING\",\n                                                        children: \"في الانتظار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ البداية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ الانتهاء\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الجزء\",\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الموسم\",\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '60px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #90caf9'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#1565c0',\n                                            marginBottom: '10px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83C\\uDFAC السيجمانت\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#1565c0',\n                                            fontSize: '0.9rem',\n                                            marginBottom: '20px',\n                                            fontStyle: 'italic'\n                                        },\n                                        children: \"\\uD83D\\uDCA1 تنسيق الوقت: HH:MM:SS (مثال: 01:30:45 للساعة الواحدة و30 دقيقة و45 ثانية)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '10px',\n                                                    color: '#1565c0',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \"عدد السيجمانت (1-10):\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '10px',\n                                                    flexWrap: 'wrap'\n                                                },\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3,\n                                                    4,\n                                                    5,\n                                                    6,\n                                                    7,\n                                                    8,\n                                                    9,\n                                                    10\n                                                ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>updateSegmentCount(num),\n                                                        style: {\n                                                            background: segmentCount === num ? 'linear-gradient(45deg, #1976d2, #42a5f5)' : '#f8f9fa',\n                                                            color: segmentCount === num ? 'white' : '#495057',\n                                                            border: segmentCount === num ? 'none' : '2px solid #dee2e6',\n                                                            borderRadius: '8px',\n                                                            padding: '8px 16px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '0.9rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: num\n                                                    }, num, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '20px'\n                                        },\n                                        children: segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: 'rgba(255,255,255,0.8)',\n                                                    borderRadius: '10px',\n                                                    padding: '20px',\n                                                    border: '2px solid #90caf9'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        style: {\n                                                            color: '#1565c0',\n                                                            marginBottom: '15px',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: [\n                                                            \"السيجمانت \",\n                                                            segment.id\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'grid',\n                                                            gap: '15px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'grid',\n                                                                gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                                gap: '15px'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"Time In (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 450,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"00:00:00\",\n                                                                            value: segment.timeIn,\n                                                                            onChange: (e)=>{\n                                                                                let newValue = e.target.value;\n                                                                                // السماح فقط بالأرقام والنقطتين\n                                                                                newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                                // تحديد الطول الأقصى\n                                                                                if (newValue.length <= 8) {\n                                                                                    handleSegmentChange(segment.id, 'timeIn', newValue);\n                                                                                    // حساب المدة إذا كان التنسيق صحيح\n                                                                                    if (validateTimeFormat(newValue)) {\n                                                                                        const duration = calculateDuration(newValue, segment.timeOut);\n                                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                                    }\n                                                                                }\n                                                                            },\n                                                                            onBlur: (e)=>{\n                                                                                // تنسيق القيمة عند فقدان التركيز\n                                                                                const formatted = formatTimeInput(e.target.value);\n                                                                                handleSegmentChange(segment.id, 'timeIn', formatted);\n                                                                                const duration = calculateDuration(formatted, segment.timeOut);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            },\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                borderColor: validateTimeFormat(segment.timeIn) ? '#28a745' : '#e0e0e0'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 453,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"Time Out (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"00:00:00\",\n                                                                            value: segment.timeOut,\n                                                                            onChange: (e)=>{\n                                                                                let newValue = e.target.value;\n                                                                                // السماح فقط بالأرقام والنقطتين\n                                                                                newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                                // تحديد الطول الأقصى\n                                                                                if (newValue.length <= 8) {\n                                                                                    handleSegmentChange(segment.id, 'timeOut', newValue);\n                                                                                    // حساب المدة إذا كان التنسيق صحيح\n                                                                                    if (validateTimeFormat(newValue)) {\n                                                                                        const duration = calculateDuration(segment.timeIn, newValue);\n                                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                                    }\n                                                                                }\n                                                                            },\n                                                                            onBlur: (e)=>{\n                                                                                // تنسيق القيمة عند فقدان التركيز\n                                                                                const formatted = formatTimeInput(e.target.value);\n                                                                                handleSegmentChange(segment.id, 'timeOut', formatted);\n                                                                                const duration = calculateDuration(segment.timeIn, formatted);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            },\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                borderColor: validateTimeFormat(segment.timeOut) ? '#28a745' : '#e0e0e0'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"المدة (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 528,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: segment.duration,\n                                                                            readOnly: true,\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                background: '#f8f9fa',\n                                                                                color: '#6c757d'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 531,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"كود السيجمانت\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 544,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"SEG001\",\n                                                                            value: segment.segmentCode,\n                                                                            onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                            style: inputStyle\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 547,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, segment.id, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        style: {\n                                            background: isSubmitting ? 'linear-gradient(45deg, #6c757d, #adb5bd)' : 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)',\n                                            opacity: isSubmitting ? 0.7 : 1\n                                        },\n                                        children: isSubmitting ? '⏳ جاري الحفظ...' : '💾 حفظ البيانات'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setFormData({\n                                                name: '',\n                                                type: 'PROGRAM',\n                                                description: '',\n                                                channel: 'DOCUMENTARY',\n                                                source: '',\n                                                status: 'WAITING',\n                                                startDate: new Date().toISOString().split('T')[0],\n                                                endDate: '',\n                                                notes: '',\n                                                episodeNumber: '',\n                                                seasonNumber: '',\n                                                partNumber: '',\n                                                hardDiskNumber: 'SERVER'\n                                            });\n                                            setSegmentCount(1);\n                                            setSegments([\n                                                {\n                                                    id: 1,\n                                                    segmentCode: 'SEG001',\n                                                    timeIn: '00:00:00',\n                                                    timeOut: '00:00:00',\n                                                    duration: '00:00:00'\n                                                }\n                                            ]);\n                                        },\n                                        style: {\n                                            background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(220,53,69,0.3)'\n                                        },\n                                        children: \"\\uD83D\\uDDD1️ مسح البيانات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 628,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"zuKwFwf3n4PMZi1YCRQjm69g+B8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});