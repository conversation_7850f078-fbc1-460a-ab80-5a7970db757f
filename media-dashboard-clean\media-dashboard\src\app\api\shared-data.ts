// ملف مشترك للبيانات - يضمن التزامن بين جميع APIs
import fs from 'fs';
import path from 'path';

// مسار ملف البيانات المؤقت
const DATA_FILE = path.join(process.cwd(), 'temp-data.json');

// قاعدة بيانات مشتركة للمواد الإعلامية
let mediaItems: any[] = [];

// تحميل البيانات من الملف عند بدء التشغيل
function loadData() {
  try {
    if (fs.existsSync(DATA_FILE)) {
      const data = fs.readFileSync(DATA_FILE, 'utf8');
      mediaItems = JSON.parse(data);
      console.log(`📂 تم تحميل ${mediaItems.length} مادة من الملف المؤقت`);
    }
  } catch (error) {
    console.error('❌ خطأ في تحميل البيانات:', error);
    mediaItems = [];
  }
}

// حفظ البيانات في الملف
function saveData() {
  try {
    fs.writeFileSync(DATA_FILE, JSON.stringify(mediaItems, null, 2));
    console.log(`💾 تم حفظ ${mediaItems.length} مادة في الملف المؤقت`);
  } catch (error) {
    console.error('❌ خطأ في حفظ البيانات:', error);
  }
}

// تحميل البيانات عند استيراد الملف
loadData();

// دالة لإضافة مادة جديدة
export function addMediaItem(item: any) {
  mediaItems.push(item);
  saveData(); // حفظ فوري
  console.log(`✅ تم إضافة مادة جديدة: ${item.name} (المجموع: ${mediaItems.length})`);
}

// دالة لحذف مادة
export function removeMediaItem(id: string) {
  const index = mediaItems.findIndex(item => item.id === id);
  if (index > -1) {
    const removed = mediaItems.splice(index, 1)[0];
    saveData(); // حفظ فوري
    console.log(`🗑️ تم حذف المادة: ${removed.name} (المجموع: ${mediaItems.length})`);
    return true;
  }
  return false;
}

// دالة للحصول على جميع المواد
export function getAllMediaItems() {
  return mediaItems;
}

// دالة للحصول على مادة بالمعرف
export function getMediaItemById(id: string) {
  return mediaItems.find(item => item.id === id);
}

// دالة لتحديث مادة
export function updateMediaItem(id: string, updatedItem: any) {
  const index = mediaItems.findIndex(item => item.id === id);
  if (index > -1) {
    mediaItems[index] = { ...mediaItems[index], ...updatedItem };
    saveData(); // حفظ فوري
    console.log(`✏️ تم تحديث المادة: ${updatedItem.name} (المعرف: ${id})`);
    return true;
  }
  return false;
}

// دالة لتحديث مادة
export function updateMediaItem(id: string, updatedItem: any) {
  const index = mediaItems.findIndex(item => item.id === id);
  if (index > -1) {
    mediaItems[index] = { ...mediaItems[index], ...updatedItem };
    saveData(); // حفظ فوري
    console.log(`✏️ تم تحديث المادة: ${updatedItem.name} (المعرف: ${id})`);
    return true;
  }
  return false;
}

// دالة لتحديث مادة
export function updateMediaItem(id: string, updatedItem: any) {
  const index = mediaItems.findIndex(item => item.id === id);
  if (index > -1) {
    mediaItems[index] = { ...mediaItems[index], ...updatedItem };
    saveData(); // حفظ فوري
    console.log(`✏️ تم تحديث المادة: ${updatedItem.name} (المعرف: ${id})`);
    return true;
  }
  return false;
}
