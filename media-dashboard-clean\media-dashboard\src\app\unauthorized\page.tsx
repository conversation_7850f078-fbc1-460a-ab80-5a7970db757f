'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthGuard';

export default function UnauthorizedPage() {
  const router = useRouter();
  const { user, logout } = useAuth();

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl',
      padding: '20px'
    }}>
      <div style={{
        background: 'white',
        borderRadius: '20px',
        padding: '50px',
        textAlign: 'center',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
        maxWidth: '600px',
        width: '100%'
      }}>
        <div style={{
          fontSize: '5rem',
          marginBottom: '20px'
        }}>
          🚫
        </div>
        
        <h1 style={{
          color: '#dc3545',
          fontSize: '2.5rem',
          marginBottom: '15px',
          fontWeight: 'bold'
        }}>
          غير مصرح لك بالوصول
        </h1>
        
        <p style={{
          color: '#6c757d',
          fontSize: '1.2rem',
          marginBottom: '30px',
          lineHeight: '1.6'
        }}>
          عذراً، ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة.
          <br />
          يرجى التواصل مع مدير النظام للحصول على الصلاحيات المناسبة.
        </p>

        {user && (
          <div style={{
            background: '#f8f9fa',
            padding: '20px',
            borderRadius: '15px',
            marginBottom: '30px',
            textAlign: 'right'
          }}>
            <h3 style={{
              color: '#333',
              marginBottom: '15px',
              fontSize: '1.2rem'
            }}>
              📋 معلومات المستخدم الحالي:
            </h3>
            <div style={{
              display: 'grid',
              gap: '10px',
              fontSize: '1rem'
            }}>
              <div>
                <strong>الاسم:</strong> {user.name}
              </div>
              <div>
                <strong>اسم المستخدم:</strong> @{user.username}
              </div>
              <div>
                <strong>الدور:</strong> {user.role}
              </div>
              {user.email && (
                <div>
                  <strong>البريد الإلكتروني:</strong> {user.email}
                </div>
              )}
            </div>
          </div>
        )}

        <div style={{
          background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
          padding: '20px',
          borderRadius: '15px',
          marginBottom: '30px',
          border: '1px solid #90caf9'
        }}>
          <h3 style={{
            color: '#1976d2',
            marginBottom: '15px',
            fontSize: '1.1rem'
          }}>
            🔑 أدوار النظام والصلاحيات:
          </h3>
          <div style={{
            textAlign: 'right',
            fontSize: '0.95rem',
            color: '#1565c0',
            lineHeight: '1.8'
          }}>
            <div><strong>👑 مدير النظام:</strong> صلاحيات كاملة لجميع أجزاء النظام</div>
            <div><strong>📝 مدير المحتوى:</strong> إدارة المواد الإعلامية (إضافة، تعديل، حذف)</div>
            <div><strong>📅 مجدول البرامج:</strong> إدارة الجداول الإذاعية والخريطة البرامجية</div>
            <div><strong>👁️ مستخدم عرض:</strong> عرض المحتوى فقط بدون إمكانية التعديل</div>
          </div>
        </div>

        <div style={{
          display: 'flex',
          gap: '15px',
          justifyContent: 'center',
          flexWrap: 'wrap'
        }}>
          <button
            onClick={() => router.push('/')}
            style={{
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              padding: '15px 30px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              transition: 'transform 0.2s',
              fontFamily: 'Cairo, Arial, sans-serif'
            }}
            onMouseEnter={(e) => e.target.style.transform = 'translateY(-2px)'}
            onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
          >
            🏠 العودة للرئيسية
          </button>

          {user && (
            <button
              onClick={logout}
              style={{
                background: 'linear-gradient(45deg, #dc3545, #c82333)',
                color: 'white',
                border: 'none',
                borderRadius: '12px',
                padding: '15px 30px',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                transition: 'transform 0.2s',
                fontFamily: 'Cairo, Arial, sans-serif'
              }}
              onMouseEnter={(e) => e.target.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
            >
              🚪 تسجيل الخروج
            </button>
          )}
        </div>

        <div style={{
          marginTop: '30px',
          padding: '15px',
          background: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '10px',
          color: '#856404'
        }}>
          <strong>💡 نصيحة:</strong> إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع مدير النظام لمراجعة صلاحياتك.
        </div>
      </div>
    </div>
  );
}
