"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit-media/page",{

/***/ "(app-pages-browser)/./src/app/edit-media/page.tsx":
/*!*************************************!*\
  !*** ./src/app/edit-media/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EditMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const mediaId = searchParams.get('id');\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: '',\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    // تحميل بيانات المادة للتعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditMediaPage.useEffect\": ()=>{\n            if (!mediaId) {\n                router.push('/media-list');\n                return;\n            }\n            const fetchMediaItem = {\n                \"EditMediaPage.useEffect.fetchMediaItem\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/media?id=\".concat(mediaId));\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            var _item_episodeNumber, _item_seasonNumber, _item_partNumber, _item_segments;\n                            const item = result.data;\n                            setFormData({\n                                name: item.name || '',\n                                type: item.type || 'PROGRAM',\n                                description: item.description || '',\n                                channel: item.channel || 'DOCUMENTARY',\n                                source: item.source || '',\n                                status: item.status || 'WAITING',\n                                startDate: item.startDate ? item.startDate.split('T')[0] : '',\n                                endDate: item.endDate ? item.endDate.split('T')[0] : '',\n                                notes: item.notes || '',\n                                episodeNumber: ((_item_episodeNumber = item.episodeNumber) === null || _item_episodeNumber === void 0 ? void 0 : _item_episodeNumber.toString()) || '',\n                                seasonNumber: ((_item_seasonNumber = item.seasonNumber) === null || _item_seasonNumber === void 0 ? void 0 : _item_seasonNumber.toString()) || '',\n                                partNumber: ((_item_partNumber = item.partNumber) === null || _item_partNumber === void 0 ? void 0 : _item_partNumber.toString()) || '',\n                                hardDiskNumber: item.hardDiskNumber || 'SERVER'\n                            });\n                            console.log('📋 البيانات المحملة للتعديل:', {\n                                name: item.name,\n                                status: item.status,\n                                segments: ((_item_segments = item.segments) === null || _item_segments === void 0 ? void 0 : _item_segments.length) || 0\n                            });\n                            if (item.segments && item.segments.length > 0) {\n                                const loadedSegments = item.segments.map({\n                                    \"EditMediaPage.useEffect.fetchMediaItem.loadedSegments\": (seg, index)=>({\n                                            id: index + 1,\n                                            segmentCode: seg.code || seg.segmentCode || \"SEG\".concat(String(index + 1).padStart(3, '0')),\n                                            timeIn: seg.timeIn || '00:00:00',\n                                            timeOut: seg.timeOut || '00:00:00',\n                                            duration: seg.duration || '00:00:00'\n                                        })\n                                }[\"EditMediaPage.useEffect.fetchMediaItem.loadedSegments\"]);\n                                setSegments(loadedSegments);\n                                setSegmentCount(item.segments.length);\n                                console.log('🎬 تم تحميل السيجمانت:', loadedSegments);\n                            }\n                        } else {\n                            showToast('فشل في تحميل بيانات المادة', 'error');\n                            router.push('/media-list');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching media item:', error);\n                        showToast('خطأ في تحميل البيانات', 'error');\n                        router.push('/media-list');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditMediaPage.useEffect.fetchMediaItem\"];\n            fetchMediaItem();\n        }\n    }[\"EditMediaPage.useEffect\"], [\n        mediaId,\n        router,\n        showToast\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const addSegment = ()=>{\n        const newSegmentCount = segmentCount + 1;\n        setSegmentCount(newSegmentCount);\n        const newSegment = {\n            id: newSegmentCount,\n            segmentCode: \"SEG\".concat(String(newSegmentCount).padStart(3, '0')),\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        };\n        setSegments((prev)=>[\n                ...prev,\n                newSegment\n            ]);\n        console.log('➕ تم إضافة سيجمانت جديد:', newSegment);\n    };\n    const removeSegment = (segmentId)=>{\n        if (segments.length > 1) {\n            setSegments((prev)=>prev.filter((segment)=>segment.id !== segmentId));\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.name.trim()) {\n            showToast('يرجى إدخال اسم المادة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(mediaId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم تحديث المادة الإعلامية بنجاح!', 'success');\n                router.push('/media-list');\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error updating media item:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        transition: 'border-color 0.3s',\n        direction: 'rtl'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n            lineNumber: 187,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '800px',\n                    margin: '0 auto',\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    fontWeight: 'bold',\n                                    color: '#333',\n                                    margin: 0,\n                                    background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent'\n                                },\n                                children: \"✏️ تعديل المادة الإعلامية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/media-list'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '10px 20px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDD19 العودة للقائمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #dee2e6'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#495057',\n                                            marginBottom: '20px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '200px 1fr',\n                                                    gap: '15px',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            color: '#495057',\n                                                            fontWeight: 'bold',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDCBE رقم الهارد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"رقم الهارد\",\n                                                        value: formData.hardDiskNumber,\n                                                        onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            maxWidth: '200px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة *\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: inputStyle,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '80px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"المصدر\",\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"WAITING\",\n                                                                children: \"في الانتظار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: \"صالح\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 343,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"APPROVED\",\n                                                                children: \"مقبول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED\",\n                                                                children: \"مرفوض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: \"منتهي الصلاحية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ البداية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ النهاية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الجزء\",\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الموسم\",\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات إضافية\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '60px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #90caf9'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-between',\n                                            alignItems: 'center',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    color: '#1976d2',\n                                                    fontSize: '1.3rem',\n                                                    margin: 0\n                                                },\n                                                children: \"\\uD83C\\uDFAC إدارة السيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #4caf50, #45a049)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"➕ إضافة سيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: 'white',\n                                                borderRadius: '10px',\n                                                padding: '20px',\n                                                marginBottom: '15px',\n                                                border: '1px solid #e0e0e0'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            style: {\n                                                                color: '#333',\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"سيجمانت \",\n                                                                segment.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        segments.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removeSegment(segment.id),\n                                                            style: {\n                                                                background: '#ff4444',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '15px',\n                                                                padding: '5px 10px',\n                                                                cursor: 'pointer',\n                                                                fontSize: '0.8rem'\n                                                            },\n                                                            children: \"❌ حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                        gap: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"كود السيجمانت\",\n                                                            value: segment.segmentCode,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"وقت البداية (00:00:00)\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"وقت النهاية (00:00:00)\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"المدة (00:00:00)\",\n                                                            value: segment.duration,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'duration', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, segment.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)'\n                                        },\n                                        children: \"✅ حفظ التعديلات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.push('/media-list'),\n                                        style: {\n                                            background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(108,117,125,0.3)'\n                                        },\n                                        children: \"❌ إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 557,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n        lineNumber: 208,\n        columnNumber: 5\n    }, this);\n}\n_s(EditMediaPage, \"U/Mp9ns9pODrm6O21lgz1y/hFgg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = EditMediaPage;\nvar _c;\n$RefreshReg$(_c, \"EditMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZWRpdC1tZWRpYS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUU0QztBQUNpQjtBQUNmO0FBRS9CLFNBQVNLOztJQUN0QixNQUFNQyxTQUFTSiwwREFBU0E7SUFDeEIsTUFBTUssZUFBZUosZ0VBQWVBO0lBQ3BDLE1BQU1LLFVBQVVELGFBQWFFLEdBQUcsQ0FBQztJQUNqQyxNQUFNLEVBQUVDLFNBQVMsRUFBRUMsY0FBYyxFQUFFLEdBQUdQLDJEQUFRQTtJQUU5QyxNQUFNLENBQUNRLFNBQVNDLFdBQVcsR0FBR2IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYyxVQUFVQyxZQUFZLEdBQUdmLCtDQUFRQSxDQUFDO1FBQ3ZDZ0IsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLGFBQWE7UUFDYkMsU0FBUztRQUNUQyxRQUFRO1FBQ1JDLFFBQVE7UUFDUkMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87UUFDUEMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLFlBQVk7UUFDWkMsZ0JBQWdCO0lBQ2xCO0lBRUEsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQytCLFVBQVVDLFlBQVksR0FBR2hDLCtDQUFRQSxDQUFDO1FBQ3ZDO1lBQ0VpQyxJQUFJO1lBQ0pDLGFBQWE7WUFDYkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFVBQVU7UUFDWjtLQUNEO0lBRUQsOEJBQThCO0lBQzlCcEMsZ0RBQVNBO21DQUFDO1lBQ1IsSUFBSSxDQUFDTyxTQUFTO2dCQUNaRixPQUFPZ0MsSUFBSSxDQUFDO2dCQUNaO1lBQ0Y7WUFFQSxNQUFNQzswREFBaUI7b0JBQ3JCLElBQUk7d0JBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLGlCQUF5QixPQUFSakM7d0JBQzlDLE1BQU1rQyxTQUFTLE1BQU1GLFNBQVNHLElBQUk7d0JBRWxDLElBQUlELE9BQU9FLE9BQU8sSUFBSUYsT0FBT0csSUFBSSxFQUFFO2dDQVloQkMscUJBQ0RBLG9CQUNGQSxrQkFPRkE7NEJBcEJaLE1BQU1BLE9BQU9KLE9BQU9HLElBQUk7NEJBQ3hCOUIsWUFBWTtnQ0FDVkMsTUFBTThCLEtBQUs5QixJQUFJLElBQUk7Z0NBQ25CQyxNQUFNNkIsS0FBSzdCLElBQUksSUFBSTtnQ0FDbkJDLGFBQWE0QixLQUFLNUIsV0FBVyxJQUFJO2dDQUNqQ0MsU0FBUzJCLEtBQUszQixPQUFPLElBQUk7Z0NBQ3pCQyxRQUFRMEIsS0FBSzFCLE1BQU0sSUFBSTtnQ0FDdkJDLFFBQVF5QixLQUFLekIsTUFBTSxJQUFJO2dDQUN2QkMsV0FBV3dCLEtBQUt4QixTQUFTLEdBQUd3QixLQUFLeEIsU0FBUyxDQUFDeUIsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUc7Z0NBQzNEeEIsU0FBU3VCLEtBQUt2QixPQUFPLEdBQUd1QixLQUFLdkIsT0FBTyxDQUFDd0IsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUc7Z0NBQ3JEdkIsT0FBT3NCLEtBQUt0QixLQUFLLElBQUk7Z0NBQ3JCQyxlQUFlcUIsRUFBQUEsc0JBQUFBLEtBQUtyQixhQUFhLGNBQWxCcUIsMENBQUFBLG9CQUFvQkUsUUFBUSxPQUFNO2dDQUNqRHRCLGNBQWNvQixFQUFBQSxxQkFBQUEsS0FBS3BCLFlBQVksY0FBakJvQix5Q0FBQUEsbUJBQW1CRSxRQUFRLE9BQU07Z0NBQy9DckIsWUFBWW1CLEVBQUFBLG1CQUFBQSxLQUFLbkIsVUFBVSxjQUFmbUIsdUNBQUFBLGlCQUFpQkUsUUFBUSxPQUFNO2dDQUMzQ3BCLGdCQUFnQmtCLEtBQUtsQixjQUFjLElBQUk7NEJBQ3pDOzRCQUVBcUIsUUFBUUMsR0FBRyxDQUFDLGdDQUFnQztnQ0FDMUNsQyxNQUFNOEIsS0FBSzlCLElBQUk7Z0NBQ2ZLLFFBQVF5QixLQUFLekIsTUFBTTtnQ0FDbkJVLFVBQVVlLEVBQUFBLGlCQUFBQSxLQUFLZixRQUFRLGNBQWJlLHFDQUFBQSxlQUFlSyxNQUFNLEtBQUk7NEJBQ3JDOzRCQUVBLElBQUlMLEtBQUtmLFFBQVEsSUFBSWUsS0FBS2YsUUFBUSxDQUFDb0IsTUFBTSxHQUFHLEdBQUc7Z0NBQzdDLE1BQU1DLGlCQUFpQk4sS0FBS2YsUUFBUSxDQUFDc0IsR0FBRzs2RkFBQyxDQUFDQyxLQUFVQyxRQUFtQjs0Q0FDckV0QixJQUFJc0IsUUFBUTs0Q0FDWnJCLGFBQWFvQixJQUFJRSxJQUFJLElBQUlGLElBQUlwQixXQUFXLElBQUksTUFBeUMsT0FBbkN1QixPQUFPRixRQUFRLEdBQUdHLFFBQVEsQ0FBQyxHQUFHOzRDQUNoRnZCLFFBQVFtQixJQUFJbkIsTUFBTSxJQUFJOzRDQUN0QkMsU0FBU2tCLElBQUlsQixPQUFPLElBQUk7NENBQ3hCQyxVQUFVaUIsSUFBSWpCLFFBQVEsSUFBSTt3Q0FDNUI7O2dDQUNBTCxZQUFZb0I7Z0NBQ1p0QixnQkFBZ0JnQixLQUFLZixRQUFRLENBQUNvQixNQUFNO2dDQUVwQ0YsUUFBUUMsR0FBRyxDQUFDLDBCQUEwQkU7NEJBQ3hDO3dCQUNGLE9BQU87NEJBQ0wxQyxVQUFVLDhCQUE4Qjs0QkFDeENKLE9BQU9nQyxJQUFJLENBQUM7d0JBQ2Q7b0JBQ0YsRUFBRSxPQUFPcUIsT0FBTzt3QkFDZFYsUUFBUVUsS0FBSyxDQUFDLDhCQUE4QkE7d0JBQzVDakQsVUFBVSx5QkFBeUI7d0JBQ25DSixPQUFPZ0MsSUFBSSxDQUFDO29CQUNkLFNBQVU7d0JBQ1J6QixXQUFXO29CQUNiO2dCQUNGOztZQUVBMEI7UUFDRjtrQ0FBRztRQUFDL0I7UUFBU0Y7UUFBUUk7S0FBVTtJQUUvQixNQUFNa0Qsb0JBQW9CLENBQUNDLE9BQWVDO1FBQ3hDL0MsWUFBWWdELENBQUFBLE9BQVM7Z0JBQ25CLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0YsTUFBTSxFQUFFQztZQUNYO0lBQ0Y7SUFFQSxNQUFNRSxzQkFBc0IsQ0FBQ0MsV0FBbUJKLE9BQWVDO1FBQzdEOUIsWUFBWStCLENBQUFBLE9BQVFBLEtBQUtWLEdBQUcsQ0FBQ2EsQ0FBQUEsVUFDM0JBLFFBQVFqQyxFQUFFLEtBQUtnQyxZQUNYO29CQUFFLEdBQUdDLE9BQU87b0JBQUUsQ0FBQ0wsTUFBTSxFQUFFQztnQkFBTSxJQUM3Qkk7SUFFUjtJQUVBLE1BQU1DLGFBQWE7UUFDakIsTUFBTUMsa0JBQWtCdkMsZUFBZTtRQUN2Q0MsZ0JBQWdCc0M7UUFDaEIsTUFBTUMsYUFBYTtZQUNqQnBDLElBQUltQztZQUNKbEMsYUFBYSxNQUErQyxPQUF6Q3VCLE9BQU9XLGlCQUFpQlYsUUFBUSxDQUFDLEdBQUc7WUFDdkR2QixRQUFRO1lBQ1JDLFNBQVM7WUFDVEMsVUFBVTtRQUNaO1FBQ0FMLFlBQVkrQixDQUFBQSxPQUFRO21CQUFJQTtnQkFBTU07YUFBVztRQUN6Q3BCLFFBQVFDLEdBQUcsQ0FBQyw0QkFBNEJtQjtJQUMxQztJQUVBLE1BQU1DLGdCQUFnQixDQUFDTDtRQUNyQixJQUFJbEMsU0FBU29CLE1BQU0sR0FBRyxHQUFHO1lBQ3ZCbkIsWUFBWStCLENBQUFBLE9BQVFBLEtBQUtRLE1BQU0sQ0FBQ0wsQ0FBQUEsVUFBV0EsUUFBUWpDLEVBQUUsS0FBS2dDO1FBQzVEO0lBQ0Y7SUFFQSxNQUFNTyxlQUFlLE9BQU9DO1FBQzFCQSxFQUFFQyxjQUFjO1FBRWhCLElBQUksQ0FBQzVELFNBQVNFLElBQUksQ0FBQzJELElBQUksSUFBSTtZQUN6QmpFLFVBQVUseUJBQXlCO1lBQ25DO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTThCLFdBQVcsTUFBTUMsTUFBTSxpQkFBeUIsT0FBUmpDLFVBQVc7Z0JBQ3ZEb0UsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CbEU7b0JBQ0FpQjtnQkFDRjtZQUNGO1lBRUEsTUFBTVcsU0FBUyxNQUFNRixTQUFTRyxJQUFJO1lBRWxDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEJsQyxVQUFVLG9DQUFvQztnQkFDOUNKLE9BQU9nQyxJQUFJLENBQUM7WUFDZCxPQUFPO2dCQUNMNUIsVUFBVSxVQUFVZ0MsT0FBT2lCLEtBQUssRUFBRTtZQUNwQztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkVixRQUFRVSxLQUFLLENBQUMsOEJBQThCQTtZQUM1Q2pELFVBQVUsMEJBQTBCO1FBQ3RDO0lBQ0Y7SUFFQSxNQUFNdUUsYUFBYTtRQUNqQkMsT0FBTztRQUNQQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUkMsY0FBYztRQUNkQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsV0FBVztJQUNiO0lBRUEsSUFBSTVFLFNBQVM7UUFDWCxxQkFDRSw4REFBQzZFO1lBQUlDLE9BQU87Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLFlBQVk7Z0JBQ1pDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGdCQUFnQjtZQUNsQjtzQkFDRSw0RUFBQ047Z0JBQUlDLE9BQU87b0JBQ1ZFLFlBQVk7b0JBQ1pQLGNBQWM7b0JBQ2RGLFNBQVM7b0JBQ1RhLFdBQVc7b0JBQ1hDLFdBQVc7Z0JBQ2I7MEJBQ0UsNEVBQUNDOzhCQUFHOzs7Ozs7Ozs7Ozs7Ozs7O0lBSVo7SUFFQSxxQkFDRSw4REFBQ1Q7UUFBSUMsT0FBTztZQUNWQyxXQUFXO1lBQ1hDLFlBQVk7WUFDWlQsU0FBUztZQUNUZ0IsWUFBWTtZQUNaWCxXQUFXO1FBQ2I7OzBCQUNFLDhEQUFDQztnQkFBSUMsT0FBTztvQkFDVlUsVUFBVTtvQkFDVkMsUUFBUTtvQkFDUlQsWUFBWTtvQkFDWlAsY0FBYztvQkFDZEYsU0FBUztvQkFDVGMsV0FBVztnQkFDYjs7a0NBQ0UsOERBQUNSO3dCQUFJQyxPQUFPOzRCQUFFRyxTQUFTOzRCQUFRQyxZQUFZOzRCQUFVQyxnQkFBZ0I7NEJBQWlCTyxjQUFjO3dCQUFPOzswQ0FDekcsOERBQUNDO2dDQUFHYixPQUFPO29DQUNUSixVQUFVO29DQUNWa0IsWUFBWTtvQ0FDWkMsT0FBTztvQ0FDUEosUUFBUTtvQ0FDUlQsWUFBWTtvQ0FDWmMsc0JBQXNCO29DQUN0QkMscUJBQXFCO2dDQUN2QjswQ0FBRzs7Ozs7OzBDQUdILDhEQUFDbEI7Z0NBQUlDLE9BQU87b0NBQUVHLFNBQVM7b0NBQVFlLEtBQUs7Z0NBQU87MENBQ3pDLDRFQUFDQztvQ0FDQ0MsU0FBUyxJQUFNeEcsT0FBT2dDLElBQUksQ0FBQztvQ0FDM0JvRCxPQUFPO3dDQUNMRSxZQUFZO3dDQUNaYSxPQUFPO3dDQUNQckIsUUFBUTt3Q0FDUkMsY0FBYzt3Q0FDZEYsU0FBUzt3Q0FDVDRCLFFBQVE7d0NBQ1J6QixVQUFVO29DQUNaOzhDQUNEOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNTCw4REFBQzBCO3dCQUFLQyxVQUFVekM7OzBDQUNkLDhEQUFDaUI7Z0NBQUlDLE9BQU87b0NBQ1ZFLFlBQVk7b0NBQ1pQLGNBQWM7b0NBQ2RGLFNBQVM7b0NBQ1RtQixjQUFjO29DQUNkbEIsUUFBUTtnQ0FDVjs7a0RBQ0UsOERBQUNjO3dDQUFHUixPQUFPOzRDQUFFZSxPQUFPOzRDQUFXSCxjQUFjOzRDQUFRaEIsVUFBVTt3Q0FBUztrREFBRzs7Ozs7O2tEQUUzRSw4REFBQ0c7d0NBQUlDLE9BQU87NENBQUVHLFNBQVM7NENBQVFlLEtBQUs7d0NBQU87OzBEQUV6Qyw4REFBQ25CO2dEQUFJQyxPQUFPO29EQUFFRyxTQUFTO29EQUFRcUIscUJBQXFCO29EQUFhTixLQUFLO29EQUFRZCxZQUFZO2dEQUFTOztrRUFDakcsOERBQUNxQjt3REFBTXpCLE9BQU87NERBQUVlLE9BQU87NERBQVdELFlBQVk7NERBQVFsQixVQUFVO3dEQUFTO2tFQUFHOzs7Ozs7a0VBRzVFLDhEQUFDOEI7d0RBQ0NuRyxNQUFLO3dEQUNMb0csYUFBWTt3REFDWnZELE9BQU9oRCxTQUFTYyxjQUFjO3dEQUM5QjBGLFVBQVUsQ0FBQzdDLElBQU1iLGtCQUFrQixrQkFBa0JhLEVBQUU4QyxNQUFNLENBQUN6RCxLQUFLO3dEQUNuRTRCLE9BQU87NERBQ0wsR0FBR1QsVUFBVTs0REFDYm1CLFVBQVU7d0RBQ1o7Ozs7Ozs7Ozs7OzswREFJSiw4REFBQ2dCO2dEQUNDbkcsTUFBSztnREFDTG9HLGFBQVk7Z0RBQ1p2RCxPQUFPaEQsU0FBU0UsSUFBSTtnREFDcEJzRyxVQUFVLENBQUM3QyxJQUFNYixrQkFBa0IsUUFBUWEsRUFBRThDLE1BQU0sQ0FBQ3pELEtBQUs7Z0RBQ3pENEIsT0FBT1Q7Z0RBQ1B1QyxRQUFROzs7Ozs7MERBR1YsOERBQUMvQjtnREFBSUMsT0FBTztvREFBRUcsU0FBUztvREFBUXFCLHFCQUFxQjtvREFBV04sS0FBSztnREFBTzs7a0VBQ3pFLDhEQUFDYTt3REFDQzNELE9BQU9oRCxTQUFTRyxJQUFJO3dEQUNwQnFHLFVBQVUsQ0FBQzdDLElBQU1iLGtCQUFrQixRQUFRYSxFQUFFOEMsTUFBTSxDQUFDekQsS0FBSzt3REFDekQ0QixPQUFPVDs7MEVBRVAsOERBQUN5QztnRUFBTzVELE9BQU07MEVBQVU7Ozs7OzswRUFDeEIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVM7Ozs7OzswRUFDdkIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVE7Ozs7OzswRUFDdEIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQU87Ozs7OzswRUFDckIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVE7Ozs7OzswRUFDdEIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVU7Ozs7OzswRUFDeEIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVM7Ozs7OzswRUFDdkIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVE7Ozs7Ozs7Ozs7OztrRUFHeEIsOERBQUMyRDt3REFDQzNELE9BQU9oRCxTQUFTSyxPQUFPO3dEQUN2Qm1HLFVBQVUsQ0FBQzdDLElBQU1iLGtCQUFrQixXQUFXYSxFQUFFOEMsTUFBTSxDQUFDekQsS0FBSzt3REFDNUQ0QixPQUFPVDs7MEVBRVAsOERBQUN5QztnRUFBTzVELE9BQU07MEVBQWM7Ozs7OzswRUFDNUIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQU87Ozs7OzswRUFDckIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJMUIsOERBQUM2RDtnREFDQ04sYUFBWTtnREFDWnZELE9BQU9oRCxTQUFTSSxXQUFXO2dEQUMzQm9HLFVBQVUsQ0FBQzdDLElBQU1iLGtCQUFrQixlQUFlYSxFQUFFOEMsTUFBTSxDQUFDekQsS0FBSztnREFDaEU0QixPQUFPO29EQUNMLEdBQUdULFVBQVU7b0RBQ2JVLFdBQVc7b0RBQ1hpQyxRQUFRO2dEQUNWOzs7Ozs7MERBR0YsOERBQUNuQztnREFBSUMsT0FBTztvREFBRUcsU0FBUztvREFBUXFCLHFCQUFxQjtvREFBV04sS0FBSztnREFBTzs7a0VBQ3pFLDhEQUFDUTt3REFDQ25HLE1BQUs7d0RBQ0xvRyxhQUFZO3dEQUNadkQsT0FBT2hELFNBQVNNLE1BQU07d0RBQ3RCa0csVUFBVSxDQUFDN0MsSUFBTWIsa0JBQWtCLFVBQVVhLEVBQUU4QyxNQUFNLENBQUN6RCxLQUFLO3dEQUMzRDRCLE9BQU9UOzs7Ozs7a0VBR1QsOERBQUN3Qzt3REFDQzNELE9BQU9oRCxTQUFTTyxNQUFNO3dEQUN0QmlHLFVBQVUsQ0FBQzdDLElBQU1iLGtCQUFrQixVQUFVYSxFQUFFOEMsTUFBTSxDQUFDekQsS0FBSzt3REFDM0Q0QixPQUFPVDs7MEVBRVAsOERBQUN5QztnRUFBTzVELE9BQU07MEVBQVU7Ozs7OzswRUFDeEIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVE7Ozs7OzswRUFDdEIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVc7Ozs7OzswRUFDekIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVc7Ozs7OzswRUFDekIsOERBQUM0RDtnRUFBTzVELE9BQU07MEVBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFJNUIsOERBQUMyQjtnREFBSUMsT0FBTztvREFBRUcsU0FBUztvREFBUXFCLHFCQUFxQjtvREFBV04sS0FBSztnREFBTzs7a0VBQ3pFLDhEQUFDbkI7OzBFQUNDLDhEQUFDMEI7Z0VBQU16QixPQUFPO29FQUFFRyxTQUFTO29FQUFTUyxjQUFjO29FQUFPRyxPQUFPO29FQUFXbkIsVUFBVTtnRUFBUzswRUFBRzs7Ozs7OzBFQUcvRiw4REFBQzhCO2dFQUNDbkcsTUFBSztnRUFDTDZDLE9BQU9oRCxTQUFTUSxTQUFTO2dFQUN6QmdHLFVBQVUsQ0FBQzdDLElBQU1iLGtCQUFrQixhQUFhYSxFQUFFOEMsTUFBTSxDQUFDekQsS0FBSztnRUFDOUQ0QixPQUFPVDs7Ozs7Ozs7Ozs7O2tFQUlYLDhEQUFDUTs7MEVBQ0MsOERBQUMwQjtnRUFBTXpCLE9BQU87b0VBQUVHLFNBQVM7b0VBQVNTLGNBQWM7b0VBQU9HLE9BQU87b0VBQVduQixVQUFVO2dFQUFTOzBFQUFHOzs7Ozs7MEVBRy9GLDhEQUFDOEI7Z0VBQ0NuRyxNQUFLO2dFQUNMNkMsT0FBT2hELFNBQVNTLE9BQU87Z0VBQ3ZCK0YsVUFBVSxDQUFDN0MsSUFBTWIsa0JBQWtCLFdBQVdhLEVBQUU4QyxNQUFNLENBQUN6RCxLQUFLO2dFQUM1RDRCLE9BQU9UOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NENBTVhuRSxDQUFBQSxTQUFTRyxJQUFJLEtBQUssWUFBWUgsU0FBU0csSUFBSSxLQUFLLFNBQVEsbUJBQ3hELDhEQUFDd0U7Z0RBQUlDLE9BQU87b0RBQUVHLFNBQVM7b0RBQVFxQixxQkFBcUJwRyxTQUFTRyxJQUFJLEtBQUssV0FBVyxZQUFZO29EQUFXMkYsS0FBSztnREFBTzs7a0VBQ2xILDhEQUFDUTt3REFDQ25HLE1BQUs7d0RBQ0xvRyxhQUFZO3dEQUNadkQsT0FBT2hELFNBQVNXLGFBQWE7d0RBQzdCNkYsVUFBVSxDQUFDN0MsSUFBTWIsa0JBQWtCLGlCQUFpQmEsRUFBRThDLE1BQU0sQ0FBQ3pELEtBQUs7d0RBQ2xFNEIsT0FBT1Q7Ozs7OztvREFFUm5FLFNBQVNHLElBQUksS0FBSyx5QkFDakIsOERBQUNtRzt3REFDQ25HLE1BQUs7d0RBQ0xvRyxhQUFZO3dEQUNadkQsT0FBT2hELFNBQVNhLFVBQVU7d0RBQzFCMkYsVUFBVSxDQUFDN0MsSUFBTWIsa0JBQWtCLGNBQWNhLEVBQUU4QyxNQUFNLENBQUN6RCxLQUFLO3dEQUMvRDRCLE9BQU9UOzs7Ozs2RUFHVCw4REFBQ21DO3dEQUNDbkcsTUFBSzt3REFDTG9HLGFBQVk7d0RBQ1p2RCxPQUFPaEQsU0FBU1ksWUFBWTt3REFDNUI0RixVQUFVLENBQUM3QyxJQUFNYixrQkFBa0IsZ0JBQWdCYSxFQUFFOEMsTUFBTSxDQUFDekQsS0FBSzt3REFDakU0QixPQUFPVDs7Ozs7Ozs7Ozs7OzRDQU1kbkUsU0FBU0csSUFBSSxLQUFLLHlCQUNqQiw4REFBQ21HO2dEQUNDbkcsTUFBSztnREFDTG9HLGFBQVk7Z0RBQ1p2RCxPQUFPaEQsU0FBU2EsVUFBVTtnREFDMUIyRixVQUFVLENBQUM3QyxJQUFNYixrQkFBa0IsY0FBY2EsRUFBRThDLE1BQU0sQ0FBQ3pELEtBQUs7Z0RBQy9ENEIsT0FBT1Q7Ozs7OzswREFJWCw4REFBQzBDO2dEQUNDTixhQUFZO2dEQUNadkQsT0FBT2hELFNBQVNVLEtBQUs7Z0RBQ3JCOEYsVUFBVSxDQUFDN0MsSUFBTWIsa0JBQWtCLFNBQVNhLEVBQUU4QyxNQUFNLENBQUN6RCxLQUFLO2dEQUMxRDRCLE9BQU87b0RBQ0wsR0FBR1QsVUFBVTtvREFDYlUsV0FBVztvREFDWGlDLFFBQVE7Z0RBQ1Y7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNTiw4REFBQ25DO2dDQUFJQyxPQUFPO29DQUNWRSxZQUFZO29DQUNaUCxjQUFjO29DQUNkRixTQUFTO29DQUNUbUIsY0FBYztvQ0FDZGxCLFFBQVE7Z0NBQ1Y7O2tEQUNFLDhEQUFDSzt3Q0FBSUMsT0FBTzs0Q0FBRUcsU0FBUzs0Q0FBUUUsZ0JBQWdCOzRDQUFpQkQsWUFBWTs0Q0FBVVEsY0FBYzt3Q0FBTzs7MERBQ3pHLDhEQUFDSjtnREFBR1IsT0FBTztvREFBRWUsT0FBTztvREFBV25CLFVBQVU7b0RBQVVlLFFBQVE7Z0RBQUU7MERBQUc7Ozs7OzswREFDaEUsOERBQUNRO2dEQUNDNUYsTUFBSztnREFDTDZGLFNBQVMzQztnREFDVHVCLE9BQU87b0RBQ0xFLFlBQVk7b0RBQ1phLE9BQU87b0RBQ1ByQixRQUFRO29EQUNSQyxjQUFjO29EQUNkRixTQUFTO29EQUNUNEIsUUFBUTtvREFDUnpCLFVBQVU7Z0RBQ1o7MERBQ0Q7Ozs7Ozs7Ozs7OztvQ0FLRnZELFNBQVNzQixHQUFHLENBQUMsQ0FBQ2Esd0JBQ2IsOERBQUN1Qjs0Q0FBcUJDLE9BQU87Z0RBQzNCRSxZQUFZO2dEQUNaUCxjQUFjO2dEQUNkRixTQUFTO2dEQUNUbUIsY0FBYztnREFDZGxCLFFBQVE7NENBQ1Y7OzhEQUNFLDhEQUFDSztvREFBSUMsT0FBTzt3REFBRUcsU0FBUzt3REFBUUUsZ0JBQWdCO3dEQUFpQkQsWUFBWTt3REFBVVEsY0FBYztvREFBTzs7c0VBQ3pHLDhEQUFDdUI7NERBQUduQyxPQUFPO2dFQUFFZSxPQUFPO2dFQUFRSixRQUFROzREQUFFOztnRUFBRztnRUFBU25DLFFBQVFqQyxFQUFFOzs7Ozs7O3dEQUMzREYsU0FBU29CLE1BQU0sR0FBRyxtQkFDakIsOERBQUMwRDs0REFDQzVGLE1BQUs7NERBQ0w2RixTQUFTLElBQU14QyxjQUFjSixRQUFRakMsRUFBRTs0REFDdkN5RCxPQUFPO2dFQUNMRSxZQUFZO2dFQUNaYSxPQUFPO2dFQUNQckIsUUFBUTtnRUFDUkMsY0FBYztnRUFDZEYsU0FBUztnRUFDVDRCLFFBQVE7Z0VBQ1J6QixVQUFVOzREQUNaO3NFQUNEOzs7Ozs7Ozs7Ozs7OERBTUwsOERBQUNHO29EQUFJQyxPQUFPO3dEQUFFRyxTQUFTO3dEQUFRcUIscUJBQXFCO3dEQUFtQk4sS0FBSztvREFBTzs7c0VBQ2pGLDhEQUFDUTs0REFDQ25HLE1BQUs7NERBQ0xvRyxhQUFZOzREQUNadkQsT0FBT0ksUUFBUWhDLFdBQVc7NERBQzFCb0YsVUFBVSxDQUFDN0MsSUFBTVQsb0JBQW9CRSxRQUFRakMsRUFBRSxFQUFFLGVBQWV3QyxFQUFFOEMsTUFBTSxDQUFDekQsS0FBSzs0REFDOUU0QixPQUFPVDs7Ozs7O3NFQUVULDhEQUFDbUM7NERBQ0NuRyxNQUFLOzREQUNMb0csYUFBWTs0REFDWnZELE9BQU9JLFFBQVEvQixNQUFNOzREQUNyQm1GLFVBQVUsQ0FBQzdDLElBQU1ULG9CQUFvQkUsUUFBUWpDLEVBQUUsRUFBRSxVQUFVd0MsRUFBRThDLE1BQU0sQ0FBQ3pELEtBQUs7NERBQ3pFNEIsT0FBT1Q7Ozs7OztzRUFFVCw4REFBQ21DOzREQUNDbkcsTUFBSzs0REFDTG9HLGFBQVk7NERBQ1p2RCxPQUFPSSxRQUFROUIsT0FBTzs0REFDdEJrRixVQUFVLENBQUM3QyxJQUFNVCxvQkFBb0JFLFFBQVFqQyxFQUFFLEVBQUUsV0FBV3dDLEVBQUU4QyxNQUFNLENBQUN6RCxLQUFLOzREQUMxRTRCLE9BQU9UOzs7Ozs7c0VBRVQsOERBQUNtQzs0REFDQ25HLE1BQUs7NERBQ0xvRyxhQUFZOzREQUNadkQsT0FBT0ksUUFBUTdCLFFBQVE7NERBQ3ZCaUYsVUFBVSxDQUFDN0MsSUFBTVQsb0JBQW9CRSxRQUFRakMsRUFBRSxFQUFFLFlBQVl3QyxFQUFFOEMsTUFBTSxDQUFDekQsS0FBSzs0REFDM0U0QixPQUFPVDs7Ozs7Ozs7Ozs7OzsyQ0F2REhmLFFBQVFqQyxFQUFFOzs7Ozs7Ozs7OzswQ0E4RHhCLDhEQUFDd0Q7Z0NBQUlDLE9BQU87b0NBQUVHLFNBQVM7b0NBQVFlLEtBQUs7b0NBQVFiLGdCQUFnQjtnQ0FBUzs7a0RBQ25FLDhEQUFDYzt3Q0FDQzVGLE1BQUs7d0NBQ0x5RSxPQUFPOzRDQUNMRSxZQUFZOzRDQUNaYSxPQUFPOzRDQUNQckIsUUFBUTs0Q0FDUkMsY0FBYzs0Q0FDZEYsU0FBUzs0Q0FDVEcsVUFBVTs0Q0FDVmtCLFlBQVk7NENBQ1pPLFFBQVE7NENBQ1JkLFdBQVc7d0NBQ2I7a0RBQ0Q7Ozs7OztrREFJRCw4REFBQ1k7d0NBQ0M1RixNQUFLO3dDQUNMNkYsU0FBUyxJQUFNeEcsT0FBT2dDLElBQUksQ0FBQzt3Q0FDM0JvRCxPQUFPOzRDQUNMRSxZQUFZOzRDQUNaYSxPQUFPOzRDQUNQckIsUUFBUTs0Q0FDUkMsY0FBYzs0Q0FDZEYsU0FBUzs0Q0FDVEcsVUFBVTs0Q0FDVmtCLFlBQVk7NENBQ1pPLFFBQVE7NENBQ1JkLFdBQVc7d0NBQ2I7a0RBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNUCw4REFBQ3RGOzs7Ozs7Ozs7OztBQUdQO0dBemlCd0JOOztRQUNQSCxzREFBU0E7UUFDSEMsNERBQWVBO1FBRUVDLHVEQUFRQTs7O0tBSnhCQyIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxlZGl0LW1lZGlhXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtcyB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvY29tcG9uZW50cy9Ub2FzdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEVkaXRNZWRpYVBhZ2UoKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcbiAgY29uc3QgbWVkaWFJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2lkJyk7XG4gIGNvbnN0IHsgc2hvd1RvYXN0LCBUb2FzdENvbnRhaW5lciB9ID0gdXNlVG9hc3QoKTtcbiAgXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHtcbiAgICBuYW1lOiAnJyxcbiAgICB0eXBlOiAnUFJPR1JBTScsXG4gICAgZGVzY3JpcHRpb246ICcnLFxuICAgIGNoYW5uZWw6ICdET0NVTUVOVEFSWScsXG4gICAgc291cmNlOiAnJyxcbiAgICBzdGF0dXM6ICdXQUlUSU5HJyxcbiAgICBzdGFydERhdGU6ICcnLFxuICAgIGVuZERhdGU6ICcnLFxuICAgIG5vdGVzOiAnJyxcbiAgICBlcGlzb2RlTnVtYmVyOiAnJyxcbiAgICBzZWFzb25OdW1iZXI6ICcnLFxuICAgIHBhcnROdW1iZXI6ICcnLFxuICAgIGhhcmREaXNrTnVtYmVyOiAnU0VSVkVSJyxcbiAgfSk7XG5cbiAgY29uc3QgW3NlZ21lbnRDb3VudCwgc2V0U2VnbWVudENvdW50XSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBbc2VnbWVudHMsIHNldFNlZ21lbnRzXSA9IHVzZVN0YXRlKFtcbiAgICB7XG4gICAgICBpZDogMSxcbiAgICAgIHNlZ21lbnRDb2RlOiAnU0VHMDAxJyxcbiAgICAgIHRpbWVJbjogJzAwOjAwOjAwJyxcbiAgICAgIHRpbWVPdXQ6ICcwMDowMDowMCcsXG4gICAgICBkdXJhdGlvbjogJzAwOjAwOjAwJ1xuICAgIH1cbiAgXSk7XG5cbiAgLy8g2KrYrdmF2YrZhCDYqNmK2KfZhtin2Kog2KfZhNmF2KfYr9ipINmE2YTYqti52K/ZitmEXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFtZWRpYUlkKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL21lZGlhLWxpc3QnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCBmZXRjaE1lZGlhSXRlbSA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvbWVkaWE/aWQ9JHttZWRpYUlkfWApO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzICYmIHJlc3VsdC5kYXRhKSB7XG4gICAgICAgICAgY29uc3QgaXRlbSA9IHJlc3VsdC5kYXRhO1xuICAgICAgICAgIHNldEZvcm1EYXRhKHtcbiAgICAgICAgICAgIG5hbWU6IGl0ZW0ubmFtZSB8fCAnJyxcbiAgICAgICAgICAgIHR5cGU6IGl0ZW0udHlwZSB8fCAnUFJPR1JBTScsXG4gICAgICAgICAgICBkZXNjcmlwdGlvbjogaXRlbS5kZXNjcmlwdGlvbiB8fCAnJyxcbiAgICAgICAgICAgIGNoYW5uZWw6IGl0ZW0uY2hhbm5lbCB8fCAnRE9DVU1FTlRBUlknLFxuICAgICAgICAgICAgc291cmNlOiBpdGVtLnNvdXJjZSB8fCAnJyxcbiAgICAgICAgICAgIHN0YXR1czogaXRlbS5zdGF0dXMgfHwgJ1dBSVRJTkcnLCAvLyDYp9iz2KrYrtiv2KfZhSDYp9mE2K3Yp9mE2Kkg2KfZhNmB2LnZhNmK2Kkg2YXZhiDYp9mE2KjZitin2YbYp9iqXG4gICAgICAgICAgICBzdGFydERhdGU6IGl0ZW0uc3RhcnREYXRlID8gaXRlbS5zdGFydERhdGUuc3BsaXQoJ1QnKVswXSA6ICcnLFxuICAgICAgICAgICAgZW5kRGF0ZTogaXRlbS5lbmREYXRlID8gaXRlbS5lbmREYXRlLnNwbGl0KCdUJylbMF0gOiAnJyxcbiAgICAgICAgICAgIG5vdGVzOiBpdGVtLm5vdGVzIHx8ICcnLFxuICAgICAgICAgICAgZXBpc29kZU51bWJlcjogaXRlbS5lcGlzb2RlTnVtYmVyPy50b1N0cmluZygpIHx8ICcnLFxuICAgICAgICAgICAgc2Vhc29uTnVtYmVyOiBpdGVtLnNlYXNvbk51bWJlcj8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgICAgIHBhcnROdW1iZXI6IGl0ZW0ucGFydE51bWJlcj8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgICAgIGhhcmREaXNrTnVtYmVyOiBpdGVtLmhhcmREaXNrTnVtYmVyIHx8ICdTRVJWRVInLFxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfk4sg2KfZhNio2YrYp9mG2KfYqiDYp9mE2YXYrdmF2YTYqSDZhNmE2KrYudiv2YrZhDonLCB7XG4gICAgICAgICAgICBuYW1lOiBpdGVtLm5hbWUsXG4gICAgICAgICAgICBzdGF0dXM6IGl0ZW0uc3RhdHVzLFxuICAgICAgICAgICAgc2VnbWVudHM6IGl0ZW0uc2VnbWVudHM/Lmxlbmd0aCB8fCAwXG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICBpZiAoaXRlbS5zZWdtZW50cyAmJiBpdGVtLnNlZ21lbnRzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgIGNvbnN0IGxvYWRlZFNlZ21lbnRzID0gaXRlbS5zZWdtZW50cy5tYXAoKHNlZzogYW55LCBpbmRleDogbnVtYmVyKSA9PiAoe1xuICAgICAgICAgICAgICBpZDogaW5kZXggKyAxLFxuICAgICAgICAgICAgICBzZWdtZW50Q29kZTogc2VnLmNvZGUgfHwgc2VnLnNlZ21lbnRDb2RlIHx8IGBTRUcke1N0cmluZyhpbmRleCArIDEpLnBhZFN0YXJ0KDMsICcwJyl9YCxcbiAgICAgICAgICAgICAgdGltZUluOiBzZWcudGltZUluIHx8ICcwMDowMDowMCcsXG4gICAgICAgICAgICAgIHRpbWVPdXQ6IHNlZy50aW1lT3V0IHx8ICcwMDowMDowMCcsXG4gICAgICAgICAgICAgIGR1cmF0aW9uOiBzZWcuZHVyYXRpb24gfHwgJzAwOjAwOjAwJ1xuICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgc2V0U2VnbWVudHMobG9hZGVkU2VnbWVudHMpO1xuICAgICAgICAgICAgc2V0U2VnbWVudENvdW50KGl0ZW0uc2VnbWVudHMubGVuZ3RoKTtcblxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfjqwg2KrZhSDYqtit2YXZitmEINin2YTYs9mK2KzZhdin2YbYqjonLCBsb2FkZWRTZWdtZW50cyk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHNob3dUb2FzdCgn2YHYtNmEINmB2Yog2KrYrdmF2YrZhCDYqNmK2KfZhtin2Kog2KfZhNmF2KfYr9ipJywgJ2Vycm9yJyk7XG4gICAgICAgICAgcm91dGVyLnB1c2goJy9tZWRpYS1saXN0Jyk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG1lZGlhIGl0ZW06JywgZXJyb3IpO1xuICAgICAgICBzaG93VG9hc3QoJ9iu2LfYoyDZgdmKINiq2K3ZhdmK2YQg2KfZhNio2YrYp9mG2KfYqicsICdlcnJvcicpO1xuICAgICAgICByb3V0ZXIucHVzaCgnL21lZGlhLWxpc3QnKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBmZXRjaE1lZGlhSXRlbSgpO1xuICB9LCBbbWVkaWFJZCwgcm91dGVyLCBzaG93VG9hc3RdKTtcblxuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IChmaWVsZDogc3RyaW5nLCB2YWx1ZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtmaWVsZF06IHZhbHVlXG4gICAgfSkpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNlZ21lbnRDaGFuZ2UgPSAoc2VnbWVudElkOiBudW1iZXIsIGZpZWxkOiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcpID0+IHtcbiAgICBzZXRTZWdtZW50cyhwcmV2ID0+IHByZXYubWFwKHNlZ21lbnQgPT4gXG4gICAgICBzZWdtZW50LmlkID09PSBzZWdtZW50SWQgXG4gICAgICAgID8geyAuLi5zZWdtZW50LCBbZmllbGRdOiB2YWx1ZSB9XG4gICAgICAgIDogc2VnbWVudFxuICAgICkpO1xuICB9O1xuXG4gIGNvbnN0IGFkZFNlZ21lbnQgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3U2VnbWVudENvdW50ID0gc2VnbWVudENvdW50ICsgMTtcbiAgICBzZXRTZWdtZW50Q291bnQobmV3U2VnbWVudENvdW50KTtcbiAgICBjb25zdCBuZXdTZWdtZW50ID0ge1xuICAgICAgaWQ6IG5ld1NlZ21lbnRDb3VudCxcbiAgICAgIHNlZ21lbnRDb2RlOiBgU0VHJHtTdHJpbmcobmV3U2VnbWVudENvdW50KS5wYWRTdGFydCgzLCAnMCcpfWAsXG4gICAgICB0aW1lSW46ICcwMDowMDowMCcsXG4gICAgICB0aW1lT3V0OiAnMDA6MDA6MDAnLFxuICAgICAgZHVyYXRpb246ICcwMDowMDowMCdcbiAgICB9O1xuICAgIHNldFNlZ21lbnRzKHByZXYgPT4gWy4uLnByZXYsIG5ld1NlZ21lbnRdKTtcbiAgICBjb25zb2xlLmxvZygn4p6VINiq2YUg2KXYttin2YHYqSDYs9mK2KzZhdin2YbYqiDYrNiv2YrYrzonLCBuZXdTZWdtZW50KTtcbiAgfTtcblxuICBjb25zdCByZW1vdmVTZWdtZW50ID0gKHNlZ21lbnRJZDogbnVtYmVyKSA9PiB7XG4gICAgaWYgKHNlZ21lbnRzLmxlbmd0aCA+IDEpIHtcbiAgICAgIHNldFNlZ21lbnRzKHByZXYgPT4gcHJldi5maWx0ZXIoc2VnbWVudCA9PiBzZWdtZW50LmlkICE9PSBzZWdtZW50SWQpKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcblxuICAgIGlmICghZm9ybURhdGEubmFtZS50cmltKCkpIHtcbiAgICAgIHNob3dUb2FzdCgn2YrYsdis2Ykg2KXYr9iu2KfZhCDYp9iz2YUg2KfZhNmF2KfYr9ipJywgJ2Vycm9yJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvbWVkaWE/aWQ9JHttZWRpYUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBmb3JtRGF0YSxcbiAgICAgICAgICBzZWdtZW50c1xuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzaG93VG9hc3QoJ9iq2YUg2KrYrdiv2YrYqyDYp9mE2YXYp9iv2Kkg2KfZhNil2LnZhNin2YXZitipINio2YbYrNin2K0hJywgJ3N1Y2Nlc3MnKTtcbiAgICAgICAgcm91dGVyLnB1c2goJy9tZWRpYS1saXN0Jyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzaG93VG9hc3QoJ9iu2LfYozogJyArIHJlc3VsdC5lcnJvciwgJ2Vycm9yJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIG1lZGlhIGl0ZW06JywgZXJyb3IpO1xuICAgICAgc2hvd1RvYXN0KCfYrti32KMg2YHZiiDYp9mE2KfYqti12KfZhCDYqNin2YTYrtin2K/ZhScsICdlcnJvcicpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBpbnB1dFN0eWxlID0ge1xuICAgIHdpZHRoOiAnMTAwJScsXG4gICAgcGFkZGluZzogJzEycHgnLFxuICAgIGJvcmRlcjogJzJweCBzb2xpZCAjZTBlMGUwJyxcbiAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgdHJhbnNpdGlvbjogJ2JvcmRlci1jb2xvciAwLjNzJyxcbiAgICBkaXJlY3Rpb246ICdydGwnIGFzIGNvbnN0XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpJyxcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIGJhY2tncm91bmQ6ICd3aGl0ZScsXG4gICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMjBweCcsXG4gICAgICAgICAgcGFkZGluZzogJzQwcHgnLFxuICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgYm94U2hhZG93OiAnMCAyMHB4IDQwcHggcmdiYSgwLDAsMCwwLjEpJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8aDI+4o+zINis2KfYsdmKINiq2K3ZhdmK2YQg2KfZhNio2YrYp9mG2KfYqi4uLjwvaDI+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17e1xuICAgICAgbWluSGVpZ2h0OiAnMTAwdmgnLFxuICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSknLFxuICAgICAgcGFkZGluZzogJzIwcHgnLFxuICAgICAgZm9udEZhbWlseTogJ0NhaXJvLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgICBkaXJlY3Rpb246ICdydGwnXG4gICAgfX0+XG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIG1heFdpZHRoOiAnODAwcHgnLFxuICAgICAgICBtYXJnaW46ICcwIGF1dG8nLFxuICAgICAgICBiYWNrZ3JvdW5kOiAnd2hpdGUnLFxuICAgICAgICBib3JkZXJSYWRpdXM6ICcyMHB4JyxcbiAgICAgICAgcGFkZGluZzogJzQwcHgnLFxuICAgICAgICBib3hTaGFkb3c6ICcwIDIwcHggNDBweCByZ2JhKDAsMCwwLDAuMSknXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBtYXJnaW5Cb3R0b206ICczMHB4JyB9fT5cbiAgICAgICAgICA8aDEgc3R5bGU9e3tcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMnJlbScsXG4gICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICBjb2xvcjogJyMzMzMnLFxuICAgICAgICAgICAgbWFyZ2luOiAwLFxuICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzY2N2VlYSwgIzc2NGJhMiknLFxuICAgICAgICAgICAgV2Via2l0QmFja2dyb3VuZENsaXA6ICd0ZXh0JyxcbiAgICAgICAgICAgIFdlYmtpdFRleHRGaWxsQ29sb3I6ICd0cmFuc3BhcmVudCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIOKcj++4jyDYqti52K/ZitmEINin2YTZhdin2K/YqSDYp9mE2KXYudmE2KfZhdmK2KlcbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcxMHB4JyB9fT5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9tZWRpYS1saXN0Jyl9XG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzZjNzU3ZCwgIzQ5NTA1NyknLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzEwcHgnLFxuICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxMHB4IDIwcHgnLFxuICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJ1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDwn5SZINin2YTYudmI2K/YqSDZhNmE2YLYp9im2YXYqVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjhmOWZhIDAlLCAjZTllY2VmIDEwMCUpJyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzE1cHgnLFxuICAgICAgICAgICAgcGFkZGluZzogJzI1cHgnLFxuICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMjVweCcsXG4gICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2RlZTJlNidcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxoMiBzdHlsZT17eyBjb2xvcjogJyM0OTUwNTcnLCBtYXJnaW5Cb3R0b206ICcyMHB4JywgZm9udFNpemU6ICcxLjNyZW0nIH19PvCfk50g2KfZhNmF2LnZhNmI2YXYp9iqINin2YTYo9iz2KfYs9mK2Kk8L2gyPlxuXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ2FwOiAnMTVweCcgfX0+XG4gICAgICAgICAgICAgIHsvKiDYrdmC2YQg2LHZgtmFINin2YTZh9in2LHYryAqL31cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZ3JpZCcsIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcyMDBweCAxZnInLCBnYXA6ICcxNXB4JywgYWxpZ25JdGVtczogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGNvbG9yOiAnIzQ5NTA1NycsIGZvbnRXZWlnaHQ6ICdib2xkJywgZm9udFNpemU6ICcwLjlyZW0nIH19PlxuICAgICAgICAgICAgICAgICAg8J+SviDYsdmC2YUg2KfZhNmH2KfYsdivOlxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItix2YLZhSDYp9mE2YfYp9ix2K9cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmhhcmREaXNrTnVtYmVyfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnaGFyZERpc2tOdW1iZXInLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAuLi5pbnB1dFN0eWxlLFxuICAgICAgICAgICAgICAgICAgICBtYXhXaWR0aDogJzIwMHB4J1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYp9iz2YUg2KfZhNmF2KfYr9ipICpcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ25hbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgc3R5bGU9e2lucHV0U3R5bGV9XG4gICAgICAgICAgICAgICAgcmVxdWlyZWRcbiAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogJzFmciAxZnInLCBnYXA6ICcxNXB4JyB9fT5cbiAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudHlwZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ3R5cGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBzdHlsZT17aW5wdXRTdHlsZX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiUFJPR1JBTVwiPtio2LHZhtin2YXYrDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlNFUklFU1wiPtmF2LPZhNiz2YQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJNT1ZJRVwiPtmB2YrZhNmFPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiU09OR1wiPtij2LrZhtmK2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJTVElOR1wiPlN0aW5nPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRklMTF9JTlwiPkZpbGwgSU48L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJGSUxMRVJcIj5GaWxsZXI8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQUk9NT1wiPlByb21vPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG5cbiAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY2hhbm5lbH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2NoYW5uZWwnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBzdHlsZT17aW5wdXRTdHlsZX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRE9DVU1FTlRBUllcIj7Yp9mE2YjYq9in2KbZgtmK2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJORVdTXCI+2KfZhNij2K7YqNin2LE8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJPVEhFUlwiPtij2K7YsdmJPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2YjYtdmBINin2YTZhdin2K/YqVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2Rlc2NyaXB0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAuLi5pbnB1dFN0eWxlLFxuICAgICAgICAgICAgICAgICAgbWluSGVpZ2h0OiAnODBweCcsXG4gICAgICAgICAgICAgICAgICByZXNpemU6ICd2ZXJ0aWNhbCdcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAvPlxuXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2dyaWQnLCBncmlkVGVtcGxhdGVDb2x1bW5zOiAnMWZyIDFmcicsIGdhcDogJzE1cHgnIH19PlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYp9mE2YXYtdiv2LFcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnNvdXJjZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ3NvdXJjZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXtpbnB1dFN0eWxlfVxuICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnc3RhdHVzJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgc3R5bGU9e2lucHV0U3R5bGV9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIldBSVRJTkdcIj7ZgdmKINin2YTYp9mG2KrYuNin2LE8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJWQUxJRFwiPti12KfZhNitPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQVBQUk9WRURcIj7ZhdmC2KjZiNmEPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiUkVKRUNURURcIj7Zhdix2YHZiNi2PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRVhQSVJFRFwiPtmF2YbYqtmH2Yog2KfZhNi12YTYp9it2YrYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogJzFmciAxZnInLCBnYXA6ICcxNXB4JyB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGRpc3BsYXk6ICdibG9jaycsIG1hcmdpbkJvdHRvbTogJzVweCcsIGNvbG9yOiAnIzQ5NTA1NycsIGZvbnRTaXplOiAnMC45cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAg2KrYp9ix2YrYriDYp9mE2KjYr9in2YrYqTpcbiAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc3RhcnREYXRlfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdzdGFydERhdGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXtpbnB1dFN0eWxlfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgc3R5bGU9e3sgZGlzcGxheTogJ2Jsb2NrJywgbWFyZ2luQm90dG9tOiAnNXB4JywgY29sb3I6ICcjNDk1MDU3JywgZm9udFNpemU6ICcwLjlyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICDYqtin2LHZitiuINin2YTZhtmH2KfZitipOlxuICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5lbmREYXRlfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdlbmREYXRlJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17aW5wdXRTdHlsZX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiDYp9mE2K3ZgtmI2YQg2KfZhNiu2KfYtdipINio2YbZiNi5INin2YTZhdin2K/YqSAqL31cbiAgICAgICAgICAgICAgeyhmb3JtRGF0YS50eXBlID09PSAnU0VSSUVTJyB8fCBmb3JtRGF0YS50eXBlID09PSAnUFJPR1JBTScpICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogZm9ybURhdGEudHlwZSA9PT0gJ1NFUklFUycgPyAnMWZyIDFmcicgOiAnMWZyIDFmcicsIGdhcDogJzE1cHgnIH19PlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItix2YLZhSDYp9mE2K3ZhNmC2KlcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZXBpc29kZU51bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnZXBpc29kZU51bWJlcicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e2lucHV0U3R5bGV9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2Zvcm1EYXRhLnR5cGUgPT09ICdTRVJJRVMnID8gKFxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItix2YLZhSDYp9mE2KzYstihXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGFydE51bWJlcn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdwYXJ0TnVtYmVyJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXtpbnB1dFN0eWxlfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYsdmC2YUg2KfZhNmF2YjYs9mFXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuc2Vhc29uTnVtYmVyfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ3NlYXNvbk51bWJlcicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17aW5wdXRTdHlsZX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAge2Zvcm1EYXRhLnR5cGUgPT09ICdNT1ZJRScgJiYgKFxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItix2YLZhSDYp9mE2KzYstihXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXJ0TnVtYmVyfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgncGFydE51bWJlcicsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIHN0eWxlPXtpbnB1dFN0eWxlfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLZhdmE2KfYrdi42KfYqiDYpdi22KfZgdmK2KlcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5ub3Rlc31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdub3RlcycsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgLi4uaW5wdXRTdHlsZSxcbiAgICAgICAgICAgICAgICAgIG1pbkhlaWdodDogJzYwcHgnLFxuICAgICAgICAgICAgICAgICAgcmVzaXplOiAndmVydGljYWwnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qINmC2LPZhSDYp9mE2LPZitis2YXYp9mG2KogKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlM2YyZmQgMCUsICNiYmRlZmIgMTAwJSknLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTVweCcsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMjVweCcsXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcyNXB4JyxcbiAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjOTBjYWY5J1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBtYXJnaW5Cb3R0b206ICcyMHB4JyB9fT5cbiAgICAgICAgICAgICAgPGgyIHN0eWxlPXt7IGNvbG9yOiAnIzE5NzZkMicsIGZvbnRTaXplOiAnMS4zcmVtJywgbWFyZ2luOiAwIH19PvCfjqwg2KXYr9in2LHYqSDYp9mE2LPZitis2YXYp9mG2Ko8L2gyPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17YWRkU2VnbWVudH1cbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzRjYWY1MCwgIzQ1YTA0OSknLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzIwcHgnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzhweCAxNnB4JyxcbiAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIOKelSDYpdi22KfZgdipINiz2YrYrNmF2KfZhtiqXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHtzZWdtZW50cy5tYXAoKHNlZ21lbnQpID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e3NlZ21lbnQuaWR9IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMHB4JyxcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMTVweCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICNlMGUwZTAnXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgbWFyZ2luQm90dG9tOiAnMTVweCcgfX0+XG4gICAgICAgICAgICAgICAgICA8aDMgc3R5bGU9e3sgY29sb3I6ICcjMzMzJywgbWFyZ2luOiAwIH19Ptiz2YrYrNmF2KfZhtiqIHtzZWdtZW50LmlkfTwvaDM+XG4gICAgICAgICAgICAgICAgICB7c2VnbWVudHMubGVuZ3RoID4gMSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVTZWdtZW50KHNlZ21lbnQuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnI2ZmNDQ0NCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTVweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnNXB4IDEwcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuOHJlbSdcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg4p2MINit2LDZgVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogJzFmciAxZnIgMWZyIDFmcicsIGdhcDogJzE1cHgnIH19PlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLZg9mI2K8g2KfZhNiz2YrYrNmF2KfZhtiqXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlZ21lbnQuc2VnbWVudENvZGV9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2VnbWVudENoYW5nZShzZWdtZW50LmlkLCAnc2VnbWVudENvZGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXtpbnB1dFN0eWxlfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2YjZgtiqINin2YTYqNiv2KfZitipICgwMDowMDowMClcIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VnbWVudC50aW1lSW59XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2VnbWVudENoYW5nZShzZWdtZW50LmlkLCAndGltZUluJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17aW5wdXRTdHlsZX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItmI2YLYqiDYp9mE2YbZh9in2YrYqSAoMDA6MDA6MDApXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlZ21lbnQudGltZU91dH1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVTZWdtZW50Q2hhbmdlKHNlZ21lbnQuaWQsICd0aW1lT3V0JywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17aW5wdXRTdHlsZX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItin2YTZhdiv2KkgKDAwOjAwOjAwKVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWdtZW50LmR1cmF0aW9ufVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNlZ21lbnRDaGFuZ2Uoc2VnbWVudC5pZCwgJ2R1cmF0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17aW5wdXRTdHlsZX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMTVweCcsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzI4YTc0NSwgIzIwYzk5NyknLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzI1cHgnLFxuICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxNXB4IDQwcHgnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4xcmVtJyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OiAnMCA0cHggMTVweCByZ2JhKDQwLDE2Nyw2OSwwLjMpJyxcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg4pyFINit2YHYuCDYp9mE2KrYudiv2YrZhNin2KpcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL21lZGlhLWxpc3QnKX1cbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjNmM3NTdkLCAjNDk1MDU3KScsXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMjVweCcsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzE1cHggNDBweCcsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjFyZW0nLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxNXB4IHJnYmEoMTA4LDExNywxMjUsMC4zKScsXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOKdjCDYpdmE2LrYp9ihXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9mb3JtPlxuICAgICAgPC9kaXY+XG4gICAgICA8VG9hc3RDb250YWluZXIgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsInVzZVNlYXJjaFBhcmFtcyIsInVzZVRvYXN0IiwiRWRpdE1lZGlhUGFnZSIsInJvdXRlciIsInNlYXJjaFBhcmFtcyIsIm1lZGlhSWQiLCJnZXQiLCJzaG93VG9hc3QiLCJUb2FzdENvbnRhaW5lciIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsIm5hbWUiLCJ0eXBlIiwiZGVzY3JpcHRpb24iLCJjaGFubmVsIiwic291cmNlIiwic3RhdHVzIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsIm5vdGVzIiwiZXBpc29kZU51bWJlciIsInNlYXNvbk51bWJlciIsInBhcnROdW1iZXIiLCJoYXJkRGlza051bWJlciIsInNlZ21lbnRDb3VudCIsInNldFNlZ21lbnRDb3VudCIsInNlZ21lbnRzIiwic2V0U2VnbWVudHMiLCJpZCIsInNlZ21lbnRDb2RlIiwidGltZUluIiwidGltZU91dCIsImR1cmF0aW9uIiwicHVzaCIsImZldGNoTWVkaWFJdGVtIiwicmVzcG9uc2UiLCJmZXRjaCIsInJlc3VsdCIsImpzb24iLCJzdWNjZXNzIiwiZGF0YSIsIml0ZW0iLCJzcGxpdCIsInRvU3RyaW5nIiwiY29uc29sZSIsImxvZyIsImxlbmd0aCIsImxvYWRlZFNlZ21lbnRzIiwibWFwIiwic2VnIiwiaW5kZXgiLCJjb2RlIiwiU3RyaW5nIiwicGFkU3RhcnQiLCJlcnJvciIsImhhbmRsZUlucHV0Q2hhbmdlIiwiZmllbGQiLCJ2YWx1ZSIsInByZXYiLCJoYW5kbGVTZWdtZW50Q2hhbmdlIiwic2VnbWVudElkIiwic2VnbWVudCIsImFkZFNlZ21lbnQiLCJuZXdTZWdtZW50Q291bnQiLCJuZXdTZWdtZW50IiwicmVtb3ZlU2VnbWVudCIsImZpbHRlciIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInRyaW0iLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJpbnB1dFN0eWxlIiwid2lkdGgiLCJwYWRkaW5nIiwiYm9yZGVyIiwiYm9yZGVyUmFkaXVzIiwiZm9udFNpemUiLCJ0cmFuc2l0aW9uIiwiZGlyZWN0aW9uIiwiZGl2Iiwic3R5bGUiLCJtaW5IZWlnaHQiLCJiYWNrZ3JvdW5kIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsInRleHRBbGlnbiIsImJveFNoYWRvdyIsImgyIiwiZm9udEZhbWlseSIsIm1heFdpZHRoIiwibWFyZ2luIiwibWFyZ2luQm90dG9tIiwiaDEiLCJmb250V2VpZ2h0IiwiY29sb3IiLCJXZWJraXRCYWNrZ3JvdW5kQ2xpcCIsIldlYmtpdFRleHRGaWxsQ29sb3IiLCJnYXAiLCJidXR0b24iLCJvbkNsaWNrIiwiY3Vyc29yIiwiZm9ybSIsIm9uU3VibWl0IiwiZ3JpZFRlbXBsYXRlQ29sdW1ucyIsImxhYmVsIiwiaW5wdXQiLCJwbGFjZWhvbGRlciIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicmVxdWlyZWQiLCJzZWxlY3QiLCJvcHRpb24iLCJ0ZXh0YXJlYSIsInJlc2l6ZSIsImgzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/edit-media/page.tsx\n"));

/***/ })

});