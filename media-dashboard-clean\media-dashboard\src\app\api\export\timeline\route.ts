import { NextRequest, NextResponse } from 'next/server';
import { TimelineItem } from '@/lib/dynamicScheduler';

// POST - تصدير الجدول الزمني إلى Excel
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { timeline, date, format = 'excel' } = body;

    if (!Array.isArray(timeline) || !date) {
      return NextResponse.json(
        { success: false, error: 'الجدول الزمني والتاريخ مطلوبان' },
        { status: 400 }
      );
    }

    // تحضير البيانات للتصدير
    const exportData = prepareExportData(timeline, date);

    switch (format) {
      case 'excel':
        return exportToExcel(exportData, date);
      case 'csv':
        return exportToCSV(exportData, date);
      case 'json':
        return exportToJSON(exportData, date);
      default:
        return NextResponse.json(
          { success: false, error: 'تنسيق التصدير غير مدعوم' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error exporting timeline:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في تصدير الجدول الزمني' },
      { status: 500 }
    );
  }
}

// تحضير البيانات للتصدير
function prepareExportData(timeline: TimelineItem[], date: string) {
  const exportData = {
    metadata: {
      title: 'جدول الإذاعة اليومي',
      date: new Date(date).toLocaleDateString('ar-SA'),
      generatedAt: new Date().toLocaleString('ar-SA'),
      totalItems: timeline.length,
      totalDuration: calculateTotalDuration(timeline)
    },
    items: timeline.map((item, index) => ({
      sequence: index + 1,
      name: item.name,
      type: getTypeLabel(item.type),
      startTime: item.startTime,
      endTime: item.endTime,
      duration: item.duration,
      status: item.status,
      progress: `${item.progress}%`,
      segmentInfo: item.segmentNumber ? `${item.segmentNumber}/${item.totalSegments}` : '',
      segmentCode: item.segment?.code || '',
      isBreak: item.isBreak ? 'نعم' : 'لا',
      isGap: item.isGap ? 'نعم' : 'لا',
      backgroundColor: item.backgroundColor || '',
      notes: getItemNotes(item)
    })),
    statistics: {
      totalContent: timeline.filter(item => item.type === 'CONTENT').length,
      totalSegments: timeline.filter(item => item.type === 'SEGMENT').length,
      totalBreaks: timeline.filter(item => item.isBreak).length,
      totalGaps: timeline.filter(item => item.isGap).length,
      completedItems: timeline.filter(item => item.status === 'منتهي').length,
      currentItems: timeline.filter(item => item.status === 'يُعرض الآن').length,
      upcomingItems: timeline.filter(item => item.status === 'قادم').length
    }
  };

  return exportData;
}

// تصدير إلى Excel (محاكاة)
function exportToExcel(data: any, date: string) {
  // في التطبيق الحقيقي، ستستخدم مكتبة مثل xlsx أو exceljs
  const excelData = {
    filename: `جدول_الإذاعة_${date}.xlsx`,
    sheets: {
      'الجدول الزمني': {
        headers: [
          'التسلسل',
          'اسم المادة',
          'النوع',
          'وقت البداية',
          'وقت النهاية',
          'المدة',
          'الحالة',
          'التقدم',
          'معلومات السيجمانت',
          'كود السيجمانت',
          'فاصل',
          'فجوة',
          'ملاحظات'
        ],
        data: data.items.map((item: any) => [
          item.sequence,
          item.name,
          item.type,
          item.startTime,
          item.endTime,
          item.duration,
          item.status,
          item.progress,
          item.segmentInfo,
          item.segmentCode,
          item.isBreak,
          item.isGap,
          item.notes
        ])
      },
      'الإحصائيات': {
        headers: ['المؤشر', 'القيمة'],
        data: [
          ['إجمالي العناصر', data.metadata.totalItems],
          ['المحتوى', data.statistics.totalContent],
          ['السيجمانت', data.statistics.totalSegments],
          ['الفواصل', data.statistics.totalBreaks],
          ['الفجوات', data.statistics.totalGaps],
          ['المنتهية', data.statistics.completedItems],
          ['قيد العرض', data.statistics.currentItems],
          ['القادمة', data.statistics.upcomingItems],
          ['إجمالي المدة', data.metadata.totalDuration]
        ]
      }
    },
    formatting: {
      direction: 'rtl',
      headerStyle: {
        backgroundColor: '#4472C4',
        fontColor: '#FFFFFF',
        fontWeight: 'bold'
      },
      dataStyle: {
        alternatingRows: true,
        borderStyle: 'thin'
      }
    }
  };

  return NextResponse.json({
    success: true,
    message: 'تم تحضير ملف Excel بنجاح',
    downloadUrl: `/api/download/excel?file=${excelData.filename}`,
    data: excelData
  });
}

// تصدير إلى CSV
function exportToCSV(data: any, date: string) {
  const headers = [
    'التسلسل',
    'اسم المادة',
    'النوع',
    'وقت البداية',
    'وقت النهاية',
    'المدة',
    'الحالة',
    'التقدم',
    'معلومات السيجمانت',
    'كود السيجمانت',
    'فاصل',
    'فجوة',
    'ملاحظات'
  ];

  const csvContent = [
    headers.join(','),
    ...data.items.map((item: any) => [
      item.sequence,
      `"${item.name}"`,
      `"${item.type}"`,
      item.startTime,
      item.endTime,
      item.duration,
      `"${item.status}"`,
      item.progress,
      `"${item.segmentInfo}"`,
      `"${item.segmentCode}"`,
      `"${item.isBreak}"`,
      `"${item.isGap}"`,
      `"${item.notes}"`
    ].join(','))
  ].join('\n');

  return NextResponse.json({
    success: true,
    message: 'تم تحضير ملف CSV بنجاح',
    filename: `جدول_الإذاعة_${date}.csv`,
    content: csvContent,
    mimeType: 'text/csv;charset=utf-8'
  });
}

// تصدير إلى JSON
function exportToJSON(data: any, date: string) {
  return NextResponse.json({
    success: true,
    message: 'تم تحضير ملف JSON بنجاح',
    filename: `جدول_الإذاعة_${date}.json`,
    content: JSON.stringify(data, null, 2),
    mimeType: 'application/json'
  });
}

// دوال مساعدة
function calculateTotalDuration(timeline: TimelineItem[]): string {
  let totalMinutes = 0;
  
  timeline.forEach(item => {
    const [hours, minutes, seconds] = item.duration.split(':').map(Number);
    totalMinutes += hours * 60 + minutes + seconds / 60;
  });

  const hours = Math.floor(totalMinutes / 60);
  const minutes = Math.floor(totalMinutes % 60);
  
  return `${hours}:${minutes.toString().padStart(2, '0')}:00`;
}

function getTypeLabel(type: string): string {
  const types: { [key: string]: string } = {
    CONTENT: 'محتوى',
    SEGMENT: 'سيجمانت',
    BREAK: 'فاصل',
    GAP: 'فجوة',
    PROGRAM: 'برنامج',
    SERIES: 'مسلسل',
    MOVIE: 'فيلم',
    SONG: 'أغنية',
    STING: 'Sting',
    FILL_IN: 'Fill IN',
    FILLER: 'Filler',
    PROMO: 'Promo'
  };
  return types[type] || type;
}

function getItemNotes(item: TimelineItem): string {
  const notes = [];
  
  if (item.isRerun) notes.push('إعادة تلقائية');
  if (item.canAddContent) notes.push('قابل للتعديل');
  if (item.parentContentId) notes.push(`تابع لـ: ${item.parentContentId}`);
  
  return notes.join(', ');
}
