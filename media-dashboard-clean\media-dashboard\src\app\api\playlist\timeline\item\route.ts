import { NextRequest, NextResponse } from 'next/server';
import { DynamicScheduler } from '@/lib/dynamicScheduler';
import { customTimelines } from '../route';

// PUT - تعديل عنصر في الجدول الزمني
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { itemId, newDuration, date } = body;

    if (!itemId || !newDuration || !date) {
      return NextResponse.json(
        { success: false, error: 'معرف العنصر والمدة الجديدة والتاريخ مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من صحة تنسيق المدة
    const durationRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
    if (!durationRegex.test(newDuration)) {
      return NextResponse.json(
        { success: false, error: 'تنسيق المدة غير صحيح. استخدم HH:MM:SS' },
        { status: 400 }
      );
    }

    // جلب الجدول الحالي
    let timeline = customTimelines[date] || [];
    
    if (timeline.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لا يوجد جدول زمني لهذا التاريخ' },
        { status: 404 }
      );
    }

    // إنشاء محرك الجدولة
    const scheduler = new DynamicScheduler();

    // تحديث مدة العنصر
    const updatedTimeline = scheduler.updateItemDuration(timeline, itemId, newDuration);

    // حفظ الجدول المحدث
    customTimelines[date] = updatedTimeline;

    return NextResponse.json({
      success: true,
      message: 'تم تحديث مدة العنصر بنجاح',
      timeline: updatedTimeline
    });

  } catch (error) {
    console.error('Error updating timeline item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث العنصر' },
      { status: 500 }
    );
  }
}

// DELETE - حذف عنصر من الجدول الزمني
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const itemId = searchParams.get('itemId');
    const date = searchParams.get('date');

    if (!itemId || !date) {
      return NextResponse.json(
        { success: false, error: 'معرف العنصر والتاريخ مطلوبان' },
        { status: 400 }
      );
    }

    // جلب الجدول الحالي
    let timeline = customTimelines[date] || [];
    
    if (timeline.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لا يوجد جدول زمني لهذا التاريخ' },
        { status: 404 }
      );
    }

    // إنشاء محرك الجدولة
    const scheduler = new DynamicScheduler();

    // حذف العنصر
    const updatedTimeline = scheduler.removeTimelineItem(timeline, itemId);

    // حفظ الجدول المحدث
    customTimelines[date] = updatedTimeline;

    return NextResponse.json({
      success: true,
      message: 'تم حذف العنصر بنجاح',
      timeline: updatedTimeline
    });

  } catch (error) {
    console.error('Error deleting timeline item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في حذف العنصر' },
      { status: 500 }
    );
  }
}

// POST - إضافة عنصر جديد للجدول الزمني
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { date, newItem, insertAfterIndex } = body;

    if (!date || !newItem) {
      return NextResponse.json(
        { success: false, error: 'التاريخ والعنصر الجديد مطلوبان' },
        { status: 400 }
      );
    }

    // جلب الجدول الحالي
    let timeline = customTimelines[date] || [];

    // إنشاء محرك الجدولة
    const scheduler = new DynamicScheduler();

    // إضافة العنصر الجديد
    const updatedTimeline = scheduler.insertTimelineItem(
      timeline, 
      newItem, 
      insertAfterIndex || timeline.length - 1
    );

    // حفظ الجدول المحدث
    customTimelines[date] = updatedTimeline;

    return NextResponse.json({
      success: true,
      message: 'تم إضافة العنصر بنجاح',
      timeline: updatedTimeline
    });

  } catch (error) {
    console.error('Error adding timeline item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة العنصر' },
      { status: 500 }
    );
  }
}
