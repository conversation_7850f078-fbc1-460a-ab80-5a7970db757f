"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/weekly-schedule/page",{

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction WeeklySchedulePage() {\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع\n    const days = [\n        'الأحد',\n        'الاثنين',\n        'الثلاثاء',\n        'الأربعاء',\n        'الخميس',\n        'الجمعة',\n        'السبت'\n    ];\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        const startDate = new Date(selectedWeek + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n        const dates = [];\n        console.log('📅 حساب تواريخ الأسبوع من:', selectedWeek);\n        for(let i = 0; i < 7; i++){\n            const date = new Date(startDate);\n            date.setDate(startDate.getDate() + i);\n            // استخدام الأرقام العربية العادية (1234567890)\n            const dateStr = date.toLocaleDateString('en-US', {\n                day: '2-digit',\n                month: '2-digit'\n            });\n            dates.push(dateStr);\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(date.toISOString().split('T')[0], \" → \").concat(dateStr));\n        }\n        return dates;\n    };\n    const weekDates = getWeekDates();\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || 'غير معروف';\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - الموسم \".concat(item.seasonNumber, \" الحلقة \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - الحلقة \".concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - الجزء \".concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم:', today.toISOString().split('T')[0]);\n            console.log('  📊 يوم الأسبوع:', today.getDay(), '(0=أحد)');\n            const sunday = new Date(today);\n            sunday.setDate(today.getDate() - today.getDay());\n            const weekStart = sunday.toISOString().split('T')[0];\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current > 0) {\n                window.scrollTo(0, scrollPositionRef.current);\n                shouldRestoreScroll.current = false;\n                console.log('📍 تم استعادة موضع التمرير:', scrollPositionRef.current);\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"خطأ في تحديث البيانات: \".concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"خطأ في الاتصال: \".concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert('يرجى إدخال اسم المادة');\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حفظ المادة المؤقتة');\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert('خطأ في حفظ المادة المؤقتة');\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm('هل تريد حذف هذه المادة المؤقتة؟')) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حذف المادة المؤقتة');\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert('خطأ في حذف المادة المؤقتة');\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ يوجد تداخل في الأوقات! اختر وقت آخر.');\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                await fetchScheduleData();\n            } else {\n                alert(result.error);\n                shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'المادة';\n        const itemType = item.isRerun ? 'إعادة' : item.isTemporary ? 'مادة مؤقتة' : 'مادة أصلية';\n        const confirmed = window.confirm(\"هل أنت متأكد من حذف \".concat(itemType, ': \"').concat(itemName, '\"؟\\n\\n') + \"الوقت: \".concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + (item.isRerun ? 'تحذير: حذف الإعادة لن يؤثر على المادة الأصلية' : item.isTemporary ? 'سيتم حذف المادة المؤقتة من الجدول' : 'تحذير: حذف المادة الأصلية سيحذف جميع إعاداتها'));\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف محلي\n            if (item.isTemporary) {\n                if (item.isRerun) {\n                    // حذف إعادة مؤقتة فقط\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                    console.log(\"✅ تم حذف إعادة مؤقتة: \".concat(itemName));\n                } else {\n                    // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                    setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isTemporary && scheduleItem.originalId === item.id)));\n                    console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها: \".concat(itemName));\n                }\n                return;\n            }\n            // حفظ موضع التمرير الحالي\n            scrollPositionRef.current = window.scrollY;\n            shouldRestoreScroll.current = true;\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        const currentDate = new Date(selectedWeek);\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        setSelectedWeek(currentDate.toISOString().split('T')[0]);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"⏳ جاري تحميل الجدول الأسبوعي...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 699,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 698,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: \"\\uD83D\\uDCC5 جاري تحديد التاريخ...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 708,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 707,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: 'flex',\n            height: '100vh',\n            fontFamily: 'Arial, sans-serif',\n            direction: 'rtl',\n            background: '#1a1d29'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    width: '300px',\n                    background: '#f8f9fa',\n                    borderLeft: '2px solid #dee2e6',\n                    padding: '20px',\n                    overflowY: 'auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            margin: '0 0 15px 0',\n                            color: '#333'\n                        },\n                        children: \"\\uD83D\\uDCDA قائمة المواد\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#fff3e0',\n                            border: '2px solid #ff9800',\n                            borderRadius: '8px',\n                            padding: '12px',\n                            marginBottom: '15px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    margin: '0 0 10px 0',\n                                    color: '#e65100',\n                                    fontSize: '0.9rem'\n                                },\n                                children: \"⚡ إضافة مادة مؤقتة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"اسم المادة...\",\n                                value: tempMediaName,\n                                onChange: (e)=>setTempMediaName(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    border: '1px solid #ddd',\n                                    borderRadius: '4px',\n                                    marginBottom: '8px',\n                                    fontSize: '13px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: tempMediaType,\n                                onChange: (e)=>setTempMediaType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    border: '1px solid #ddd',\n                                    borderRadius: '4px',\n                                    marginBottom: '8px',\n                                    fontSize: '13px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: \"\\uD83D\\uDCFB برنامج\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: \"\\uD83D\\uDCFA مسلسل\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 771,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MOVIE\",\n                                        children: \"\\uD83C\\uDFA5 فيلم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"LIVE\",\n                                        children: \"\\uD83D\\uDD34 برنامج هواء مباشر\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PENDING\",\n                                        children: \"\\uD83D\\uDFE1 مادة قيد التسليم\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 758,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"المدة (مثل: 01:30:00)\",\n                                value: tempMediaDuration,\n                                onChange: (e)=>setTempMediaDuration(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    border: '1px solid #ddd',\n                                    borderRadius: '4px',\n                                    marginBottom: '8px',\n                                    fontSize: '13px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 777,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"ملاحظات (اختياري)...\",\n                                value: tempMediaNotes,\n                                onChange: (e)=>setTempMediaNotes(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    border: '1px solid #ddd',\n                                    borderRadius: '4px',\n                                    marginBottom: '10px',\n                                    fontSize: '13px'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: addTempMedia,\n                                style: {\n                                    width: '100%',\n                                    padding: '8px',\n                                    background: '#ff9800',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '4px',\n                                    fontSize: '13px',\n                                    fontWeight: 'bold',\n                                    cursor: 'pointer',\n                                    marginBottom: '8px'\n                                },\n                                children: \"➕ إضافة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 807,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: async ()=>{\n                                    console.log('🔄 تحديث الإعادات...');\n                                    scrollPositionRef.current = window.scrollY;\n                                    shouldRestoreScroll.current = true;\n                                    await fetchScheduleData();\n                                },\n                                style: {\n                                    width: '100%',\n                                    padding: '6px',\n                                    background: '#4caf50',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '4px',\n                                    fontSize: '12px',\n                                    cursor: 'pointer'\n                                },\n                                children: \"♻️ تحديث الإعادات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 825,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 732,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: selectedType,\n                        onChange: (e)=>setSelectedType(e.target.value),\n                        style: {\n                            width: '100%',\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '5px',\n                            marginBottom: '10px',\n                            fontSize: '14px',\n                            backgroundColor: '#f8f9fa'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"\\uD83C\\uDFAC جميع الأنواع\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 861,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"SERIES\",\n                                children: \"\\uD83D\\uDCFA مسلسل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 862,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"MOVIE\",\n                                children: \"\\uD83C\\uDFA5 فيلم\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"PROGRAM\",\n                                children: \"\\uD83D\\uDCFB برنامج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 864,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"PROMO\",\n                                children: \"\\uD83D\\uDCE2 إعلان\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 865,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"STING\",\n                                children: \"⚡ ستينج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 866,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"FILL_IN\",\n                                children: \"\\uD83D\\uDD04 فيل إن\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 867,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"FILLER\",\n                                children: \"⏸️ فيلر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 868,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 848,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"\\uD83D\\uDD0D البحث في المواد...\",\n                        value: searchTerm,\n                        onChange: (e)=>setSearchTerm(e.target.value),\n                        style: {\n                            width: '100%',\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '5px',\n                            marginBottom: '15px',\n                            fontSize: '14px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 872,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '12px',\n                            color: '#666',\n                            marginBottom: '10px',\n                            textAlign: 'center',\n                            padding: '5px',\n                            background: '#e9ecef',\n                            borderRadius: '4px'\n                        },\n                        children: [\n                            \"\\uD83D\\uDCCA \",\n                            filteredMedia.length,\n                            \" من \",\n                            allAvailableMedia.length,\n                            \" مادة\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 888,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '8px'\n                        },\n                        children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center',\n                                padding: '20px',\n                                color: '#666',\n                                background: '#f8f9fa',\n                                borderRadius: '8px',\n                                border: '2px dashed #dee2e6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 911,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontWeight: 'bold',\n                                        marginBottom: '5px'\n                                    },\n                                    children: \"لا توجد مواد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 912,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '12px'\n                                    },\n                                    children: searchTerm || selectedType ? 'جرب تغيير الفلتر أو البحث' : 'أضف مواد جديدة من صفحة المستخدم'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 913,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                            lineNumber: 903,\n                            columnNumber: 13\n                        }, this) : filteredMedia.map((item)=>{\n                            // تحديد لون المادة حسب النوع\n                            const getItemStyle = ()=>{\n                                if (item.isTemporary) {\n                                    switch(item.type){\n                                        case 'LIVE':\n                                            return {\n                                                background: '#ffebee',\n                                                border: '2px solid #f44336',\n                                                borderLeft: '5px solid #f44336'\n                                            };\n                                        case 'PENDING':\n                                            return {\n                                                background: '#fff8e1',\n                                                border: '2px solid #ffc107',\n                                                borderLeft: '5px solid #ffc107'\n                                            };\n                                        default:\n                                            return {\n                                                background: '#f3e5f5',\n                                                border: '2px solid #9c27b0',\n                                                borderLeft: '5px solid #9c27b0'\n                                            };\n                                    }\n                                }\n                                return {\n                                    background: '#fff',\n                                    border: '1px solid #ddd'\n                                };\n                            };\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                draggable: true,\n                                onDragStart: (e)=>handleDragStart(e, item),\n                                style: {\n                                    ...getItemStyle(),\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    cursor: 'grab',\n                                    transition: 'all 0.2s',\n                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                    position: 'relative'\n                                },\n                                onMouseEnter: (e)=>{\n                                    e.currentTarget.style.transform = 'translateY(-2px)';\n                                    e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                },\n                                onMouseLeave: (e)=>{\n                                    e.currentTarget.style.transform = 'translateY(0)';\n                                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                },\n                                children: [\n                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            deleteTempMedia(item.id);\n                                        },\n                                        style: {\n                                            position: 'absolute',\n                                            top: '5px',\n                                            left: '5px',\n                                            background: '#f44336',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '50%',\n                                            width: '20px',\n                                            height: '20px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center'\n                                        },\n                                        title: \"حذف المادة المؤقتة\",\n                                        children: \"\\xd7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 974,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontWeight: 'bold',\n                                            color: '#333',\n                                            marginBottom: '4px'\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '10px',\n                                                    background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                    color: 'white',\n                                                    padding: '2px 6px',\n                                                    borderRadius: '10px',\n                                                    marginLeft: '5px'\n                                                },\n                                                children: item.type === 'LIVE' ? '🔴 هواء' : item.type === 'PENDING' ? '🟡 قيد التسليم' : '🟣 مؤقت'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1003,\n                                                columnNumber: 23\n                                            }, this),\n                                            getMediaDisplayText(item)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '12px',\n                                            color: '#666'\n                                        },\n                                        children: [\n                                            item.type,\n                                            \" • \",\n                                            item.duration\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1018,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '11px',\n                                            color: '#888',\n                                            marginTop: '4px'\n                                        },\n                                        children: item.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1022,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, item.id, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 950,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 901,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 722,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    flex: 1,\n                    padding: '20px',\n                    overflowY: 'auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'space-between',\n                            alignItems: 'center',\n                            marginBottom: '20px',\n                            background: '#fff',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = '/daily-schedule',\n                                        style: {\n                                            background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            padding: '10px 20px',\n                                            fontSize: '0.9rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                            transition: 'transform 0.2s ease',\n                                            marginLeft: '10px'\n                                        },\n                                        onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                        onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                        children: \"\\uD83D\\uDCCB الجدول الإذاعي\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1046,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            try {\n                                                console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                if (!response.ok) {\n                                                    throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                }\n                                                const blob = await response.blob();\n                                                const url = window.URL.createObjectURL(blob);\n                                                const a = document.createElement('a');\n                                                a.href = url;\n                                                a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                document.body.appendChild(a);\n                                                a.click();\n                                                window.URL.revokeObjectURL(url);\n                                                document.body.removeChild(a);\n                                                console.log('✅ تم تصدير الخريطة بنجاح');\n                                            } catch (error) {\n                                                console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                alert('فشل في تصدير الخريطة: ' + error.message);\n                                            }\n                                        },\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            padding: '10px 20px',\n                                            fontSize: '0.9rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                            transition: 'transform 0.2s ease',\n                                            marginLeft: '10px'\n                                        },\n                                        onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                        onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                        children: \"\\uD83D\\uDCCA تصدير الخريطة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1071,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.href = '/',\n                                        style: {\n                                            background: 'linear-gradient(45deg, #e74c3c, #c0392b)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '8px',\n                                            padding: '10px 20px',\n                                            fontSize: '0.9rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                            transition: 'transform 0.2s ease',\n                                            marginLeft: '10px'\n                                        },\n                                        onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                        onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1116,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        style: {\n                                            margin: 0,\n                                            color: '#333'\n                                        },\n                                        children: \"\\uD83D\\uDCC5 الخريطة البرامجية الأسبوعية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1045,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>changeWeek(-1),\n                                        style: {\n                                            padding: '8px 15px',\n                                            background: '#007bff',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"← الأسبوع السابق\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        value: selectedWeek,\n                                        onChange: (e)=>{\n                                            const selectedDate = new Date(e.target.value + 'T12:00:00');\n                                            const dayOfWeek = selectedDate.getDay();\n                                            console.log('📅 تغيير التاريخ من التقويم:', {\n                                                selectedDate: e.target.value,\n                                                dayOfWeek: dayOfWeek,\n                                                dayName: [\n                                                    'الأحد',\n                                                    'الاثنين',\n                                                    'الثلاثاء',\n                                                    'الأربعاء',\n                                                    'الخميس',\n                                                    'الجمعة',\n                                                    'السبت'\n                                                ][dayOfWeek]\n                                            });\n                                            // حساب بداية الأسبوع (الأحد)\n                                            const sunday = new Date(selectedDate);\n                                            sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                            const weekStart = sunday.toISOString().split('T')[0];\n                                            console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                            setSelectedWeek(weekStart);\n                                        },\n                                        style: {\n                                            padding: '8px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>changeWeek(1),\n                                        style: {\n                                            padding: '8px 15px',\n                                            background: '#007bff',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"الأسبوع التالي →\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1186,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1035,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',\n                            borderRadius: '10px',\n                            overflow: 'hidden',\n                            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                            minHeight: '70vh'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            style: {\n                                width: '100%',\n                                borderCollapse: 'collapse'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        style: {\n                                            background: '#f8f9fa'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                style: {\n                                                    padding: '12px',\n                                                    border: '1px solid #dee2e6',\n                                                    fontWeight: 'bold',\n                                                    minWidth: '80px'\n                                                },\n                                                children: \"الوقت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1214,\n                                                columnNumber: 17\n                                            }, this),\n                                            days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '12px',\n                                                        border: '1px solid #dee2e6',\n                                                        fontWeight: 'bold',\n                                                        minWidth: '120px',\n                                                        textAlign: 'center'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '1rem',\n                                                                marginBottom: '4px'\n                                                            },\n                                                            children: day\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1230,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                color: '#666',\n                                                                fontWeight: 'normal'\n                                                            },\n                                                            children: weekDates[index]\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1223,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1213,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    style: {\n                                                        background: '#f8f9fa',\n                                                        fontWeight: 'bold',\n                                                        textAlign: 'center',\n                                                        padding: '8px',\n                                                        border: '1px solid #dee2e6'\n                                                    },\n                                                    children: hour\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                days.map((_, dayIndex)=>{\n                                                    const cellItems = getItemsForCell(dayIndex, hour);\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        onDragOver: handleDragOver,\n                                                        onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                        style: {\n                                                            border: '1px solid #dee2e6',\n                                                            padding: '8px',\n                                                            minHeight: '80px',\n                                                            cursor: 'pointer',\n                                                            background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',\n                                                            verticalAlign: 'top'\n                                                        },\n                                                        children: cellItems.map((item)=>{\n                                                            var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                draggable: true,\n                                                                onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                onClick: ()=>deleteItem(item),\n                                                                style: {\n                                                                    background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                    border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                    borderRadius: '4px',\n                                                                    padding: '2px 4px',\n                                                                    marginBottom: '2px',\n                                                                    fontSize: '0.7rem',\n                                                                    cursor: 'grab',\n                                                                    transition: 'all 0.2s ease'\n                                                                },\n                                                                onMouseEnter: (e)=>{\n                                                                    e.currentTarget.style.transform = 'scale(1.02)';\n                                                                    e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                },\n                                                                onMouseLeave: (e)=>{\n                                                                    e.currentTarget.style.transform = 'scale(1)';\n                                                                    e.currentTarget.style.boxShadow = 'none';\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontWeight: 'bold',\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            gap: '4px'\n                                                                        },\n                                                                        children: [\n                                                                            item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: '#666'\n                                                                                },\n                                                                                children: [\n                                                                                    \"♻️ \",\n                                                                                    item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                    item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1306,\n                                                                                columnNumber: 33\n                                                                            }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                },\n                                                                                children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1310,\n                                                                                columnNumber: 33\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                style: {\n                                                                                    color: '#ff9800'\n                                                                                },\n                                                                                children: \"\\uD83C\\uDF1F\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1318,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            item.mediaItem ? getMediaDisplayText(item.mediaItem) : 'غير معروف'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1304,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: '0.6rem',\n                                                                            color: '#666'\n                                                                        },\n                                                                        children: [\n                                                                            item.startTime,\n                                                                            \" - \",\n                                                                            item.endTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1322,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            fontSize: '0.5rem',\n                                                                            color: '#888',\n                                                                            fontStyle: 'italic'\n                                                                        },\n                                                                        children: \"إعادة - يمكن الحذف للتعديل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1326,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, item.id, true, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1270,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, dayIndex, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1256,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            ]\n                                        }, hourIndex, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1242,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                            lineNumber: 1210,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#e8f5e8',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginTop: '20px',\n                            border: '2px solid #4caf50'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    color: '#2e7d32',\n                                    margin: '0 0 15px 0'\n                                },\n                                children: \"\\uD83D\\uDCCB تعليمات الاستخدام:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1349,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: '1fr 1fr',\n                                    gap: '15px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83C\\uDFAF إضافة المواد:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1353,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    margin: '5px 0',\n                                                    paddingRight: '20px',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"اسحب المواد من القائمة اليمنى إلى الجدول\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1355,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDD04 اسحب المواد من الجدول نفسه لنسخها لمواعيد أخرى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1356,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83C\\uDFAC استخدم فلتر النوع للتصفية حسب نوع المادة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1357,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"\\uD83D\\uDD0D استخدم البحث للعثور على المواد بسرعة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1358,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1354,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1352,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83D\\uDDD1️ حذف المواد:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1362,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    margin: '5px 0',\n                                                    paddingRight: '20px',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"المواد الأصلية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1364,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" حذف نهائي مع جميع إعاداتها\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"الإعادات:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" حذف مع ترك الحقل فارغ للتعديل\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1365,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"سيظهر تأكيد قبل الحذف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1366,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1363,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1361,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1351,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: '1fr 1fr',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83C\\uDF1F المواد الأصلية (البرايم تايم):\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1373,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    margin: '5px 0',\n                                                    paddingRight: '20px',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"الأحد-الأربعاء: 18:00-00:00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1375,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"الخميس-السبت: 18:00-02:00\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1376,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"لون ذهبي في الجدول\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1377,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1374,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1372,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"♻️ الإعادات التلقائية (جزئين):\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1381,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                style: {\n                                                    margin: '5px 0',\n                                                    paddingRight: '20px',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الأحد-الأربعاء:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1383,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1383,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '2px 0',\n                                                            paddingRight: '15px',\n                                                            fontSize: '0.8rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"ج1: نفس العمود 00:00-07:59\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1385,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"ج2: العمود التالي 08:00-17:59\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1386,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1384,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الخميس-السبت:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1388,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1388,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '2px 0',\n                                                            paddingRight: '15px',\n                                                            fontSize: '0.8rem'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"ج1: نفس العمود 02:00-07:59\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1390,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: \"ج2: العمود التالي 08:00-17:59\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1391,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1389,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"لون رمادي - يمكن حذفها للتعديل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1393,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1382,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1380,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1371,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '10px',\n                                    padding: '10px',\n                                    background: '#fff3e0',\n                                    borderRadius: '8px',\n                                    border: '1px solid #ff9800'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCC5 إدارة التواريخ:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1399,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" استخدم الكالندر والأزرار للتنقل بين الأسابيع • كل أسبوع يُحفظ بشكل منفصل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1398,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '10px',\n                                    padding: '10px',\n                                    background: '#e3f2fd',\n                                    borderRadius: '8px',\n                                    border: '1px solid #2196f3'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCA1 ملاحظة مهمة:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1403,\n                                        columnNumber: 13\n                                    }, this),\n                                    \" عند إضافة مواد قليلة (١-٣ مواد) في البرايم، ستظهر مع فواصل زمنية في الإعادات لتجنب التكرار المفرط. أضف المزيد من المواد للحصول على تنوع أكبر.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1402,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1342,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 1033,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    bottom: '20px',\n                    left: '20px',\n                    color: '#6c757d',\n                    fontSize: '0.75rem',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: 'ltr'\n                },\n                children: \"Powered By Mahmoud Ismail\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 1409,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 714,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"v2z1JHXlWVqbyv5DN3U+nYUgV3s=\");\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ })

});