// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// نموذج المادة الإعلامية
model MediaItem {
  id          String   @id @default(cuid())
  name        String   // اسم المادة
  type        MediaType // نوع المادة
  description String?  // وصف
  channel     Channel  // القناة
  source      String?  // المصدر
  status      Status   // الحالة
  startDate   DateTime @default(now()) // تاريخ بداية
  endDate     DateTime? // تاريخ انتهاء
  notes       String?  // ملاحظات
  
  // حقول خاصة بالنوع
  episodeNumber Int?    // رقم الحلقة (للمسلسل والبرنامج)
  seasonNumber  Int?    // رقم الموسم (للبرنامج)
  partNumber    Int?    // رقم الجزء (للمسلسل والفيلم)
  
  // العلاقات
  segments      Segment[]
  scheduleItems ScheduleItem[]
  playlistItems PlaylistItem[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// نموذج السيجمانت
model Segment {
  id          String   @id @default(cuid())
  mediaItemId String
  mediaItem   MediaItem @relation(fields: [mediaItemId], references: [id], onDelete: Cascade)
  
  segmentNumber Int      // رقم السيجمانت (1-10)
  timeIn        String   // وقت البداية (00:00:00)
  timeOut       String   // وقت النهاية
  duration      String   // المدة (محسوبة تلقائياً)
  code          String?  // كود السيجمانت
  
  // العلاقات
  playlistItems PlaylistItem[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([mediaItemId, segmentNumber])
}

// نموذج الخريطة البرامجية
model ScheduleItem {
  id          String   @id @default(cuid())
  mediaItemId String
  mediaItem   MediaItem @relation(fields: [mediaItemId], references: [id], onDelete: Cascade)
  
  dayOfWeek   Int      // يوم الأسبوع (0-6)
  startTime   String   // وقت البداية
  endTime     String   // وقت النهاية
  order       Int      // ترتيب العرض
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  @@unique([dayOfWeek, startTime])
}

// نموذج جدول الإذاعة اليومية
model PlaylistItem {
  id          String   @id @default(cuid())
  date        DateTime // تاريخ الإذاعة

  mediaItemId String?
  mediaItem   MediaItem? @relation(fields: [mediaItemId], references: [id], onDelete: Cascade)

  segmentId   String?
  segment     Segment? @relation(fields: [segmentId], references: [id], onDelete: Cascade)

  // بيانات العنصر
  name        String   // اسم المادة
  type        String   // نوع المادة
  startTime   String   // وقت البداية
  endTime     String   // وقت النهاية
  duration    String   // المدة
  code        String?  // كود المادة
  status      String   @default("قادم") // الحالة (قادم، يُعرض الآن، منتهي)

  order       Int      // ترتيب العرض
  isBreak     Boolean  @default(false) // هل هو فاصل إعلاني
  breakType   String?  // نوع الفاصل (إعلان، برومو، إلخ)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([date, startTime])
}

// الأنواع المختلفة
enum MediaType {
  PROGRAM     // برنامج
  SERIES      // مسلسل
  MOVIE       // فيلم
  SONG        // أغنية
  STING       // Sting
  FILL_IN     // Fill IN
  FILLER      // Filler
  PROMO       // Promo
}

enum Channel {
  DOCUMENTARY // الوثائقية
  NEWS        // الأخبار
  OTHER       // أخرى
}

enum Status {
  VALID       // صالح
  REJECTED_CENSORSHIP // مرفوض رقابي
  REJECTED_TECHNICAL  // مرفوض هندسي
  WAITING     // Waiting
}
