// محرك الجدولة الديناميكي المتقدم
// Dynamic Scheduler Engine

export interface MediaItem {
  id: string;
  name: string;
  type: string;
  segments: Segment[];
  duration?: string;
}

export interface Segment {
  id: string;
  segmentNumber: number;
  timeIn: string;
  timeOut: string;
  duration: string;
  code: string;
}

export interface ScheduleItem {
  id: string;
  mediaItemId: string;
  mediaItem?: MediaItem;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  order: number;
  isRerun?: boolean;
}

export interface TimelineItem {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  duration: string;
  type: 'CONTENT' | 'SEGMENT' | 'BREAK' | 'GAP';
  status: 'منتهي' | 'يُعرض الآن' | 'قادم';
  progress: number;
  
  // للمحتوى
  mediaItem?: MediaItem;
  scheduleId?: string;
  
  // للسيجمانت
  segment?: Segment;
  segmentNumber?: number;
  totalSegments?: number;
  parentContentId?: string;
  
  // للفواصل
  isBreak?: boolean;
  isGap?: boolean;
  canAddContent?: boolean;
  breakItems?: BreakItem[];
  
  // للعرض
  backgroundColor?: string;
  textColor?: string;
  isEditable?: boolean;
}

export interface BreakItem {
  id: string;
  mediaItemId: string;
  mediaItem?: MediaItem;
  duration: string;
  order: number;
}

export interface DynamicScheduleConfig {
  dayStartTime: string; // '08:00'
  dayEndTime: string; // '08:00' (next day)
  defaultBreakDuration: string; // '00:00:30'
  postContentBreakDuration: string; // '00:01:00'
  autoBreakRules: {
    betweenSegments: boolean;
    afterContent: boolean;
    beforeContent: boolean;
  };
}

export class DynamicScheduler {
  private config: DynamicScheduleConfig;
  
  constructor(config?: Partial<DynamicScheduleConfig>) {
    this.config = {
      dayStartTime: '08:00',
      dayEndTime: '08:00',
      defaultBreakDuration: '00:00:30',
      postContentBreakDuration: '00:01:00',
      autoBreakRules: {
        betweenSegments: true,
        afterContent: true,
        beforeContent: false
      },
      ...config
    };
  }

  // بناء الجدول الديناميكي من الخريطة البرامجية
  buildDynamicTimeline(
    scheduleItems: ScheduleItem[],
    date: string,
    existingBreaks: { [breakId: string]: BreakItem[] } = {}
  ): TimelineItem[] {
    const timeline: TimelineItem[] = [];
    let currentTime = this.config.dayStartTime;
    
    // ترتيب المواد حسب الوقت
    const sortedItems = scheduleItems
      .filter(item => !item.isRerun) // استبعاد الإعادات من الجدول اليومي
      .sort((a, b) => this.timeToMinutes(a.startTime) - this.timeToMinutes(b.startTime));

    for (const scheduleItem of sortedItems) {
      if (!scheduleItem.mediaItem) continue;

      // إضافة فجوة إذا كانت هناك مساحة قبل المادة
      if (this.timeToMinutes(currentTime) < this.timeToMinutes(scheduleItem.startTime)) {
        const gapItem = this.createGapItem(currentTime, scheduleItem.startTime);
        timeline.push(gapItem);
        currentTime = scheduleItem.startTime;
      }

      // معالجة المادة والسيجمانت
      const contentItems = this.processContentWithSegments(
        scheduleItem,
        currentTime,
        existingBreaks
      );
      
      timeline.push(...contentItems);
      
      // تحديث الوقت الحالي
      const lastItem = contentItems[contentItems.length - 1];
      currentTime = lastItem.endTime;
    }

    // إضافة فجوة نهائية إذا لزم الأمر
    const dayEndMinutes = this.timeToMinutes(this.config.dayEndTime) + (24 * 60); // اليوم التالي
    if (this.timeToMinutes(currentTime) < dayEndMinutes) {
      const finalGap = this.createGapItem(currentTime, this.config.dayEndTime);
      timeline.push(finalGap);
    }

    // حساب حالات العرض
    return this.calculateTimelineStatus(timeline);
  }

  // معالجة المحتوى مع السيجمانت والفواصل
  private processContentWithSegments(
    scheduleItem: ScheduleItem,
    startTime: string,
    existingBreaks: { [breakId: string]: BreakItem[] }
  ): TimelineItem[] {
    const items: TimelineItem[] = [];
    const mediaItem = scheduleItem.mediaItem!;
    let currentTime = startTime;

    if (mediaItem.segments && mediaItem.segments.length > 0) {
      // معالجة السيجمانت
      for (let i = 0; i < mediaItem.segments.length; i++) {
        const segment = mediaItem.segments[i];
        
        // إضافة السيجمانت
        const segmentEndTime = this.addTimeToTime(currentTime, segment.duration);
        const segmentItem: TimelineItem = {
          id: `${scheduleItem.id}_segment_${i + 1}`,
          name: `${mediaItem.name} - سيجمانت ${i + 1}`,
          startTime: currentTime,
          endTime: segmentEndTime,
          duration: segment.duration,
          type: 'SEGMENT',
          status: 'قادم',
          progress: 0,
          mediaItem,
          segment,
          segmentNumber: i + 1,
          totalSegments: mediaItem.segments.length,
          parentContentId: scheduleItem.id,
          scheduleId: scheduleItem.id,
          backgroundColor: '#e3f2fd',
          textColor: '#1565c0',
          isEditable: true
        };
        
        items.push(segmentItem);
        currentTime = segmentEndTime;

        // إضافة فاصل بين السيجمانت (إلا بعد الأخير)
        if (i < mediaItem.segments.length - 1 && this.config.autoBreakRules.betweenSegments) {
          const breakEndTime = this.addTimeToTime(currentTime, this.config.defaultBreakDuration);
          const breakId = `break_${scheduleItem.id}_${i + 1}`;
          
          const breakItem: TimelineItem = {
            id: breakId,
            name: 'فاصل بين السيجمانت',
            startTime: currentTime,
            endTime: breakEndTime,
            duration: this.config.defaultBreakDuration,
            type: 'BREAK',
            status: 'قادم',
            progress: 0,
            isBreak: true,
            canAddContent: true,
            breakItems: existingBreaks[breakId] || [],
            parentContentId: scheduleItem.id,
            backgroundColor: '#fff3cd',
            textColor: '#856404',
            isEditable: true
          };
          
          items.push(breakItem);
          currentTime = breakEndTime;
        }
      }
    } else {
      // مادة بدون سيجمانت
      const contentDuration = this.calculateDuration(scheduleItem.startTime, scheduleItem.endTime);
      const contentEndTime = this.addTimeToTime(currentTime, contentDuration);
      
      const contentItem: TimelineItem = {
        id: scheduleItem.id,
        name: mediaItem.name,
        startTime: currentTime,
        endTime: contentEndTime,
        duration: contentDuration,
        type: 'CONTENT',
        status: 'قادم',
        progress: 0,
        mediaItem,
        scheduleId: scheduleItem.id,
        backgroundColor: '#e8f5e8',
        textColor: '#2e7d32',
        isEditable: true
      };
      
      items.push(contentItem);
      currentTime = contentEndTime;
    }

    // إضافة فاصل بعد المحتوى
    if (this.config.autoBreakRules.afterContent) {
      const postBreakEndTime = this.addTimeToTime(currentTime, this.config.postContentBreakDuration);
      const postBreakId = `post_break_${scheduleItem.id}`;
      
      const postBreakItem: TimelineItem = {
        id: postBreakId,
        name: 'فاصل بعد المادة',
        startTime: currentTime,
        endTime: postBreakEndTime,
        duration: this.config.postContentBreakDuration,
        type: 'BREAK',
        status: 'قادم',
        progress: 0,
        isBreak: true,
        canAddContent: true,
        breakItems: existingBreaks[postBreakId] || [],
        parentContentId: scheduleItem.id,
        backgroundColor: '#fff3cd',
        textColor: '#856404',
        isEditable: true
      };
      
      items.push(postBreakItem);
    }

    return items;
  }

  // إنشاء عنصر فجوة
  private createGapItem(startTime: string, endTime: string): TimelineItem {
    return {
      id: `gap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: 'فترة فراغ',
      startTime,
      endTime,
      duration: this.calculateDuration(startTime, endTime),
      type: 'GAP',
      status: 'قادم',
      progress: 0,
      isGap: true,
      canAddContent: true,
      backgroundColor: '#f8f9fa',
      textColor: '#6c757d',
      isEditable: true
    };
  }

  // حساب حالات العرض والتقدم
  private calculateTimelineStatus(timeline: TimelineItem[]): TimelineItem[] {
    const currentTime = new Date();
    const currentTimeString = currentTime.toTimeString().slice(0, 5);

    return timeline.map(item => ({
      ...item,
      status: this.getTimeStatus(item.startTime, item.endTime, currentTimeString),
      progress: this.getTimeProgress(item.startTime, item.endTime, currentTimeString)
    }));
  }

  // دوال مساعدة للوقت
  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private addTimeToTime(baseTime: string, addTime: string): string {
    const baseMinutes = this.timeToMinutes(baseTime);
    const addMinutes = this.timeToMinutes(addTime);
    const totalMinutes = baseMinutes + addMinutes;
    
    const hours = Math.floor(totalMinutes / 60) % 24;
    const minutes = totalMinutes % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }

  private calculateDuration(startTime: string, endTime: string): string {
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    const durationMinutes = endMinutes - startMinutes;
    
    if (durationMinutes <= 0) return '00:00:00';
    
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
  }

  private getTimeStatus(startTime: string, endTime: string, currentTime: string): 'منتهي' | 'يُعرض الآن' | 'قادم' {
    const currentMinutes = this.timeToMinutes(currentTime);
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    
    if (currentMinutes < startMinutes) {
      return 'قادم';
    } else if (currentMinutes >= startMinutes && currentMinutes <= endMinutes) {
      return 'يُعرض الآن';
    } else {
      return 'منتهي';
    }
  }

  private getTimeProgress(startTime: string, endTime: string, currentTime: string): number {
    const currentMinutes = this.timeToMinutes(currentTime);
    const startMinutes = this.timeToMinutes(startTime);
    const endMinutes = this.timeToMinutes(endTime);
    
    if (currentMinutes < startMinutes) {
      return 0;
    } else if (currentMinutes > endMinutes) {
      return 100;
    } else {
      const progress = ((currentMinutes - startMinutes) / (endMinutes - startMinutes)) * 100;
      return Math.round(Math.max(0, Math.min(100, progress)));
    }
  }

  // إعادة حساب الجدول بعد التعديلات
  recalculateTimeline(timeline: TimelineItem[]): TimelineItem[] {
    let currentTime = this.config.dayStartTime;
    
    const updatedTimeline = timeline.map(item => {
      const updatedItem = {
        ...item,
        startTime: currentTime,
        endTime: this.addTimeToTime(currentTime, item.duration)
      };
      
      currentTime = updatedItem.endTime;
      return updatedItem;
    });

    return this.calculateTimelineStatus(updatedTimeline);
  }

  // إدراج عنصر جديد في الجدول
  insertTimelineItem(timeline: TimelineItem[], newItem: Partial<TimelineItem>, insertAfterIndex: number): TimelineItem[] {
    const completeNewItem: TimelineItem = {
      id: newItem.id || `item_${Date.now()}`,
      name: newItem.name || 'عنصر جديد',
      startTime: '00:00',
      endTime: '00:00',
      duration: newItem.duration || '00:01:00',
      type: newItem.type || 'BREAK',
      status: 'قادم',
      progress: 0,
      backgroundColor: newItem.backgroundColor || '#fff3cd',
      textColor: newItem.textColor || '#856404',
      isEditable: true,
      ...newItem
    };

    const newTimeline = [...timeline];
    newTimeline.splice(insertAfterIndex + 1, 0, completeNewItem);
    
    return this.recalculateTimeline(newTimeline);
  }

  // حذف عنصر من الجدول
  removeTimelineItem(timeline: TimelineItem[], itemId: string): TimelineItem[] {
    const newTimeline = timeline.filter(item => item.id !== itemId);
    return this.recalculateTimeline(newTimeline);
  }

  // تحديث مدة عنصر
  updateItemDuration(timeline: TimelineItem[], itemId: string, newDuration: string): TimelineItem[] {
    const newTimeline = timeline.map(item => 
      item.id === itemId ? { ...item, duration: newDuration } : item
    );
    
    return this.recalculateTimeline(newTimeline);
  }
}
