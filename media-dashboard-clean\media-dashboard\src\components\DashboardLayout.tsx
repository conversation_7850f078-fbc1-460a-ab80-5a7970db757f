'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard, useAuth } from './AuthGuard';
import Sidebar from './Sidebar';

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  icon?: string;
  requiredPermissions?: string[];
  requiredRole?: string;
  fullWidth?: boolean;
}

export default function DashboardLayout({
  children,
  title = 'لوحة التحكم',
  subtitle = 'إدارة النظام',
  icon = '📊',
  requiredPermissions,
  requiredRole,
  fullWidth = false
}: DashboardLayoutProps) {
  const router = useRouter();
  const { user, logout, hasPermission } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const navigationItems = [
    { name: 'لوحة التحكم', icon: '📊', active: false, path: '/dashboard' },
    { name: 'المواد الإعلامية', icon: '🎬', active: false, path: '/media-list', permission: 'MEDIA_READ' },
    { name: 'إضافة مادة', icon: '➕', active: false, path: '/add-media', permission: 'MEDIA_CREATE' },
    { name: 'الخريطة البرامجية', icon: '📅', active: false, path: '/weekly-schedule', permission: 'SCHEDULE_READ' },
    { name: 'جدول الإذاعة اليومي', icon: '📊', active: false, path: '/daily-schedule', permission: 'SCHEDULE_READ' },
    { name: 'المستخدمين', icon: '👥', active: false, path: '/admin-dashboard', adminOnly: true },
    { name: 'الإحصائيات', icon: '📈', active: false, path: '/statistics', adminOnly: true }
  ].filter(item => {
    if (item.adminOnly && user?.role !== 'ADMIN') return false;
    if (item.permission && !hasPermission(item.permission)) return false;
    return true;
  });

  return (
    <AuthGuard requiredPermissions={requiredPermissions} requiredRole={requiredRole}>
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        color: 'white',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        {/* الشريط الجانبي */}
        <Sidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />

        {/* شريط التنقل العلوي */}
        <div style={{
          background: '#1a1d29',
          padding: '15px 30px',
          borderBottom: '1px solid #2d3748',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          {/* زر القائمة واللوجو */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.5rem',
                cursor: 'pointer',
                padding: '5px'
              }}
            >
              ☰
            </button>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              fontSize: '1.2rem',
              fontWeight: '900',
              fontFamily: 'Arial, sans-serif',
              gap: '5px'
            }}>
              <span style={{
                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '900',
                fontSize: '1.5rem'
              }}>
                X
              </span>
              <span style={{
                color: '#6c757d',
                fontSize: '1rem',
                fontWeight: '300'
              }}>
                -
              </span>
              <span style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontWeight: '800',
                letterSpacing: '1px'
              }}>
                Prime
              </span>
            </div>
          </div>

          {/* عناصر التنقل */}
          <div style={{ display: 'flex', gap: '5px' }}>
            {navigationItems.map((item, index) => (
              <button
                key={index}
                onClick={() => router.push(item.path)}
                style={{
                  background: item.active ? '#4299e1' : 'transparent',
                  color: item.active ? 'white' : '#a0aec0',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!item.active) {
                    e.target.style.background = '#2d3748';
                    e.target.style.color = 'white';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.active) {
                    e.target.style.background = 'transparent';
                    e.target.style.color = '#a0aec0';
                  }
                }}
              >
                <span>{item.icon}</span>
                {item.name}
              </button>
            ))}
          </div>

          {/* أدوات المستخدم */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}>
              🔍
            </button>
            <button style={{
              background: 'transparent',
              border: 'none',
              color: '#a0aec0',
              fontSize: '1.2rem',
              cursor: 'pointer'
            }}>
              ⚙️
            </button>
            <button
              onClick={logout}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.2rem',
                cursor: 'pointer'
              }}
            >
              🚪
            </button>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div style={{ 
          padding: '30px',
          marginRight: sidebarOpen ? '280px' : '0',
          transition: 'margin-right 0.3s ease'
        }}>
          {/* رأس الصفحة */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '30px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem'
              }}>
                {icon}
              </div>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  margin: '0 0 5px 0',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  {title}
                </h1>
                <p style={{
                  color: '#a0aec0',
                  margin: 0,
                  fontSize: '1rem'
                }}>
                  {subtitle}
                </p>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '20px',
              color: '#a0aec0'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  background: '#68d391',
                  borderRadius: '50%'
                }}></div>
                <span style={{ fontSize: '0.9rem' }}>متصل</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>🔄</span>
                <span style={{ fontSize: '0.9rem' }}>
                  {currentTime.toLocaleTimeString('ar-EG')}
                </span>
              </div>
            </div>
          </div>

          {/* محتوى الصفحة */}
          <div style={{
            background: '#2d3748',
            borderRadius: '20px',
            padding: '25px',
            border: '1px solid #4a5568',
            boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
            maxWidth: fullWidth ? 'none' : '1000px',
            margin: '0 auto',
            width: fullWidth ? '100%' : 'auto'
          }}>
            {children}
          </div>
        </div>

        {/* النص السفلي */}
        <div style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          color: '#6c757d',
          fontSize: '0.75rem',
          fontFamily: 'Arial, sans-serif',
          direction: 'ltr'
        }}>
          Powered By Mahmoud Ismail
        </div>
      </div>
    </AuthGuard>
  );
}
