"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _styles_dashboard_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/dashboard.css */ \"(app-pages-browser)/./src/styles/dashboard.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Dashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Dashboard.useEffect.timer\"], 1000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // بيانات الإحصائيات الحقيقية\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMedia: 0,\n        validMedia: 0,\n        rejectedMedia: 0,\n        expiredMedia: 0,\n        pendingMedia: 0,\n        activeUsers: 0,\n        onlineUsers: 0,\n        todayAdded: 0\n    });\n    // جلب البيانات الحقيقية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchRealStats();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchRealStats = async ()=>{\n        try {\n            // محاكاة جلب البيانات من localStorage أو API\n            const mediaData = JSON.parse(localStorage.getItem('mediaItems') || '[]');\n            const userData = JSON.parse(localStorage.getItem('users') || '[]');\n            // حساب الإحصائيات الحقيقية\n            const totalMedia = mediaData.length;\n            const validMedia = mediaData.filter((item)=>item.status === 'approved').length;\n            const rejectedMedia = mediaData.filter((item)=>item.status === 'rejected').length;\n            const expiredMedia = mediaData.filter((item)=>{\n                if (!item.endDate) return false;\n                return new Date(item.endDate) < new Date();\n            }).length;\n            const pendingMedia = mediaData.filter((item)=>item.status === 'pending').length;\n            // حساب المواد المضافة اليوم\n            const today = new Date().toDateString();\n            const todayAdded = mediaData.filter((item)=>{\n                if (!item.createdAt) return false;\n                return new Date(item.createdAt).toDateString() === today;\n            }).length;\n            setStats({\n                totalMedia,\n                validMedia,\n                rejectedMedia,\n                expiredMedia,\n                pendingMedia,\n                activeUsers: userData.length,\n                onlineUsers: userData.filter((u)=>u.isActive).length,\n                todayAdded\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // بيانات افتراضية في حالة الخطأ\n            setStats({\n                totalMedia: 0,\n                validMedia: 0,\n                rejectedMedia: 0,\n                expiredMedia: 0,\n                pendingMedia: 0,\n                activeUsers: 4,\n                onlineUsers: 1,\n                todayAdded: 0\n            });\n        }\n    };\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        fontSize: '1.2rem',\n                                        fontWeight: '900',\n                                        fontFamily: 'Arial, sans-serif'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '800',\n                                                letterSpacing: '1px'\n                                            },\n                                            children: \"Prime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '900',\n                                                fontSize: '1.5rem',\n                                                marginLeft: '3px'\n                                            },\n                                            children: \"X\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        marginRight: sidebarOpen ? '280px' : '0',\n                        transition: 'margin-right 0.3s ease'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: \"لوحة التحكم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: \"رؤية عامة للعملية في الوقت الفعلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#68d391',\n                                                        margin: '5px 0 0 0',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"مراقبة \",\n                                                        stats.totalMedia,\n                                                        \" مادة إعلامية و \",\n                                                        stats.activeUsers,\n                                                        \" مستخدمين نشطين\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"البيانات المحلية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"المزامنة: \",\n                                                        currentTime.toLocaleTimeString('ar-EG')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#2d3748',\n                                        borderRadius: '12px',\n                                        padding: '25px',\n                                        border: '1px solid #4a5568',\n                                        position: 'relative',\n                                        overflow: 'hidden'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '15px',\n                                                left: '15px',\n                                                background: stats.todayAdded > 0 ? '#68d391' : '#6c757d',\n                                                color: 'white',\n                                                padding: '4px 8px',\n                                                borderRadius: '6px',\n                                                fontSize: '0.8rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: [\n                                                \"+\",\n                                                stats.todayAdded,\n                                                \" اليوم\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #9f7aea 0%, #667eea 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                fontWeight: 'bold',\n                                                color: 'white',\n                                                marginBottom: '5px'\n                                            },\n                                            children: stats.totalMedia.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: 'white',\n                                                fontWeight: 'bold',\n                                                fontSize: '1rem',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"إجمالي المواد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"جميع المواد المسجلة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#2d3748',\n                                        borderRadius: '12px',\n                                        padding: '25px',\n                                        border: '1px solid #4a5568',\n                                        position: 'relative'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '15px',\n                                                left: '15px',\n                                                background: '#68d391',\n                                                color: 'white',\n                                                padding: '4px 8px',\n                                                borderRadius: '6px',\n                                                fontSize: '0.8rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: [\n                                                stats.totalMedia > 0 ? Math.round(stats.validMedia / stats.totalMedia * 100) : 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"✅\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                fontWeight: 'bold',\n                                                color: 'white',\n                                                marginBottom: '5px'\n                                            },\n                                            children: stats.validMedia.toLocaleString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: 'white',\n                                                fontWeight: 'bold',\n                                                fontSize: '1rem',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"المواد المعتمدة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"جاهزة للبث\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#2d3748',\n                                        borderRadius: '12px',\n                                        padding: '25px',\n                                        border: '1px solid #4a5568',\n                                        position: 'relative'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '15px',\n                                                left: '15px',\n                                                background: '#f56565',\n                                                color: 'white',\n                                                padding: '4px 8px',\n                                                borderRadius: '6px',\n                                                fontSize: '0.8rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: [\n                                                stats.totalMedia > 0 ? Math.round(stats.rejectedMedia / stats.totalMedia * 100) : 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                fontWeight: 'bold',\n                                                color: 'white',\n                                                marginBottom: '5px'\n                                            },\n                                            children: stats.rejectedMedia\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: 'white',\n                                                fontWeight: 'bold',\n                                                fontSize: '1rem',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"المواد المرفوضة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"مرفوضة تقنياً\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#2d3748',\n                                        borderRadius: '12px',\n                                        padding: '25px',\n                                        border: '1px solid #4a5568',\n                                        position: 'relative',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '15px',\n                                                left: '15px',\n                                                background: '#68d391',\n                                                color: 'white',\n                                                padding: '4px 8px',\n                                                borderRadius: '6px',\n                                                fontSize: '0.8rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: \"مباشر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #ed64a6 0%, #d53f8c 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem',\n                                                marginBottom: '15px',\n                                                margin: '0 auto 15px auto'\n                                            },\n                                            children: \"\\uD83D\\uDCC5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '1.8rem',\n                                                fontWeight: 'bold',\n                                                color: 'white',\n                                                marginBottom: '5px'\n                                            },\n                                            children: new Date().toLocaleDateString('ar-EG', {\n                                                weekday: 'long',\n                                                day: 'numeric',\n                                                month: 'short'\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: 'white',\n                                                fontWeight: 'bold',\n                                                fontSize: '1rem',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"التاريخ الحالي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                fontSize: '1.1rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: currentTime.toLocaleTimeString('ar-EG', {\n                                                hour: '2-digit',\n                                                minute: '2-digit'\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#2d3748',\n                                        borderRadius: '12px',\n                                        padding: '25px',\n                                        border: '1px solid #4a5568',\n                                        position: 'relative'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '15px',\n                                                left: '15px',\n                                                background: '#ed8936',\n                                                color: 'white',\n                                                padding: '4px 8px',\n                                                borderRadius: '6px',\n                                                fontSize: '0.8rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: [\n                                                stats.totalMedia > 0 ? Math.round(stats.expiredMedia / stats.totalMedia * 100) : 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"⏰\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                fontWeight: 'bold',\n                                                color: 'white',\n                                                marginBottom: '5px'\n                                            },\n                                            children: stats.expiredMedia\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: 'white',\n                                                fontWeight: 'bold',\n                                                fontSize: '1rem',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"المواد المنتهية\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 620,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"انتهت صلاحيتها\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#2d3748',\n                                        borderRadius: '12px',\n                                        padding: '25px',\n                                        border: '1px solid #4a5568',\n                                        position: 'relative'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '15px',\n                                                left: '15px',\n                                                background: '#9f7aea',\n                                                color: 'white',\n                                                padding: '4px 8px',\n                                                borderRadius: '6px',\n                                                fontSize: '0.8rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: [\n                                                stats.totalMedia > 0 ? Math.round(stats.pendingMedia / stats.totalMedia * 100) : 0,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 644,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #9f7aea 0%, #805ad5 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"⏳\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                fontWeight: 'bold',\n                                                color: 'white',\n                                                marginBottom: '5px'\n                                            },\n                                            children: stats.pendingMedia\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: 'white',\n                                                fontWeight: 'bold',\n                                                fontSize: '1rem',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"قيد المراجعة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 678,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"في انتظار الموافقة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 637,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#2d3748',\n                                        borderRadius: '12px',\n                                        padding: '25px',\n                                        border: '1px solid #4a5568'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #4299e1 0%, #3182ce 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"\\uD83D\\uDC65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                fontWeight: 'bold',\n                                                color: 'white',\n                                                marginBottom: '5px'\n                                            },\n                                            children: stats.activeUsers\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: 'white',\n                                                fontWeight: 'bold',\n                                                fontSize: '1rem',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"المستخدمين المسجلين\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"إجمالي المستخدمين\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 730,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#2d3748',\n                                        borderRadius: '12px',\n                                        padding: '25px',\n                                        border: '1px solid #4a5568',\n                                        position: 'relative'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: 'absolute',\n                                                top: '15px',\n                                                left: '15px',\n                                                background: '#68d391',\n                                                color: 'white',\n                                                padding: '4px 8px',\n                                                borderRadius: '6px',\n                                                fontSize: '0.8rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: \"متصل الآن\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #38b2ac 0%, #319795 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"\\uD83D\\uDFE2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 759,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                fontWeight: 'bold',\n                                                color: 'white',\n                                                marginBottom: '5px'\n                                            },\n                                            children: stats.onlineUsers\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: 'white',\n                                                fontWeight: 'bold',\n                                                fontSize: '1rem',\n                                                marginBottom: '5px'\n                                            },\n                                            children: \"المستخدمين المتصلين\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"نشط حالياً\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"PX9MxTqOw6/uJqWRT1Stv/N7xJU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});