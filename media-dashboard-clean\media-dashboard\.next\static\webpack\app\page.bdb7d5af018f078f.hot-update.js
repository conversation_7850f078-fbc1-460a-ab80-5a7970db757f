"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [showUserInfo, setShowUserInfo] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // إذا لم يكن المستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول\n            if (!user && !localStorage.getItem('user')) {\n                router.push('/login');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                maxWidth: '1200px',\n                margin: '0 auto'\n            },\n            children: [\n                user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '15px',\n                        padding: '20px',\n                        marginBottom: '20px',\n                        boxShadow: '0 10px 30px rgba(0,0,0,0.1)',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        backdropFilter: 'blur(10px)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        width: '50px',\n                                        height: '50px',\n                                        borderRadius: '50%',\n                                        overflow: 'hidden',\n                                        background: 'white',\n                                        border: '2px solid #667eea',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: \"/images/logo.jpeg\",\n                                        alt: \"شعار النظام\",\n                                        style: {\n                                            width: '40px',\n                                            height: '40px',\n                                            objectFit: 'contain'\n                                        },\n                                        onError: (e)=>{\n                                            // في حالة عدم وجود الصورة، عرض الحرف الأول\n                                            e.currentTarget.style.display = 'none';\n                                            e.currentTarget.parentElement.innerHTML = '\\n                      <div style=\"\\n                        width: 50px;\\n                        height: 50px;\\n                        background: linear-gradient(45deg, #667eea, #764ba2);\\n                        borderRadius: 50%;\\n                        display: flex;\\n                        alignItems: center;\\n                        justifyContent: center;\\n                        color: white;\\n                        fontSize: 1.5rem;\\n                        fontWeight: bold;\\n                      \">'.concat(user.name.charAt(0), \"</div>\\n                    \");\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                margin: 0,\n                                                color: '#333',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: [\n                                                \"مرحباً، \",\n                                                user.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                margin: 0,\n                                                color: '#6c757d',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                user.role === 'ADMIN' && '👑 مدير النظام',\n                                                user.role === 'MEDIA_MANAGER' && '📝 مدير المحتوى',\n                                                user.role === 'SCHEDULER' && '📅 مجدول البرامج',\n                                                user.role === 'VIEWER' && '👁️ مستخدم عرض'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '10px'\n                            },\n                            children: [\n                                user.role === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/admin-dashboard'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 15px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDC51 لوحة المدير\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowUserInfo(!showUserInfo),\n                                    style: {\n                                        background: '#f8f9fa',\n                                        color: '#333',\n                                        border: '1px solid #dee2e6',\n                                        borderRadius: '8px',\n                                        padding: '8px 15px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"⚙️ الإعدادات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 15px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDEAA خروج\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'white',\n                        borderRadius: '20px',\n                        padding: '40px',\n                        boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            style: {\n                                fontSize: '2.5rem',\n                                fontWeight: 'bold',\n                                color: '#333',\n                                textAlign: 'center',\n                                marginBottom: '40px',\n                                background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                WebkitBackgroundClip: 'text',\n                                WebkitTextFillColor: 'transparent'\n                            },\n                            children: \"\\uD83C\\uDFAC نظام إدارة المحتوى الإعلامي\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                gap: '20px',\n                                marginBottom: '40px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #1976d215 0%, #1976d225 100%)',\n                                        border: '2px solid #1976d230',\n                                        borderRadius: '15px',\n                                        padding: '20px',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83C\\uDFAC\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#1976d2',\n                                                margin: '10px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: \"إجمالي المواد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#1976d2'\n                                            },\n                                            children: \"1,234\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #2e7d3215 0%, #2e7d3225 100%)',\n                                        border: '2px solid #2e7d3230',\n                                        borderRadius: '15px',\n                                        padding: '20px',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDCFA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#2e7d32',\n                                                margin: '10px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: \"البرامج النشطة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#2e7d32'\n                                            },\n                                            children: \"89\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #ed6c0215 0%, #ed6c0225 100%)',\n                                        border: '2px solid #ed6c0230',\n                                        borderRadius: '15px',\n                                        padding: '20px',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83C\\uDFB5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#ed6c02',\n                                                margin: '10px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: \"الأغاني\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#ed6c02'\n                                            },\n                                            children: \"456\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #d32f2f15 0%, #d32f2f25 100%)',\n                                        border: '2px solid #d32f2f30',\n                                        borderRadius: '15px',\n                                        padding: '20px',\n                                        textAlign: 'center'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"❌\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#d32f2f',\n                                                margin: '10px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: \"المواد المرفوضة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#d32f2f'\n                                            },\n                                            children: \"12\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: '2fr 1fr',\n                                gap: '20px',\n                                marginTop: '40px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',\n                                        borderRadius: '15px',\n                                        padding: '30px',\n                                        border: '1px solid #e0e0e0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: '#333',\n                                                marginBottom: '20px',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA النشاط الأخير\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                textAlign: 'center',\n                                                color: '#666',\n                                                padding: '40px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '3rem',\n                                                        marginBottom: '10px'\n                                                    },\n                                                    children: \"\\uD83D\\uDCC8\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد أنشطة حديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',\n                                        borderRadius: '15px',\n                                        padding: '30px',\n                                        border: '1px solid #e0e0e0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: '#333',\n                                                marginBottom: '20px',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDD14 الإشعارات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                textAlign: 'center',\n                                                color: '#666',\n                                                padding: '20px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '3rem',\n                                                        marginBottom: '10px'\n                                                    },\n                                                    children: \"\\uD83D\\uDD15\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"لا توجد إشعارات جديدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '40px',\n                                display: 'flex',\n                                justifyContent: 'center',\n                                gap: '20px',\n                                flexWrap: 'wrap'\n                            },\n                            children: [\n                                hasPermission('MEDIA_CREATE') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/add-media'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #2e7d32, #2e7d32dd)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"➕ إضافة مادة إعلامية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                hasPermission('MEDIA_READ') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/media-list'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #f093fb, #f5576c)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"\\uD83D\\uDCDA قائمة المواد الإعلامية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/statistics'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #9c27b0, #7b1fa2)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"\\uD83D\\uDCCA إحصائيات النظام\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this),\n                                hasPermission('SCHEDULE_READ') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/weekly-schedule'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #673ab7, #512da8)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"\\uD83D\\uDCC5 الخريطة البرامجية الأسبوعية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/export'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #9c27b0, #9c27b0dd)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '25px',\n                                        padding: '15px 30px',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold',\n                                        cursor: 'pointer',\n                                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                        transition: 'transform 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                    onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                    children: \"\\uD83D\\uDCE4 تصدير البيانات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '40px',\n                                textAlign: 'center',\n                                padding: '20px',\n                                background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',\n                                borderRadius: '15px',\n                                border: '1px solid #e0e0e0'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#333',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDE80 مرحباً بك في نظام إدارة المحتوى الإعلامي المتطور\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#666',\n                                        lineHeight: '1.6'\n                                    },\n                                    children: \"نظام شامل لإدارة المواد الإعلامية مع دعم كامل للغة العربية وواجهة مستخدم عصرية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginTop: '20px'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#555',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: \"✨ الميزات الرئيسية: إدارة المواد الإعلامية • الخريطة البرامجية الأسبوعية • جدول الإذاعة اليومية • تصدير إلى Excel مع دعم RTL\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"SlaB3zud4Dn3sgLC32VA8bZxTTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});