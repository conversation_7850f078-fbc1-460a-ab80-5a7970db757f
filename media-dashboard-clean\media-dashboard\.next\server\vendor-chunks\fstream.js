/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fstream";
exports.ids = ["vendor-chunks/fstream"];
exports.modules = {

/***/ "(rsc)/./node_modules/fstream/fstream.js":
/*!*****************************************!*\
  !*** ./node_modules/fstream/fstream.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.Abstract = __webpack_require__(/*! ./lib/abstract.js */ \"(rsc)/./node_modules/fstream/lib/abstract.js\")\nexports.Reader = __webpack_require__(/*! ./lib/reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\")\nexports.Writer = __webpack_require__(/*! ./lib/writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\")\n\nexports.File = {\n  Reader: __webpack_require__(/*! ./lib/file-reader.js */ \"(rsc)/./node_modules/fstream/lib/file-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/file-writer.js */ \"(rsc)/./node_modules/fstream/lib/file-writer.js\")\n}\n\nexports.Dir = {\n  Reader: __webpack_require__(/*! ./lib/dir-reader.js */ \"(rsc)/./node_modules/fstream/lib/dir-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/dir-writer.js */ \"(rsc)/./node_modules/fstream/lib/dir-writer.js\")\n}\n\nexports.Link = {\n  Reader: __webpack_require__(/*! ./lib/link-reader.js */ \"(rsc)/./node_modules/fstream/lib/link-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/link-writer.js */ \"(rsc)/./node_modules/fstream/lib/link-writer.js\")\n}\n\nexports.Proxy = {\n  Reader: __webpack_require__(/*! ./lib/proxy-reader.js */ \"(rsc)/./node_modules/fstream/lib/proxy-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/proxy-writer.js */ \"(rsc)/./node_modules/fstream/lib/proxy-writer.js\")\n}\n\nexports.Reader.Dir = exports.DirReader = exports.Dir.Reader\nexports.Reader.File = exports.FileReader = exports.File.Reader\nexports.Reader.Link = exports.LinkReader = exports.Link.Reader\nexports.Reader.Proxy = exports.ProxyReader = exports.Proxy.Reader\n\nexports.Writer.Dir = exports.DirWriter = exports.Dir.Writer\nexports.Writer.File = exports.FileWriter = exports.File.Writer\nexports.Writer.Link = exports.LinkWriter = exports.Link.Writer\nexports.Writer.Proxy = exports.ProxyWriter = exports.Proxy.Writer\n\nexports.collect = __webpack_require__(/*! ./lib/collect.js */ \"(rsc)/./node_modules/fstream/lib/collect.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/fstream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/abstract.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/abstract.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// the parent class for all fstreams.\n\nmodule.exports = Abstract\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\n\nfunction Abstract () {\n  Stream.call(this)\n}\n\ninherits(Abstract, Stream)\n\nAbstract.prototype.on = function (ev, fn) {\n  if (ev === 'ready' && this.ready) {\n    process.nextTick(fn.bind(this))\n  } else {\n    Stream.prototype.on.call(this, ev, fn)\n  }\n  return this\n}\n\nAbstract.prototype.abort = function () {\n  this._aborted = true\n  this.emit('abort')\n}\n\nAbstract.prototype.destroy = function () {}\n\nAbstract.prototype.warn = function (msg, code) {\n  var self = this\n  var er = decorate(msg, code, self)\n  if (!self.listeners('warn')) {\n    console.error('%s %s\\n' +\n    'path = %s\\n' +\n    'syscall = %s\\n' +\n    'fstream_type = %s\\n' +\n    'fstream_path = %s\\n' +\n    'fstream_unc_path = %s\\n' +\n    'fstream_class = %s\\n' +\n    'fstream_stack =\\n%s\\n',\n      code || 'UNKNOWN',\n      er.stack,\n      er.path,\n      er.syscall,\n      er.fstream_type,\n      er.fstream_path,\n      er.fstream_unc_path,\n      er.fstream_class,\n      er.fstream_stack.join('\\n'))\n  } else {\n    self.emit('warn', er)\n  }\n}\n\nAbstract.prototype.info = function (msg, code) {\n  this.emit('info', msg, code)\n}\n\nAbstract.prototype.error = function (msg, code, th) {\n  var er = decorate(msg, code, this)\n  if (th) throw er\n  else this.emit('error', er)\n}\n\nfunction decorate (er, code, self) {\n  if (!(er instanceof Error)) er = new Error(er)\n  er.code = er.code || code\n  er.path = er.path || self.path\n  er.fstream_type = er.fstream_type || self.type\n  er.fstream_path = er.fstream_path || self.path\n  if (self._path !== self.path) {\n    er.fstream_unc_path = er.fstream_unc_path || self._path\n  }\n  if (self.linkpath) {\n    er.fstream_linkpath = er.fstream_linkpath || self.linkpath\n  }\n  er.fstream_class = er.fstream_class || self.constructor.name\n  er.fstream_stack = er.fstream_stack ||\n    new Error().stack.split(/\\n/).slice(3).map(function (s) {\n      return s.replace(/^ {4}at /, '')\n    })\n\n  return er\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/abstract.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/collect.js":
/*!*********************************************!*\
  !*** ./node_modules/fstream/lib/collect.js ***!
  \*********************************************/
/***/ ((module) => {

eval("module.exports = collect\n\nfunction collect (stream) {\n  if (stream._collected) return\n\n  if (stream._paused) return stream.on('resume', collect.bind(null, stream))\n\n  stream._collected = true\n  stream.pause()\n\n  stream.on('data', save)\n  stream.on('end', save)\n  var buf = []\n  function save (b) {\n    if (typeof b === 'string') b = new Buffer(b)\n    if (Buffer.isBuffer(b) && !b.length) return\n    buf.push(b)\n  }\n\n  stream.on('entry', saveEntry)\n  var entryBuffer = []\n  function saveEntry (e) {\n    collect(e)\n    entryBuffer.push(e)\n  }\n\n  stream.on('proxy', proxyPause)\n  function proxyPause (p) {\n    p.pause()\n  }\n\n  // replace the pipe method with a new version that will\n  // unlock the buffered stuff.  if you just call .pipe()\n  // without a destination, then it'll re-play the events.\n  stream.pipe = (function (orig) {\n    return function (dest) {\n      // console.error(' === open the pipes', dest && dest.path)\n\n      // let the entries flow through one at a time.\n      // Once they're all done, then we can resume completely.\n      var e = 0\n      ;(function unblockEntry () {\n        var entry = entryBuffer[e++]\n        // console.error(\" ==== unblock entry\", entry && entry.path)\n        if (!entry) return resume()\n        entry.on('end', unblockEntry)\n        if (dest) dest.add(entry)\n        else stream.emit('entry', entry)\n      })()\n\n      function resume () {\n        stream.removeListener('entry', saveEntry)\n        stream.removeListener('data', save)\n        stream.removeListener('end', save)\n\n        stream.pipe = orig\n        if (dest) stream.pipe(dest)\n\n        buf.forEach(function (b) {\n          if (b) stream.emit('data', b)\n          else stream.emit('end')\n        })\n\n        stream.resume()\n      }\n\n      return dest\n    }\n  })(stream.pipe)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/collect.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/dir-reader.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-reader.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A thing that emits \"entry\" events with Reader objects\n// Pausing it causes it to stop emitting entry events, and also\n// pauses the current entry if there is one.\n\nmodule.exports = DirReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\")\nvar assert = (__webpack_require__(/*! assert */ \"assert\").ok)\n\ninherits(DirReader, Reader)\n\nfunction DirReader (props) {\n  var self = this\n  if (!(self instanceof DirReader)) {\n    throw new Error('DirReader must be called as constructor.')\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    throw new Error('Non-directory type ' + props.type)\n  }\n\n  self.entries = null\n  self._index = -1\n  self._paused = false\n  self._length = -1\n\n  if (props.sort) {\n    this.sort = props.sort\n  }\n\n  Reader.call(this, props)\n}\n\nDirReader.prototype._getEntries = function () {\n  var self = this\n\n  // race condition.  might pause() before calling _getEntries,\n  // and then resume, and try to get them a second time.\n  if (self._gotEntries) return\n  self._gotEntries = true\n\n  fs.readdir(self._path, function (er, entries) {\n    if (er) return self.error(er)\n\n    self.entries = entries\n\n    self.emit('entries', entries)\n    if (self._paused) self.once('resume', processEntries)\n    else processEntries()\n\n    function processEntries () {\n      self._length = self.entries.length\n      if (typeof self.sort === 'function') {\n        self.entries = self.entries.sort(self.sort.bind(self))\n      }\n      self._read()\n    }\n  })\n}\n\n// start walking the dir, and emit an \"entry\" event for each one.\nDirReader.prototype._read = function () {\n  var self = this\n\n  if (!self.entries) return self._getEntries()\n\n  if (self._paused || self._currentEntry || self._aborted) {\n    // console.error('DR paused=%j, current=%j, aborted=%j', self._paused, !!self._currentEntry, self._aborted)\n    return\n  }\n\n  self._index++\n  if (self._index >= self.entries.length) {\n    if (!self._ended) {\n      self._ended = true\n      self.emit('end')\n      self.emit('close')\n    }\n    return\n  }\n\n  // ok, handle this one, then.\n\n  // save creating a proxy, by stat'ing the thing now.\n  var p = path.resolve(self._path, self.entries[self._index])\n  assert(p !== self._path)\n  assert(self.entries[self._index])\n\n  // set this to prevent trying to _read() again in the stat time.\n  self._currentEntry = p\n  fs[ self.props.follow ? 'stat' : 'lstat' ](p, function (er, stat) {\n    if (er) return self.error(er)\n\n    var who = self._proxy || self\n\n    stat.path = p\n    stat.basename = path.basename(p)\n    stat.dirname = path.dirname(p)\n    var childProps = self.getChildProps.call(who, stat)\n    childProps.path = p\n    childProps.basename = path.basename(p)\n    childProps.dirname = path.dirname(p)\n\n    var entry = Reader(childProps, stat)\n\n    // console.error(\"DR Entry\", p, stat.size)\n\n    self._currentEntry = entry\n\n    // \"entry\" events are for direct entries in a specific dir.\n    // \"child\" events are for any and all children at all levels.\n    // This nomenclature is not completely final.\n\n    entry.on('pause', function (who) {\n      if (!self._paused && !entry._disowned) {\n        self.pause(who)\n      }\n    })\n\n    entry.on('resume', function (who) {\n      if (self._paused && !entry._disowned) {\n        self.resume(who)\n      }\n    })\n\n    entry.on('stat', function (props) {\n      self.emit('_entryStat', entry, props)\n      if (entry._aborted) return\n      if (entry._paused) {\n        entry.once('resume', function () {\n          self.emit('entryStat', entry, props)\n        })\n      } else self.emit('entryStat', entry, props)\n    })\n\n    entry.on('ready', function EMITCHILD () {\n      // console.error(\"DR emit child\", entry._path)\n      if (self._paused) {\n        // console.error(\"  DR emit child - try again later\")\n        // pause the child, and emit the \"entry\" event once we drain.\n        // console.error(\"DR pausing child entry\")\n        entry.pause(self)\n        return self.once('resume', EMITCHILD)\n      }\n\n      // skip over sockets.  they can't be piped around properly,\n      // so there's really no sense even acknowledging them.\n      // if someone really wants to see them, they can listen to\n      // the \"socket\" events.\n      if (entry.type === 'Socket') {\n        self.emit('socket', entry)\n      } else {\n        self.emitEntry(entry)\n      }\n    })\n\n    var ended = false\n    entry.on('close', onend)\n    entry.on('disown', onend)\n    function onend () {\n      if (ended) return\n      ended = true\n      self.emit('childEnd', entry)\n      self.emit('entryEnd', entry)\n      self._currentEntry = null\n      if (!self._paused) {\n        self._read()\n      }\n    }\n\n    // XXX Remove this.  Works in node as of 0.6.2 or so.\n    // Long filenames should not break stuff.\n    entry.on('error', function (er) {\n      if (entry._swallowErrors) {\n        self.warn(er)\n        entry.emit('end')\n        entry.emit('close')\n      } else {\n        self.emit('error', er)\n      }\n    })\n\n    // proxy up some events.\n    ;[\n      'child',\n      'childEnd',\n      'warn'\n    ].forEach(function (ev) {\n      entry.on(ev, self.emit.bind(self, ev))\n    })\n  })\n}\n\nDirReader.prototype.disown = function (entry) {\n  entry.emit('beforeDisown')\n  entry._disowned = true\n  entry.parent = entry.root = null\n  if (entry === this._currentEntry) {\n    this._currentEntry = null\n  }\n  entry.emit('disown')\n}\n\nDirReader.prototype.getChildProps = function () {\n  return {\n    depth: this.depth + 1,\n    root: this.root || this,\n    parent: this,\n    follow: this.follow,\n    filter: this.filter,\n    sort: this.props.sort,\n    hardlinks: this.props.hardlinks\n  }\n}\n\nDirReader.prototype.pause = function (who) {\n  var self = this\n  if (self._paused) return\n  who = who || self\n  self._paused = true\n  if (self._currentEntry && self._currentEntry.pause) {\n    self._currentEntry.pause(who)\n  }\n  self.emit('pause', who)\n}\n\nDirReader.prototype.resume = function (who) {\n  var self = this\n  if (!self._paused) return\n  who = who || self\n\n  self._paused = false\n  // console.error('DR Emit Resume', self._path)\n  self.emit('resume', who)\n  if (self._paused) {\n    // console.error('DR Re-paused', self._path)\n    return\n  }\n\n  if (self._currentEntry) {\n    if (self._currentEntry.resume) self._currentEntry.resume(who)\n  } else self._read()\n}\n\nDirReader.prototype.emitEntry = function (entry) {\n  this.emit('entry', entry)\n  this.emit('child', entry)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/dir-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/dir-writer.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-writer.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// It is expected that, when .add() returns false, the consumer\n// of the DirWriter will pause until a \"drain\" event occurs. Note\n// that this is *almost always going to be the case*, unless the\n// thing being written is some sort of unsupported type, and thus\n// skipped over.\n\nmodule.exports = DirWriter\n\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(rsc)/./node_modules/fstream/node_modules/mkdirp/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar collect = __webpack_require__(/*! ./collect.js */ \"(rsc)/./node_modules/fstream/lib/collect.js\")\n\ninherits(DirWriter, Writer)\n\nfunction DirWriter (props) {\n  var self = this\n  if (!(self instanceof DirWriter)) {\n    self.error('DirWriter must be called as constructor.', null, true)\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    self.error('Non-directory type ' + props.type + ' ' +\n      JSON.stringify(props), null, true)\n  }\n\n  Writer.call(this, props)\n}\n\nDirWriter.prototype._create = function () {\n  var self = this\n  mkdir(self._path, Writer.dirmode, function (er) {\n    if (er) return self.error(er)\n    // ready to start getting entries!\n    self.ready = true\n    self.emit('ready')\n    self._process()\n  })\n}\n\n// a DirWriter has an add(entry) method, but its .write() doesn't\n// do anything.  Why a no-op rather than a throw?  Because this\n// leaves open the door for writing directory metadata for\n// gnu/solaris style dumpdirs.\nDirWriter.prototype.write = function () {\n  return true\n}\n\nDirWriter.prototype.end = function () {\n  this._ended = true\n  this._process()\n}\n\nDirWriter.prototype.add = function (entry) {\n  var self = this\n\n  // console.error('\\tadd', entry._path, '->', self._path)\n  collect(entry)\n  if (!self.ready || self._currentEntry) {\n    self._buffer.push(entry)\n    return false\n  }\n\n  // create a new writer, and pipe the incoming entry into it.\n  if (self._ended) {\n    return self.error('add after end')\n  }\n\n  self._buffer.push(entry)\n  self._process()\n\n  return this._buffer.length === 0\n}\n\nDirWriter.prototype._process = function () {\n  var self = this\n\n  // console.error('DW Process p=%j', self._processing, self.basename)\n\n  if (self._processing) return\n\n  var entry = self._buffer.shift()\n  if (!entry) {\n    // console.error(\"DW Drain\")\n    self.emit('drain')\n    if (self._ended) self._finish()\n    return\n  }\n\n  self._processing = true\n  // console.error(\"DW Entry\", entry._path)\n\n  self.emit('entry', entry)\n\n  // ok, add this entry\n  //\n  // don't allow recursive copying\n  var p = entry\n  var pp\n  do {\n    pp = p._path || p.path\n    if (pp === self.root._path || pp === self._path ||\n      (pp && pp.indexOf(self._path) === 0)) {\n      // console.error('DW Exit (recursive)', entry.basename, self._path)\n      self._processing = false\n      if (entry._collected) entry.pipe()\n      return self._process()\n    }\n    p = p.parent\n  } while (p)\n\n  // console.error(\"DW not recursive\")\n\n  // chop off the entry's root dir, replace with ours\n  var props = {\n    parent: self,\n    root: self.root || self,\n    type: entry.type,\n    depth: self.depth + 1\n  }\n\n  pp = entry._path || entry.path || entry.props.path\n  if (entry.parent) {\n    pp = pp.substr(entry.parent._path.length + 1)\n  }\n  // get rid of any ../../ shenanigans\n  props.path = path.join(self.path, path.join('/', pp))\n\n  // if i have a filter, the child should inherit it.\n  props.filter = self.filter\n\n  // all the rest of the stuff, copy over from the source.\n  Object.keys(entry.props).forEach(function (k) {\n    if (!props.hasOwnProperty(k)) {\n      props[k] = entry.props[k]\n    }\n  })\n\n  // not sure at this point what kind of writer this is.\n  var child = self._currentChild = new Writer(props)\n  child.on('ready', function () {\n    // console.error(\"DW Child Ready\", child.type, child._path)\n    // console.error(\"  resuming\", entry._path)\n    entry.pipe(child)\n    entry.resume()\n  })\n\n  // XXX Make this work in node.\n  // Long filenames should not break stuff.\n  child.on('error', function (er) {\n    if (child._swallowErrors) {\n      self.warn(er)\n      child.emit('end')\n      child.emit('close')\n    } else {\n      self.emit('error', er)\n    }\n  })\n\n  // we fire _end internally *after* end, so that we don't move on\n  // until any \"end\" listeners have had their chance to do stuff.\n  child.on('close', onend)\n  var ended = false\n  function onend () {\n    if (ended) return\n    ended = true\n    // console.error(\"* DW Child end\", child.basename)\n    self._currentChild = null\n    self._processing = false\n    self._process()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/dir-writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/file-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basically just a wrapper around an fs.ReadStream\n\nmodule.exports = FileReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\")\nvar EOF = {EOF: true}\nvar CLOSE = {CLOSE: true}\n\ninherits(FileReader, Reader)\n\nfunction FileReader (props) {\n  // console.error(\"    FR create\", props.path, props.size, new Error().stack)\n  var self = this\n  if (!(self instanceof FileReader)) {\n    throw new Error('FileReader must be called as constructor.')\n  }\n\n  // should already be established as a File type\n  // XXX Todo: preserve hardlinks by tracking dev+inode+nlink,\n  // with a HardLinkReader class.\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'File' && props.File))) {\n    throw new Error('Non-file type ' + props.type)\n  }\n\n  self._buffer = []\n  self._bytesEmitted = 0\n  Reader.call(self, props)\n}\n\nFileReader.prototype._getStream = function () {\n  var self = this\n  var stream = self._stream = fs.createReadStream(self._path, self.props)\n\n  if (self.props.blksize) {\n    stream.bufferSize = self.props.blksize\n  }\n\n  stream.on('open', self.emit.bind(self, 'open'))\n\n  stream.on('data', function (c) {\n    // console.error('\\t\\t%d %s', c.length, self.basename)\n    self._bytesEmitted += c.length\n    // no point saving empty chunks\n    if (!c.length) {\n      return\n    } else if (self._paused || self._buffer.length) {\n      self._buffer.push(c)\n      self._read()\n    } else self.emit('data', c)\n  })\n\n  stream.on('end', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering End', self._path)\n      self._buffer.push(EOF)\n      self._read()\n    } else {\n      self.emit('end')\n    }\n\n    if (self._bytesEmitted !== self.props.size) {\n      self.error(\"Didn't get expected byte count\\n\" +\n        'expect: ' + self.props.size + '\\n' +\n        'actual: ' + self._bytesEmitted)\n    }\n  })\n\n  stream.on('close', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering Close', self._path)\n      self._buffer.push(CLOSE)\n      self._read()\n    } else {\n      // console.error('FR close 1', self._path)\n      self.emit('close')\n    }\n  })\n\n  stream.on('error', function (e) {\n    self.emit('error', e)\n  })\n\n  self._read()\n}\n\nFileReader.prototype._read = function () {\n  var self = this\n  // console.error('FR _read', self._path)\n  if (self._paused) {\n    // console.error('FR _read paused', self._path)\n    return\n  }\n\n  if (!self._stream) {\n    // console.error('FR _getStream calling', self._path)\n    return self._getStream()\n  }\n\n  // clear out the buffer, if there is one.\n  if (self._buffer.length) {\n    // console.error('FR _read has buffer', self._buffer.length, self._path)\n    var buf = self._buffer\n    for (var i = 0, l = buf.length; i < l; i++) {\n      var c = buf[i]\n      if (c === EOF) {\n        // console.error('FR Read emitting buffered end', self._path)\n        self.emit('end')\n      } else if (c === CLOSE) {\n        // console.error('FR Read emitting buffered close', self._path)\n        self.emit('close')\n      } else {\n        // console.error('FR Read emitting buffered data', self._path)\n        self.emit('data', c)\n      }\n\n      if (self._paused) {\n        // console.error('FR Read Re-pausing at '+i, self._path)\n        self._buffer = buf.slice(i)\n        return\n      }\n    }\n    self._buffer.length = 0\n  }\n// console.error(\"FR _read done\")\n// that's about all there is to it.\n}\n\nFileReader.prototype.pause = function (who) {\n  var self = this\n  // console.error('FR Pause', self._path)\n  if (self._paused) return\n  who = who || self\n  self._paused = true\n  if (self._stream) self._stream.pause()\n  self.emit('pause', who)\n}\n\nFileReader.prototype.resume = function (who) {\n  var self = this\n  // console.error('FR Resume', self._path)\n  if (!self._paused) return\n  who = who || self\n  self.emit('resume', who)\n  self._paused = false\n  if (self._stream) self._stream.resume()\n  self._read()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/file-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/file-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = FileWriter\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar EOF = {}\n\ninherits(FileWriter, Writer)\n\nfunction FileWriter (props) {\n  var self = this\n  if (!(self instanceof FileWriter)) {\n    throw new Error('FileWriter must be called as constructor.')\n  }\n\n  // should already be established as a File type\n  if (props.type !== 'File' || !props.File) {\n    throw new Error('Non-file type ' + props.type)\n  }\n\n  self._buffer = []\n  self._bytesWritten = 0\n\n  Writer.call(this, props)\n}\n\nFileWriter.prototype._create = function () {\n  var self = this\n  if (self._stream) return\n\n  var so = {}\n  if (self.props.flags) so.flags = self.props.flags\n  so.mode = Writer.filemode\n  if (self._old && self._old.blksize) so.bufferSize = self._old.blksize\n\n  self._stream = fs.createWriteStream(self._path, so)\n\n  self._stream.on('open', function () {\n    // console.error(\"FW open\", self._buffer, self._path)\n    self.ready = true\n    self._buffer.forEach(function (c) {\n      if (c === EOF) self._stream.end()\n      else self._stream.write(c)\n    })\n    self.emit('ready')\n    // give this a kick just in case it needs it.\n    self.emit('drain')\n  })\n\n  self._stream.on('error', function (er) { self.emit('error', er) })\n\n  self._stream.on('drain', function () { self.emit('drain') })\n\n  self._stream.on('close', function () {\n    // console.error('\\n\\nFW Stream Close', self._path, self.size)\n    self._finish()\n  })\n}\n\nFileWriter.prototype.write = function (c) {\n  var self = this\n\n  self._bytesWritten += c.length\n\n  if (!self.ready) {\n    if (!Buffer.isBuffer(c) && typeof c !== 'string') {\n      throw new Error('invalid write data')\n    }\n    self._buffer.push(c)\n    return false\n  }\n\n  var ret = self._stream.write(c)\n  // console.error('\\t-- fw wrote, _stream says', ret, self._stream._queue.length)\n\n  // allow 2 buffered writes, because otherwise there's just too\n  // much stop and go bs.\n  if (ret === false && self._stream._queue) {\n    return self._stream._queue.length <= 2\n  } else {\n    return ret\n  }\n}\n\nFileWriter.prototype.end = function (c) {\n  var self = this\n\n  if (c) self.write(c)\n\n  if (!self.ready) {\n    self._buffer.push(EOF)\n    return false\n  }\n\n  return self._stream.end()\n}\n\nFileWriter.prototype._finish = function () {\n  var self = this\n  if (typeof self.size === 'number' && self._bytesWritten !== self.size) {\n    self.error(\n      'Did not get expected byte count.\\n' +\n      'expect: ' + self.size + '\\n' +\n      'actual: ' + self._bytesWritten)\n  }\n  Writer.prototype._finish.call(self)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/file-writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/get-type.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/get-type.js ***!
  \**********************************************/
/***/ ((module) => {

eval("module.exports = getType\n\nfunction getType (st) {\n  var types = [\n    'Directory',\n    'File',\n    'SymbolicLink',\n    'Link', // special for hardlinks from tarballs\n    'BlockDevice',\n    'CharacterDevice',\n    'FIFO',\n    'Socket'\n  ]\n  var type\n\n  if (st.type && types.indexOf(st.type) !== -1) {\n    st[st.type] = true\n    return st.type\n  }\n\n  for (var i = 0, l = types.length; i < l; i++) {\n    type = types[i]\n    var is = st[type] || st['is' + type]\n    if (typeof is === 'function') is = is.call(st)\n    if (is) {\n      st[type] = true\n      st.type = type\n      return type\n    }\n  }\n\n  return null\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvZ2V0LXR5cGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9DQUFvQyxPQUFPO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJEOlxcRG9jIGRhdGFiYXNlXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxmc3RyZWFtXFxsaWJcXGdldC10eXBlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gZ2V0VHlwZVxuXG5mdW5jdGlvbiBnZXRUeXBlIChzdCkge1xuICB2YXIgdHlwZXMgPSBbXG4gICAgJ0RpcmVjdG9yeScsXG4gICAgJ0ZpbGUnLFxuICAgICdTeW1ib2xpY0xpbmsnLFxuICAgICdMaW5rJywgLy8gc3BlY2lhbCBmb3IgaGFyZGxpbmtzIGZyb20gdGFyYmFsbHNcbiAgICAnQmxvY2tEZXZpY2UnLFxuICAgICdDaGFyYWN0ZXJEZXZpY2UnLFxuICAgICdGSUZPJyxcbiAgICAnU29ja2V0J1xuICBdXG4gIHZhciB0eXBlXG5cbiAgaWYgKHN0LnR5cGUgJiYgdHlwZXMuaW5kZXhPZihzdC50eXBlKSAhPT0gLTEpIHtcbiAgICBzdFtzdC50eXBlXSA9IHRydWVcbiAgICByZXR1cm4gc3QudHlwZVxuICB9XG5cbiAgZm9yICh2YXIgaSA9IDAsIGwgPSB0eXBlcy5sZW5ndGg7IGkgPCBsOyBpKyspIHtcbiAgICB0eXBlID0gdHlwZXNbaV1cbiAgICB2YXIgaXMgPSBzdFt0eXBlXSB8fCBzdFsnaXMnICsgdHlwZV1cbiAgICBpZiAodHlwZW9mIGlzID09PSAnZnVuY3Rpb24nKSBpcyA9IGlzLmNhbGwoc3QpXG4gICAgaWYgKGlzKSB7XG4gICAgICBzdFt0eXBlXSA9IHRydWVcbiAgICAgIHN0LnR5cGUgPSB0eXBlXG4gICAgICByZXR1cm4gdHlwZVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBudWxsXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/get-type.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/link-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basically just a wrapper around an fs.readlink\n//\n// XXX: Enhance this to support the Link type, by keeping\n// a lookup table of {<dev+inode>:<path>}, so that hardlinks\n// can be preserved in tarballs.\n\nmodule.exports = LinkReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\")\n\ninherits(LinkReader, Reader)\n\nfunction LinkReader (props) {\n  var self = this\n  if (!(self instanceof LinkReader)) {\n    throw new Error('LinkReader must be called as constructor.')\n  }\n\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'SymbolicLink' && props.SymbolicLink))) {\n    throw new Error('Non-link type ' + props.type)\n  }\n\n  Reader.call(self, props)\n}\n\n// When piping a LinkReader into a LinkWriter, we have to\n// already have the linkpath property set, so that has to\n// happen *before* the \"ready\" event, which means we need to\n// override the _stat method.\nLinkReader.prototype._stat = function (currentStat) {\n  var self = this\n  fs.readlink(self._path, function (er, linkpath) {\n    if (er) return self.error(er)\n    self.linkpath = self.props.linkpath = linkpath\n    self.emit('linkpath', linkpath)\n    Reader.prototype._stat.call(self, currentStat)\n  })\n}\n\nLinkReader.prototype._read = function () {\n  var self = this\n  if (self._paused) return\n  // basically just a no-op, since we got all the info we need\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end')\n    self.emit('close')\n    self._ended = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvbGluay1yZWFkZXIuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLG1CQUFtQjtBQUN6Qzs7QUFFQTs7QUFFQSxTQUFTLG1CQUFPLENBQUMsb0VBQWE7QUFDOUIsZUFBZSxtQkFBTyxDQUFDLDJEQUFVO0FBQ2pDLGFBQWEsbUJBQU8sQ0FBQywrREFBYTs7QUFFbEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcZnN0cmVhbVxcbGliXFxsaW5rLXJlYWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBCYXNpY2FsbHkganVzdCBhIHdyYXBwZXIgYXJvdW5kIGFuIGZzLnJlYWRsaW5rXG4vL1xuLy8gWFhYOiBFbmhhbmNlIHRoaXMgdG8gc3VwcG9ydCB0aGUgTGluayB0eXBlLCBieSBrZWVwaW5nXG4vLyBhIGxvb2t1cCB0YWJsZSBvZiB7PGRlditpbm9kZT46PHBhdGg+fSwgc28gdGhhdCBoYXJkbGlua3Ncbi8vIGNhbiBiZSBwcmVzZXJ2ZWQgaW4gdGFyYmFsbHMuXG5cbm1vZHVsZS5leHBvcnRzID0gTGlua1JlYWRlclxuXG52YXIgZnMgPSByZXF1aXJlKCdncmFjZWZ1bC1mcycpXG52YXIgaW5oZXJpdHMgPSByZXF1aXJlKCdpbmhlcml0cycpXG52YXIgUmVhZGVyID0gcmVxdWlyZSgnLi9yZWFkZXIuanMnKVxuXG5pbmhlcml0cyhMaW5rUmVhZGVyLCBSZWFkZXIpXG5cbmZ1bmN0aW9uIExpbmtSZWFkZXIgKHByb3BzKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBpZiAoIShzZWxmIGluc3RhbmNlb2YgTGlua1JlYWRlcikpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ0xpbmtSZWFkZXIgbXVzdCBiZSBjYWxsZWQgYXMgY29uc3RydWN0b3IuJylcbiAgfVxuXG4gIGlmICghKChwcm9wcy50eXBlID09PSAnTGluaycgJiYgcHJvcHMuTGluaykgfHxcbiAgICAocHJvcHMudHlwZSA9PT0gJ1N5bWJvbGljTGluaycgJiYgcHJvcHMuU3ltYm9saWNMaW5rKSkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vbi1saW5rIHR5cGUgJyArIHByb3BzLnR5cGUpXG4gIH1cblxuICBSZWFkZXIuY2FsbChzZWxmLCBwcm9wcylcbn1cblxuLy8gV2hlbiBwaXBpbmcgYSBMaW5rUmVhZGVyIGludG8gYSBMaW5rV3JpdGVyLCB3ZSBoYXZlIHRvXG4vLyBhbHJlYWR5IGhhdmUgdGhlIGxpbmtwYXRoIHByb3BlcnR5IHNldCwgc28gdGhhdCBoYXMgdG9cbi8vIGhhcHBlbiAqYmVmb3JlKiB0aGUgXCJyZWFkeVwiIGV2ZW50LCB3aGljaCBtZWFucyB3ZSBuZWVkIHRvXG4vLyBvdmVycmlkZSB0aGUgX3N0YXQgbWV0aG9kLlxuTGlua1JlYWRlci5wcm90b3R5cGUuX3N0YXQgPSBmdW5jdGlvbiAoY3VycmVudFN0YXQpIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIGZzLnJlYWRsaW5rKHNlbGYuX3BhdGgsIGZ1bmN0aW9uIChlciwgbGlua3BhdGgpIHtcbiAgICBpZiAoZXIpIHJldHVybiBzZWxmLmVycm9yKGVyKVxuICAgIHNlbGYubGlua3BhdGggPSBzZWxmLnByb3BzLmxpbmtwYXRoID0gbGlua3BhdGhcbiAgICBzZWxmLmVtaXQoJ2xpbmtwYXRoJywgbGlua3BhdGgpXG4gICAgUmVhZGVyLnByb3RvdHlwZS5fc3RhdC5jYWxsKHNlbGYsIGN1cnJlbnRTdGF0KVxuICB9KVxufVxuXG5MaW5rUmVhZGVyLnByb3RvdHlwZS5fcmVhZCA9IGZ1bmN0aW9uICgpIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIGlmIChzZWxmLl9wYXVzZWQpIHJldHVyblxuICAvLyBiYXNpY2FsbHkganVzdCBhIG5vLW9wLCBzaW5jZSB3ZSBnb3QgYWxsIHRoZSBpbmZvIHdlIG5lZWRcbiAgLy8gZnJvbSB0aGUgX3N0YXQgbWV0aG9kXG4gIGlmICghc2VsZi5fZW5kZWQpIHtcbiAgICBzZWxmLmVtaXQoJ2VuZCcpXG4gICAgc2VsZi5lbWl0KCdjbG9zZScpXG4gICAgc2VsZi5fZW5kZWQgPSB0cnVlXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/link-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/link-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = LinkWriter\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar rimraf = __webpack_require__(/*! rimraf */ \"rimraf\")\n\ninherits(LinkWriter, Writer)\n\nfunction LinkWriter (props) {\n  var self = this\n  if (!(self instanceof LinkWriter)) {\n    throw new Error('LinkWriter must be called as constructor.')\n  }\n\n  // should already be established as a Link type\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'SymbolicLink' && props.SymbolicLink))) {\n    throw new Error('Non-link type ' + props.type)\n  }\n\n  if (props.linkpath === '') props.linkpath = '.'\n  if (!props.linkpath) {\n    self.error('Need linkpath property to create ' + props.type)\n  }\n\n  Writer.call(this, props)\n}\n\nLinkWriter.prototype._create = function () {\n  // console.error(\" LW _create\")\n  var self = this\n  var hard = self.type === 'Link' || process.platform === 'win32'\n  var link = hard ? 'link' : 'symlink'\n  var lp = hard ? path.resolve(self.dirname, self.linkpath) : self.linkpath\n\n  // can only change the link path by clobbering\n  // For hard links, let's just assume that's always the case, since\n  // there's no good way to read them if we don't already know.\n  if (hard) return clobber(self, lp, link)\n\n  fs.readlink(self._path, function (er, p) {\n    // only skip creation if it's exactly the same link\n    if (p && p === lp) return finish(self)\n    clobber(self, lp, link)\n  })\n}\n\nfunction clobber (self, lp, link) {\n  rimraf(self._path, function (er) {\n    if (er) return self.error(er)\n    create(self, lp, link)\n  })\n}\n\nfunction create (self, lp, link) {\n  fs[link](lp, self._path, function (er) {\n    // if this is a hard link, and we're in the process of writing out a\n    // directory, it's very possible that the thing we're linking to\n    // doesn't exist yet (especially if it was intended as a symlink),\n    // so swallow ENOENT errors here and just soldier in.\n    // Additionally, an EPERM or EACCES can happen on win32 if it's trying\n    // to make a link to a directory.  Again, just skip it.\n    // A better solution would be to have fs.symlink be supported on\n    // windows in some nice fashion.\n    if (er) {\n      if ((er.code === 'ENOENT' ||\n        er.code === 'EACCES' ||\n        er.code === 'EPERM') && process.platform === 'win32') {\n        self.ready = true\n        self.emit('ready')\n        self.emit('end')\n        self.emit('close')\n        self.end = self._finish = function () {}\n      } else return self.error(er)\n    }\n    finish(self)\n  })\n}\n\nfunction finish (self) {\n  self.ready = true\n  self.emit('ready')\n  if (self._ended && !self._finished) self._finish()\n}\n\nLinkWriter.prototype.end = function () {\n  // console.error(\"LW finish in end\")\n  this._ended = true\n  if (this.ready) {\n    this._finished = true\n    this._finish()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/link-writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/proxy-reader.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-reader.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A reader for when we don't yet know what kind of thing\n// the thing is.\n\nmodule.exports = ProxyReader\n\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(rsc)/./node_modules/fstream/lib/get-type.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\")\n\ninherits(ProxyReader, Reader)\n\nfunction ProxyReader (props) {\n  var self = this\n  if (!(self instanceof ProxyReader)) {\n    throw new Error('ProxyReader must be called as constructor.')\n  }\n\n  self.props = props\n  self._buffer = []\n  self.ready = false\n\n  Reader.call(self, props)\n}\n\nProxyReader.prototype._stat = function () {\n  var self = this\n  var props = self.props\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat'\n\n  fs[stat](props.path, function (er, current) {\n    var type\n    if (er || !current) {\n      type = 'File'\n    } else {\n      type = getType(current)\n    }\n\n    props[type] = true\n    props.type = self.type = type\n\n    self._old = current\n    self._addProxy(Reader(props, current))\n  })\n}\n\nProxyReader.prototype._addProxy = function (proxy) {\n  var self = this\n  if (self._proxyTarget) {\n    return self.error('proxy already set')\n  }\n\n  self._proxyTarget = proxy\n  proxy._proxy = self\n\n  ;[\n    'error',\n    'data',\n    'end',\n    'close',\n    'linkpath',\n    'entry',\n    'entryEnd',\n    'child',\n    'childEnd',\n    'warn',\n    'stat'\n  ].forEach(function (ev) {\n    // console.error('~~ proxy event', ev, self.path)\n    proxy.on(ev, self.emit.bind(self, ev))\n  })\n\n  self.emit('proxy', proxy)\n\n  proxy.on('ready', function () {\n    // console.error(\"~~ proxy is ready!\", self.path)\n    self.ready = true\n    self.emit('ready')\n  })\n\n  var calls = self._buffer\n  self._buffer.length = 0\n  calls.forEach(function (c) {\n    proxy[c[0]].apply(proxy, c[1])\n  })\n}\n\nProxyReader.prototype.pause = function () {\n  return this._proxyTarget ? this._proxyTarget.pause() : false\n}\n\nProxyReader.prototype.resume = function () {\n  return this._proxyTarget ? this._proxyTarget.resume() : false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/proxy-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/proxy-writer.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-writer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A writer for when we don't know what kind of thing\n// the thing is.  That is, it's not explicitly set,\n// so we're going to make it whatever the thing already\n// is, or \"File\"\n//\n// Until then, collect all events.\n\nmodule.exports = ProxyWriter\n\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(rsc)/./node_modules/fstream/lib/writer.js\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(rsc)/./node_modules/fstream/lib/get-type.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar collect = __webpack_require__(/*! ./collect.js */ \"(rsc)/./node_modules/fstream/lib/collect.js\")\nvar fs = __webpack_require__(/*! fs */ \"fs\")\n\ninherits(ProxyWriter, Writer)\n\nfunction ProxyWriter (props) {\n  var self = this\n  if (!(self instanceof ProxyWriter)) {\n    throw new Error('ProxyWriter must be called as constructor.')\n  }\n\n  self.props = props\n  self._needDrain = false\n\n  Writer.call(self, props)\n}\n\nProxyWriter.prototype._stat = function () {\n  var self = this\n  var props = self.props\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat'\n\n  fs[stat](props.path, function (er, current) {\n    var type\n    if (er || !current) {\n      type = 'File'\n    } else {\n      type = getType(current)\n    }\n\n    props[type] = true\n    props.type = self.type = type\n\n    self._old = current\n    self._addProxy(Writer(props, current))\n  })\n}\n\nProxyWriter.prototype._addProxy = function (proxy) {\n  // console.error(\"~~ set proxy\", this.path)\n  var self = this\n  if (self._proxy) {\n    return self.error('proxy already set')\n  }\n\n  self._proxy = proxy\n  ;[\n    'ready',\n    'error',\n    'close',\n    'pipe',\n    'drain',\n    'warn'\n  ].forEach(function (ev) {\n    proxy.on(ev, self.emit.bind(self, ev))\n  })\n\n  self.emit('proxy', proxy)\n\n  var calls = self._buffer\n  calls.forEach(function (c) {\n    // console.error(\"~~ ~~ proxy buffered call\", c[0], c[1])\n    proxy[c[0]].apply(proxy, c[1])\n  })\n  self._buffer.length = 0\n  if (self._needsDrain) self.emit('drain')\n}\n\nProxyWriter.prototype.add = function (entry) {\n  // console.error(\"~~ proxy add\")\n  collect(entry)\n\n  if (!this._proxy) {\n    this._buffer.push(['add', [entry]])\n    this._needDrain = true\n    return false\n  }\n  return this._proxy.add(entry)\n}\n\nProxyWriter.prototype.write = function (c) {\n  // console.error('~~ proxy write')\n  if (!this._proxy) {\n    this._buffer.push(['write', [c]])\n    this._needDrain = true\n    return false\n  }\n  return this._proxy.write(c)\n}\n\nProxyWriter.prototype.end = function (c) {\n  // console.error('~~ proxy end')\n  if (!this._proxy) {\n    this._buffer.push(['end', [c]])\n    return false\n  }\n  return this._proxy.end(c)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/proxy-writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/reader.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/reader.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Reader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(rsc)/./node_modules/fstream/lib/get-type.js\")\nvar hardLinks = Reader.hardLinks = {}\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(rsc)/./node_modules/fstream/lib/abstract.js\")\n\n// Must do this *before* loading the child classes\ninherits(Reader, Abstract)\n\nvar LinkReader = __webpack_require__(/*! ./link-reader.js */ \"(rsc)/./node_modules/fstream/lib/link-reader.js\")\n\nfunction Reader (props, currentStat) {\n  var self = this\n  if (!(self instanceof Reader)) return new Reader(props, currentStat)\n\n  if (typeof props === 'string') {\n    props = { path: props }\n  }\n\n  // polymorphism.\n  // call fstream.Reader(dir) to get a DirReader object, etc.\n  // Note that, unlike in the Writer case, ProxyReader is going\n  // to be the *normal* state of affairs, since we rarely know\n  // the type of a file prior to reading it.\n\n  var type\n  var ClassType\n\n  if (props.type && typeof props.type === 'function') {\n    type = props.type\n    ClassType = type\n  } else {\n    type = getType(props)\n    ClassType = Reader\n  }\n\n  if (currentStat && !type) {\n    type = getType(currentStat)\n    props[type] = true\n    props.type = type\n  }\n\n  switch (type) {\n    case 'Directory':\n      ClassType = __webpack_require__(/*! ./dir-reader.js */ \"(rsc)/./node_modules/fstream/lib/dir-reader.js\")\n      break\n\n    case 'Link':\n    // XXX hard links are just files.\n    // However, it would be good to keep track of files' dev+inode\n    // and nlink values, and create a HardLinkReader that emits\n    // a linkpath value of the original copy, so that the tar\n    // writer can preserve them.\n    // ClassType = HardLinkReader\n    // break\n\n    case 'File':\n      ClassType = __webpack_require__(/*! ./file-reader.js */ \"(rsc)/./node_modules/fstream/lib/file-reader.js\")\n      break\n\n    case 'SymbolicLink':\n      ClassType = LinkReader\n      break\n\n    case 'Socket':\n      ClassType = __webpack_require__(/*! ./socket-reader.js */ \"(rsc)/./node_modules/fstream/lib/socket-reader.js\")\n      break\n\n    case null:\n      ClassType = __webpack_require__(/*! ./proxy-reader.js */ \"(rsc)/./node_modules/fstream/lib/proxy-reader.js\")\n      break\n  }\n\n  if (!(self instanceof ClassType)) {\n    return new ClassType(props)\n  }\n\n  Abstract.call(self)\n\n  if (!props.path) {\n    self.error('Must provide a path', null, true)\n  }\n\n  self.readable = true\n  self.writable = false\n\n  self.type = type\n  self.props = props\n  self.depth = props.depth = props.depth || 0\n  self.parent = props.parent || null\n  self.root = props.root || (props.parent && props.parent.root) || self\n\n  self._path = self.path = path.resolve(props.path)\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_')\n    if (self._path.length >= 260) {\n      // how DOES one create files on the moon?\n      // if the path has spaces in it, then UNC will fail.\n      self._swallowErrors = true\n      // if (self._path.indexOf(\" \") === -1) {\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\')\n    // }\n    }\n  }\n  self.basename = props.basename = path.basename(self.path)\n  self.dirname = props.dirname = path.dirname(self.path)\n\n  // these have served their purpose, and are now just noisy clutter\n  props.parent = props.root = null\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size\n  self.filter = typeof props.filter === 'function' ? props.filter : null\n  if (props.sort === 'alpha') props.sort = alphasort\n\n  // start the ball rolling.\n  // this will stat the thing, and then call self._read()\n  // to start reading whatever it is.\n  // console.error(\"calling stat\", props.path, currentStat)\n  self._stat(currentStat)\n}\n\nfunction alphasort (a, b) {\n  return a === b ? 0\n    : a.toLowerCase() > b.toLowerCase() ? 1\n      : a.toLowerCase() < b.toLowerCase() ? -1\n        : a > b ? 1\n          : -1\n}\n\nReader.prototype._stat = function (currentStat) {\n  var self = this\n  var props = self.props\n  var stat = props.follow ? 'stat' : 'lstat'\n  // console.error(\"Reader._stat\", self._path, currentStat)\n  if (currentStat) process.nextTick(statCb.bind(null, null, currentStat))\n  else fs[stat](self._path, statCb)\n\n  function statCb (er, props_) {\n    // console.error(\"Reader._stat, statCb\", self._path, props_, props_.nlink)\n    if (er) return self.error(er)\n\n    Object.keys(props_).forEach(function (k) {\n      props[k] = props_[k]\n    })\n\n    // if it's not the expected size, then abort here.\n    if (undefined !== self.size && props.size !== self.size) {\n      return self.error('incorrect size')\n    }\n    self.size = props.size\n\n    var type = getType(props)\n    var handleHardlinks = props.hardlinks !== false\n\n    // special little thing for handling hardlinks.\n    if (handleHardlinks && type !== 'Directory' && props.nlink && props.nlink > 1) {\n      var k = props.dev + ':' + props.ino\n      // console.error(\"Reader has nlink\", self._path, k)\n      if (hardLinks[k] === self._path || !hardLinks[k]) {\n        hardLinks[k] = self._path\n      } else {\n        // switch into hardlink mode.\n        type = self.type = self.props.type = 'Link'\n        self.Link = self.props.Link = true\n        self.linkpath = self.props.linkpath = hardLinks[k]\n        // console.error(\"Hardlink detected, switching mode\", self._path, self.linkpath)\n        // Setting __proto__ would arguably be the \"correct\"\n        // approach here, but that just seems too wrong.\n        self._stat = self._read = LinkReader.prototype._read\n      }\n    }\n\n    if (self.type && self.type !== type) {\n      self.error('Unexpected type: ' + type)\n    }\n\n    // if the filter doesn't pass, then just skip over this one.\n    // still have to emit end so that dir-walking can move on.\n    if (self.filter) {\n      var who = self._proxy || self\n      // special handling for ProxyReaders\n      if (!self.filter.call(who, who, props)) {\n        if (!self._disowned) {\n          self.abort()\n          self.emit('end')\n          self.emit('close')\n        }\n        return\n      }\n    }\n\n    // last chance to abort or disown before the flow starts!\n    var events = ['_stat', 'stat', 'ready']\n    var e = 0\n    ;(function go () {\n      if (self._aborted) {\n        self.emit('end')\n        self.emit('close')\n        return\n      }\n\n      if (self._paused && self.type !== 'Directory') {\n        self.once('resume', go)\n        return\n      }\n\n      var ev = events[e++]\n      if (!ev) {\n        return self._read()\n      }\n      self.emit(ev, props)\n      go()\n    })()\n  }\n}\n\nReader.prototype.pipe = function (dest) {\n  var self = this\n  if (typeof dest.add === 'function') {\n    // piping to a multi-compatible, and we've got directory entries.\n    self.on('entry', function (entry) {\n      var ret = dest.add(entry)\n      if (ret === false) {\n        self.pause()\n      }\n    })\n  }\n\n  // console.error(\"R Pipe apply Stream Pipe\")\n  return Stream.prototype.pipe.apply(this, arguments)\n}\n\nReader.prototype.pause = function (who) {\n  this._paused = true\n  who = who || this\n  this.emit('pause', who)\n  if (this._stream) this._stream.pause(who)\n}\n\nReader.prototype.resume = function (who) {\n  this._paused = false\n  who = who || this\n  this.emit('resume', who)\n  if (this._stream) this._stream.resume(who)\n  this._read()\n}\n\nReader.prototype._read = function () {\n  this.error('Cannot read unknown type: ' + this.type)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/socket-reader.js":
/*!***************************************************!*\
  !*** ./node_modules/fstream/lib/socket-reader.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Just get the stats, and then don't do anything.\n// You can't really \"read\" from a socket.  You \"connect\" to it.\n// Mostly, this is here so that reading a dir with a socket in it\n// doesn't blow up.\n\nmodule.exports = SocketReader\n\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(rsc)/./node_modules/fstream/lib/reader.js\")\n\ninherits(SocketReader, Reader)\n\nfunction SocketReader (props) {\n  var self = this\n  if (!(self instanceof SocketReader)) {\n    throw new Error('SocketReader must be called as constructor.')\n  }\n\n  if (!(props.type === 'Socket' && props.Socket)) {\n    throw new Error('Non-socket type ' + props.type)\n  }\n\n  Reader.call(self, props)\n}\n\nSocketReader.prototype._read = function () {\n  var self = this\n  if (self._paused) return\n  // basically just a no-op, since we got all the info we have\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end')\n    self.emit('close')\n    self._ended = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvc29ja2V0LXJlYWRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxlQUFlLG1CQUFPLENBQUMsMkRBQVU7QUFDakMsYUFBYSxtQkFBTyxDQUFDLCtEQUFhOztBQUVsQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXGZzdHJlYW1cXGxpYlxcc29ja2V0LXJlYWRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBKdXN0IGdldCB0aGUgc3RhdHMsIGFuZCB0aGVuIGRvbid0IGRvIGFueXRoaW5nLlxuLy8gWW91IGNhbid0IHJlYWxseSBcInJlYWRcIiBmcm9tIGEgc29ja2V0LiAgWW91IFwiY29ubmVjdFwiIHRvIGl0LlxuLy8gTW9zdGx5LCB0aGlzIGlzIGhlcmUgc28gdGhhdCByZWFkaW5nIGEgZGlyIHdpdGggYSBzb2NrZXQgaW4gaXRcbi8vIGRvZXNuJ3QgYmxvdyB1cC5cblxubW9kdWxlLmV4cG9ydHMgPSBTb2NrZXRSZWFkZXJcblxudmFyIGluaGVyaXRzID0gcmVxdWlyZSgnaW5oZXJpdHMnKVxudmFyIFJlYWRlciA9IHJlcXVpcmUoJy4vcmVhZGVyLmpzJylcblxuaW5oZXJpdHMoU29ja2V0UmVhZGVyLCBSZWFkZXIpXG5cbmZ1bmN0aW9uIFNvY2tldFJlYWRlciAocHJvcHMpIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIGlmICghKHNlbGYgaW5zdGFuY2VvZiBTb2NrZXRSZWFkZXIpKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdTb2NrZXRSZWFkZXIgbXVzdCBiZSBjYWxsZWQgYXMgY29uc3RydWN0b3IuJylcbiAgfVxuXG4gIGlmICghKHByb3BzLnR5cGUgPT09ICdTb2NrZXQnICYmIHByb3BzLlNvY2tldCkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ05vbi1zb2NrZXQgdHlwZSAnICsgcHJvcHMudHlwZSlcbiAgfVxuXG4gIFJlYWRlci5jYWxsKHNlbGYsIHByb3BzKVxufVxuXG5Tb2NrZXRSZWFkZXIucHJvdG90eXBlLl9yZWFkID0gZnVuY3Rpb24gKCkge1xuICB2YXIgc2VsZiA9IHRoaXNcbiAgaWYgKHNlbGYuX3BhdXNlZCkgcmV0dXJuXG4gIC8vIGJhc2ljYWxseSBqdXN0IGEgbm8tb3AsIHNpbmNlIHdlIGdvdCBhbGwgdGhlIGluZm8gd2UgaGF2ZVxuICAvLyBmcm9tIHRoZSBfc3RhdCBtZXRob2RcbiAgaWYgKCFzZWxmLl9lbmRlZCkge1xuICAgIHNlbGYuZW1pdCgnZW5kJylcbiAgICBzZWxmLmVtaXQoJ2Nsb3NlJylcbiAgICBzZWxmLl9lbmRlZCA9IHRydWVcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/socket-reader.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/lib/writer.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/writer.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Writer\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(rsc)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar rimraf = __webpack_require__(/*! rimraf */ \"rimraf\")\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(rsc)/./node_modules/fstream/node_modules/mkdirp/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar umask = process.platform === 'win32' ? 0 : process.umask()\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(rsc)/./node_modules/fstream/lib/get-type.js\")\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(rsc)/./node_modules/fstream/lib/abstract.js\")\n\n// Must do this *before* loading the child classes\ninherits(Writer, Abstract)\n\nWriter.dirmode = parseInt('0777', 8) & (~umask)\nWriter.filemode = parseInt('0666', 8) & (~umask)\n\nvar DirWriter = __webpack_require__(/*! ./dir-writer.js */ \"(rsc)/./node_modules/fstream/lib/dir-writer.js\")\nvar LinkWriter = __webpack_require__(/*! ./link-writer.js */ \"(rsc)/./node_modules/fstream/lib/link-writer.js\")\nvar FileWriter = __webpack_require__(/*! ./file-writer.js */ \"(rsc)/./node_modules/fstream/lib/file-writer.js\")\nvar ProxyWriter = __webpack_require__(/*! ./proxy-writer.js */ \"(rsc)/./node_modules/fstream/lib/proxy-writer.js\")\n\n// props is the desired state.  current is optionally the current stat,\n// provided here so that subclasses can avoid statting the target\n// more than necessary.\nfunction Writer (props, current) {\n  var self = this\n\n  if (typeof props === 'string') {\n    props = { path: props }\n  }\n\n  // polymorphism.\n  // call fstream.Writer(dir) to get a DirWriter object, etc.\n  var type = getType(props)\n  var ClassType = Writer\n\n  switch (type) {\n    case 'Directory':\n      ClassType = DirWriter\n      break\n    case 'File':\n      ClassType = FileWriter\n      break\n    case 'Link':\n    case 'SymbolicLink':\n      ClassType = LinkWriter\n      break\n    case null:\n    default:\n      // Don't know yet what type to create, so we wrap in a proxy.\n      ClassType = ProxyWriter\n      break\n  }\n\n  if (!(self instanceof ClassType)) return new ClassType(props)\n\n  // now get down to business.\n\n  Abstract.call(self)\n\n  if (!props.path) self.error('Must provide a path', null, true)\n\n  // props is what we want to set.\n  // set some convenience properties as well.\n  self.type = props.type\n  self.props = props\n  self.depth = props.depth || 0\n  self.clobber = props.clobber === false ? props.clobber : true\n  self.parent = props.parent || null\n  self.root = props.root || (props.parent && props.parent.root) || self\n\n  self._path = self.path = path.resolve(props.path)\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_')\n    if (self._path.length >= 260) {\n      self._swallowErrors = true\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\')\n    }\n  }\n  self.basename = path.basename(props.path)\n  self.dirname = path.dirname(props.path)\n  self.linkpath = props.linkpath || null\n\n  props.parent = props.root = null\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size\n\n  if (typeof props.mode === 'string') {\n    props.mode = parseInt(props.mode, 8)\n  }\n\n  self.readable = false\n  self.writable = true\n\n  // buffer until ready, or while handling another entry\n  self._buffer = []\n  self.ready = false\n\n  self.filter = typeof props.filter === 'function' ? props.filter : null\n\n  // start the ball rolling.\n  // this checks what's there already, and then calls\n  // self._create() to call the impl-specific creation stuff.\n  self._stat(current)\n}\n\n// Calling this means that it's something we can't create.\n// Just assert that it's already there, otherwise raise a warning.\nWriter.prototype._create = function () {\n  var self = this\n  fs[self.props.follow ? 'stat' : 'lstat'](self._path, function (er) {\n    if (er) {\n      return self.warn('Cannot create ' + self._path + '\\n' +\n        'Unsupported type: ' + self.type, 'ENOTSUP')\n    }\n    self._finish()\n  })\n}\n\nWriter.prototype._stat = function (current) {\n  var self = this\n  var props = self.props\n  var stat = props.follow ? 'stat' : 'lstat'\n  var who = self._proxy || self\n\n  if (current) statCb(null, current)\n  else fs[stat](self._path, statCb)\n\n  function statCb (er, current) {\n    if (self.filter && !self.filter.call(who, who, current)) {\n      self._aborted = true\n      self.emit('end')\n      self.emit('close')\n      return\n    }\n\n    // if it's not there, great.  We'll just create it.\n    // if it is there, then we'll need to change whatever differs\n    if (er || !current) {\n      return create(self)\n    }\n\n    self._old = current\n    var currentType = getType(current)\n\n    // if it's a type change, then we need to clobber or error.\n    // if it's not a type change, then let the impl take care of it.\n    if (currentType !== self.type || self.type === 'File' && current.nlink > 1) {\n      return rimraf(self._path, function (er) {\n        if (er) return self.error(er)\n        self._old = null\n        create(self)\n      })\n    }\n\n    // otherwise, just handle in the app-specific way\n    // this creates a fs.WriteStream, or mkdir's, or whatever\n    create(self)\n  }\n}\n\nfunction create (self) {\n  // console.error(\"W create\", self._path, Writer.dirmode)\n\n  // XXX Need to clobber non-dirs that are in the way,\n  // unless { clobber: false } in the props.\n  mkdir(path.dirname(self._path), Writer.dirmode, function (er, made) {\n    // console.error(\"W created\", path.dirname(self._path), er)\n    if (er) return self.error(er)\n\n    // later on, we have to set the mode and owner for these\n    self._madeDir = made\n    return self._create()\n  })\n}\n\nfunction endChmod (self, want, current, path, cb) {\n  var wantMode = want.mode\n  var chmod = want.follow || self.type !== 'SymbolicLink'\n    ? 'chmod' : 'lchmod'\n\n  if (!fs[chmod]) return cb()\n  if (typeof wantMode !== 'number') return cb()\n\n  var curMode = current.mode & parseInt('0777', 8)\n  wantMode = wantMode & parseInt('0777', 8)\n  if (wantMode === curMode) return cb()\n\n  fs[chmod](path, wantMode, cb)\n}\n\nfunction endChown (self, want, current, path, cb) {\n  // Don't even try it unless root.  Too easy to EPERM.\n  if (process.platform === 'win32') return cb()\n  if (!process.getuid || process.getuid() !== 0) return cb()\n  if (typeof want.uid !== 'number' &&\n    typeof want.gid !== 'number') return cb()\n\n  if (current.uid === want.uid &&\n    current.gid === want.gid) return cb()\n\n  var chown = (self.props.follow || self.type !== 'SymbolicLink')\n    ? 'chown' : 'lchown'\n  if (!fs[chown]) return cb()\n\n  if (typeof want.uid !== 'number') want.uid = current.uid\n  if (typeof want.gid !== 'number') want.gid = current.gid\n\n  fs[chown](path, want.uid, want.gid, cb)\n}\n\nfunction endUtimes (self, want, current, path, cb) {\n  if (!fs.utimes || process.platform === 'win32') return cb()\n\n  var utimes = (want.follow || self.type !== 'SymbolicLink')\n    ? 'utimes' : 'lutimes'\n\n  if (utimes === 'lutimes' && !fs[utimes]) {\n    utimes = 'utimes'\n  }\n\n  if (!fs[utimes]) return cb()\n\n  var curA = current.atime\n  var curM = current.mtime\n  var meA = want.atime\n  var meM = want.mtime\n\n  if (meA === undefined) meA = curA\n  if (meM === undefined) meM = curM\n\n  if (!isDate(meA)) meA = new Date(meA)\n  if (!isDate(meM)) meA = new Date(meM)\n\n  if (meA.getTime() === curA.getTime() &&\n    meM.getTime() === curM.getTime()) return cb()\n\n  fs[utimes](path, meA, meM, cb)\n}\n\n// XXX This function is beastly.  Break it up!\nWriter.prototype._finish = function () {\n  var self = this\n\n  if (self._finishing) return\n  self._finishing = true\n\n  // console.error(\" W Finish\", self._path, self.size)\n\n  // set up all the things.\n  // At this point, we're already done writing whatever we've gotta write,\n  // adding files to the dir, etc.\n  var todo = 0\n  var errState = null\n  var done = false\n\n  if (self._old) {\n    // the times will almost *certainly* have changed.\n    // adds the utimes syscall, but remove another stat.\n    self._old.atime = new Date(0)\n    self._old.mtime = new Date(0)\n    // console.error(\" W Finish Stale Stat\", self._path, self.size)\n    setProps(self._old)\n  } else {\n    var stat = self.props.follow ? 'stat' : 'lstat'\n    // console.error(\" W Finish Stating\", self._path, self.size)\n    fs[stat](self._path, function (er, current) {\n      // console.error(\" W Finish Stated\", self._path, self.size, current)\n      if (er) {\n        // if we're in the process of writing out a\n        // directory, it's very possible that the thing we're linking to\n        // doesn't exist yet (especially if it was intended as a symlink),\n        // so swallow ENOENT errors here and just soldier on.\n        if (er.code === 'ENOENT' &&\n          (self.type === 'Link' || self.type === 'SymbolicLink') &&\n          process.platform === 'win32') {\n          self.ready = true\n          self.emit('ready')\n          self.emit('end')\n          self.emit('close')\n          self.end = self._finish = function () {}\n          return\n        } else return self.error(er)\n      }\n      setProps(self._old = current)\n    })\n  }\n\n  return\n\n  function setProps (current) {\n    todo += 3\n    endChmod(self, self.props, current, self._path, next('chmod'))\n    endChown(self, self.props, current, self._path, next('chown'))\n    endUtimes(self, self.props, current, self._path, next('utimes'))\n  }\n\n  function next (what) {\n    return function (er) {\n      // console.error(\"   W Finish\", what, todo)\n      if (errState) return\n      if (er) {\n        er.fstream_finish_call = what\n        return self.error(errState = er)\n      }\n      if (--todo > 0) return\n      if (done) return\n      done = true\n\n      // we may still need to set the mode/etc. on some parent dirs\n      // that were created previously.  delay end/close until then.\n      if (!self._madeDir) return end()\n      else endMadeDir(self, self._path, end)\n\n      function end (er) {\n        if (er) {\n          er.fstream_finish_call = 'setupMadeDir'\n          return self.error(er)\n        }\n        // all the props have been set, so we're completely done.\n        self.emit('end')\n        self.emit('close')\n      }\n    }\n  }\n}\n\nfunction endMadeDir (self, p, cb) {\n  var made = self._madeDir\n  // everything *between* made and path.dirname(self._path)\n  // needs to be set up.  Note that this may just be one dir.\n  var d = path.dirname(p)\n\n  endMadeDir_(self, d, function (er) {\n    if (er) return cb(er)\n    if (d === made) {\n      return cb()\n    }\n    endMadeDir(self, d, cb)\n  })\n}\n\nfunction endMadeDir_ (self, p, cb) {\n  var dirProps = {}\n  Object.keys(self.props).forEach(function (k) {\n    dirProps[k] = self.props[k]\n\n    // only make non-readable dirs if explicitly requested.\n    if (k === 'mode' && self.type !== 'Directory') {\n      dirProps[k] = dirProps[k] | parseInt('0111', 8)\n    }\n  })\n\n  var todo = 3\n  var errState = null\n  fs.stat(p, function (er, current) {\n    if (er) return cb(errState = er)\n    endChmod(self, dirProps, current, p, next)\n    endChown(self, dirProps, current, p, next)\n    endUtimes(self, dirProps, current, p, next)\n  })\n\n  function next (er) {\n    if (errState) return\n    if (er) return cb(errState = er)\n    if (--todo === 0) return cb()\n  }\n}\n\nWriter.prototype.pipe = function () {\n  this.error(\"Can't pipe from writable stream\")\n}\n\nWriter.prototype.add = function () {\n  this.error(\"Can't add to non-Directory type\")\n}\n\nWriter.prototype.write = function () {\n  return true\n}\n\nfunction objectToString (d) {\n  return Object.prototype.toString.call(d)\n}\n\nfunction isDate (d) {\n  return typeof d === 'object' && objectToString(d) === '[object Date]'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/lib/writer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/fstream/node_modules/mkdirp/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/fstream/node_modules/mkdirp/index.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var path = __webpack_require__(/*! path */ \"path\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar _0777 = parseInt('0777', 8);\n\nmodule.exports = mkdirP.mkdirp = mkdirP.mkdirP = mkdirP;\n\nfunction mkdirP (p, opts, f, made) {\n    if (typeof opts === 'function') {\n        f = opts;\n        opts = {};\n    }\n    else if (!opts || typeof opts !== 'object') {\n        opts = { mode: opts };\n    }\n    \n    var mode = opts.mode;\n    var xfs = opts.fs || fs;\n    \n    if (mode === undefined) {\n        mode = _0777\n    }\n    if (!made) made = null;\n    \n    var cb = f || /* istanbul ignore next */ function () {};\n    p = path.resolve(p);\n    \n    xfs.mkdir(p, mode, function (er) {\n        if (!er) {\n            made = made || p;\n            return cb(null, made);\n        }\n        switch (er.code) {\n            case 'ENOENT':\n                /* istanbul ignore if */\n                if (path.dirname(p) === p) return cb(er);\n                mkdirP(path.dirname(p), opts, function (er, made) {\n                    /* istanbul ignore if */\n                    if (er) cb(er, made);\n                    else mkdirP(p, opts, cb, made);\n                });\n                break;\n\n            // In the case of any other error, just see if there's a dir\n            // there already.  If so, then hooray!  If not, then something\n            // is borked.\n            default:\n                xfs.stat(p, function (er2, stat) {\n                    // if the stat fails, then that's super weird.\n                    // let the original error be the failure reason.\n                    if (er2 || !stat.isDirectory()) cb(er, made)\n                    else cb(null, made);\n                });\n                break;\n        }\n    });\n}\n\nmkdirP.sync = function sync (p, opts, made) {\n    if (!opts || typeof opts !== 'object') {\n        opts = { mode: opts };\n    }\n    \n    var mode = opts.mode;\n    var xfs = opts.fs || fs;\n    \n    if (mode === undefined) {\n        mode = _0777\n    }\n    if (!made) made = null;\n\n    p = path.resolve(p);\n\n    try {\n        xfs.mkdirSync(p, mode);\n        made = made || p;\n    }\n    catch (err0) {\n        switch (err0.code) {\n            case 'ENOENT' :\n                made = sync(path.dirname(p), opts, made);\n                sync(p, opts, made);\n                break;\n\n            // In the case of any other error, just see if there's a dir\n            // there already.  If so, then hooray!  If not, then something\n            // is borked.\n            default:\n                var stat;\n                try {\n                    stat = xfs.statSync(p);\n                }\n                catch (err1) /* istanbul ignore next */ {\n                    throw err0;\n                }\n                /* istanbul ignore if */\n                if (!stat.isDirectory()) throw err0;\n                break;\n        }\n    }\n\n    return made;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fstream/node_modules/mkdirp/index.js\n");

/***/ })

};
;