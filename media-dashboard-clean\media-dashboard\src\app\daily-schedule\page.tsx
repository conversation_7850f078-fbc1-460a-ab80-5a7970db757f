'use client';

import React, { useState, useEffect } from 'react';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import './daily-schedule.css';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  duration: string;
  segments?: Segment[];
  episodeNumber?: number;
  seasonNumber?: number;
  partNumber?: number;
}

interface Segment {
  id: string;
  name: string;
  timeIn: string;
  timeOut: string;
  duration: string;
  segmentCode: string;
}

interface ScheduleItem {
  id: string;
  mediaItemId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isRerun: boolean;
  mediaItem: MediaItem;
}

interface GridRow {
  id: string;
  type: 'segment' | 'filler' | 'empty';
  time?: string;
  content?: string;
  mediaItemId?: string;
  segmentId?: string;
  segmentCode?: string; // كود السيجمنت للسيرفرات
  duration?: string;
  canDelete?: boolean;
  isRerun?: boolean;
  isTemporary?: boolean;
  originalStartTime?: string;
  targetTime?: string;
}

export default function DailySchedulePage() {
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([]);
  const [availableMedia, setAvailableMedia] = useState<MediaItem[]>([]);
  const [gridRows, setGridRows] = useState<GridRow[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('ALL');
  const [loading, setLoading] = useState(false);
  const [weeklySchedule, setWeeklySchedule] = useState<any[]>([]);
  const [showWeeklySchedule, setShowWeeklySchedule] = useState(false);

  // تهيئة التاريخ الحالي
  useEffect(() => {
    const today = new Date();
    setSelectedDate(today.toISOString().split('T')[0]);
  }, []);

  // جلب البيانات عند تغيير التاريخ
  useEffect(() => {
    if (selectedDate) {
      fetchScheduleData();
    }
  }, [selectedDate]);

  // جلب بيانات الجدول الإذاعي
  const fetchScheduleData = async () => {
    setLoading(true);
    try {
      console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);

      const response = await fetch(`/api/daily-schedule?date=${selectedDate}`);
      const data = await response.json();

      if (data.success) {
        setScheduleItems(data.data.scheduleItems);
        setAvailableMedia(data.data.availableMedia || []);
        setGridRows(data.data.scheduleRows || []);

        if (data.fromSavedFile) {
          console.log('📂 تم تحميل جدول محفوظ مسبقاً');
          console.log('💾 تاريخ الحفظ:', data.savedAt);
          console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);
        } else {
          console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');
          console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');
        }

        console.log('📦 المواد المتاحة:', data.data.availableMedia?.length || 0);

        // عرض عينة من المواد المتاحة
        if (data.data.availableMedia && data.data.availableMedia.length > 0) {
          console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));
        }
      }

      // جلب الجدول الأسبوعي للمراجعة
      await fetchWeeklySchedule();
    } catch (error) {
      console.error('❌ خطأ في جلب البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  // جلب الجدول الأسبوعي للمراجعة
  const fetchWeeklySchedule = async () => {
    try {
      const date = new Date(selectedDate);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekStartStr = weekStart.toISOString().split('T')[0];

      const response = await fetch(`/api/weekly-schedule?weekStart=${weekStartStr}`);
      const data = await response.json();

      console.log('📊 استجابة API للجدول الأسبوعي:', data);

      if (data.success && data.data) {
        // استخراج مواد اليوم المحدد من scheduleItems
        const dayOfWeek = new Date(selectedDate).getDay();
        const daySchedule = [];

        console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);
        console.log('📦 البيانات المتاحة:', data.data);

        // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)
        if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {
          console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);

          const dayItems = data.data.scheduleItems
            .filter((item: any) => {
              console.log('🔍 فحص المادة:', item.mediaItem?.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);
              return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء
            })
            .sort((a: any, b: any) => {
              // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59
              const timeA = a.startTime;
              const timeB = b.startTime;

              const getTimeOrder = (time: string) => {
                const hour = parseInt(time.split(':')[0]);
                if (hour >= 8 && hour < 18) return 1; // صباح ومساء
                if (hour >= 18) return 2; // برايم تايم
                return 3; // منتصف الليل والفجر
              };

              const orderA = getTimeOrder(timeA);
              const orderB = getTimeOrder(timeB);

              if (orderA !== orderB) return orderA - orderB;
              return timeA.localeCompare(timeB);
            });

          console.log('✅ مواد اليوم المفلترة:', dayItems.length);

          dayItems.forEach((item: any) => {
            const scheduleItem = {
              time: item.startTime,
              name: item.mediaItem?.name || 'مادة غير محددة',
              episodeNumber: item.episodeNumber || item.mediaItem?.episodeNumber,
              partNumber: item.partNumber || item.mediaItem?.partNumber,
              seasonNumber: item.seasonNumber || item.mediaItem?.seasonNumber,
              isRerun: item.isRerun || false
            };
            daySchedule.push(scheduleItem);
            console.log('📺 إضافة مادة للخريطة:', scheduleItem);
          });
        } else {
          console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');
        }

        setWeeklySchedule(daySchedule);
        console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');
      } else {
        console.log('❌ فشل في جلب البيانات أو البيانات فارغة');
        setWeeklySchedule([]);
      }
    } catch (error) {
      console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);
      setWeeklySchedule([]);
    }
  };



  // فلترة المواد المتاحة
  const filteredMedia = availableMedia.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'ALL' || item.type === filterType;
    return matchesSearch && matchesType;
  });

  // أنواع المواد للفلترة
  const mediaTypes = ['ALL', 'PROGRAM', 'SERIES', 'MOVIE', 'PROMO', 'STING', 'FILL_IN', 'FILLER'];

  // إضافة صف فارغ
  const addEmptyRow = (afterIndex: number) => {
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;

    const newRows = [...gridRows];
    const newRow: GridRow = {
      id: `empty_${Date.now()}`,
      type: 'empty',
      canDelete: true
    };
    newRows.splice(afterIndex + 1, 0, newRow);
    setGridRows(newRows);

    // استعادة موضع التمرير
    setTimeout(() => {
      if (gridBody) {
        gridBody.scrollTop = currentScrollTop;
      }
    }, 50);
  };

  // إضافة صفوف فارغة متعددة
  const addMultipleEmptyRows = (afterIndex: number, count: number = 5) => {
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;

    const newRows = [...gridRows];
    for (let i = 0; i < count; i++) {
      const newRow: GridRow = {
        id: `empty_${Date.now()}_${i}`,
        type: 'empty',
        canDelete: true
      };
      newRows.splice(afterIndex + 1 + i, 0, newRow);
    }
    setGridRows(newRows);
    console.log(`✅ تم إضافة ${count} صف فارغ`);

    // استعادة موضع التمرير
    setTimeout(() => {
      if (gridBody) {
        gridBody.scrollTop = currentScrollTop;
      }
    }, 50);
  };

  // التحقق من الحاجة لإضافة صفوف فارغة
  const checkAndAddEmptyRows = (currentIndex: number) => {
    const currentRows = gridRows;
    const nextEmptyIndex = currentRows.findIndex((row, index) =>
      index > currentIndex && row.type === 'empty'
    );

    // إذا لم توجد صفوف فارغة بعد الفاصل الحالي
    if (nextEmptyIndex === -1) {
      console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 5 صفوف');
      addMultipleEmptyRows(currentIndex, 5);
    } else {
      console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);
    }
  };

  // حذف صف فارغ
  const deleteRow = (rowId: string) => {
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;

    const newRows = gridRows.filter(row => row.id !== rowId);
    recalculateTimes(newRows);

    // استعادة موضع التمرير بعد الحذف
    setTimeout(() => {
      if (gridBody) {
        gridBody.scrollTop = currentScrollTop;
      }
    }, 100);
  };

  // حذف سيجمنت
  const deleteSegment = (rowId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا السيجمنت؟')) {
      const newRows = gridRows.filter(row => row.id !== rowId);
      recalculateTimes(newRows);
      console.log('🗑️ تم حذف السيجمنت');
    }
  };

  // حذف فاصل بدون تأكيد
  const deleteFiller = (rowId: string) => {
    const newRows = gridRows.filter(row => row.id !== rowId);
    recalculateTimes(newRows);
    console.log('🗑️ تم حذف الفاصل');
  };

  // تحريك صف لأعلى
  const moveRowUp = (index: number) => {
    if (index <= 0) return;

    const newRows = [...gridRows];
    [newRows[index - 1], newRows[index]] = [newRows[index], newRows[index - 1]];

    recalculateTimes(newRows);
    console.log('⬆️ تم تحريك الصف لأعلى');
  };

  // تحريك صف لأسفل
  const moveRowDown = (index: number) => {
    if (index >= gridRows.length - 1) return;

    const newRows = [...gridRows];
    [newRows[index], newRows[index + 1]] = [newRows[index + 1], newRows[index]];

    recalculateTimes(newRows);
    console.log('⬇️ تم تحريك الصف لأسفل');
  };

  // معالجة سحب الصفوف داخل الجدول
  const handleRowDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData('text/plain', index.toString());
    e.dataTransfer.effectAllowed = 'move';
  };

  // معالجة إسقاط الصفوف داخل الجدول
  const handleRowDrop = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));

    if (sourceIndex === targetIndex) return;

    const newRows = [...gridRows];
    const [movedRow] = newRows.splice(sourceIndex, 1);
    newRows.splice(targetIndex, 0, movedRow);

    recalculateTimes(newRows);
    console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);
  };

  // معالجة إسقاط المواد
  const handleDrop = (e: React.DragEvent, rowIndex: number) => {
    e.preventDefault();
    try {
      // محاولة الحصول على البيانات بطرق مختلفة
      let mediaData;
      const jsonData = e.dataTransfer.getData('application/json');
      const textData = e.dataTransfer.getData('text/plain');

      if (jsonData) {
        mediaData = JSON.parse(jsonData);
      } else if (textData) {
        try {
          mediaData = JSON.parse(textData);
        } catch {
          console.error('❌ لا يمكن تحليل البيانات المسحوبة');
          return;
        }
      } else {
        console.error('❌ لا توجد بيانات مسحوبة');
        return;
      }

      // التحقق من أن الصف فارغ
      const targetRow = gridRows[rowIndex];
      if (targetRow.type !== 'empty') {
        alert('يمكن إسقاط المواد في الصفوف الفارغة فقط');
        return;
      }

      console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);

      // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر
      if (!mediaData || typeof mediaData !== 'object') {
        console.error('❌ بيانات المادة غير صحيحة:', mediaData);
        console.error('❌ نوع البيانات:', typeof mediaData);
        return;
      }

      // التأكد من وجود الاسم
      const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';
      const itemType = mediaData.type || 'UNKNOWN';
      const itemId = mediaData.id || Date.now().toString();

      console.log('📥 معلومات المادة:', {
        name: itemName,
        type: itemType,
        id: itemId,
        segments: mediaData.segments?.length || 0
      });

      // تحديد نوع المادة المسحوبة
      let dragItemType: 'filler' | 'segment' = 'filler';
      let itemContent = itemName;

      // إضافة تفاصيل المادة
      const details = [];
      if (mediaData.episodeNumber) details.push(`ح${mediaData.episodeNumber}`);
      if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(`م${mediaData.seasonNumber}`);
      if (mediaData.partNumber) details.push(`ج${mediaData.partNumber}`);

      const detailsText = details.length > 0 ? ` (${details.join(' - ')})` : '';

      // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات
      if (['PROMO', 'STING', 'FILLER', 'FILL_IN'].includes(itemType)) {
        dragItemType = 'filler';
        itemContent = `${itemName}${detailsText} - ${itemType}`;
      } else {
        dragItemType = 'segment';
        itemContent = `${itemName}${detailsText} (مادة إضافية)`;
      }

      // حساب المدة الحقيقية للمادة
      let itemDuration = '00:01:00'; // مدة افتراضية

      console.log('🔍 تحليل مدة المادة:', {
        name: itemName,
        hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),
        segmentsCount: mediaData.segments?.length || 0,
        hasDuration: !!mediaData.duration,
        directDuration: mediaData.duration
      });

      if (mediaData.segments && mediaData.segments.length > 0) {
        // حساب إجمالي مدة جميع السيجمنتات
        let totalSeconds = 0;

        mediaData.segments.forEach((segment: any, index: number) => {
          if (segment.duration) {
            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);
            const segmentSeconds = hours * 3600 + minutes * 60 + seconds;
            totalSeconds += segmentSeconds;

            console.log(`  📺 سيجمنت ${index + 1}: ${segment.duration} (${segmentSeconds} ثانية)`);
          }
        });

        if (totalSeconds > 0) {
          const hours = Math.floor(totalSeconds / 3600);
          const minutes = Math.floor((totalSeconds % 3600) / 60);
          const secs = totalSeconds % 60;
          itemDuration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        console.log('📊 حساب مدة المادة من السيجمنتات:', {
          name: itemName,
          segments: mediaData.segments.length,
          totalSeconds,
          finalDuration: itemDuration
        });
      } else if (mediaData.duration) {
        // استخدام المدة المباشرة إذا كانت موجودة
        itemDuration = mediaData.duration;
        console.log('📊 استخدام مدة مباشرة:', itemDuration);
      } else {
        console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);
      }

      // إنشاء كود للمادة المسحوبة
      let itemCode = '';
      if (mediaData.segmentCode) {
        itemCode = mediaData.segmentCode;
      } else if (mediaData.id) {
        itemCode = `${itemType}_${mediaData.id}`;
      } else {
        itemCode = `${itemType}_${Date.now().toString().slice(-6)}`;
      }

      // إنشاء صف جديد مع المدة الحقيقية
      const newRow: GridRow = {
        id: `dropped_${Date.now()}`,
        type: dragItemType,
        content: itemContent,
        mediaItemId: itemId,
        segmentCode: itemCode, // كود المادة للسيرفرات
        duration: itemDuration,
        canDelete: true
      };

      // التأكد من أن الصف المستهدف فارغ
      if (gridRows[rowIndex].type !== 'empty') {
        console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);
        return;
      }

      // استبدال الصف الفارغ مباشرة
      const newRows = [...gridRows];
      newRows[rowIndex] = newRow;

      console.log('✅ تم إضافة المادة:', {
        name: itemName,
        type: dragItemType,
        duration: itemDuration,
        position: rowIndex,
        content: itemContent,
        beforeType: gridRows[rowIndex].type,
        afterType: newRow.type
      });

      // حفظ موضع التمرير الحالي
      const gridBody = document.querySelector('.grid-body');
      const currentScrollTop = gridBody?.scrollTop || 0;

      // تحديث الصفوف مباشرة
      setGridRows(newRows);

      // إعادة حساب الأوقات بعد تأخير قصير
      setTimeout(() => {
        recalculateTimes(newRows);

        // التحقق من الحاجة لإضافة صفوف فارغة
        checkAndAddEmptyRows(rowIndex);

        // استعادة موضع التمرير بعد التحديث
        setTimeout(() => {
          if (gridBody) {
            gridBody.scrollTop = currentScrollTop;
            console.log('📍 تم استعادة موضع التمرير:', currentScrollTop);
          }
        }, 100);
      }, 50);

    } catch (error) {
      console.error('❌ خطأ في إسقاط المادة:', error);
    }
  };

  // إعادة حساب الأوقات مثل Excel
  const recalculateTimes = (rows: GridRow[]) => {
    const newRows = [...rows];
    let currentTime = '08:00:00'; // نقطة البداية بالثواني
    let hasFillers = false; // هل تم إضافة فواصل؟

    // التحقق من وجود فواصل
    hasFillers = rows.some(row => row.type === 'filler');

    console.log('🔄 بدء إعادة حساب الأوقات من 08:00:00', hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');

    for (let i = 0; i < newRows.length; i++) {
      const row = newRows[i];

      if (row.type === 'segment' || row.type === 'filler') {
        // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل
        if (i === 0 || hasFillers) {
          newRows[i] = { ...row, time: currentTime };
        } else {
          newRows[i] = { ...row, time: undefined };
        }

        if (row.duration) {
          // حساب الوقت التالي بناءً على المدة
          const nextTime = calculateNextTime(currentTime, row.duration);

          console.log(`⏰ ${row.type}: "${row.content}" - من ${currentTime} إلى ${nextTime} (مدة: ${row.duration})`);

          currentTime = nextTime;
        }
      } else if (row.type === 'empty') {
        // الصفوف الفارغة لا تؤثر على الوقت
        newRows[i] = { ...row, time: undefined };
      }

      // التحقق من الوصول لوقت مادة أساسية
      if (row.originalStartTime && hasFillers) {
        const targetMinutes = timeToMinutes(row.originalStartTime);
        const currentMinutes = timeToMinutes(currentTime);
        const difference = targetMinutes - currentMinutes;

        if (Math.abs(difference) > 5) { // فرق أكثر من 5 دقائق
          console.log(`⚠️ انحراف زمني: المادة "${row.content}" مجدولة في ${row.originalStartTime} لكن ستدخل في ${currentTime} (فرق: ${difference} دقيقة)`);
        } else {
          console.log(`✅ توقيت صحيح: المادة "${row.content}" ستدخل في ${currentTime} (مجدولة: ${row.originalStartTime})`);
        }
      }
    }

    console.log(`🏁 انتهاء الحساب - الوقت النهائي: ${currentTime}`);

    // حفظ موضع التمرير قبل التحديث
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;

    // تحديث الصفوف دائماً لضمان التحديث الصحيح
    setGridRows(newRows);

    // استعادة موضع التمرير بعد التحديث
    setTimeout(() => {
      if (gridBody) {
        gridBody.scrollTop = currentScrollTop;
      }
    }, 50);
  };

  // تحويل الوقت إلى دقائق
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // حساب المدة الإجمالية للمادة بدقة
  const calculateTotalDuration = (item: any): string => {
    if (item.segments && item.segments.length > 0) {
      let totalSeconds = 0;

      item.segments.forEach((segment: any) => {
        if (segment.duration) {
          const [hours, minutes, seconds] = segment.duration.split(':').map(Number);
          totalSeconds += hours * 3600 + minutes * 60 + seconds;
        }
      });

      if (totalSeconds > 0) {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const secs = totalSeconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
    }

    return item.duration || '00:01:00';
  };

  // حفظ تعديلات الجدول
  const saveScheduleChanges = async () => {
    try {
      console.log('💾 بدء حفظ التعديلات...');
      console.log('📅 التاريخ:', selectedDate);
      console.log('📝 عدد الصفوف:', gridRows.length);

      const response = await fetch('/api/daily-schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          date: selectedDate,
          scheduleRows: gridRows
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        console.log('✅ تم حفظ تعديلات الجدول الإذاعي بنجاح');
        alert('✅ تم حفظ التعديلات بنجاح!');
      } else {
        console.error('❌ فشل في حفظ التعديلات:', result.error);
        alert('❌ فشل في حفظ التعديلات: ' + result.error);
      }
    } catch (error) {
      console.error('❌ خطأ في حفظ التعديلات:', error);
      alert('❌ خطأ في الاتصال بالخادم');
    }
  };

  // تصدير الجدول الإذاعي إلى Excel
  const exportDailySchedule = async () => {
    try {
      console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');

      if (!selectedDate) {
        alert('يرجى تحديد التاريخ أولاً');
        return;
      }

      const response = await fetch(`/api/export-daily-schedule-new?date=${selectedDate}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Daily_Schedule_${selectedDate}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      console.log('✅ تم تصدير الجدول الإذاعي بنجاح');
      alert('✅ تم تصدير الجدول بنجاح!');
    } catch (error) {
      console.error('❌ خطأ في تصدير الجدول:', error);
      alert('❌ فشل في تصدير الجدول: ' + error.message);
    }
  };

  // حساب الوقت التالي بناءً على المدة (بدقة الثواني)
  const calculateNextTime = (startTime: string, duration: string): string => {
    // تحليل وقت البداية
    const startParts = startTime.split(':');
    const startHours = parseInt(startParts[0]);
    const startMins = parseInt(startParts[1]);
    const startSecs = parseInt(startParts[2] || '0');

    // تحليل المدة
    const [durHours, durMins, durSecs] = duration.split(':').map(Number);

    // حساب إجمالي الثواني
    let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;
    totalSeconds += durHours * 3600 + durMins * 60 + durSecs;

    // تحويل إلى ساعات ودقائق وثواني
    const hours = Math.floor(totalSeconds / 3600) % 24;
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <AuthGuard requiredPermissions={['SCHEDULE_READ']}>
      <DashboardLayout title="جدول الإذاعة اليومي" subtitle="البرامج المجدولة اليوم" icon="📊" fullWidth={true}>

      {/* Controls */}
      <div className="schedule-controls">
        <div className="date-selector">
          <label htmlFor="schedule-date">اختر التاريخ:</label>
          <input
            id="schedule-date"
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="glass-input"
          />
        </div>
        
        <div className="header-buttons">
          <button
            onClick={saveScheduleChanges}
            className="glass-button primary"
          >
            💾 حفظ التعديلات
          </button>

          <button
            onClick={exportDailySchedule}
            className="glass-button export"
            style={{
              background: 'linear-gradient(45deg, #17a2b8, #138496)',
              color: 'white'
            }}
          >
            📊 تصدير Excel
          </button>

          <button
            onClick={() => setShowWeeklySchedule(!showWeeklySchedule)}
            className="glass-button"
          >
            {showWeeklySchedule ? '📋 إخفاء الخريطة' : '📅 عرض الخريطة'}
          </button>


        </div>
      </div>

      <div className="schedule-content">
        {/* Weekly Schedule Sidebar */}
        {showWeeklySchedule && (
          <div className="weekly-sidebar">
            <h3 className="sidebar-title">الخريطة البرامجية</h3>
            <div className="weekly-schedule-list">
              {Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? (
                weeklySchedule.map((item, index) => (
                  <div key={index} className="weekly-item">
                    <div className="weekly-time">{item.time}</div>
                    <div className="weekly-content">
                      <div className="weekly-name">{item.name}</div>
                      {item.episodeNumber && (
                        <div className="weekly-details">ح{item.episodeNumber}</div>
                      )}
                      {item.partNumber && (
                        <div className="weekly-details">ج{item.partNumber}</div>
                      )}
                    </div>
                    <div className="weekly-status">
                      {item.isRerun ? '🔄' : '🎯'}
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-data">لا توجد بيانات للخريطة البرامجية</div>
              )}
            </div>
          </div>
        )}

        {/* Sidebar */}
        <div className="media-sidebar">
          <h3 className="sidebar-title">المواد المتاحة</h3>
          
          {/* Search and Filter */}
          <div className="sidebar-controls">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="filter-select"
            >
              {mediaTypes.map(type => (
                <option key={type} value={type}>
                  {type === 'ALL' ? 'جميع الأنواع' : type}
                </option>
              ))}
            </select>
            
            <input
              type="text"
              placeholder="بحث في المواد..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          {/* Media List */}
          <div className="media-list">
            {filteredMedia.map(item => (
              <div
                key={item.id}
                className={`media-item ${item.type.toLowerCase()}`}
                draggable
                onDragStart={(e) => {
                  e.dataTransfer.setData('application/json', JSON.stringify(item));
                }}
              >
                <div className="media-name">{item.name}</div>
                <div className="media-details">
                  <span className="media-type">{item.type}</span>
                  <span className="media-duration">{calculateTotalDuration(item)}</span>
                </div>
                <div className="media-info">
                  {item.episodeNumber && <span className="info-tag">ح{item.episodeNumber}</span>}
                  {item.seasonNumber && item.seasonNumber > 0 && <span className="info-tag">م{item.seasonNumber}</span>}
                  {item.partNumber && <span className="info-tag">ج{item.partNumber}</span>}
                  {item.segments && <span className="info-tag">{item.segments.length} سيجمنت</span>}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Grid */}
        <div className="schedule-grid">
          <div className="grid-header">
            <div className="code-column">الكود</div>
            <div className="time-column">الوقت</div>
            <div className="content-column">المحتوى</div>
            <div className="duration-column">المدة</div>
            <div className="status-column">الحالة</div>
            <div className="actions-column">إجراءات</div>
          </div>

          <div className="grid-body">
            {loading ? (
              <div className="loading">جاري التحميل...</div>
            ) : (
              gridRows.map((row, index) => (
                <div
                  key={row.id}
                  className={`grid-row ${row.type} ${row.isRerun ? 'rerun' : ''} ${row.isTemporary ? 'temporary' : ''}`}
                  draggable={row.type === 'filler' || row.type === 'empty'}
                  onDragStart={(e) => handleRowDragStart(e, index)}
                  onDrop={(e) => handleRowDrop(e, index)}
                  onDragOver={(e) => e.preventDefault()}
                >
                  <div className="code-cell">
                    {(row.type === 'segment' || row.type === 'filler') ?
                      (row.segmentCode || row.mediaItemId || `${row.type.toUpperCase()}_${row.id.slice(-6)}`) :
                      ''
                    }
                  </div>
                  <div className="time-cell">
                    {row.time || ''}
                  </div>
                  <div
                    className="content-cell"
                    onDrop={(e) => handleDrop(e, index)}
                    onDragOver={(e) => e.preventDefault()}
                  >
                    {row.content || ''}
                  </div>
                  <div className="duration-cell">
                    {row.duration || ''}
                  </div>
                  <div className="status-cell">
                    {row.type === 'segment' && row.isTemporary && '🟣 مؤقت'}
                    {row.type === 'segment' && !row.isRerun && !row.isTemporary && (
                      row.originalStartTime ? (
                        Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ?
                        '⚠️ انحراف' : '✅ دقيق'
                      ) : '🎯 أساسي'
                    )}
                    {row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة'}
                    {row.type === 'filler' && '📺 فاصل'}
                    {row.type === 'empty' && '⚪ فارغ'}
                  </div>
                  <div className="actions-cell">
                    {row.type === 'empty' && (
                      <>
                        <button
                          className="action-btn add-row"
                          title="إضافة صف"
                          onClick={() => addEmptyRow(index)}
                        >
                          ➕
                        </button>
                        <button
                          className="action-btn add-multiple-rows"
                          title="إضافة 5 صفوف"
                          onClick={() => addMultipleEmptyRows(index, 5)}
                        >
                          ➕➕
                        </button>
                        {row.canDelete && (
                          <button
                            className="action-btn delete-row"
                            title="حذف صف"
                            onClick={() => deleteRow(row.id)}
                          >
                            ➖
                          </button>
                        )}
                      </>
                    )}
                    {row.type === 'filler' && (
                      <>
                        <button
                          className="action-btn move-up"
                          title="تحريك لأعلى"
                          onClick={() => moveRowUp(index)}
                          disabled={index === 0}
                        >
                          ⬆️
                        </button>
                        <button
                          className="action-btn move-down"
                          title="تحريك لأسفل"
                          onClick={() => moveRowDown(index)}
                          disabled={index === gridRows.length - 1}
                        >
                          ⬇️
                        </button>
                        <button
                          className="action-btn delete-row"
                          title="حذف فاصل"
                          onClick={() => {
                            // تحويل الفاصل إلى صف فارغ
                            const newRows = [...gridRows];
                            newRows[index] = {
                              id: `empty_${Date.now()}`,
                              type: 'empty',
                              canDelete: true
                            };

                            // إعادة حساب الأوقات
                            recalculateTimes(newRows);
                          }}
                        >
                          🗑️
                        </button>
                      </>
                    )}
                    {row.type === 'segment' && row.isTemporary && (
                      <button
                        className="action-btn replace-temp"
                        title="استبدال بمادة حقيقية"
                        onClick={() => {
                          alert('💡 لاستبدال المادة المؤقتة:\n\n1. أضف المادة الحقيقية لقاعدة البيانات\n2. احذف المادة المؤقتة\n3. اسحب المادة الجديدة من القائمة الجانبية');
                        }}
                        style={{ color: '#9c27b0' }}
                      >
                        🔄
                      </button>
                    )}
                    {row.type === 'segment' && row.canDelete && !row.isTemporary && (
                      <button
                        className="action-btn delete-row"
                        title="حذف سيجمنت"
                        onClick={() => deleteSegment(row.id)}
                      >
                        ❌
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
      </DashboardLayout>
    </AuthGuard>
  );
}
