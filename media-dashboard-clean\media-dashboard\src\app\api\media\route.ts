import { NextRequest, NextResponse } from 'next/server';

// استيراد البيانات المشتركة
import { getAllMediaItems, addMediaItem, removeMediaItem, getMediaItemById, updateMediaItem } from '../shared-data';

// GET - جلب جميع المواد الإعلامية أو مادة واحدة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (id) {
      // جلب مادة واحدة
      const mediaItem = getMediaItemById(id);
      if (!mediaItem) {
        return NextResponse.json(
          { success: false, error: 'المادة غير موجودة' },
          { status: 404 }
        );
      }
      return NextResponse.json({
        success: true,
        data: mediaItem
      });
    } else {
      // جلب جميع المواد
      const mediaItems = getAllMediaItems();
      return NextResponse.json({
        success: true,
        data: mediaItems
      });
    }
  } catch (error) {
    console.error('Error fetching media items:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب المواد الإعلامية' },
      { status: 500 }
    );
  }
}

// POST - إضافة مادة إعلامية جديدة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { formData, segments } = body;

    // التحقق من البيانات المطلوبة
    if (!formData.name || !formData.type) {
      return NextResponse.json(
        { success: false, error: 'اسم المادة ونوعها مطلوبان' },
        { status: 400 }
      );
    }

    // إنشاء المادة الإعلامية مع السيجمانت (مؤقت: في الذاكرة)
    const mediaItem = {
      id: `media_${Date.now()}`,
      name: formData.name,
      type: formData.type,
      description: formData.description || null,
      channel: formData.channel,
      source: formData.source || null,
      status: formData.status,
      startDate: formData.startDate || new Date().toISOString(),
      endDate: formData.endDate || null,
      notes: formData.notes || null,
      episodeNumber: formData.episodeNumber ? parseInt(formData.episodeNumber) : null,
      seasonNumber: formData.seasonNumber ? parseInt(formData.seasonNumber) : null,
      partNumber: formData.partNumber ? parseInt(formData.partNumber) : null,
      hardDiskNumber: formData.hardDiskNumber || 'SERVER',
      segments: segments.map((segment: any) => ({
        id: `seg_${Date.now()}_${segment.id}`,
        segmentNumber: segment.id,
        timeIn: segment.timeIn,
        timeOut: segment.timeOut,
        duration: segment.duration,
        code: segment.segmentCode || null
      })),
      createdAt: new Date().toISOString()
    };

    // حفظ في الذاكرة مؤقت
    addMediaItem(mediaItem);

    return NextResponse.json({
      success: true,
      data: mediaItem,
      message: 'تم حفظ المادة الإعلامية بنجاح'
    });

  } catch (error) {
    console.error('Error creating media item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في حفظ المادة الإعلامية' },
      { status: 500 }
    );
  }
}

// PUT - تحديث مادة إعلامية
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المادة مطلوب' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { formData, segments } = body;

    // التحقق من البيانات المطلوبة
    if (!formData.name || !formData.type) {
      return NextResponse.json(
        { success: false, error: 'اسم المادة ونوعها مطلوبان' },
        { status: 400 }
      );
    }

    // تحديث المادة الإعلامية
    const updatedMediaItem = {
      id,
      name: formData.name,
      type: formData.type,
      description: formData.description || null,
      channel: formData.channel,
      source: formData.source || null,
      status: formData.status,
      startDate: formData.startDate || new Date().toISOString(),
      endDate: formData.endDate || null,
      notes: formData.notes || null,
      episodeNumber: formData.episodeNumber ? parseInt(formData.episodeNumber) : null,
      seasonNumber: formData.seasonNumber ? parseInt(formData.seasonNumber) : null,
      partNumber: formData.partNumber ? parseInt(formData.partNumber) : null,
      hardDiskNumber: formData.hardDiskNumber || 'SERVER',
      segments: segments.map((segment: any) => ({
        id: `seg_${Date.now()}_${segment.id}`,
        segmentNumber: segment.id,
        timeIn: segment.timeIn,
        timeOut: segment.timeOut,
        duration: segment.duration,
        segmentCode: segment.segmentCode || null
      })),
      updatedAt: new Date().toISOString()
    };

    // تحديث في الذاكرة
    const success = updateMediaItem(id, updatedMediaItem);
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'المادة غير موجودة' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedMediaItem,
      message: 'تم تحديث المادة الإعلامية بنجاح'
    });

  } catch (error) {
    console.error('Error updating media item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث المادة الإعلامية' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مادة إعلامية
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف المادة مطلوب' },
        { status: 400 }
      );
    }

    // حذف من الذاكرة مؤقت
    const success = removeMediaItem(id);
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'المادة غير موجودة' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المادة الإعلامية بنجاح'
    });

  } catch (error) {
    console.error('Error deleting media item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المادة الإعلامية' },
      { status: 500 }
    );
  }
}
