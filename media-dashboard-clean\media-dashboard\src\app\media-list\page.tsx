'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Segment {
  id: string;
  segmentNumber: number;
  timeIn: string;
  timeOut: string;
  duration: string;
  code: string | null;
}

interface MediaItem {
  id: string;
  name: string;
  type: string;
  description: string | null;
  channel: string;
  source: string | null;
  status: string;
  startDate: string;
  endDate: string | null;
  notes: string | null;
  episodeNumber: number | null;
  seasonNumber: number | null;
  partNumber: number | null;
  segments: Segment[];
  createdAt: string;
}

export default function MediaListPage() {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('ALL');
  const [selectedStatus, setSelectedStatus] = useState('ALL');
  const [sortBy, setSortBy] = useState('newest');
  const [isExporting, setIsExporting] = useState(false);

  useEffect(() => {
    fetchMediaItems();
  }, []);

  useEffect(() => {
    filterAndSortItems();
  }, [mediaItems, searchTerm, selectedType, selectedStatus, sortBy]);

  const fetchMediaItems = async () => {
    try {
      const response = await fetch('/api/media');
      const result = await response.json();

      if (result.success) {
        setMediaItems(result.data);
      } else {
        setError(result.error);
      }
    } catch (error) {
      console.error('Error fetching media items:', error);
      setError('فشل في جلب المواد الإعلامية');
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortItems = () => {
    let filtered = [...mediaItems];

    // البحث بالاسم
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // فلترة بالنوع
    if (selectedType !== 'ALL') {
      filtered = filtered.filter(item => item.type === selectedType);
    }

    // فلترة بالحالة
    if (selectedStatus !== 'ALL') {
      filtered = filtered.filter(item => item.status === selectedStatus);
    }

    // الترتيب
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
        break;
      case 'type':
        filtered.sort((a, b) => a.type.localeCompare(b.type));
        break;
    }

    setFilteredItems(filtered);
  };

  const deleteMediaItem = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه المادة؟')) return;

    try {
      const response = await fetch(`/api/media?id=${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setMediaItems(mediaItems.filter(item => item.id !== id));
        alert('تم حذف المادة بنجاح');
      } else {
        alert('فشل في حذف المادة: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting media item:', error);
      alert('حدث خطأ أثناء حذف المادة');
    }
  };

  const exportToExcel = async () => {
    setIsExporting(true);
    try {
      console.log('🚀 بدء تصدير قاعدة البيانات...');

      const response = await fetch('/api/export');

      if (!response.ok) {
        throw new Error('فشل في تصدير البيانات');
      }

      // الحصول على الملف كـ blob
      const blob = await response.blob();

      // إنشاء رابط التحميل
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // تحديد اسم الملف
      const fileName = `Media_Database_${new Date().toISOString().split('T')[0]}.xlsx`;
      link.download = fileName;

      // تحميل الملف
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // تنظيف الذاكرة
      window.URL.revokeObjectURL(url);

      console.log('✅ تم تصدير قاعدة البيانات بنجاح');
      alert('✅ تم تصدير قاعدة البيانات بنجاح!');

    } catch (error) {
      console.error('❌ خطأ في التصدير:', error);
      alert('❌ فشل في تصدير قاعدة البيانات. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsExporting(false);
    }
  };

  const getTypeLabel = (type: string) => {
    const types: { [key: string]: string } = {
      PROGRAM: 'برنامج',
      SERIES: 'مسلسل',
      MOVIE: 'فيلم',
      SONG: 'أغنية',
      STING: 'Sting',
      FILL_IN: 'Fill IN',
      FILLER: 'Filler',
      PROMO: 'Promo'
    };
    return types[type] || type;
  };

  const getStatusLabel = (status: string) => {
    const statuses: { [key: string]: string } = {
      VALID: 'صالح',
      REJECTED_CENSORSHIP: 'مرفوض رقابي',
      REJECTED_TECHNICAL: 'مرفوض هندسي',
      WAITING: 'في الانتظار'
    };
    return statuses[status] || status;
  };

  const getChannelLabel = (channel: string) => {
    const channels: { [key: string]: string } = {
      DOCUMENTARY: 'الوثائقية',
      NEWS: 'الأخبار',
      OTHER: 'أخرى'
    };
    return channels[channel] || channel;
  };

  if (loading) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        background: '#1a1d29',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ color: 'white', fontSize: '1.5rem' }}>⏳ جاري التحميل...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        background: '#1a1d29',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ color: 'white', fontSize: '1.5rem' }}>❌ خطأ: {error}</div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: '#1a1d29',
      padding: '20px'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '20px',
          padding: '30px',
          marginBottom: '30px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
          textAlign: 'center'
        }}>
          <h1 style={{ 
            color: '#2c3e50', 
            marginBottom: '20px', 
            fontSize: '2.5rem',
            fontWeight: 'bold'
          }}>
            📚 قائمة المواد الإعلامية
          </h1>
          
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link href="/add-media" style={{
              background: 'linear-gradient(45deg, #28a745, #20c997)',
              color: 'white',
              padding: '12px 25px',
              borderRadius: '25px',
              textDecoration: 'none',
              fontWeight: 'bold',
              boxShadow: '0 4px 15px rgba(40,167,69,0.3)'
            }}>
              ➕ إضافة مادة جديدة
            </Link>

            <button
              onClick={exportToExcel}
              disabled={isExporting}
              style={{
                background: isExporting
                  ? 'linear-gradient(45deg, #6c757d, #5a6268)'
                  : 'linear-gradient(45deg, #17a2b8, #138496)',
                color: 'white',
                padding: '12px 25px',
                borderRadius: '25px',
                border: 'none',
                fontWeight: 'bold',
                cursor: isExporting ? 'not-allowed' : 'pointer',
                boxShadow: '0 4px 15px rgba(23,162,184,0.3)',
                fontSize: '1rem'
              }}
            >
              {isExporting ? '⏳ جاري التصدير...' : '📊 تصدير Excel'}
            </button>

            <Link href="/" style={{
              background: 'linear-gradient(45deg, #007bff, #0056b3)',
              color: 'white',
              padding: '12px 25px',
              borderRadius: '25px',
              textDecoration: 'none',
              fontWeight: 'bold',
              boxShadow: '0 4px 15px rgba(0,123,255,0.3)'
            }}>
              🏠 الرئيسية
            </Link>
          </div>
        </div>

        {/* البحث والفلترة */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '20px',
          padding: '25px',
          marginBottom: '25px',
          boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ color: '#2c3e50', marginBottom: '20px', fontSize: '1.3rem' }}>
            🔍 البحث والفلترة
          </h2>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
            {/* البحث */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#495057', fontSize: '0.9rem' }}>
                البحث بالاسم أو الوصف:
              </label>
              <input
                type="text"
                placeholder="ابحث عن مادة إعلامية..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: 'rtl'
                }}
              />
            </div>

            {/* فلترة بالنوع */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#495057', fontSize: '0.9rem' }}>
                نوع المادة:
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: 'rtl'
                }}
              >
                <option value="ALL">جميع الأنواع</option>
                <option value="PROGRAM">برنامج</option>
                <option value="SERIES">مسلسل</option>
                <option value="MOVIE">فيلم</option>
                <option value="SONG">أغنية</option>
                <option value="STING">Sting</option>
                <option value="FILL_IN">Fill IN</option>
                <option value="FILLER">Filler</option>
                <option value="PROMO">Promo</option>
              </select>
            </div>

            {/* فلترة بالحالة */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#495057', fontSize: '0.9rem' }}>
                الحالة:
              </label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: 'rtl'
                }}
              >
                <option value="ALL">جميع الحالات</option>
                <option value="VALID">صالح</option>
                <option value="REJECTED_CENSORSHIP">مرفوض رقابي</option>
                <option value="REJECTED_TECHNICAL">مرفوض هندسي</option>
                <option value="WAITING">في الانتظار</option>
              </select>
            </div>

            {/* الترتيب */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#495057', fontSize: '0.9rem' }}>
                ترتيب حسب:
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '2px solid #e0e0e0',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: 'rtl'
                }}
              >
                <option value="newest">الأحدث أولاً</option>
                <option value="oldest">الأقدم أولاً</option>
                <option value="name">الاسم (أ-ي)</option>
                <option value="type">النوع</option>
              </select>
            </div>
          </div>

          {/* إحصائيات البحث */}
          <div style={{
            marginTop: '15px',
            padding: '10px',
            background: '#f8f9fa',
            borderRadius: '8px',
            textAlign: 'center',
            color: '#6c757d'
          }}>
            📊 عرض {filteredItems.length} من أصل {mediaItems.length} مادة إعلامية
          </div>
        </div>

        {/* Media Items */}
        {filteredItems.length === 0 ? (
          <div style={{
            background: 'rgba(255,255,255,0.95)',
            borderRadius: '20px',
            padding: '50px',
            textAlign: 'center',
            boxShadow: '0 10px 30px rgba(0,0,0,0.2)'
          }}>
            <h2 style={{ color: '#6c757d', fontSize: '1.5rem' }}>
              📭 لا توجد مواد إعلامية محفوظة
            </h2>
            <p style={{ color: '#6c757d', marginTop: '10px' }}>
              ابدأ بإضافة مادة إعلامية جديدة
            </p>
          </div>
        ) : (
          <div style={{ display: 'grid', gap: '20px' }}>
            {filteredItems.map((item) => (
              <div key={item.id} style={{
                background: 'rgba(255,255,255,0.95)',
                borderRadius: '15px',
                padding: '25px',
                boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
                border: '1px solid #e9ecef'
              }}>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr auto', gap: '20px', alignItems: 'start' }}>
                  <div>
                    <h3 style={{ color: '#2c3e50', marginBottom: '15px', fontSize: '1.4rem' }}>
                      {item.name}
                    </h3>
                    
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px', marginBottom: '15px' }}>
                      <div>
                        <strong>النوع:</strong> {getTypeLabel(item.type)}
                      </div>
                      <div>
                        <strong>القناة:</strong> {getChannelLabel(item.channel)}
                      </div>
                      <div>
                        <strong>الحالة:</strong> {getStatusLabel(item.status)}
                      </div>
                      <div>
                        <strong>عدد السيجمانت:</strong> {item.segments.length}
                      </div>
                    </div>

                    {item.description && (
                      <p style={{ color: '#6c757d', marginBottom: '10px' }}>
                        <strong>الوصف:</strong> {item.description}
                      </p>
                    )}

                    {item.segments.length > 0 && (
                      <div style={{ marginTop: '15px' }}>
                        <strong>السيجمانت:</strong>
                        <div style={{ display: 'grid', gap: '8px', marginTop: '8px' }}>
                          {item.segments.map((segment) => (
                            <div key={segment.id} style={{
                              background: '#f8f9fa',
                              padding: '8px 12px',
                              borderRadius: '8px',
                              fontSize: '0.9rem'
                            }}>
                              <strong>#{segment.segmentNumber}</strong> - 
                              {segment.code && ` ${segment.code} - `}
                              {segment.timeIn} → {segment.timeOut} ({segment.duration})
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                    <button
                      onClick={() => {
                        // توجيه لصفحة التعديل مع معرف المادة
                        window.location.href = `/edit-media?id=${item.id}`;
                      }}
                      style={{
                        background: 'linear-gradient(45deg, #007bff, #0056b3)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '8px',
                        padding: '8px 16px',
                        cursor: 'pointer',
                        fontSize: '0.9rem',
                        marginBottom: '5px'
                      }}
                    >
                      ✏️ تعديل
                    </button>

                    <button
                      onClick={() => deleteMediaItem(item.id)}
                      style={{
                        background: 'linear-gradient(45deg, #dc3545, #c82333)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '8px',
                        padding: '8px 16px',
                        cursor: 'pointer',
                        fontSize: '0.9rem'
                      }}
                    >
                      🗑️ حذف
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
