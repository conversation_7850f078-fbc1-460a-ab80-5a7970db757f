"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const validateTimeFormat = (time)=>{\n        const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;\n        return timeRegex.test(time);\n    };\n    const formatTimeInput = (value)=>{\n        // إزالة أي أحرف غير رقمية أو نقطتين\n        const cleaned = value.replace(/[^\\d:]/g, '');\n        // تقسيم النص إلى أجزاء\n        const parts = cleaned.split(':');\n        // تنسيق كل جزء\n        const hours = parts[0] ? parts[0].padStart(2, '0').slice(0, 2) : '00';\n        const minutes = parts[1] ? parts[1].padStart(2, '0').slice(0, 2) : '00';\n        const seconds = parts[2] ? parts[2].padStart(2, '0').slice(0, 2) : '00';\n        return \"\".concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    };\n    const calculateDuration = (timeIn, timeOut)=>{\n        if (!timeIn || !timeOut || !validateTimeFormat(timeIn) || !validateTimeFormat(timeOut)) {\n            return '00:00:00';\n        }\n        const [inHours, inMinutes, inSeconds] = timeIn.split(':').map(Number);\n        const [outHours, outMinutes, outSeconds] = timeOut.split(':').map(Number);\n        const inTotalSeconds = inHours * 3600 + inMinutes * 60 + inSeconds;\n        const outTotalSeconds = outHours * 3600 + outMinutes * 60 + outSeconds;\n        const durationSeconds = outTotalSeconds - inTotalSeconds;\n        if (durationSeconds <= 0) return '00:00:00';\n        const hours = Math.floor(durationSeconds / 3600);\n        const minutes = Math.floor(durationSeconds % 3600 / 60);\n        const seconds = durationSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const updateSegmentCount = (count)=>{\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            const existingSegment = segments.find((s)=>s.id === i);\n            newSegments.push(existingSegment || {\n                id: i,\n                segmentCode: \"SEG\".concat(i.toString().padStart(3, '0')),\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حفظ المادة الإعلامية بنجاح!', 'success');\n                // إعادة تعيين النموذج\n                setFormData({\n                    name: '',\n                    type: 'PROGRAM',\n                    description: '',\n                    channel: 'DOCUMENTARY',\n                    source: '',\n                    status: 'WAITING',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: 'SERVER'\n                });\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: 'SEG001',\n                        timeIn: '00:00:00',\n                        timeOut: '00:00:00',\n                        duration: '00:00:00'\n                    }\n                ]);\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            showToast('حدث خطأ أثناء حفظ البيانات', 'error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"إضافة مادة إعلامية جديدة\",\n        subtitle: \"إدارة المحتوى الإعلامي\",\n        icon: \"➕\",\n        requiredPermissions: [\n            'MEDIA_CREATE'\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#374151',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #4b5563'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 1fr',\n                                            gap: '15px',\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    fontWeight: 'bold',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"\\uD83D\\uDCBE رقم الهارد:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"رقم الهارد\",\n                                                value: formData.hardDiskNumber,\n                                                onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    maxWidth: '200px'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"اسم المادة *\",\n                                        value: formData.name,\n                                        onChange: (e)=>handleInputChange('name', e.target.value),\n                                        style: inputStyle,\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 180px 1fr',\n                                            gap: '15px',\n                                            alignItems: 'end'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"نوع المادة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: 'white',\n                                                            color: '#333'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 238,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"القناة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: 'white',\n                                                            color: '#333'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: 'white',\n                                                            color: '#333'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"approved\",\n                                                                children: \"معتمد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"pending\",\n                                                                children: \"قيد المراجعة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"rejected\",\n                                                                children: \"مرفوض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"expired\",\n                                                                children: \"منتهي الصلاحية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        placeholder: \"وصف المادة\",\n                                        value: formData.description,\n                                        onChange: (e)=>handleInputChange('description', e.target.value),\n                                        style: {\n                                            ...inputStyle,\n                                            minHeight: '80px',\n                                            resize: 'vertical'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"المصدر\",\n                                        value: formData.source,\n                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                        style: {\n                                            ...inputStyle,\n                                            background: 'white',\n                                            color: '#333'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#495057',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"تاريخ البداية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.startDate,\n                                                        onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#495057',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.endDate,\n                                                        onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this),\n                                    (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الحلقة\",\n                                                value: formData.episodeNumber,\n                                                onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 19\n                                            }, this),\n                                            formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الموسم\",\n                                                value: formData.seasonNumber,\n                                                onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 17\n                                    }, this),\n                                    formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        placeholder: \"رقم الجزء\",\n                                        value: formData.partNumber,\n                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                        style: inputStyle\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        placeholder: \"ملاحظات\",\n                                        value: formData.notes,\n                                        onChange: (e)=>handleInputChange('notes', e.target.value),\n                                        style: {\n                                            ...inputStyle,\n                                            minHeight: '60px',\n                                            resize: 'vertical'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#374151',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #4b5563'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#60a5fa',\n                                    marginBottom: '10px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: \"\\uD83C\\uDFAC السيجمانت\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: '#93c5fd',\n                                    fontSize: '0.9rem',\n                                    marginBottom: '20px',\n                                    fontStyle: 'italic'\n                                },\n                                children: \"\\uD83D\\uDCA1 تنسيق الوقت: HH:MM:SS (مثال: 01:30:45 للساعة الواحدة و30 دقيقة و45 ثانية)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '10px',\n                                            color: '#1565c0',\n                                            fontSize: '1rem'\n                                        },\n                                        children: \"عدد السيجمانت (1-10):\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '10px',\n                                            flexWrap: 'wrap'\n                                        },\n                                        children: [\n                                            1,\n                                            2,\n                                            3,\n                                            4,\n                                            5,\n                                            6,\n                                            7,\n                                            8,\n                                            9,\n                                            10\n                                        ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>updateSegmentCount(num),\n                                                style: {\n                                                    background: segmentCount === num ? 'linear-gradient(45deg, #1976d2, #42a5f5)' : '#f8f9fa',\n                                                    color: segmentCount === num ? 'white' : '#495057',\n                                                    border: segmentCount === num ? 'none' : '2px solid #dee2e6',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: num\n                                            }, num, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '20px'\n                                },\n                                children: segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: 'rgba(255,255,255,0.8)',\n                                            borderRadius: '10px',\n                                            padding: '20px',\n                                            border: '2px solid #90caf9'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: '#1565c0',\n                                                    marginBottom: '15px',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: [\n                                                    \"السيجمانت \",\n                                                    segment.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gap: '15px'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                        gap: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1565c0',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"Time In (HH:MM:SS)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 446,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"00:00:00\",\n                                                                    value: segment.timeIn,\n                                                                    onChange: (e)=>{\n                                                                        let newValue = e.target.value;\n                                                                        // السماح فقط بالأرقام والنقطتين\n                                                                        newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                        // تحديد الطول الأقصى\n                                                                        if (newValue.length <= 8) {\n                                                                            handleSegmentChange(segment.id, 'timeIn', newValue);\n                                                                            // حساب المدة إذا كان التنسيق صحيح\n                                                                            if (validateTimeFormat(newValue)) {\n                                                                                const duration = calculateDuration(newValue, segment.timeOut);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            }\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        // تنسيق القيمة عند فقدان التركيز\n                                                                        const formatted = formatTimeInput(e.target.value);\n                                                                        handleSegmentChange(segment.id, 'timeIn', formatted);\n                                                                        const duration = calculateDuration(formatted, segment.timeOut);\n                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                    },\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        borderColor: validateTimeFormat(segment.timeIn) ? '#28a745' : '#e0e0e0'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 449,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1565c0',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"Time Out (HH:MM:SS)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 485,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"00:00:00\",\n                                                                    value: segment.timeOut,\n                                                                    onChange: (e)=>{\n                                                                        let newValue = e.target.value;\n                                                                        // السماح فقط بالأرقام والنقطتين\n                                                                        newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                        // تحديد الطول الأقصى\n                                                                        if (newValue.length <= 8) {\n                                                                            handleSegmentChange(segment.id, 'timeOut', newValue);\n                                                                            // حساب المدة إذا كان التنسيق صحيح\n                                                                            if (validateTimeFormat(newValue)) {\n                                                                                const duration = calculateDuration(segment.timeIn, newValue);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            }\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        // تنسيق القيمة عند فقدان التركيز\n                                                                        const formatted = formatTimeInput(e.target.value);\n                                                                        handleSegmentChange(segment.id, 'timeOut', formatted);\n                                                                        const duration = calculateDuration(segment.timeIn, formatted);\n                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                    },\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        borderColor: validateTimeFormat(segment.timeOut) ? '#28a745' : '#e0e0e0'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1565c0',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"المدة (HH:MM:SS)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: segment.duration,\n                                                                    readOnly: true,\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: '#f8f9fa',\n                                                                        color: '#6c757d'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1565c0',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"كود السيجمانت\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"SEG001\",\n                                                                    value: segment.segmentCode,\n                                                                    onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                    style: inputStyle\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, segment.id, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '15px',\n                            justifyContent: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                style: {\n                                    background: isSubmitting ? 'linear-gradient(45deg, #6c757d, #adb5bd)' : 'linear-gradient(45deg, #28a745, #20c997)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '25px',\n                                    padding: '15px 40px',\n                                    fontSize: '1.1rem',\n                                    fontWeight: 'bold',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    boxShadow: '0 4px 15px rgba(40,167,69,0.3)',\n                                    opacity: isSubmitting ? 0.7 : 1\n                                },\n                                children: isSubmitting ? '⏳ جاري الحفظ...' : '💾 حفظ البيانات'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 559,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    setFormData({\n                                        name: '',\n                                        type: 'PROGRAM',\n                                        description: '',\n                                        channel: 'DOCUMENTARY',\n                                        source: '',\n                                        status: 'WAITING',\n                                        startDate: new Date().toISOString().split('T')[0],\n                                        endDate: '',\n                                        notes: '',\n                                        episodeNumber: '',\n                                        seasonNumber: '',\n                                        partNumber: '',\n                                        hardDiskNumber: 'SERVER'\n                                    });\n                                    setSegmentCount(1);\n                                    setSegments([\n                                        {\n                                            id: 1,\n                                            segmentCode: 'SEG001',\n                                            timeIn: '00:00:00',\n                                            timeOut: '00:00:00',\n                                            duration: '00:00:00'\n                                        }\n                                    ]);\n                                },\n                                style: {\n                                    background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '25px',\n                                    padding: '15px 40px',\n                                    fontSize: '1.1rem',\n                                    fontWeight: 'bold',\n                                    cursor: 'pointer',\n                                    boxShadow: '0 4px 15px rgba(220,53,69,0.3)'\n                                },\n                                children: \"\\uD83D\\uDDD1️ مسح البيانات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 558,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 623,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"zuKwFwf3n4PMZi1YCRQjm69g+B8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});