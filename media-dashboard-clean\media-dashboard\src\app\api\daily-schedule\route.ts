import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// استيراد البيانات المشتركة
import { getAllMediaItems, getMediaItemById } from '../shared-data';

// Helper functions
function getWeekStart(date: Date): Date {
  const d = new Date(date);
  const day = d.getDay();
  const diff = d.getDate() - day;
  return new Date(d.setDate(diff));
}

function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

function timeToMinutes(time: string): number {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
}

function addMinutesToTime(timeStr: string, minutes: number): string {
  const totalMinutes = timeToMinutes(timeStr) + minutes;
  const hours = Math.floor(totalMinutes / 60) % 24;
  const mins = totalMinutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

// GET - جلب الجدول الإذاعي لتاريخ محدد
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const dateParam = searchParams.get('date');
    
    if (!dateParam) {
      return NextResponse.json(
        { success: false, error: 'التاريخ مطلوب' },
        { status: 400 }
      );
    }

    const date = new Date(dateParam + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية
    const dayOfWeek = date.getDay();

    // حساب بداية الأسبوع (الأحد)
    const sunday = new Date(date);
    sunday.setDate(date.getDate() - date.getDay());
    const weekStart = sunday.toISOString().split('T')[0];

    // أسماء الأيام للتوضيح
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

    console.log('🔍 تفاصيل حساب الأسبوع:');
    console.log('  📅 التاريخ المطلوب:', dateParam);
    console.log('  📅 التاريخ المحول:', date.toISOString().split('T')[0]);
    console.log('  📊 يوم الأسبوع (رقم):', dayOfWeek);
    console.log('  📊 يوم الأسبوع (اسم):', dayNames[dayOfWeek]);
    console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);
    console.log('  📅 يوم الأحد:', sunday.toISOString().split('T')[0]);

    // التحقق من وجود جدول محفوظ مسبقاً
    const savedFileName = `daily-schedule-${dateParam}.json`;
    const savedFilePath = path.join(process.cwd(), 'saved-schedules', savedFileName);

    if (fs.existsSync(savedFilePath)) {
      console.log('📂 تم العثور على جدول محفوظ:', savedFileName);

      try {
        const savedData = JSON.parse(fs.readFileSync(savedFilePath, 'utf8'));

        console.log('✅ تم تحميل الجدول المحفوظ بنجاح');
        console.log('📊 إحصائيات الجدول المحفوظ:', {
          totalRows: savedData.totalRows,
          segments: savedData.segments,
          fillers: savedData.fillers,
          emptyRows: savedData.emptyRows,
          savedAt: savedData.savedAt
        });

        // جلب المواد المتاحة للقائمة الجانبية
        const allMedia = getAllMediaItems();

        return NextResponse.json({
          success: true,
          data: {
            date: dateParam,
            dayOfWeek,
            scheduleItems: [], // سيتم ملؤها من الجدول المحفوظ
            scheduleRows: savedData.scheduleRows,
            availableMedia: allMedia
          },
          fromSavedFile: true,
          savedAt: savedData.savedAt
        });
      } catch (error) {
        console.error('❌ خطأ في قراءة الجدول المحفوظ:', error);
        // المتابعة لبناء جدول جديد
      }
    }

    // جلب البيانات من الخريطة البرامجية
    const scheduleResponse = await fetch(`${request.nextUrl.origin}/api/weekly-schedule?weekStart=${weekStart}`);
    const scheduleData = await scheduleResponse.json();

    if (!scheduleData.success) {
      return NextResponse.json(
        { success: false, error: 'فشل في جلب بيانات الخريطة البرامجية' },
        { status: 500 }
      );
    }

    // فلترة المواد لليوم المحدد فقط (المواد الأصلية + الإعادات)
    console.log('📦 إجمالي المواد في الخريطة:', scheduleData.data.scheduleItems?.length || 0);

    // عرض عينة من المواد للتحقق
    if (scheduleData.data.scheduleItems && scheduleData.data.scheduleItems.length > 0) {
      console.log('📋 عينة من المواد في الخريطة:');
      scheduleData.data.scheduleItems.slice(0, 5).forEach((item: any, index: number) => {
        console.log(`  ${index + 1}. ${item.mediaItem?.name} - يوم ${item.dayOfWeek} - ${item.startTime} - إعادة: ${item.isRerun}`);
      });
    }

    const dayItems = scheduleData.data.scheduleItems.filter((item: any) => {
      const matches = item.dayOfWeek === dayOfWeek;
      if (matches) {
        console.log(`✅ مادة متطابقة: ${item.mediaItem?.name} - يوم ${item.dayOfWeek} - ${item.startTime}`);
      }
      return matches;
    });

    // فصل المواد الأصلية عن الإعادات
    const originalItems = dayItems.filter((item: any) => !item.isRerun);
    const rerunItems = dayItems.filter((item: any) => item.isRerun);

    // ترتيب المواد حسب الوقت
    const sortedOriginalItems = originalItems.sort((a: any, b: any) => 
      a.startTime.localeCompare(b.startTime)
    );

    // بناء الجدول الإذاعي الاحترافي
    const scheduleRows = [];

    // دالة لحساب الوقت التالي
    const calculateNextTime = (startTime: string, duration: string): string => {
      const [startHours, startMins] = startTime.split(':').map(Number);
      const [durHours, durMins, durSecs] = duration.split(':').map(Number);

      let totalMinutes = startHours * 60 + startMins;
      totalMinutes += durHours * 60 + durMins + Math.ceil(durSecs / 60);

      const hours = Math.floor(totalMinutes / 60) % 24;
      const minutes = totalMinutes % 60;

      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    };

    // دالة لحساب الوقت المتبقي حتى الهدف
    const calculateRemainingTime = (currentTime: string, targetTime: string): number => {
      const currentMinutes = timeToMinutes(currentTime);
      let targetMinutes = timeToMinutes(targetTime);

      // إذا كان الهدف في اليوم التالي
      if (targetMinutes <= currentMinutes) {
        targetMinutes += 24 * 60;
      }

      return targetMinutes - currentMinutes;
    };

    // ترتيب المواد حسب الوقت الطبيعي (08:00 أولاً)
    const allItems = [...dayItems].sort((a, b) => {
      const timeA = a.startTime;
      const timeB = b.startTime;

      // ترتيب طبيعي: 08:00, 09:00, ..., 23:00, 00:00, 01:00, ..., 07:00
      const getTimeOrder = (time: string) => {
        const hour = parseInt(time.split(':')[0]);
        // 08:00-23:59 = 0-15, 00:00-07:59 = 16-23
        return hour >= 8 ? hour - 8 : hour + 16;
      };

      const orderA = getTimeOrder(timeA);
      const orderB = getTimeOrder(timeB);

      if (orderA !== orderB) return orderA - orderB;
      return timeA.localeCompare(timeB);
    });

    console.log('🏗️ بناء جدول إذاعي لـ', allItems.length, 'مادة');

    // بناء الجدول الإذاعي الاحترافي
    let isFirstSegment = true;

    allItems.forEach((item: any, itemIndex: number) => {
      // التعامل مع المواد المؤقتة
      if (item.isTemporary) {
        console.log(`🟣 إضافة مادة مؤقتة: ${item.mediaItem?.name} (3 سيجمنت × 13 دقيقة)`);

        // إضافة 3 سيجمنت للمادة المؤقتة
        for (let segIndex = 0; segIndex < 3; segIndex++) {
          scheduleRows.push({
            id: `temp_segment_${item.id}_${segIndex}`,
            type: 'segment',
            time: isFirstSegment ? '08:00:00' : undefined,
            content: `${item.mediaItem?.name || 'مادة مؤقتة'} - سيجمنت ${segIndex + 1}${item.isRerun ? ' (إعادة)' : ''} [مؤقت]`,
            mediaItemId: item.mediaItemId,
            segmentId: `temp_seg_${segIndex}`,
            segmentCode: `TEMP_${item.mediaItemId}_${segIndex + 1}`, // كود للمواد المؤقتة
            duration: '00:13:00', // 13 دقيقة لكل سيجمنت
            isRerun: item.isRerun || false,
            isTemporary: true,
            canDelete: true,
            originalStartTime: item.startTime
          });

          isFirstSegment = false;

          // إضافة صفوف فارغة بين السيجمنتات (3 صفوف فقط)
          if (segIndex < 2) {
            for (let i = 0; i < 3; i++) {
              scheduleRows.push({
                id: `empty_temp_seg_${item.id}_${segIndex}_${i}`,
                type: 'empty',
                canDelete: true
              });
            }
          }
        }
      } else {
        // المواد العادية
        if (!item.mediaItem || !item.mediaItem.segments) return;

        const mediaItem = item.mediaItem;

        console.log(`📺 إضافة المادة: ${mediaItem.name} (${mediaItem.segments.length} سيجمنت)`);

        // إضافة السيجمنتات بدون أوقات (إلا الأول)
        mediaItem.segments.forEach((segment: any, segIndex: number) => {
          scheduleRows.push({
            id: `segment_${item.id}_${segment.id}`,
            type: 'segment',
            time: isFirstSegment ? '08:00:00' : undefined, // الوقت فقط للسيجمنت الأول
            content: `${mediaItem.name} - ${segment.name}${item.isRerun ? ' (إعادة)' : ''}`,
            mediaItemId: item.mediaItemId,
            segmentId: segment.id,
            segmentCode: segment.code || segment.segmentCode || `${mediaItem.id}_${segment.segmentNumber}`, // كود المادة
            duration: segment.duration,
            isRerun: item.isRerun || false,
            canDelete: true,
            originalStartTime: item.startTime // حفظ الوقت الأصلي للمرجع
          });

          isFirstSegment = false;

          // إضافة صفوف فارغة بين السيجمنتات (3 صفوف فقط)
          if (segIndex < mediaItem.segments.length - 1) {
            for (let i = 0; i < 3; i++) {
              scheduleRows.push({
                id: `empty_seg_${item.id}_${segment.id}_${i}`,
                type: 'empty',
                canDelete: true
              });
            }
          }
        });
      }

      // إضافة 5 صفوف فارغة بين المواد
      if (itemIndex < allItems.length - 1) {
        const nextItem = allItems[itemIndex + 1];

        for (let i = 0; i < 5; i++) {
          scheduleRows.push({
            id: `filler_${item.id}_${i}`,
            type: 'empty',
            canDelete: true,
            targetTime: nextItem.startTime // حفظ الوقت المستهدف
          });
        }
      } else {
        // المادة الأخيرة - إضافة 5 صفوف فقط
        for (let i = 0; i < 5; i++) {
          scheduleRows.push({
            id: `end_filler_${item.id}_${i}`,
            type: 'empty',
            canDelete: true
          });
        }
      }
    });



    // جلب جميع المواد المتاحة للقائمة الجانبية
    const allMedia = getAllMediaItems();
    
    console.log(`📦 تم جلب ${dayItems.length} مادة إجمالية`);
    console.log(`📋 المواد المجدولة: ${JSON.stringify(dayItems.map(i => i.mediaItem?.name))}`);
    console.log(`📝 صفوف الجدول: ${scheduleRows.length} صف`);
    console.log(`📚 المواد المتاحة: ${allMedia.length} مادة`);

    return NextResponse.json({
      success: true,
      data: {
        date: dateParam,
        dayOfWeek,
        scheduleItems: dayItems,
        scheduleRows,
        availableMedia: allMedia
      }
    });

  } catch (error) {
    console.error('❌ خطأ في جلب الجدول الإذاعي:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الجدول الإذاعي' },
      { status: 500 }
    );
  }
}

// POST - حفظ تعديلات الجدول الإذاعي
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { date, scheduleRows } = body;

    if (!date || !scheduleRows) {
      return NextResponse.json(
        { success: false, error: 'البيانات مطلوبة' },
        { status: 400 }
      );
    }

    // حفظ تعديلات الجدول الإذاعي في ملف JSON
    console.log('💾 حفظ تعديلات الجدول الإذاعي للتاريخ:', date);
    console.log('📝 عدد الصفوف:', scheduleRows.length);

    // إنشاء اسم ملف فريد للتاريخ
    const fileName = `daily-schedule-${date}.json`;
    const filePath = path.join(process.cwd(), 'saved-schedules', fileName);

    // إنشاء مجلد إذا لم يكن موجوداً
    const dirPath = path.dirname(filePath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }

    // بيانات الحفظ
    const saveData = {
      date: date,
      savedAt: new Date().toISOString(),
      scheduleRows: scheduleRows,
      totalRows: scheduleRows.length,
      segments: scheduleRows.filter((row: any) => row.type === 'segment').length,
      fillers: scheduleRows.filter((row: any) => row.type === 'filler').length,
      emptyRows: scheduleRows.filter((row: any) => row.type === 'empty').length
    };

    // حفظ الملف
    fs.writeFileSync(filePath, JSON.stringify(saveData, null, 2), 'utf8');

    console.log('✅ تم حفظ الجدول في:', filePath);

    return NextResponse.json({
      success: true,
      message: 'تم حفظ التعديلات بنجاح',
      savedFile: fileName,
      stats: {
        totalRows: saveData.totalRows,
        segments: saveData.segments,
        fillers: saveData.fillers,
        emptyRows: saveData.emptyRows
      }
    });

  } catch (error) {
    console.error('❌ خطأ في حفظ الجدول الإذاعي:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في حفظ التعديلات' },
      { status: 500 }
    );
  }
}
