import { NextResponse } from 'next/server';
import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';

// تحميل بيانات الجدول الأسبوعي
function loadWeeklySchedule(weekStart: string) {
  try {
    // محاولة عدة مسارات محتملة للملف
    const possiblePaths = [
      path.join(process.cwd(), 'weekly-schedules.json'),
      path.join(process.cwd(), 'data', 'weekly-schedules.json'),
      path.join(__dirname, '..', '..', '..', '..', 'weekly-schedules.json'),
      path.join(__dirname, '..', '..', '..', '..', 'data', 'weekly-schedules.json')
    ];

    let filePath = null;
    for (const testPath of possiblePaths) {
      console.log('🔍 البحث عن ملف الجداول في:', testPath);
      if (fs.existsSync(testPath)) {
        filePath = testPath;
        console.log('✅ تم العثور على الملف في:', filePath);
        break;
      }
    }

    if (!filePath) {
      console.log('📂 لم يتم العثور على ملف الجداول في أي من المسارات المحتملة');
      return null;
    }

    const fileContent = fs.readFileSync(filePath, 'utf8');
    const allSchedules = JSON.parse(fileContent);

    const schedule = allSchedules[weekStart];
    if (!schedule) {
      console.log('📅 لم يتم العثور على جدول للأسبوع:', weekStart);
      return null;
    }

    console.log('📂 تم تحميل الجدول الأسبوعي بنجاح');
    return schedule;
  } catch (error) {
    console.error('❌ خطأ في تحميل الجدول:', error);
    return null;
  }
}

// تحديد لون الخلية حسب نوع المحتوى
function getCellColor(item: any): string {
  if (!item) {
    return 'FFFFFFFF'; // أبيض للخلايا الفارغة
  }

  // التحقق من كونها مادة مؤقتة
  if (item.isTemporary || item.type === 'temporary') {
    console.log('🟠 مادة مؤقتة - لون برتقالي:', item.name || item.title);
    return 'FFFFA500'; // برتقالي للمواد المؤقتة
  }

  // التحقق من كونها إعادة
  if (item.isRerun || item.type === 'rerun' || (item.mediaItem && item.mediaItem.name?.includes('(إعادة)'))) {
    console.log('🔘 إعادة - لون رمادي:', item.mediaItem?.name || item.name);
    return 'FFC0C0C0'; // رمادي للإعادات
  }

  // البرايم بلون ذهبي
  console.log('🟡 برايم - لون ذهبي:', item.mediaItem?.name || item.name);
  return 'FFFFD700'; // ذهبي للبرايم
}

// تحديد لون النص
function getTextColor(backgroundColor: string): string {
  if (backgroundColor === 'FFFFFFFF') {
    return 'FF000000'; // نص أسود على خلفية بيضاء
  }
  return 'FF000000'; // نص أسود على باقي الخلفيات
}

// تنسيق الوقت للعرض
function formatTime(hour: number): string {
  return `${hour.toString().padStart(2, '0')}:00`;
}

// أسماء الأيام
function getDayName(dayIndex: number): string {
  const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  return days[dayIndex] || `يوم ${dayIndex}`;
}

// دالة تحويل الوقت إلى دقائق
function timeToMinutes(time: string): number {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
}

// دالة إضافة دقائق للوقت
function addMinutesToTime(time: string, minutes: number): string {
  const totalMinutes = timeToMinutes(time) + minutes;
  const hours = Math.floor(totalMinutes / 60) % 24;
  const mins = totalMinutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

// دالة توليد الإعادات التلقائية
function generateReruns(scheduleItems: any[], weekStart: string, tempItems: any[] = []): any[] {
  const reruns: any[] = [];

  console.log(`🔄 توليد الإعادات للأسبوع: ${weekStart}`);

  // دمج المواد العادية والمؤقتة
  const allItems = [...scheduleItems, ...tempItems];
  const regularItems = allItems.filter(item => !item.isRerun && !item.isTemporary);
  const temporaryItems = allItems.filter(item => !item.isRerun && item.isTemporary);

  console.log(`📊 المواد: ${regularItems.length} عادية + ${temporaryItems.length} مؤقتة`);

  // تجميع المواد حسب اليوم لتوليد إعادات متتالية
  const itemsByDay = new Map<number, any[]>();

  // تجميع المواد العادية حسب اليوم
  regularItems.forEach(item => {
    const dayOfWeek = item.dayOfWeek;
    if (!itemsByDay.has(dayOfWeek)) {
      itemsByDay.set(dayOfWeek, []);
    }
    itemsByDay.get(dayOfWeek)!.push(item);
  });

  // تجميع المواد المؤقتة حسب اليوم
  temporaryItems.forEach(item => {
    const dayOfWeek = item.dayOfWeek;
    if (!itemsByDay.has(dayOfWeek)) {
      itemsByDay.set(dayOfWeek, []);
    }
    itemsByDay.get(dayOfWeek)!.push(item);
  });

  // ترتيب المواد في كل يوم حسب الوقت
  itemsByDay.forEach((items, day) => {
    items.sort((a, b) => a.startTime.localeCompare(b.startTime));
  });

  // توليد الإعادات لكل يوم
  itemsByDay.forEach((dayItems, dayOfWeek) => {
    if (dayItems.length > 0) {
      const dayReruns = generateSequentialReruns(dayItems, weekStart, dayOfWeek);
      reruns.push(...dayReruns);
    }
  });

  return reruns;
}

// دالة توليد إعادات متتالية لمواد يوم واحد
function generateSequentialReruns(dayItems: any[], weekStart: string, dayOfWeek: number): any[] {
  const reruns: any[] = [];

  if (dayItems.length === 0) return reruns;

  console.log(`🔄 توليد إعادات لـ ${dayItems.length} مادة من اليوم ${dayOfWeek}`);

  // تحديد أوقات الإعادات حسب اليوم
  let rerunStartTime: string;
  let rerunDay: number;

  if ([0, 1, 2, 3].includes(dayOfWeek)) {
    // الأحد-الأربعاء: الإعادات تبدأ من 00:00 في نفس اليوم
    rerunStartTime = '00:00';
    rerunDay = dayOfWeek;
  } else if ([4, 5, 6].includes(dayOfWeek)) {
    // الخميس-السبت: الإعادات تبدأ من 02:00 في نفس اليوم
    rerunStartTime = '02:00';
    rerunDay = dayOfWeek;
  } else {
    return reruns;
  }

  // إنشاء قائمة مستمرة من المواد
  function getNextItem(index: number) {
    return dayItems[index % dayItems.length];
  }

  let currentTime = rerunStartTime;
  let itemSequenceIndex = 0;

  // الجزء الأول: حتى 08:00
  while (timeToMinutes(currentTime) < timeToMinutes('08:00')) {
    const item = getNextItem(itemSequenceIndex);
    const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);
    const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);

    if (timeToMinutes(rerunEndTime) <= timeToMinutes('08:00')) {
      reruns.push({
        id: `rerun_${item.id}_seq${itemSequenceIndex}_${rerunDay}`,
        mediaItemId: item.mediaItemId,
        dayOfWeek: rerunDay,
        startTime: currentTime,
        endTime: rerunEndTime,
        weekStart: weekStart,
        isRerun: true,
        isTemporary: item.isTemporary || false,
        mediaItem: item.mediaItem,
        originalId: item.id
      });

      currentTime = rerunEndTime;
      itemSequenceIndex++;
    } else {
      break;
    }
  }

  // الجزء الثاني: اليوم التالي من 08:00 إلى 18:00
  let nextDay: number;
  if (dayOfWeek === 6) { // السبت
    nextDay = 0; // الأحد
  } else {
    nextDay = (rerunDay + 1) % 7;
  }

  currentTime = '08:00';

  while (timeToMinutes(currentTime) < timeToMinutes('18:00')) {
    const item = getNextItem(itemSequenceIndex);
    const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);
    const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);

    if (timeToMinutes(rerunEndTime) <= timeToMinutes('18:00')) {
      reruns.push({
        id: `rerun_${item.id}_seq${itemSequenceIndex}_${nextDay}`,
        mediaItemId: item.mediaItemId,
        dayOfWeek: nextDay,
        startTime: currentTime,
        endTime: rerunEndTime,
        weekStart: weekStart,
        isRerun: true,
        isTemporary: item.isTemporary || false,
        mediaItem: item.mediaItem,
        originalId: item.id
      });

      currentTime = rerunEndTime;
      itemSequenceIndex++;
    } else {
      break;
    }
  }

  return reruns;
}

export async function GET(request: Request) {
  try {
    console.log('📊 بدء عملية تصدير الخريطة الأسبوعية...');
    
    // استخراج معاملات الطلب
    const url = new URL(request.url);
    const weekStart = url.searchParams.get('weekStart');
    
    if (!weekStart) {
      return NextResponse.json({ 
        success: false, 
        error: 'يجب تحديد تاريخ بداية الأسبوع' 
      }, { status: 400 });
    }
    
    // تحميل بيانات الجدول
    const scheduleData = loadWeeklySchedule(weekStart);

    if (!scheduleData) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم العثور على بيانات الجدول'
      }, { status: 404 });
    }

    // تحميل المواد المؤقتة
    let temporaryItems: any[] = [];
    try {
      const possibleTempPaths = [
        path.join(process.cwd(), 'data', 'temp-items.json'),
        path.join(process.cwd(), 'temporary-items.json'),
        path.join(process.cwd(), 'temp-data.json')
      ];

      for (const tempPath of possibleTempPaths) {
        if (fs.existsSync(tempPath)) {
          const tempContent = fs.readFileSync(tempPath, 'utf8');
          const parsedTemp = JSON.parse(tempContent);

          // التأكد من أن البيانات مصفوفة
          if (Array.isArray(parsedTemp)) {
            temporaryItems = parsedTemp;
          } else if (parsedTemp && typeof parsedTemp === 'object') {
            // إذا كانت البيانات كائن، حاول استخراج المصفوفة
            temporaryItems = Object.values(parsedTemp).flat();
          }

          console.log('📦 تم تحميل', temporaryItems.length, 'مادة مؤقتة من:', tempPath);
          break;
        }
      }

      if (temporaryItems.length === 0) {
        console.log('📦 لا توجد مواد مؤقتة محفوظة');
      }
    } catch (error) {
      console.log('📦 خطأ في تحميل المواد المؤقتة:', error);
      temporaryItems = []; // تأكد من أن المتغير مصفوفة
    }

    // توليد الإعادات التلقائية
    const reruns = generateReruns(scheduleData, weekStart, temporaryItems);
    console.log('🔄 تم توليد', reruns.length, 'إعادة');

    // دمج جميع البيانات (البرايم + الإعادات + المواد المؤقتة)
    const allScheduleData = [...scheduleData, ...reruns, ...temporaryItems];
    console.log('📊 إجمالي المواد للتصدير:', allScheduleData.length);
    
    console.log('📋 بدء إنشاء ملف Excel للخريطة...');
    
    // إنشاء ملف Excel
    const workbook = new ExcelJS.Workbook();
    
    // تعيين خصائص الملف
    workbook.creator = 'نظام إدارة المواد الإعلامية';
    workbook.title = 'الخريطة الأسبوعية للبث';
    workbook.subject = `خريطة الأسبوع ${weekStart}`;
    workbook.created = new Date();
    
    // إنشاء ورقة العمل
    const worksheet = workbook.addWorksheet('الخريطة الأسبوعية');
    
    // تعيين اتجاه الورقة من اليمين لليسار
    worksheet.views = [{ 
      rightToLeft: true,
      zoomScale: 70 
    }];
    
    // إنشاء الرؤوس مع التواريخ
    const startDate = new Date(weekStart);
    const headers = ['الوقت'];

    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dayName = getDayName(i);
      const dateStr = currentDate.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit'
      });

      headers.push(`${dayName}\n${dateStr}`);
    }

    worksheet.addRow(headers);
    
    // تنسيق الرؤوس
    const headerRow = worksheet.getRow(1);
    headerRow.height = 30;
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF4472C4' }
      };
      cell.font = {
        bold: true,
        color: { argb: 'FFFFFFFF' },
        size: 14
      };
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle'
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
    
    // تحويل البيانات إلى شبكة منظمة حسب اليوم والساعة
    const scheduleGrid: any[][] = Array(7).fill(null).map(() => Array(24).fill(null));

    // ملء الشبكة بالبيانات
    if (Array.isArray(allScheduleData)) {
      allScheduleData.forEach((item: any) => {
        if (item && item.dayOfWeek !== undefined && item.startTime) {
          const dayIndex = item.dayOfWeek;
          const [hours] = item.startTime.split(':').map(Number);

          // تحويل الساعة إلى فهرس في الشبكة (8 صباحاً = فهرس 0)
          let hourIndex;
          if (hours >= 8) {
            hourIndex = hours - 8; // 8-23 -> 0-15
          } else {
            hourIndex = hours + 16; // 0-7 -> 16-23
          }

          if (dayIndex >= 0 && dayIndex < 7 && hourIndex >= 0 && hourIndex < 24) {
            scheduleGrid[dayIndex][hourIndex] = item;
          }
        }
      });
    }

    console.log('📊 تم تنظيم البيانات في شبكة 7×24');

    // إضافة البيانات (24 ساعة × 7 أيام)
    for (let hour = 8; hour < 32; hour++) { // من 8 صباحاً إلى 8 صباحاً اليوم التالي
      const displayHour = hour >= 24 ? hour - 24 : hour;
      const timeLabel = formatTime(displayHour);

      const rowData = [timeLabel];

      // إضافة بيانات كل يوم
      for (let day = 0; day < 7; day++) {
        const hourIndex = hour - 8; // تحويل إلى فهرس الشبكة
        const hourData = scheduleGrid[day][hourIndex];

        let cellContent = '';
        if (hourData) {
          // التعامل مع المواد المؤقتة
          if (hourData.isTemporary || hourData.type === 'temporary') {
            cellContent = hourData.name || hourData.title || 'مادة مؤقتة';
            if (hourData.duration) {
              cellContent += ` (${hourData.duration})`;
            }
          }
          // التعامل مع المواد العادية
          else if (hourData.mediaItem && hourData.mediaItem.name) {
            cellContent = hourData.mediaItem.name;

            // إضافة معلومات إضافية
            if (hourData.episodeNumber) {
              cellContent += ` - ح${hourData.episodeNumber}`;
            }
            if (hourData.partNumber) {
              cellContent += ` - ج${hourData.partNumber}`;
            }
            if (hourData.isRerun) {
              cellContent += ' (إعادة)';
            }
          }
          // التعامل مع البيانات المباشرة (للإعادات)
          else if (hourData.name) {
            cellContent = hourData.name;
            if (hourData.isRerun) {
              cellContent += ' (إعادة)';
            }
          }
        }

        rowData.push(cellContent);
      }

      const row = worksheet.addRow(rowData);

      // تنسيق الصف
      row.eachCell((cell, colNumber) => {
        if (colNumber === 1) {
          // تنسيق عمود الوقت
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFF0F0F0' }
          };
          cell.font = {
            bold: true,
            size: 12
          };
        } else {
          // تنسيق خلايا البيانات
          const dayIndex = colNumber - 2;
          const hourIndex = hour - 8;
          const hourData = scheduleGrid[dayIndex][hourIndex];

          const backgroundColor = getCellColor(hourData);
          const textColor = getTextColor(backgroundColor);

          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: backgroundColor }
          };
          cell.font = {
            color: { argb: textColor },
            size: 10
          };
        }

        cell.alignment = {
          horizontal: 'center',
          vertical: 'middle',
          wrapText: true
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });

      // تعيين ارتفاع الصف
      row.height = 25;
    }
    
    // تعيين عرض الأعمدة
    const columnWidths = [12, 20, 20, 20, 20, 20, 20, 20]; // الوقت + 7 أيام
    columnWidths.forEach((width, index) => {
      worksheet.getColumn(index + 1).width = width;
    });
    
    console.log('✅ تم إنشاء الخريطة الأسبوعية بنجاح');
    
    // تحويل إلى buffer
    const buffer = await workbook.xlsx.writeBuffer();
    
    console.log('✅ تم إنشاء ملف Excel بنجاح');
    
    // إرسال الملف
    const fileName = `Weekly_Schedule_${weekStart}.xlsx`;
    
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': buffer.byteLength.toString(),
        'Cache-Control': 'no-cache',
      },
    });
    
  } catch (error) {
    console.error('❌ خطأ في تصدير الخريطة:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'فشل في تصدير الخريطة' 
    }, { status: 500 });
  }
}
