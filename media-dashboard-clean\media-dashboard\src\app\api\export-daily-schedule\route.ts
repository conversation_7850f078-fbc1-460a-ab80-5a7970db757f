import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as path from 'path';

// تحديد ألوان الخلفية حسب حالة المادة
function getBackgroundColor(status: string, isTemporary: boolean = false): string {
  if (isTemporary) {
    return 'FFFFA500'; // برتقالي للمواد المؤقتة
  }
  
  switch (status) {
    case 'REJECTED':
    case 'EXPIRED':
      return 'FFFF0000'; // أحمر للمرفوض/المنتهي
    case 'VALID':
    case 'WAITING':
      return 'FF00FF00'; // أخضر للعادي/الانتظار
    default:
      return 'FFFFFFFF'; // أبيض افتراضي
  }
}

// تحديد لون النص
function getTextColor(backgroundColor: string): string {
  if (backgroundColor === 'FFFFFFFF') {
    return 'FF000000'; // نص أسود على خلفية بيضاء
  }
  return 'FF000000'; // نص أسود على باقي الخلفيات
}

// تنسيق الوقت للعرض (HH:MM:SS)
function formatTimeWithSeconds(time: string): string {
  if (time.includes(':')) {
    const parts = time.split(':');
    if (parts.length === 2) {
      return `${parts[0]}:${parts[1]}:00`;
    }
    return time;
  }
  return '00:00:00';
}

// تحويل المدة من تنسيق HH:MM:SS إلى ثواني
function durationToSeconds(duration: string): number {
  const parts = duration.split(':').map(Number);
  if (parts.length === 3) {
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  }
  return 0;
}

// تنسيق المدة للعرض
function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json(
        { success: false, error: 'التاريخ مطلوب' },
        { status: 400 }
      );
    }

    console.log('📊 بدء عملية تصدير الجدول الإذاعي اليومي للتاريخ:', date);

    // تحميل الجدول الإذاعي المحفوظ
    const savedSchedulePath = path.join(process.cwd(), 'saved-schedules', `daily-schedule-${date}.json`);
    
    if (!fs.existsSync(savedSchedulePath)) {
      return NextResponse.json(
        { success: false, error: 'لا يوجد جدول محفوظ لهذا التاريخ' },
        { status: 404 }
      );
    }

    const savedData = JSON.parse(fs.readFileSync(savedSchedulePath, 'utf8'));
    const scheduleRows = savedData.scheduleRows || [];

    console.log('📂 تم تحميل الجدول المحفوظ بنجاح');
    console.log('📊 عدد الصفوف:', scheduleRows.length);

    // إنشاء مصفوفة البيانات للتصدير
    const exportData: any[] = [];

    // إضافة تاريخ اليوم أعلى الجدول
    exportData.push([`تاريخ الإذاعة: ${date}`, '', '', '', '', '', '', '', '']);
    exportData.push(['', '', '', '', '', '', '', '', '']); // صف فارغ

    // إضافة رأس الجدول (من اليمين لليسار حسب الصورة)
    const headers = [
      'DURATION',
      'OUT',
      'IN',
      'رقم الهارد',
      'DESCRIPTION',
      'TITLE',
      'TYPE',
      'وقت الإذاعة',
      'ID CODE'
    ];
    exportData.push(headers);

    // معالجة كل صف في الجدول
    scheduleRows.forEach((row: any, index: number) => {
      if (row.type === 'segment' && row.mediaItem) {
        const mediaItem = row.mediaItem;
        const segment = row.segment;

        // حساب الأوقات الفعلية من الجدول الإذاعي
        const broadcastTime = formatTimeWithSeconds(row.broadcastTime || row.startTime || '00:00:00');
        const startTime = formatTimeWithSeconds(row.startTime || '00:00:00');
        const endTime = formatTimeWithSeconds(row.endTime || '00:00:00');
        const duration = segment?.duration || '00:00:00';

        // تحديد النوع حسب الصورة المرفقة
        let type = 'Promo'; // افتراضي
        switch (mediaItem.type) {
          case 'MOVIE':
            type = 'Movie';
            break;
          case 'SERIES':
            type = 'Series';
            break;
          case 'PROGRAM':
            type = 'Promo'; // البرامج تظهر كـ Promo في الصورة
            break;
          case 'PROMO':
            type = 'Promo';
            break;
          case 'FILLER':
            type = 'Filler';
            break;
          case 'STING':
            type = 'Sting';
            break;
          case 'FILL_IN':
            type = 'Fill IN';
            break;
          default:
            type = 'Promo';
        }

        // تحديد العنوان والوصف
        let title = mediaItem.name || 'غير محدد';
        let description = `${date} ${startTime} ${mediaItem.name || 'غير محدد'}`;

        // إضافة رقم الحلقة/الجزء للعنوان والوصف
        if (mediaItem.episodeNumber) {
          title += ` - ح${mediaItem.episodeNumber}`;
          description += ` - ح${mediaItem.episodeNumber}`;
        }
        if (mediaItem.partNumber) {
          title += ` - ج${mediaItem.partNumber}`;
          description += ` - ج${mediaItem.partNumber}`;
        }
        if (mediaItem.seasonNumber) {
          title += ` - س${mediaItem.seasonNumber}`;
          description += ` - س${mediaItem.seasonNumber}`;
        }

        // إضافة رقم الجزء للوصف
        if (segment?.segmentNumber) {
          description += ` - الجزء ${segment.segmentNumber}`;
        }

        // كود المادة
        const idCode = segment?.code || `DPR${String(index + 1).padStart(5, '0')}`;

        // إضافة الصف (من اليمين لليسار حسب الصورة)
        const rowData = [
          duration,           // DURATION
          endTime,            // OUT
          startTime,          // IN
          'SERVER',           // رقم الهارد
          description,        // DESCRIPTION
          title,              // TITLE
          type,               // TYPE
          broadcastTime,      // وقت الإذاعة الفعلي
          idCode              // ID CODE
        ];

        exportData.push(rowData);
      } else if (row.type === 'filler') {
        // صف فارغ (فيلر)
        const broadcastTime = formatTimeWithSeconds(row.broadcastTime || row.startTime || '00:00:00');
        const startTime = formatTimeWithSeconds(row.startTime || '00:00:00');
        const endTime = formatTimeWithSeconds(row.endTime || '00:00:00');
        const duration = row.duration || '00:11:07';

        const rowData = [
          duration,           // DURATION
          endTime,            // OUT
          startTime,          // IN
          'SERVER',           // رقم الهارد
          `${date} ${broadcastTime} فيلر`,  // DESCRIPTION
          'فيلر',             // TITLE
          'Filler',           // TYPE
          broadcastTime,      // وقت الإذاعة الفعلي
          `DPR${String(index + 1).padStart(5, '0')}`  // ID CODE
        ];

        exportData.push(rowData);
      }
    });

    console.log('📋 تم تحضير البيانات للتصدير');
    console.log('📊 عدد الصفوف المصدرة:', exportData.length - 1);

    // إنشاء مصنف Excel
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(exportData);

    // تطبيق التنسيق
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');

    for (let row = range.s.r; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];

        if (cell) {
          // تنسيق الخط
          cell.s = {
            font: {
              name: 'Arial',
              sz: 10,
              bold: row === 0, // رأس الجدول عريض
              color: { rgb: 'FF000000' } // نص أسود
            },
            alignment: {
              horizontal: 'center',
              vertical: 'center',
              wrapText: true,
              readingOrder: 2 // RTL
            },
            border: {
              top: { style: 'thin', color: { rgb: 'FF000000' } },
              bottom: { style: 'thin', color: { rgb: 'FF000000' } },
              left: { style: 'thin', color: { rgb: 'FF000000' } },
              right: { style: 'thin', color: { rgb: 'FF000000' } }
            }
          };

          // تحديد الألوان حسب نوع المحتوى
          let backgroundColor = 'FFFFFFFF'; // أبيض افتراضي
          let textColor = 'FF000000'; // نص أسود

          // تنسيق خاص لصف التاريخ
          if (row === 0) {
            cell.s.font.bold = true;
            cell.s.font.sz = 12;
            cell.s.alignment.horizontal = 'right';
            backgroundColor = 'FFFFFFFF';
          }
          // تنسيق خاص لرأس الجدول
          else if (row === 2) {
            cell.s.font.bold = true;
            cell.s.alignment.horizontal = 'center';
            backgroundColor = 'FF4CAF50'; // أخضر للرأس
            textColor = 'FFFFFFFF'; // نص أبيض
          }
          // تنسيق المحتوى حسب النوع
          else if (row > 2) {
            const rowData = exportData[row];
            if (rowData && rowData.length > 6) {
              const type = rowData[6]; // عمود TYPE

              // تحديد اللون حسب النوع
              switch (type) {
                case 'Movie':
                case 'Series':
                case 'Promo':
                  backgroundColor = 'FF90EE90'; // أخضر فاتح للمحتوى الأساسي
                  break;
                case 'Promo':
                  backgroundColor = 'FFFFE4B5'; // بيج للبرومو
                  break;
                case 'Sting':
                  backgroundColor = 'FFFF6B6B'; // أحمر فاتح للستينغ
                  break;
                case 'Filler':
                  backgroundColor = 'FFFFA500'; // برتقالي للفيلر
                  break;
                case 'Song':
                  backgroundColor = 'FFDA70D6'; // بنفسجي للأغاني
                  break;
                case 'Fill IN':
                  backgroundColor = 'FF87CEEB'; // أزرق فاتح للـ Fill IN
                  break;
                default:
                  backgroundColor = 'FFFFFFFF'; // أبيض للباقي
              }
            }
          }

          cell.s.fill = {
            fgColor: { rgb: backgroundColor }
          };

          cell.s.font.color = { rgb: textColor };
        }
      }
    }

    // تحديد عرض الأعمدة (من اليمين لليسار حسب الصورة)
    const columnWidths = [
      { wch: 12 }, // DURATION
      { wch: 10 }, // OUT
      { wch: 10 }, // IN
      { wch: 12 }, // رقم الهارد
      { wch: 30 }, // DESCRIPTION
      { wch: 25 }, // TITLE
      { wch: 12 }, // TYPE
      { wch: 15 }, // وقت الإذاعة
      { wch: 15 }  // ID CODE
    ];
    worksheet['!cols'] = columnWidths;

    // إعداد اتجاه الشيت من اليمين لليسار مع زووم 70%
    if (!worksheet['!views']) worksheet['!views'] = [{}];
    worksheet['!views'][0] = {
      rightToLeft: true,
      zoomScale: 70,
      zoomScaleNormal: 70,
      zoomScalePageLayoutView: 70,
      showGridLines: true,
      showRowColHeaders: true
    };

    // إعداد خصائص المصنف للـ RTL
    if (!workbook.Workbook) workbook.Workbook = {};
    if (!workbook.Workbook.Views) workbook.Workbook.Views = [];
    workbook.Workbook.Views[0] = {
      rightToLeft: true
    };

    // إعداد خصائص الورقة
    if (!workbook.Sheets) workbook.Sheets = {};
    const sheetName = `جدول إذاعي ${date}`;
    if (!workbook.Sheets[sheetName]) workbook.Sheets[sheetName] = {};
    workbook.Sheets[sheetName]['!dir'] = 'rtl';

    // إضافة الورقة للمصنف
    XLSX.utils.book_append_sheet(workbook, worksheet, `جدول إذاعي ${date}`);

    console.log('✅ تم إنشاء ملف Excel بنجاح');

    // تحويل المصنف إلى buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // إرجاع الملف
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="Daily_Schedule_${date}.xlsx"`
      }
    });

  } catch (error) {
    console.error('❌ خطأ في تصدير الجدول الإذاعي:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في تصدير الجدول الإذاعي' },
      { status: 500 }
    );
  }
}
