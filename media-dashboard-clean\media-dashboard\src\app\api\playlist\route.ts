import { NextRequest, NextResponse } from 'next/server';

// استيراد البيانات المشتركة
import { mediaItems } from '../statistics/route';
import { scheduleItems, isSmallMedia } from '../schedule/route';
import { DynamicScheduler } from '@/lib/dynamicScheduler';

// بيانات جدول الإذاعة اليومية
let playlistItems: any[] = [];
let timelineBreaks: { [breakId: string]: any[] } = {};

// GET - جلب جدول الإذاعة اليومية باستخدام المحرك الديناميكي
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date') || new Date().toISOString().split('T')[0];
    const mode = searchParams.get('mode') || 'timeline'; // timeline أو table
    const dayOfWeek = new Date(date).getDay(); // 0 = الأحد

    // إذا كان الطلب للجدول البسيط
    if (mode === 'table') {
      // جلب العناصر المحفوظة لهذا التاريخ
      const savedItems = playlistItems.filter(item => item.date === date);

      // إذا لم توجد عناصر محفوظة، إنشاء جدول افتراضي من الخريطة البرامجية
      if (savedItems.length === 0) {
        const scheduledForToday = scheduleItems
          .filter(item => item.dayOfWeek === dayOfWeek)
          .map(item => ({
            ...item,
            mediaItem: mediaItems.find(m => m.id === item.mediaItemId)
          }))
          .filter(item => item.mediaItem)
          .sort((a, b) => a.startTime.localeCompare(b.startTime));

        // تحويل إلى تنسيق الجدول
        const tableItems = scheduledForToday.map((item, index) => ({
          id: `table_${Date.now()}_${index}`,
          name: item.mediaItem.name,
          type: item.mediaItem.type,
          startTime: item.startTime,
          endTime: item.endTime,
          duration: item.mediaItem.duration,
          code: item.mediaItem.code || '',
          status: getTimeStatus(item.startTime, item.endTime, new Date().toTimeString().slice(0, 8)),
          date,
          order: index + 1
        }));

        return NextResponse.json({
          success: true,
          data: tableItems
        });
      }

      // تحديث حالة العناصر المحفوظة
      const updatedItems = savedItems.map(item => ({
        ...item,
        status: getTimeStatus(item.startTime, item.endTime, new Date().toTimeString().slice(0, 8))
      }));

      return NextResponse.json({
        success: true,
        data: updatedItems
      });
    }

    // الوضع الافتراضي - Timeline
    // إنشاء محرك الجدولة الديناميكي
    const scheduler = new DynamicScheduler({
      dayStartTime: '08:00',
      dayEndTime: '08:00',
      defaultBreakDuration: '00:00:30',
      postContentBreakDuration: '00:01:00',
      autoBreakRules: {
        betweenSegments: true,
        afterContent: true,
        beforeContent: false
      }
    });

    // جلب المواد المجدولة لهذا اليوم مع ربطها بالمواد الإعلامية
    const scheduledForToday = scheduleItems
      .filter(item => item.dayOfWeek === dayOfWeek)
      .map(item => ({
        ...item,
        mediaItem: mediaItems.find(m => m.id === item.mediaItemId)
      }))
      .filter(item => item.mediaItem) // استبعاد المواد المحذوفة
      .sort((a, b) => a.startTime.localeCompare(b.startTime));

    // بناء الجدول الديناميكي
    const timeline = scheduler.buildDynamicTimeline(
      scheduledForToday,
      date,
      timelineBreaks
    );

    // فلترة المواد الصغيرة للفواصل
    const smallMediaForBreaks = mediaItems.filter(item => isSmallMedia(item.type));

    return NextResponse.json({
      success: true,
      data: timeline,
      date,
      dayOfWeek,
      currentTime: new Date().toTimeString().slice(0, 5),
      availableBreakMedia: smallMediaForBreaks,
      scheduledItems: scheduledForToday.length,
      statistics: {
        total: timeline.length,
        completed: timeline.filter(item => item.status === 'منتهي').length,
        current: timeline.filter(item => item.status === 'يُعرض الآن').length,
        upcoming: timeline.filter(item => item.status === 'قادم').length,
        breaks: timeline.filter(item => item.isBreak || item.isGap).length,
        segments: timeline.filter(item => item.type === 'SEGMENT').length,
        content: timeline.filter(item => item.type === 'CONTENT').length
      }
    });
  } catch (error) {
    console.error('Error fetching dynamic playlist:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب جدول الإذاعة الديناميكي' },
      { status: 500 }
    );
  }
}

// POST - إضافة مادة مخصصة لجدول الإذاعة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { mediaItemId, date, startTime, endTime, name, type } = body;

    // التحقق من البيانات المطلوبة
    if (!startTime || !endTime) {
      return NextResponse.json(
        { success: false, error: 'أوقات البداية والنهاية مطلوبة' },
        { status: 400 }
      );
    }

    // إنشاء عنصر جديد في جدول الإذاعة
    const newPlaylistItem = {
      id: `playlist_${Date.now()}`,
      mediaItemId: mediaItemId || null,
      date: date || new Date().toISOString().split('T')[0],
      startTime,
      endTime,
      name: name || '',
      type: type || 'مخصص',
      status: 'قادم',
      progress: 0,
      createdAt: new Date().toISOString()
    };

    playlistItems.push(newPlaylistItem);

    return NextResponse.json({
      success: true,
      data: newPlaylistItem,
      message: 'تم إضافة المادة لجدول الإذاعة بنجاح'
    });

  } catch (error) {
    console.error('Error adding to playlist:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المادة لجدول الإذاعة' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مادة من جدول الإذاعة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف العنصر مطلوب' },
        { status: 400 }
      );
    }

    const itemIndex = playlistItems.findIndex(item => item.id === id);
    if (itemIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'العنصر غير موجود' },
        { status: 404 }
      );
    }

    playlistItems.splice(itemIndex, 1);

    return NextResponse.json({
      success: true,
      message: 'تم حذف المادة من جدول الإذاعة بنجاح'
    });

  } catch (error) {
    console.error('Error deleting playlist item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المادة من جدول الإذاعة' },
      { status: 500 }
    );
  }
}

// دالة مساعدة لحساب المدة
function calculateDuration(startTime: string, endTime: string): string {
  const [startHours, startMinutes] = startTime.split(':').map(Number);
  const [endHours, endMinutes] = endTime.split(':').map(Number);
  
  const startTotalMinutes = startHours * 60 + startMinutes;
  const endTotalMinutes = endHours * 60 + endMinutes;
  
  const durationMinutes = endTotalMinutes - startTotalMinutes;
  
  if (durationMinutes <= 0) return '0 دقيقة';
  
  const hours = Math.floor(durationMinutes / 60);
  const minutes = durationMinutes % 60;
  
  if (hours > 0) {
    return `${hours} ساعة${minutes > 0 ? ` و${minutes} دقيقة` : ''}`;
  } else {
    return `${minutes} دقيقة`;
  }
}

// دوال مساعدة جديدة لنظام الجدولة المتقدم

// تحويل الوقت إلى دقائق
function timeToMinutes(time: string): number {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
}

// إضافة وقت إلى وقت آخر
function addTimeToTime(baseTime: string, addTime: string): string {
  const baseMinutes = timeToMinutes(baseTime);
  const addMinutes = timeToMinutes(addTime);
  const totalMinutes = baseMinutes + addMinutes;

  const hours = Math.floor(totalMinutes / 60) % 24;
  const minutes = totalMinutes % 60;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

// تحديد حالة العرض حسب الوقت
function getTimeStatus(startTime: string, endTime: string, currentTime: string): string {
  const currentMinutes = timeToMinutes(currentTime);
  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);

  if (currentMinutes < startMinutes) {
    return 'قادم';
  } else if (currentMinutes >= startMinutes && currentMinutes <= endMinutes) {
    return 'يُعرض الآن';
  } else {
    return 'منتهي';
  }
}

// حساب نسبة التقدم
function getTimeProgress(startTime: string, endTime: string, currentTime: string): number {
  const currentMinutes = timeToMinutes(currentTime);
  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);

  if (currentMinutes < startMinutes) {
    return 0;
  } else if (currentMinutes > endMinutes) {
    return 100;
  } else {
    const progress = ((currentMinutes - startMinutes) / (endMinutes - startMinutes)) * 100;
    return Math.round(Math.max(0, Math.min(100, progress)));
  }
}

// تصدير البيانات للاستخدام في API endpoints أخرى
export {
  playlistItems,
  timeToMinutes,
  addTimeToTime,
  getTimeStatus,
  getTimeProgress
};
