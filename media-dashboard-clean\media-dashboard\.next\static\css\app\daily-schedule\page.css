/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/daily-schedule/daily-schedule.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/* Daily Schedule Styles */
.daily-schedule-container {
  min-height: 100vh;
  background: #1a1d29;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  color: white;
}

/* Header */
.schedule-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  color: #333;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-name {
  font-weight: bold;
  color: #333;
  font-size: 1.1rem;
}

.user-role {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Controls */
.schedule-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.date-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-selector label {
  font-weight: bold;
  color: #333;
}

.glass-input, .glass-button {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 10px 15px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.glass-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.glass-button {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border: 1px solid rgba(0, 123, 255, 0.2);
  cursor: pointer;
  font-weight: bold;
}

.glass-button:hover {
  background: rgba(0, 123, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
}

.glass-button.primary {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.glass-button.primary:hover {
  background: rgba(40, 167, 69, 0.2);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
}

.header-buttons {
  display: flex;
  gap: 10px;
}

/* Main Content */
.schedule-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.weekly-sidebar {
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.weekly-schedule-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weekly-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background: rgba(0, 123, 255, 0.05);
  border-radius: 8px;
  border-right: 3px solid #007bff;
}

.weekly-time {
  font-weight: bold;
  color: #007bff;
  min-width: 60px;
  font-size: 0.9rem;
}

.weekly-content {
  flex: 1;
}

.weekly-name {
  font-weight: bold;
  color: #333;
  font-size: 0.9rem;
}

.weekly-details {
  font-size: 0.8rem;
  color: #666;
  margin-top: 2px;
}

.weekly-status {
  font-size: 1.2rem;
}

.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* Sidebar */
.media-sidebar {
  width: 300px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.sidebar-title {
  color: #333;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.sidebar-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.filter-select, .search-input {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 0.9rem;
}

.search-input::placeholder {
  color: #666;
}

.media-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.media-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px;
  cursor: grab;
  transition: all 0.3s ease;
}

.media-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.media-item:active {
  cursor: grabbing;
}

.media-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.media-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #666;
}

.media-type {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  padding: 2px 6px;
  border-radius: 4px;
}

.media-episode {
  font-size: 0.8rem;
  color: #28a745;
  font-weight: bold;
}

.media-info {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.info-tag {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: bold;
  white-space: nowrap;
}

/* Grid */
.schedule-grid {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.grid-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  display: grid;
  grid-template-columns: 140px 120px 1fr 100px 120px 120px;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
  gap: 2px;
}

.grid-header > div {
  padding: 15px 10px;
  text-align: center;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.grid-body {
  flex: 1;
  overflow-y: auto;
}

.grid-row {
  display: grid;
  grid-template-columns: 140px 120px 1fr 100px 120px 120px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 45px;
  transition: all 0.3s ease;
  gap: 2px;
}

.grid-row:hover {
  background: rgba(0, 123, 255, 0.05);
}

.grid-row.segment {
  background: rgba(40, 167, 69, 0.1);
  border-left: 4px solid #28a745;
  position: relative;
}

.grid-row.segment.rerun {
  background: rgba(108, 117, 125, 0.1);
  border-left: 4px solid #6c757d;
}

.grid-row.segment.temporary {
  background: rgba(156, 39, 176, 0.1);
  border-left: 4px solid #9c27b0;
  position: relative;
}

.grid-row.segment.temporary::before {
  content: "🟣";
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 0.8rem;
}

.grid-row.segment:hover .action-btn {
  opacity: 1;
}

.grid-row.filler {
  background: rgba(255, 193, 7, 0.1);
  border-left: 4px solid #ffc107;
}

.grid-row.empty {
  background: rgba(248, 249, 250, 0.5);
  border-style: dashed;
}

.grid-row > div {
  padding: 10px;
  display: flex;
  align-items: center;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
}

.code-cell {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #007bff;
  background: rgba(0, 123, 255, 0.05);
  text-align: center;
  font-size: 0.85rem;
  padding: 8px 5px !important;
  word-break: break-all;
  border-right: 2px solid #007bff;
}

.code-column {
  text-align: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.time-cell {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #28a745;
  text-align: center;
  font-size: 0.9rem;
  padding: 8px 5px !important;
  background: rgba(40, 167, 69, 0.05);
  border-right: 2px solid #28a745;
}

.time-cell {
  font-weight: bold;
  color: #007bff;
  justify-content: center;
}

.content-cell {
  color: #333;
  min-height: 40px;
  position: relative;
}

.content-cell:empty::after {
  content: "اسحب المادة هنا...";
  color: #999;
  font-style: italic;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.duration-cell {
  color: #666;
  font-size: 0.9rem;
  justify-content: center;
}

.status-cell {
  color: #666;
  font-size: 0.8rem;
  justify-content: center;
  font-weight: bold;
}

.actions-cell {
  justify-content: center;
  gap: 5px;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(1.1);
}

.add-row {
  color: #28a745;
}

.add-multiple-rows {
  color: #17a2b8;
  font-weight: bold;
}

.delete-row {
  color: #dc3545;
}

.action-btn {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.action-btn:hover {
  opacity: 1;
}

.actions-cell:hover .action-btn {
  opacity: 1;
}

.move-up, .move-down {
  color: #007bff;
}

.move-up:disabled, .move-down:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.grid-row[draggable="true"] {
  cursor: move;
}

.grid-row[draggable="true"]:hover {
  background: rgba(0, 123, 255, 0.05);
  border-left: 3px solid #007bff;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
  font-size: 1.2rem;
}

/* Media Type Colors */
.media-item.program { border-left: 4px solid #007bff; }
.media-item.series { border-left: 4px solid #28a745; }
.media-item.movie { border-left: 4px solid #dc3545; }
.media-item.promo { border-left: 4px solid #ffc107; }
.media-item.sting { border-left: 4px solid #6f42c1; }
.media-item.filler { border-left: 4px solid #fd7e14; }

/* Responsive */
@media (max-width: 1200px) {
  .schedule-content {
    flex-direction: column;
  }
  
  .media-sidebar {
    width: 100%;
    height: 200px;
  }
  
  .media-list {
    flex-direction: row;
    overflow-x: auto;
  }
  
  .media-item {
    min-width: 200px;
  }
}

