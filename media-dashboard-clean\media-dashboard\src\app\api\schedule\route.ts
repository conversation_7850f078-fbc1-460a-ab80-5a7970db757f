import { NextRequest, NextResponse } from 'next/server';

// استيراد البيانات المشتركة
import { getAllMediaItems, getMediaItemById } from '../shared-data';

// بيانات الخريطة البرامجية مع بيانات تجريبية لاختبار النظام المتتالي
let scheduleItems: any[] = [
  // الأحد - Prime Time (18:00-23:00) → إعادة الاثنين (00:00-17:00)
  {
    id: 'schedule_sun_1',
    mediaItemId: 'media_1', // مسلسل الدراما التاريخية
    dayOfWeek: 0, // الأحد
    startTime: '18:00',
    endTime: '19:00', // مدة: 60 دقيقة
    order: 1,
    createdAt: new Date().toISOString()
  },
  {
    id: 'schedule_sun_2',
    mediaItemId: 'media_11', // برنامج الطبخ الشعبي
    dayOfWeek: 0, // الأحد
    startTime: '19:00',
    endTime: '19:30', // مدة: 30 دقيقة
    order: 2,
    createdAt: new Date().toISOString()
  },
  {
    id: 'schedule_sun_3',
    mediaItemId: 'media_3', // فيلم الأكشن
    dayOfWeek: 0, // الأحد
    startTime: '19:30',
    endTime: '21:45', // مدة: 135 دقيقة
    order: 3,
    createdAt: new Date().toISOString()
  },

  // الاثنين - مواد إضافية للاختبار
  {
    id: 'schedule_mon_1',
    mediaItemId: 'media_4', // برنامج الأخبار
    dayOfWeek: 1, // الاثنين
    startTime: '18:00',
    endTime: '19:00', // مدة: 60 دقيقة
    order: 1,
    createdAt: new Date().toISOString()
  },
  {
    id: 'schedule_mon_2',
    mediaItemId: 'media_5', // برنامج رياضي
    dayOfWeek: 1, // الاثنين
    startTime: '19:00',
    endTime: '20:00', // مدة: 60 دقيقة
    order: 2,
    createdAt: new Date().toISOString()
  },

  // الخميس - Prime Time (18:00-23:00) → إعادة الجمعة (02:00-17:00)
  {
    id: 'schedule_thu_1',
    mediaItemId: 'media_4', // برنامج الأخبار
    dayOfWeek: 4, // الخميس
    startTime: '18:00',
    endTime: '19:00', // مدة: 60 دقيقة
    order: 1,
    createdAt: new Date().toISOString()
  },
  {
    id: 'schedule_thu_2',
    mediaItemId: 'media_5', // برنامج رياضي
    dayOfWeek: 4, // الخميس
    startTime: '19:00',
    endTime: '20:30', // مدة: 90 دقيقة
    order: 2,
    createdAt: new Date().toISOString()
  },
  {
    id: 'schedule_thu_3',
    mediaItemId: 'media_6', // فيلم كوميدي
    dayOfWeek: 4, // الخميس
    startTime: '20:30',
    endTime: '22:00', // مدة: 90 دقيقة
    order: 3,
    createdAt: new Date().toISOString()
  }
];

// GET - جلب الخريطة البرامجية
export async function GET() {
  try {
    const mediaItems = getAllMediaItems();

    // فلترة المواد الكبيرة فقط (البرامج، الأفلام، المسلسلات)
    const bigMediaTypes = ['PROGRAM', 'SERIES', 'MOVIE'];
    const availableBigMedia = mediaItems.filter(item => bigMediaTypes.includes(item.type));

    // ربط المواد الإعلامية بعناصر الخريطة مع إضافة الإعادات
    const enrichedSchedule: any[] = [];

    // إضافة المواد الأصلية أولاً
    scheduleItems.forEach(item => {
      const mediaItem = mediaItems.find(m => m.id === item.mediaItemId);
      if (mediaItem) {
        enrichedSchedule.push({
          ...item,
          mediaItem,
          isRerun: false
        });
      }
    });

    // **نظام الإعادات البسيط والواضح**
    console.log('🔄 نظام الإعادات البسيط...');
    console.log('📊 عدد المواد الأصلية:', scheduleItems.length);

    // القاعدة البسيطة: أي مادة من 18:00 فما فوق تُعاد في نفس اليوم من 00:00
    scheduleItems.forEach((item, index) => {
      const mediaItem = mediaItems.find(m => m.id === item.mediaItemId);
      if (!mediaItem) {
        console.log(`❌ لم يتم العثور على المادة: ${item.mediaItemId}`);
        return;
      }

      // استخراج الساعة من وقت البداية
      const [hours, minutes] = item.startTime.split(':').map(Number);
      console.log(`🔍 فحص المادة ${index + 1}: ${mediaItem.name} (${item.startTime}) - الساعة: ${hours}`);

      // إذا كانت المادة تبدأ من 18:00 فما فوق
      if (hours >= 18) {
        console.log(`✅ مادة في البرايم تايم: ${mediaItem.name}`);
        // حساب وقت الإعادة البسيط
        const rerunHours = hours - 18; // 18→0, 19→1, 20→2, 21→3, 22→4, 23→5
        const rerunStart = `${rerunHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

        // حساب وقت النهاية بطريقة بسيطة
        const [endHours, endMinutes] = item.endTime.split(':').map(Number);

        // حساب المدة الأصلية بالدقائق
        const startTotalMinutes = hours * 60 + minutes;
        const endTotalMinutes = endHours * 60 + endMinutes;
        let durationMinutes = endTotalMinutes - startTotalMinutes;

        // إذا كانت المدة سالبة، فهذا يعني أن المادة تمتد لليوم التالي
        if (durationMinutes < 0) {
          durationMinutes += 24 * 60; // إضافة 24 ساعة
        }

        // حساب وقت النهاية للإعادة
        const rerunStartMinutes = rerunHours * 60 + minutes;
        const rerunEndMinutes = rerunStartMinutes + durationMinutes;
        const rerunEndHours = Math.floor(rerunEndMinutes / 60);
        const rerunEndMins = rerunEndMinutes % 60;

        const rerunEnd = `${rerunEndHours.toString().padStart(2, '0')}:${rerunEndMins.toString().padStart(2, '0')}`;

        // إنشاء الإعادة
        enrichedSchedule.push({
          id: `rerun_${item.id}`,
          mediaItemId: item.mediaItemId,
          dayOfWeek: item.dayOfWeek, // نفس اليوم الإذاعي
          startTime: rerunStart,
          endTime: rerunEnd,
          order: 2000, // ترقيم عالي للإعادات
          isRerun: true,
          originalScheduleId: item.id,
          mediaItem,
          createdAt: new Date().toISOString()
        });

        console.log(`✅ إعادة: ${mediaItem.name} | ${item.startTime}-${item.endTime} → ${rerunStart}-${rerunEnd}`);
      } else {
        console.log(`❌ المادة ليست في البرايم تايم: ${mediaItem.name} (${item.startTime})`);
      }
    });

    console.log(`🎯 إجمالي المواد بعد الإعادات: ${enrichedSchedule.length}`);
    console.log(`🔄 عدد الإعادات: ${enrichedSchedule.filter(item => item.isRerun).length}`);

    return NextResponse.json({
      success: true,
      data: enrichedSchedule,
      availableMedia: availableBigMedia,
      primeTimeInfo: getPrimeTimeInfo()
    });
  } catch (error) {
    console.error('Error fetching schedule:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في جلب الخريطة البرامجية' },
      { status: 500 }
    );
  }
}

// POST - إضافة مادة للخريطة البرامجية
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { mediaItemId, dayOfWeek, startTime, endTime, order } = body;

    // التحقق من البيانات المطلوبة
    if (!mediaItemId || dayOfWeek === undefined || !startTime || !endTime) {
      return NextResponse.json(
        { success: false, error: 'جميع البيانات مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من وجود المادة الإعلامية
    const mediaItem = getMediaItemById(mediaItemId);
    if (!mediaItem) {
      return NextResponse.json(
        { success: false, error: 'المادة الإعلامية غير موجودة' },
        { status: 404 }
      );
    }

    // التحقق من عدم تداخل الأوقات (تحويل الأوقات إلى دقائق للمقارنة الدقيقة)
    const startMinutes = timeToMinutes(startTime);
    const endMinutes = timeToMinutes(endTime);

    const conflictingItem = scheduleItems.find(item => {
      if (item.dayOfWeek !== dayOfWeek) return false;

      const itemStartMinutes = timeToMinutes(item.startTime);
      const itemEndMinutes = timeToMinutes(item.endTime);

      // التحقق من التداخل
      return (
        (startMinutes >= itemStartMinutes && startMinutes < itemEndMinutes) ||
        (endMinutes > itemStartMinutes && endMinutes <= itemEndMinutes) ||
        (startMinutes <= itemStartMinutes && endMinutes >= itemEndMinutes)
      );
    });

    if (conflictingItem) {
      const conflictingMedia = getMediaItemById(conflictingItem.mediaItemId);
      return NextResponse.json(
        {
          success: false,
          error: `يوجد تداخل في الأوقات مع: ${conflictingMedia?.name || 'مادة أخرى'} (${conflictingItem.startTime} - ${conflictingItem.endTime})`
        },
        { status: 400 }
      );
    }

    // إنشاء عنصر جديد في الخريطة
    const newScheduleItem = {
      id: `schedule_${Date.now()}`,
      mediaItemId,
      dayOfWeek,
      startTime,
      endTime,
      order: order || scheduleItems.filter(item => item.dayOfWeek === dayOfWeek).length + 1,
      createdAt: new Date().toISOString()
    };

    scheduleItems.push(newScheduleItem);

    // إذا كانت المادة في البرايم تايم، قم بتوليد الإعادات تلقائياً
    if (isPrimeTime(startTime, endTime, dayOfWeek)) {
      console.log('🔄 المادة في البرايم تايم - سيتم توليد الإعادات تلقائياً');
    }

    return NextResponse.json({
      success: true,
      data: {
        ...newScheduleItem,
        mediaItem
      },
      message: 'تم إضافة المادة للخريطة البرامجية بنجاح'
    });

  } catch (error) {
    console.error('Error adding to schedule:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في إضافة المادة للخريطة البرامجية' },
      { status: 500 }
    );
  }
}

// PUT - تحديث موضع مادة في الخريطة
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, dayOfWeek, startTime, endTime, order } = body;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف العنصر مطلوب' },
        { status: 400 }
      );
    }

    const itemIndex = scheduleItems.findIndex(item => item.id === id);
    if (itemIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'العنصر غير موجود' },
        { status: 404 }
      );
    }

    // تحديث البيانات
    scheduleItems[itemIndex] = {
      ...scheduleItems[itemIndex],
      dayOfWeek: dayOfWeek !== undefined ? dayOfWeek : scheduleItems[itemIndex].dayOfWeek,
      startTime: startTime || scheduleItems[itemIndex].startTime,
      endTime: endTime || scheduleItems[itemIndex].endTime,
      order: order !== undefined ? order : scheduleItems[itemIndex].order,
      updatedAt: new Date().toISOString()
    };

    const mediaItem = getMediaItemById(scheduleItems[itemIndex].mediaItemId);

    return NextResponse.json({
      success: true,
      data: {
        ...scheduleItems[itemIndex],
        mediaItem
      },
      message: 'تم تحديث موضع المادة بنجاح'
    });

  } catch (error) {
    console.error('Error updating schedule item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في تحديث موضع المادة' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مادة من الخريطة
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'معرف العنصر مطلوب' },
        { status: 400 }
      );
    }

    const itemIndex = scheduleItems.findIndex(item => item.id === id);
    if (itemIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'العنصر غير موجود' },
        { status: 404 }
      );
    }

    scheduleItems.splice(itemIndex, 1);

    return NextResponse.json({
      success: true,
      message: 'تم حذف المادة من الخريطة البرامجية بنجاح'
    });

  } catch (error) {
    console.error('Error deleting schedule item:', error);
    return NextResponse.json(
      { success: false, error: 'فشل في حذف المادة من الخريطة البرامجية' },
      { status: 500 }
    );
  }
}

// دوال مساعدة لنظام البث المتقدم

// تحويل الوقت إلى دقائق
function timeToMinutes(time: string): number {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
}

// معلومات البرايم تايم
function getPrimeTimeInfo() {
  return {
    weekdays: { // الأحد إلى الأربعاء
      start: '18:00',
      end: '00:00',
      days: [0, 1, 2, 3] // الأحد=0, الاثنين=1, الثلاثاء=2, الأربعاء=3
    },
    weekend: { // الخميس إلى السبت
      start: '18:00',
      end: '02:00', // +1 day
      days: [4, 5, 6] // الخميس=4, الجمعة=5, السبت=6
    }
  };
}

// توليد الإعادات المتتالية لجميع الأيام (نظام البث الإذاعي)
function generateSequentialReruns(scheduleItems: any[], mediaItems: any[], enrichedSchedule: any[]) {
  const primeInfo = getPrimeTimeInfo();

  console.log('🔄 بدء توليد الإعادات (نظام البث الإذاعي)...');

  // معالجة كل يوم إذاعي على حدة (08:00 إلى 08:00 اليوم التالي)
  for (let dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
    // الحصول على مواد البرايم تايم لهذا اليوم الإذاعي
    const primeTimeItems = scheduleItems
      .filter(item => item.dayOfWeek === dayOfWeek && isPrimeTime(item.startTime, item.endTime, dayOfWeek))
      .sort((a, b) => a.startTime.localeCompare(b.startTime)); // ترتيب حسب الوقت

    console.log(`📅 اليوم الإذاعي ${dayOfWeek}: ${primeTimeItems.length} مادة في البرايم تايم`);

    if (primeTimeItems.length === 0) continue;

    // في النظام الإذاعي: الإعادات تكون في اليوم التالي
    // البرايم تايم: 18:00-00:00 → الإعادات: 00:00-08:00 (اليوم التالي)
    const rerunDay = (dayOfWeek + 1) % 7; // اليوم التالي!
    let rerunStartTime: string;

    if (primeInfo.weekdays.days.includes(dayOfWeek)) {
      // الأحد - الأربعاء: إعادة من 00:00 (اليوم التالي)
      rerunStartTime = '00:00';
      console.log(`📺 أيام الأسبوع - إعادة في اليوم التالي ${rerunDay} من ${rerunStartTime}`);
    } else if (primeInfo.weekend.days.includes(dayOfWeek)) {
      // الخميس - السبت: إعادة من 02:00 (اليوم التالي)
      rerunStartTime = '02:00';
      console.log(`🎬 نهاية الأسبوع - إعادة في اليوم التالي ${rerunDay} من ${rerunStartTime}`);
    } else {
      continue; // يوم غير معرف
    }

    // توليد الإعادات المتتالية في اليوم التالي
    let currentRerunTime = rerunStartTime;

    primeTimeItems.forEach((item, index) => {
      const mediaItem = mediaItems.find(m => m.id === item.mediaItemId);
      if (!mediaItem) return;

      // حساب مدة المادة
      const duration = calculateDurationBetweenTimes(item.startTime, item.endTime);
      const endTime = addMinutesToTime(currentRerunTime, timeToMinutes(duration));

      console.log(`🔄 إنشاء إعادة: ${mediaItem.name} من ${currentRerunTime} إلى ${endTime} (اليوم التالي)`);

      // التأكد من عدم تجاوز 08:00 (نهاية اليوم الإذاعي)
      if (timeToMinutes(endTime) <= timeToMinutes('08:00') || timeToMinutes(endTime) >= timeToMinutes('00:00')) {
        const rerunItem = {
          id: `rerun_${item.id}_${Date.now()}_${index}`,
          mediaItemId: item.mediaItemId,
          dayOfWeek: rerunDay, // اليوم التالي
          startTime: currentRerunTime,
          endTime: endTime,
          order: index + 1000, // ترقيم عالي للإعادات لتظهر بعد المواد الأصلية
          isRerun: true,
          originalScheduleId: item.id,
          mediaItem,
          createdAt: new Date().toISOString()
        };

        enrichedSchedule.push(rerunItem);
        console.log(`✅ تم إضافة إعادة: ${mediaItem.name} في اليوم التالي ${rerunDay}`);

        // تحديث الوقت للمادة التالية
        currentRerunTime = endTime;
      } else {
        console.log(`⚠️ تجاوز نهاية اليوم الإذاعي (08:00) للمادة: ${mediaItem.name}`);
      }
    });
  }

  console.log(`🎯 تم إنشاء ${enrichedSchedule.filter(item => item.isRerun).length} إعادة في النظام الإذاعي`);
}

// دالة مساعدة لإضافة دقائق إلى وقت
function addMinutesToTime(timeStr: string, minutes: number): string {
  const totalMinutes = timeToMinutes(timeStr) + minutes;
  const hours = Math.floor(totalMinutes / 60) % 24;
  const mins = totalMinutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

// توليد إعادات متعددة للمادة (نظام الإزاحة الزمنية) - للتوافق مع النظام القديم
function generateMultipleReruns(originalItem: any, mediaItem: any): any[] {
  const reruns: any[] = [];

  // التحقق من أن المادة في البرايم تايم
  if (!isPrimeTime(originalItem.startTime, originalItem.endTime, originalItem.dayOfWeek)) {
    return reruns;
  }

  // حساب الإزاحة من 18:00
  const originalStartMinutes = timeToMinutes(originalItem.startTime);
  const primeStartMinutes = timeToMinutes('18:00');
  const offsetMinutes = originalStartMinutes - primeStartMinutes;

  // حساب مدة المادة
  const duration = calculateDurationBetweenTimes(originalItem.startTime, originalItem.endTime);
  const durationMinutes = timeToMinutes(duration);

  // توليد الإعادات بناءً على الإزاحة
  const rerunSlots = generateRerunSlotsWithOffset(originalItem.dayOfWeek, offsetMinutes, durationMinutes);

  rerunSlots.forEach((slot, index) => {
    reruns.push({
      id: `rerun_${originalItem.id}_${index + 1}`,
      mediaItemId: originalItem.mediaItemId,
      dayOfWeek: slot.dayOfWeek,
      startTime: slot.startTime,
      endTime: slot.endTime,
      order: slot.order,
      isRerun: true,
      originalScheduleId: originalItem.id,
      mediaItem,
      createdAt: new Date().toISOString()
    });
  });

  return reruns;
}

// توليد فترات زمنية للإعادات بناءً على الإزاحة الزمنية
function generateRerunSlotsWithOffset(originalDayOfWeek: number, offsetMinutes: number, durationMinutes: number) {
  const slots: any[] = [];
  const primeInfo = getPrimeTimeInfo();

  if (primeInfo.weekdays.days.includes(originalDayOfWeek)) {
    // الأحد - الأربعاء: إعادات في اليوم التالي
    const nextDay = (originalDayOfWeek + 1) % 7;

    // 3 إعادات بفارق 6 ساعات: 00:00, 06:00, 12:00 + الإزاحة
    const baseHours = [0, 6, 12];

    baseHours.forEach((baseHour, index) => {
      const rerunStartMinutes = (baseHour * 60) + offsetMinutes;
      const rerunHour = Math.floor(rerunStartMinutes / 60);
      const rerunMin = rerunStartMinutes % 60;

      if (rerunHour >= 0 && rerunHour < 18) { // ضمن النطاق المسموح
        const startTime = `${rerunHour.toString().padStart(2, '0')}:${rerunMin.toString().padStart(2, '0')}`;
        const endMinutes = rerunStartMinutes + durationMinutes;
        const endHour = Math.floor(endMinutes / 60);
        const endMin = endMinutes % 60;
        const endTime = `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;

        if (endHour < 18) { // تأكد من عدم تجاوز 18:00
          slots.push({
            dayOfWeek: nextDay,
            startTime,
            endTime,
            order: index + 1
          });
        }
      }
    });

  } else if (primeInfo.weekend.days.includes(originalDayOfWeek)) {
    // الخميس - السبت: إعادات في اليوم التالي

    // 3 إعادات بفارق 6 ساعات: 02:00, 08:00, 14:00 + الإزاحة
    const nextDay = (originalDayOfWeek + 1) % 7;
    const baseHours = [2, 8, 14];

    baseHours.forEach((baseHour, index) => {
      const rerunStartMinutes = (baseHour * 60) + offsetMinutes;
      const rerunHour = Math.floor(rerunStartMinutes / 60);
      const rerunMin = rerunStartMinutes % 60;

      if (rerunHour >= 2 && rerunHour < 18) { // ضمن النطاق المسموح
        const startTime = `${rerunHour.toString().padStart(2, '0')}:${rerunMin.toString().padStart(2, '0')}`;
        const endMinutes = rerunStartMinutes + durationMinutes;
        const endHour = Math.floor(endMinutes / 60);
        const endMin = endMinutes % 60;
        const endTime = `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;

        if (endHour < 18) { // تأكد من عدم تجاوز 18:00
          slots.push({
            dayOfWeek: nextDay, // اليوم التالي
            startTime,
            endTime,
            order: index + 1
          });
        }
      }
    });
  }

  return slots;
}



// توليد إعادة تلقائية للمادة (الدالة القديمة للتوافق)
function generateRerunItem(originalItem: any, mediaItem: any) {
  const reruns = generateMultipleReruns(originalItem, mediaItem);
  return reruns.length > 0 ? reruns[0] : null;
}



// حساب المدة بين وقتين
function calculateDurationBetweenTimes(startTime: string, endTime: string): string {
  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);

  let durationMinutes = endMinutes - startMinutes;
  if (durationMinutes < 0) {
    durationMinutes += 24 * 60; // إضافة 24 ساعة إذا كان الوقت يمتد لليوم التالي
  }

  const hours = Math.floor(durationMinutes / 60);
  const minutes = durationMinutes % 60;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}



// التحقق من أن المادة في البرايم تايم
function isPrimeTime(startTime: string, _endTime: string, dayOfWeek: number): boolean {
  const primeInfo = getPrimeTimeInfo();
  const startMinutes = timeToMinutes(startTime);

  if (primeInfo.weekdays.days.includes(dayOfWeek)) {
    // أيام الأسبوع (الأحد-الأربعاء): 18:00 - 00:00
    return startMinutes >= timeToMinutes('18:00') && startMinutes < timeToMinutes('24:00');
  } else if (primeInfo.weekend.days.includes(dayOfWeek)) {
    // نهاية الأسبوع (الخميس-السبت): 18:00 - 02:00 (+1 day)
    return startMinutes >= timeToMinutes('18:00') || startMinutes < timeToMinutes('02:00');
  }

  return false;
}



// فلترة المواد حسب النوع للخريطة البرامجية
function isBigMedia(mediaType: string): boolean {
  const bigMediaTypes = ['PROGRAM', 'SERIES', 'MOVIE'];
  return bigMediaTypes.includes(mediaType);
}

// فلترة المواد الصغيرة للفواصل
function isSmallMedia(mediaType: string): boolean {
  const smallMediaTypes = ['STING', 'FILL_IN', 'FILLER', 'PROMO', 'SONG'];
  return smallMediaTypes.includes(mediaType);
}

// تصدير البيانات والدوال للاستخدام في API endpoints أخرى
export {
  scheduleItems,
  getPrimeTimeInfo,
  generateRerunItem,
  isPrimeTime,
  isBigMedia,
  isSmallMedia
};
