"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit-media/page",{

/***/ "(app-pages-browser)/./src/app/edit-media/page.tsx":
/*!*************************************!*\
  !*** ./src/app/edit-media/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EditMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const mediaId = searchParams.get('id');\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: '',\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    // تحميل بيانات المادة للتعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditMediaPage.useEffect\": ()=>{\n            if (!mediaId) {\n                router.push('/media-list');\n                return;\n            }\n            const fetchMediaItem = {\n                \"EditMediaPage.useEffect.fetchMediaItem\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/media?id=\".concat(mediaId));\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            var _item_episodeNumber, _item_seasonNumber, _item_partNumber, _item_segments;\n                            const item = result.data;\n                            setFormData({\n                                name: item.name || '',\n                                type: item.type || 'PROGRAM',\n                                description: item.description || '',\n                                channel: item.channel || 'DOCUMENTARY',\n                                source: item.source || '',\n                                status: item.status || 'WAITING',\n                                startDate: item.startDate ? item.startDate.split('T')[0] : '',\n                                endDate: item.endDate ? item.endDate.split('T')[0] : '',\n                                notes: item.notes || '',\n                                episodeNumber: ((_item_episodeNumber = item.episodeNumber) === null || _item_episodeNumber === void 0 ? void 0 : _item_episodeNumber.toString()) || '',\n                                seasonNumber: ((_item_seasonNumber = item.seasonNumber) === null || _item_seasonNumber === void 0 ? void 0 : _item_seasonNumber.toString()) || '',\n                                partNumber: ((_item_partNumber = item.partNumber) === null || _item_partNumber === void 0 ? void 0 : _item_partNumber.toString()) || '',\n                                hardDiskNumber: item.hardDiskNumber || 'SERVER'\n                            });\n                            console.log('📋 البيانات المحملة للتعديل:', {\n                                name: item.name,\n                                status: item.status,\n                                segments: ((_item_segments = item.segments) === null || _item_segments === void 0 ? void 0 : _item_segments.length) || 0\n                            });\n                            if (item.segments && item.segments.length > 0) {\n                                const loadedSegments = item.segments.map({\n                                    \"EditMediaPage.useEffect.fetchMediaItem.loadedSegments\": (seg, index)=>({\n                                            id: index + 1,\n                                            segmentCode: seg.code || seg.segmentCode || \"SEG\".concat(String(index + 1).padStart(3, '0')),\n                                            timeIn: seg.timeIn || '00:00:00',\n                                            timeOut: seg.timeOut || '00:00:00',\n                                            duration: seg.duration || '00:00:00'\n                                        })\n                                }[\"EditMediaPage.useEffect.fetchMediaItem.loadedSegments\"]);\n                                setSegments(loadedSegments);\n                                setSegmentCount(item.segments.length);\n                                console.log('🎬 تم تحميل السيجمانت:', loadedSegments);\n                            }\n                        } else {\n                            showToast('فشل في تحميل بيانات المادة', 'error');\n                            router.push('/media-list');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching media item:', error);\n                        showToast('خطأ في تحميل البيانات', 'error');\n                        router.push('/media-list');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditMediaPage.useEffect.fetchMediaItem\"];\n            fetchMediaItem();\n        }\n    }[\"EditMediaPage.useEffect\"], [\n        mediaId\n    ]); // إزالة router و showToast من dependencies\n    const handleInputChange = (field, value)=>{\n        console.log(\"\\uD83D\\uDD04 تغيير \".concat(field, \":\"), value);\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const addSegment = ()=>{\n        const newSegmentCount = segmentCount + 1;\n        setSegmentCount(newSegmentCount);\n        const newSegment = {\n            id: newSegmentCount,\n            segmentCode: \"SEG\".concat(String(newSegmentCount).padStart(3, '0')),\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        };\n        setSegments((prev)=>[\n                ...prev,\n                newSegment\n            ]);\n        console.log('➕ تم إضافة سيجمانت جديد:', newSegment);\n    };\n    const removeSegment = (segmentId)=>{\n        if (segments.length > 1) {\n            setSegments((prev)=>prev.filter((segment)=>segment.id !== segmentId));\n            console.log('🗑️ تم حذف السيجمانت:', segmentId);\n        } else {\n            alert('لا يمكن حذف السيجمانت الوحيد المتبقي');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.name.trim()) {\n            showToast('يرجى إدخال اسم المادة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(mediaId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم تحديث المادة الإعلامية بنجاح!', 'success');\n                router.push('/media-list');\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error updating media item:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        transition: 'border-color 0.3s',\n        direction: 'rtl'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n            lineNumber: 191,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '800px',\n                    margin: '0 auto',\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    fontWeight: 'bold',\n                                    color: '#333',\n                                    margin: 0,\n                                    background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent'\n                                },\n                                children: \"✏️ تعديل المادة الإعلامية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/media-list'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '10px 20px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDD19 العودة للقائمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #dee2e6'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#495057',\n                                            marginBottom: '20px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '200px 1fr',\n                                                    gap: '15px',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            color: '#495057',\n                                                            fontWeight: 'bold',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDCBE رقم الهارد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"رقم الهارد\",\n                                                        value: formData.hardDiskNumber,\n                                                        onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            maxWidth: '200px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة *\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: inputStyle,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '80px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"المصدر\",\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"WAITING\",\n                                                                children: \"في الانتظار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 346,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: \"صالح\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"APPROVED\",\n                                                                children: \"مقبول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED\",\n                                                                children: \"مرفوض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: \"منتهي الصلاحية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ البداية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ النهاية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الجزء\",\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الموسم\",\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات إضافية\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '60px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #90caf9'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-between',\n                                            alignItems: 'center',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    color: '#1976d2',\n                                                    fontSize: '1.3rem',\n                                                    margin: 0\n                                                },\n                                                children: \"\\uD83C\\uDFAC إدارة السيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #4caf50, #45a049)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"➕ إضافة سيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, this),\n                                    segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: 'white',\n                                                borderRadius: '10px',\n                                                padding: '20px',\n                                                marginBottom: '15px',\n                                                border: '1px solid #e0e0e0'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            style: {\n                                                                color: '#333',\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"سيجمانت \",\n                                                                segment.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        segments.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removeSegment(segment.id),\n                                                            style: {\n                                                                background: '#ff4444',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '15px',\n                                                                padding: '5px 10px',\n                                                                cursor: 'pointer',\n                                                                fontSize: '0.8rem'\n                                                            },\n                                                            children: \"❌ حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                        gap: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"كود السيجمانت\",\n                                                            value: segment.segmentCode,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"وقت البداية (00:00:00)\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"وقت النهاية (00:00:00)\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"المدة (00:00:00)\",\n                                                            value: segment.duration,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'duration', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, segment.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)'\n                                        },\n                                        children: \"✅ حفظ التعديلات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.push('/media-list'),\n                                        style: {\n                                            background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(108,117,125,0.3)'\n                                        },\n                                        children: \"❌ إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 561,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n}\n_s(EditMediaPage, \"U/Mp9ns9pODrm6O21lgz1y/hFgg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = EditMediaPage;\nvar _c;\n$RefreshReg$(_c, \"EditMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/edit-media/page.tsx\n"));

/***/ })

});