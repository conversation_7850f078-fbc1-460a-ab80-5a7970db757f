import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// مسار ملف بيانات المستخدمين
const USERS_FILE = path.join(process.cwd(), 'users-data.json');

// تعريف الأدوار والصلاحيات
const ROLES = {
  ADMIN: {
    name: 'مدير النظام',
    permissions: ['ALL'],
    description: 'صلاحيات كاملة لجميع أجزاء النظام'
  },
  MEDIA_MANAGER: {
    name: 'مدير المحتوى',
    permissions: ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE'],
    description: 'إدارة المواد الإعلامية (إضافة، تعديل، حذف)'
  },
  SCHEDULER: {
    name: 'مجدول البرامج',
    permissions: ['SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', 'MEDIA_READ'],
    description: 'إدارة الجداول الإذاعية والخريطة البرامجية'
  },
  VIEWER: {
    name: 'مستخدم عرض',
    permissions: ['MEDIA_READ', 'SCHEDULE_READ'],
    description: 'عرض المحتوى فقط بدون إمكانية التعديل'
  }
};

// المستخدمون الافتراضيون
const defaultUsers = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123',
    name: 'مدير النظام الرئيسي',
    email: '<EMAIL>',
    role: 'ADMIN',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '2',
    username: 'media_manager',
    password: 'media123',
    name: 'أحمد محمد - مدير المحتوى',
    email: '<EMAIL>',
    role: 'MEDIA_MANAGER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '3',
    username: 'scheduler',
    password: 'schedule123',
    name: 'فاطمة علي - مجدولة البرامج',
    email: '<EMAIL>',
    role: 'SCHEDULER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '4',
    username: 'viewer',
    password: 'view123',
    name: 'محمد سالم - مستخدم عرض',
    email: '<EMAIL>',
    role: 'VIEWER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  }
];

// دالة لتحميل المستخدمين من الملف
function loadUsers() {
  try {
    if (fs.existsSync(USERS_FILE)) {
      const data = fs.readFileSync(USERS_FILE, 'utf8');
      const users = JSON.parse(data);
      console.log(`👥 تم تحميل ${users.length} مستخدم من الملف`);
      return users;
    } else {
      console.log('📁 ملف المستخدمين غير موجود، سيتم إنشاؤه مع المستخدمين الافتراضيين');
      saveUsers(defaultUsers);
      return defaultUsers;
    }
  } catch (error) {
    console.error('❌ خطأ في تحميل المستخدمين:', error);
    return defaultUsers;
  }
}

// دالة لحفظ المستخدمين في الملف
function saveUsers(users: any[]) {
  try {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
    console.log(`💾 تم حفظ ${users.length} مستخدم في الملف`);
  } catch (error) {
    console.error('❌ خطأ في حفظ المستخدمين:', error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json();

    if (!username || !password) {
      return NextResponse.json({
        success: false,
        error: 'يرجى إدخال اسم المستخدم وكلمة المرور'
      }, { status: 400 });
    }

    // تحميل المستخدمين من الملف
    const users = loadUsers();

    // البحث عن المستخدم
    const user = users.find(u => u.username === username && u.password === password && u.isActive);

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      }, { status: 401 });
    }

    // تحديث وقت آخر تسجيل دخول
    user.lastLogin = new Date().toISOString();
    saveUsers(users);

    // إنشاء token مؤقت (في التطبيق الحقيقي استخدم JWT)
    const token = `token_${user.id}_${Date.now()}`;

    // إرجاع بيانات المستخدم (بدون كلمة المرور)
    const { password: _, ...userWithoutPassword } = user;

    // إضافة معلومات الصلاحيات
    const userWithPermissions = {
      ...userWithoutPassword,
      permissions: ROLES[user.role as keyof typeof ROLES]?.permissions || []
    };

    return NextResponse.json({
      success: true,
      user: userWithPermissions,
      token,
      message: 'تم تسجيل الدخول بنجاح'
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 });
  }
}

// API للحصول على معلومات المستخدم الحالي
export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'غير مصرح'
      }, { status: 401 });
    }

    // استخراج معرف المستخدم من التوكن (مؤقت)
    const userId = token.split('_')[1];
    const users = loadUsers();
    const user = users.find(u => u.id === userId);

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'مستخدم غير موجود'
      }, { status: 404 });
    }

    const { password: _, ...userWithoutPassword } = user;

    // إضافة معلومات الصلاحيات
    const userWithPermissions = {
      ...userWithoutPassword,
      permissions: ROLES[user.role as keyof typeof ROLES]?.permissions || []
    };

    return NextResponse.json({
      success: true,
      user: userWithPermissions
    });

  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 });
  }
}
