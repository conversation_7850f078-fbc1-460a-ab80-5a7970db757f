import { NextRequest, NextResponse } from 'next/server';

// بيانات المستخدمين المؤقتة - سيتم استبدالها بقاعدة بيانات حقيقية
const users = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123',
    name: 'مدير النظام',
    role: 'ADMIN',
    permissions: ['ALL']
  },
  {
    id: '2',
    username: 'media_manager',
    password: 'media123',
    name: 'مدير المحتوى',
    role: 'MEDIA_MANAGER',
    permissions: ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE']
  },
  {
    id: '3',
    username: 'scheduler',
    password: 'schedule123',
    name: 'مجدول البرامج',
    role: 'SCHEDULER',
    permissions: ['SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', 'MEDIA_READ']
  },
  {
    id: '4',
    username: 'viewer',
    password: 'view123',
    name: 'مستخدم عرض',
    role: 'VIEWER',
    permissions: ['MEDIA_READ', 'SCHEDULE_READ']
  }
];

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json();

    if (!username || !password) {
      return NextResponse.json({
        success: false,
        error: 'يرجى إدخال اسم المستخدم وكلمة المرور'
      }, { status: 400 });
    }

    // التحقق من صحة تسجيل الدخول باستخدام الملف المشترك
    const user = validateLogin(username, password);

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      }, { status: 401 });
    }

    // إنشاء token مؤقت (في التطبيق الحقيقي استخدم JWT)
    const token = `token_${user.id}_${Date.now()}`;

    // إرجاع بيانات المستخدم (بدون كلمة المرور)
    const { password: _, ...userWithoutPassword } = user;

    // إضافة معلومات الصلاحيات
    const userWithPermissions = {
      ...userWithoutPassword,
      permissions: ROLES[user.role as keyof typeof ROLES]?.permissions || []
    };

    return NextResponse.json({
      success: true,
      user: userWithPermissions,
      token,
      message: 'تم تسجيل الدخول بنجاح'
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 });
  }
}

// API للحصول على معلومات المستخدم الحالي
export async function GET(request: NextRequest) {
  try {
    const token = request.headers.get('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json({
        success: false,
        error: 'غير مصرح'
      }, { status: 401 });
    }

    // استخراج معرف المستخدم من التوكن (مؤقت)
    const userId = token.split('_')[1];
    const users = getAllUsers();
    const user = users.find(u => u.id === userId);

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'مستخدم غير موجود'
      }, { status: 404 });
    }

    const { password: _, ...userWithoutPassword } = user;

    // إضافة معلومات الصلاحيات
    const userWithPermissions = {
      ...userWithoutPassword,
      permissions: ROLES[user.role as keyof typeof ROLES]?.permissions || []
    };

    return NextResponse.json({
      success: true,
      user: userWithPermissions
    });

  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم'
    }, { status: 500 });
  }
}
