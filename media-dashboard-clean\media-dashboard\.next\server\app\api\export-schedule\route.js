/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/export-schedule/route";
exports.ids = ["app/api/export-schedule/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport-schedule%2Froute&page=%2Fapi%2Fexport-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport-schedule%2Froute&page=%2Fapi%2Fexport-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_export_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/export-schedule/route.ts */ \"(rsc)/./src/app/api/export-schedule/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/export-schedule/route\",\n        pathname: \"/api/export-schedule\",\n        filename: \"route\",\n        bundlePath: \"app/api/export-schedule/route\"\n    },\n    resolvedPagePath: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\export-schedule\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_export_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport-schedule%2Froute&page=%2Fapi%2Fexport-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/export-schedule/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/export-schedule/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! exceljs */ \"(rsc)/./node_modules/exceljs/excel.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// تحميل بيانات الجدول الأسبوعي\nfunction loadWeeklySchedule(weekStart) {\n    try {\n        // محاولة عدة مسارات محتملة للملف\n        const possiblePaths = [\n            path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'weekly-schedules.json'),\n            path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data', 'weekly-schedules.json'),\n            path__WEBPACK_IMPORTED_MODULE_3___default().join(__dirname, '..', '..', '..', '..', 'weekly-schedules.json'),\n            path__WEBPACK_IMPORTED_MODULE_3___default().join(__dirname, '..', '..', '..', '..', 'data', 'weekly-schedules.json')\n        ];\n        let filePath = null;\n        for (const testPath of possiblePaths){\n            console.log('🔍 البحث عن ملف الجداول في:', testPath);\n            if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(testPath)) {\n                filePath = testPath;\n                console.log('✅ تم العثور على الملف في:', filePath);\n                break;\n            }\n        }\n        if (!filePath) {\n            console.log('📂 لم يتم العثور على ملف الجداول في أي من المسارات المحتملة');\n            return null;\n        }\n        const fileContent = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(filePath, 'utf8');\n        const allSchedules = JSON.parse(fileContent);\n        const schedule = allSchedules[weekStart];\n        if (!schedule) {\n            console.log('📅 لم يتم العثور على جدول للأسبوع:', weekStart);\n            return null;\n        }\n        console.log('📂 تم تحميل الجدول الأسبوعي بنجاح');\n        return schedule;\n    } catch (error) {\n        console.error('❌ خطأ في تحميل الجدول:', error);\n        return null;\n    }\n}\n// تحديد لون الخلية حسب نوع المحتوى\nfunction getCellColor(item) {\n    if (!item) {\n        return 'FFFFFFFF'; // أبيض للخلايا الفارغة\n    }\n    // التحقق من كونها مادة مؤقتة\n    if (item.isTemporary || item.type === 'temporary') {\n        console.log('🟠 مادة مؤقتة - لون برتقالي:', item.name || item.title);\n        return 'FFFFA500'; // برتقالي للمواد المؤقتة\n    }\n    // التحقق من كونها إعادة\n    if (item.isRerun || item.type === 'rerun' || item.mediaItem && item.mediaItem.name?.includes('(إعادة)')) {\n        console.log('🔘 إعادة - لون رمادي:', item.mediaItem?.name || item.name);\n        return 'FFC0C0C0'; // رمادي للإعادات\n    }\n    // البرايم بلون ذهبي\n    console.log('🟡 برايم - لون ذهبي:', item.mediaItem?.name || item.name);\n    return 'FFFFD700'; // ذهبي للبرايم\n}\n// تحديد لون النص\nfunction getTextColor(backgroundColor) {\n    if (backgroundColor === 'FFFFFFFF') {\n        return 'FF000000'; // نص أسود على خلفية بيضاء\n    }\n    return 'FF000000'; // نص أسود على باقي الخلفيات\n}\n// تنسيق الوقت للعرض\nfunction formatTime(hour) {\n    return `${hour.toString().padStart(2, '0')}:00`;\n}\n// أسماء الأيام\nfunction getDayName(dayIndex) {\n    const days = [\n        'الأحد',\n        'الاثنين',\n        'الثلاثاء',\n        'الأربعاء',\n        'الخميس',\n        'الجمعة',\n        'السبت'\n    ];\n    return days[dayIndex] || `يوم ${dayIndex}`;\n}\n// دالة تحويل الوقت إلى دقائق\nfunction timeToMinutes(time) {\n    const [hours, minutes] = time.split(':').map(Number);\n    return hours * 60 + minutes;\n}\n// دالة إضافة دقائق للوقت\nfunction addMinutesToTime(time, minutes) {\n    const totalMinutes = timeToMinutes(time) + minutes;\n    const hours = Math.floor(totalMinutes / 60) % 24;\n    const mins = totalMinutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n}\n// دالة توليد الإعادات التلقائية\nfunction generateReruns(scheduleItems, weekStart, tempItems = []) {\n    const reruns = [];\n    console.log(`🔄 توليد الإعادات للأسبوع: ${weekStart}`);\n    // دمج المواد العادية والمؤقتة\n    const allItems = [\n        ...scheduleItems,\n        ...tempItems\n    ];\n    const regularItems = allItems.filter((item)=>!item.isRerun && !item.isTemporary);\n    const temporaryItems = allItems.filter((item)=>!item.isRerun && item.isTemporary);\n    console.log(`📊 المواد: ${regularItems.length} عادية + ${temporaryItems.length} مؤقتة`);\n    // تجميع المواد حسب اليوم لتوليد إعادات متتالية\n    const itemsByDay = new Map();\n    // تجميع المواد العادية حسب اليوم\n    regularItems.forEach((item)=>{\n        const dayOfWeek = item.dayOfWeek;\n        if (!itemsByDay.has(dayOfWeek)) {\n            itemsByDay.set(dayOfWeek, []);\n        }\n        itemsByDay.get(dayOfWeek).push(item);\n    });\n    // تجميع المواد المؤقتة حسب اليوم\n    temporaryItems.forEach((item)=>{\n        const dayOfWeek = item.dayOfWeek;\n        if (!itemsByDay.has(dayOfWeek)) {\n            itemsByDay.set(dayOfWeek, []);\n        }\n        itemsByDay.get(dayOfWeek).push(item);\n    });\n    // ترتيب المواد في كل يوم حسب الوقت\n    itemsByDay.forEach((items, day)=>{\n        items.sort((a, b)=>a.startTime.localeCompare(b.startTime));\n    });\n    // توليد الإعادات لكل يوم\n    itemsByDay.forEach((dayItems, dayOfWeek)=>{\n        if (dayItems.length > 0) {\n            const dayReruns = generateSequentialReruns(dayItems, weekStart, dayOfWeek);\n            reruns.push(...dayReruns);\n        }\n    });\n    return reruns;\n}\n// دالة توليد إعادات متتالية لمواد يوم واحد\nfunction generateSequentialReruns(dayItems, weekStart, dayOfWeek) {\n    const reruns = [];\n    if (dayItems.length === 0) return reruns;\n    console.log(`🔄 توليد إعادات لـ ${dayItems.length} مادة من اليوم ${dayOfWeek}`);\n    // تحديد أوقات الإعادات حسب اليوم\n    let rerunStartTime;\n    let rerunDay;\n    if ([\n        0,\n        1,\n        2,\n        3\n    ].includes(dayOfWeek)) {\n        // الأحد-الأربعاء: الإعادات تبدأ من 00:00 في نفس اليوم\n        rerunStartTime = '00:00';\n        rerunDay = dayOfWeek;\n    } else if ([\n        4,\n        5,\n        6\n    ].includes(dayOfWeek)) {\n        // الخميس-السبت: الإعادات تبدأ من 02:00 في نفس اليوم\n        rerunStartTime = '02:00';\n        rerunDay = dayOfWeek;\n    } else {\n        return reruns;\n    }\n    // إنشاء قائمة مستمرة من المواد\n    function getNextItem(index) {\n        return dayItems[index % dayItems.length];\n    }\n    let currentTime = rerunStartTime;\n    let itemSequenceIndex = 0;\n    // الجزء الأول: حتى 08:00\n    while(timeToMinutes(currentTime) < timeToMinutes('08:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('08:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${rerunDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: rerunDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: weekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                mediaItem: item.mediaItem,\n                originalId: item.id\n            });\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            break;\n        }\n    }\n    // الجزء الثاني: اليوم التالي من 08:00 إلى 18:00\n    let nextDay;\n    if (dayOfWeek === 6) {\n        nextDay = 0; // الأحد\n    } else {\n        nextDay = (rerunDay + 1) % 7;\n    }\n    currentTime = '08:00';\n    while(timeToMinutes(currentTime) < timeToMinutes('18:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('18:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${nextDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: nextDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: weekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                mediaItem: item.mediaItem,\n                originalId: item.id\n            });\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            break;\n        }\n    }\n    return reruns;\n}\nasync function GET(request) {\n    try {\n        console.log('📊 بدء عملية تصدير الخريطة الأسبوعية...');\n        // استخراج معاملات الطلب\n        const url = new URL(request.url);\n        const weekStart = url.searchParams.get('weekStart');\n        if (!weekStart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يجب تحديد تاريخ بداية الأسبوع'\n            }, {\n                status: 400\n            });\n        }\n        // تحميل بيانات الجدول\n        const scheduleData = loadWeeklySchedule(weekStart);\n        if (!scheduleData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لم يتم العثور على بيانات الجدول'\n            }, {\n                status: 404\n            });\n        }\n        // تحميل المواد المؤقتة\n        let temporaryItems = [];\n        try {\n            const possibleTempPaths = [\n                path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data', 'temp-items.json'),\n                path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'temporary-items.json'),\n                path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'temp-data.json')\n            ];\n            for (const tempPath of possibleTempPaths){\n                if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(tempPath)) {\n                    const tempContent = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(tempPath, 'utf8');\n                    const parsedTemp = JSON.parse(tempContent);\n                    // التأكد من أن البيانات مصفوفة\n                    if (Array.isArray(parsedTemp)) {\n                        temporaryItems = parsedTemp;\n                    } else if (parsedTemp && typeof parsedTemp === 'object') {\n                        // إذا كانت البيانات كائن، حاول استخراج المصفوفة\n                        temporaryItems = Object.values(parsedTemp).flat();\n                    }\n                    console.log('📦 تم تحميل', temporaryItems.length, 'مادة مؤقتة من:', tempPath);\n                    break;\n                }\n            }\n            if (temporaryItems.length === 0) {\n                console.log('📦 لا توجد مواد مؤقتة محفوظة');\n            }\n        } catch (error) {\n            console.log('📦 خطأ في تحميل المواد المؤقتة:', error);\n            temporaryItems = []; // تأكد من أن المتغير مصفوفة\n        }\n        // توليد الإعادات التلقائية\n        const reruns = generateReruns(scheduleData, weekStart, temporaryItems);\n        console.log('🔄 تم توليد', reruns.length, 'إعادة');\n        // دمج جميع البيانات (البرايم + الإعادات + المواد المؤقتة)\n        const allScheduleData = [\n            ...scheduleData,\n            ...reruns,\n            ...temporaryItems\n        ];\n        console.log('📊 إجمالي المواد للتصدير:', allScheduleData.length);\n        console.log('📋 بدء إنشاء ملف Excel للخريطة...');\n        // إنشاء ملف Excel\n        const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_1___default().Workbook)();\n        // تعيين خصائص الملف\n        workbook.creator = 'نظام إدارة المواد الإعلامية';\n        workbook.title = 'الخريطة الأسبوعية للبث';\n        workbook.subject = `خريطة الأسبوع ${weekStart}`;\n        workbook.created = new Date();\n        // إنشاء ورقة العمل\n        const worksheet = workbook.addWorksheet('الخريطة الأسبوعية');\n        // تعيين اتجاه الورقة من اليمين لليسار\n        worksheet.views = [\n            {\n                rightToLeft: true,\n                zoomScale: 70\n            }\n        ];\n        // إنشاء الرؤوس مع التواريخ\n        const startDate = new Date(weekStart);\n        const headers = [\n            'الوقت'\n        ];\n        for(let i = 0; i < 7; i++){\n            const currentDate = new Date(startDate);\n            currentDate.setDate(startDate.getDate() + i);\n            const dayName = getDayName(i);\n            const dateStr = currentDate.toLocaleDateString('en-GB', {\n                day: '2-digit',\n                month: '2-digit'\n            });\n            headers.push(`${dayName}\\n${dateStr}`);\n        }\n        worksheet.addRow(headers);\n        // تنسيق الرؤوس\n        const headerRow = worksheet.getRow(1);\n        headerRow.height = 30;\n        headerRow.eachCell((cell)=>{\n            cell.fill = {\n                type: 'pattern',\n                pattern: 'solid',\n                fgColor: {\n                    argb: 'FF4472C4'\n                }\n            };\n            cell.font = {\n                bold: true,\n                color: {\n                    argb: 'FFFFFFFF'\n                },\n                size: 14\n            };\n            cell.alignment = {\n                horizontal: 'center',\n                vertical: 'middle'\n            };\n            cell.border = {\n                top: {\n                    style: 'thin'\n                },\n                left: {\n                    style: 'thin'\n                },\n                bottom: {\n                    style: 'thin'\n                },\n                right: {\n                    style: 'thin'\n                }\n            };\n        });\n        // تحويل البيانات إلى شبكة منظمة حسب اليوم والساعة\n        const scheduleGrid = Array(7).fill(null).map(()=>Array(24).fill(null));\n        // ملء الشبكة بالبيانات\n        if (Array.isArray(allScheduleData)) {\n            allScheduleData.forEach((item)=>{\n                if (item && item.dayOfWeek !== undefined && item.startTime) {\n                    const dayIndex = item.dayOfWeek;\n                    const [hours] = item.startTime.split(':').map(Number);\n                    // تحويل الساعة إلى فهرس في الشبكة (8 صباحاً = فهرس 0)\n                    let hourIndex;\n                    if (hours >= 8) {\n                        hourIndex = hours - 8; // 8-23 -> 0-15\n                    } else {\n                        hourIndex = hours + 16; // 0-7 -> 16-23\n                    }\n                    if (dayIndex >= 0 && dayIndex < 7 && hourIndex >= 0 && hourIndex < 24) {\n                        scheduleGrid[dayIndex][hourIndex] = item;\n                    }\n                }\n            });\n        }\n        console.log('📊 تم تنظيم البيانات في شبكة 7×24');\n        // إضافة البيانات (24 ساعة × 7 أيام)\n        for(let hour = 8; hour < 32; hour++){\n            const displayHour = hour >= 24 ? hour - 24 : hour;\n            const timeLabel = formatTime(displayHour);\n            const rowData = [\n                timeLabel\n            ];\n            // إضافة بيانات كل يوم\n            for(let day = 0; day < 7; day++){\n                const hourIndex = hour - 8; // تحويل إلى فهرس الشبكة\n                const hourData = scheduleGrid[day][hourIndex];\n                let cellContent = '';\n                if (hourData) {\n                    // التعامل مع المواد المؤقتة\n                    if (hourData.isTemporary || hourData.type === 'temporary') {\n                        cellContent = hourData.name || hourData.title || 'مادة مؤقتة';\n                        if (hourData.duration) {\n                            cellContent += ` (${hourData.duration})`;\n                        }\n                    } else if (hourData.mediaItem && hourData.mediaItem.name) {\n                        cellContent = hourData.mediaItem.name;\n                        // إضافة معلومات إضافية\n                        if (hourData.episodeNumber) {\n                            cellContent += ` - ح${hourData.episodeNumber}`;\n                        }\n                        if (hourData.partNumber) {\n                            cellContent += ` - ج${hourData.partNumber}`;\n                        }\n                        if (hourData.isRerun) {\n                            cellContent += ' (إعادة)';\n                        }\n                    } else if (hourData.name) {\n                        cellContent = hourData.name;\n                        if (hourData.isRerun) {\n                            cellContent += ' (إعادة)';\n                        }\n                    }\n                }\n                rowData.push(cellContent);\n            }\n            const row = worksheet.addRow(rowData);\n            // تنسيق الصف\n            row.eachCell((cell, colNumber)=>{\n                if (colNumber === 1) {\n                    // تنسيق عمود الوقت\n                    cell.fill = {\n                        type: 'pattern',\n                        pattern: 'solid',\n                        fgColor: {\n                            argb: 'FFF0F0F0'\n                        }\n                    };\n                    cell.font = {\n                        bold: true,\n                        size: 12\n                    };\n                } else {\n                    // تنسيق خلايا البيانات\n                    const dayIndex = colNumber - 2;\n                    const hourIndex = hour - 8;\n                    const hourData = scheduleGrid[dayIndex][hourIndex];\n                    const backgroundColor = getCellColor(hourData);\n                    const textColor = getTextColor(backgroundColor);\n                    cell.fill = {\n                        type: 'pattern',\n                        pattern: 'solid',\n                        fgColor: {\n                            argb: backgroundColor\n                        }\n                    };\n                    cell.font = {\n                        color: {\n                            argb: textColor\n                        },\n                        size: 10\n                    };\n                }\n                cell.alignment = {\n                    horizontal: 'center',\n                    vertical: 'middle',\n                    wrapText: true\n                };\n                cell.border = {\n                    top: {\n                        style: 'thin'\n                    },\n                    left: {\n                        style: 'thin'\n                    },\n                    bottom: {\n                        style: 'thin'\n                    },\n                    right: {\n                        style: 'thin'\n                    }\n                };\n            });\n            // تعيين ارتفاع الصف\n            row.height = 25;\n        }\n        // تعيين عرض الأعمدة\n        const columnWidths = [\n            12,\n            20,\n            20,\n            20,\n            20,\n            20,\n            20,\n            20\n        ]; // الوقت + 7 أيام\n        columnWidths.forEach((width, index)=>{\n            worksheet.getColumn(index + 1).width = width;\n        });\n        console.log('✅ تم إنشاء الخريطة الأسبوعية بنجاح');\n        // تحويل إلى buffer\n        const buffer = await workbook.xlsx.writeBuffer();\n        console.log('✅ تم إنشاء ملف Excel بنجاح');\n        // إرسال الملف\n        const fileName = `Weekly_Schedule_${weekStart}.xlsx`;\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n                'Content-Disposition': `attachment; filename=\"${fileName}\"`,\n                'Content-Length': buffer.byteLength.toString(),\n                'Cache-Control': 'no-cache'\n            }\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تصدير الخريطة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تصدير الخريطة'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/export-schedule/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "constants":
/*!****************************!*\
  !*** external "constants" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("constants");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "rimraf":
/*!*************************!*\
  !*** external "rimraf" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("rimraf");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/exceljs","vendor-chunks/jszip","vendor-chunks/bluebird","vendor-chunks/unzipper","vendor-chunks/@fast-csv","vendor-chunks/pako","vendor-chunks/fstream","vendor-chunks/uuid","vendor-chunks/readable-stream","vendor-chunks/lazystream","vendor-chunks/archiver-utils","vendor-chunks/duplexer2","vendor-chunks/compress-commons","vendor-chunks/archiver","vendor-chunks/tar-stream","vendor-chunks/readdir-glob","vendor-chunks/graceful-fs","vendor-chunks/zip-stream","vendor-chunks/xmlchars","vendor-chunks/glob","vendor-chunks/dayjs","vendor-chunks/crc32-stream","vendor-chunks/inherits","vendor-chunks/fs.realpath","vendor-chunks/buffer-indexof-polyfill","vendor-chunks/bl","vendor-chunks/binary","vendor-chunks/async","vendor-chunks/wrappy","vendor-chunks/util-deprecate","vendor-chunks/traverse","vendor-chunks/tmp","vendor-chunks/string_decoder","vendor-chunks/saxes","vendor-chunks/safe-buffer","vendor-chunks/process-nextick-args","vendor-chunks/path-is-absolute","vendor-chunks/once","vendor-chunks/normalize-path","vendor-chunks/minimatch","vendor-chunks/lodash.uniq","vendor-chunks/lodash.union","vendor-chunks/lodash.isundefined","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnil","vendor-chunks/lodash.isfunction","vendor-chunks/lodash.isequal","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.groupby","vendor-chunks/lodash.flatten","vendor-chunks/lodash.escaperegexp","vendor-chunks/lodash.difference","vendor-chunks/lodash.defaults","vendor-chunks/listenercount","vendor-chunks/lie","vendor-chunks/inflight","vendor-chunks/immediate","vendor-chunks/fs-constants","vendor-chunks/fast-csv","vendor-chunks/end-of-stream","vendor-chunks/crc-32","vendor-chunks/core-util-is","vendor-chunks/concat-map","vendor-chunks/chainsaw","vendor-chunks/buffers","vendor-chunks/buffer-crc32","vendor-chunks/brace-expansion","vendor-chunks/big-integer","vendor-chunks/balanced-match"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport-schedule%2Froute&page=%2Fapi%2Fexport-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();