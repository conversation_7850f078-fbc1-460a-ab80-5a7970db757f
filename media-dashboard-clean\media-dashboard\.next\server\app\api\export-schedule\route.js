/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/export-schedule/route";
exports.ids = ["app/api/export-schedule/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport-schedule%2Froute&page=%2Fapi%2Fexport-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport-schedule%2Froute&page=%2Fapi%2Fexport-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_export_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/export-schedule/route.ts */ \"(rsc)/./src/app/api/export-schedule/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/export-schedule/route\",\n        pathname: \"/api/export-schedule\",\n        filename: \"route\",\n        bundlePath: \"app/api/export-schedule/route\"\n    },\n    resolvedPagePath: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\export-schedule\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_export_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport-schedule%2Froute&page=%2Fapi%2Fexport-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/export-schedule/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/export-schedule/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! exceljs */ \"(rsc)/./node_modules/exceljs/excel.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// تحميل بيانات الجدول الأسبوعي من نفس API المستخدم في الصفحة\nasync function loadWeeklySchedule(weekStart) {\n    try {\n        console.log(`🔄 تحميل بيانات الأسبوع ${weekStart} من API...`);\n        // استخدام نفس API المستخدم في صفحة الخريطة\n        const weeklyScheduleModule = __webpack_require__(/*! ../weekly-schedule/route */ \"(rsc)/./src/app/api/weekly-schedule/route.ts\");\n        // محاكاة طلب GET\n        const mockRequest = {\n            url: `http://localhost:3000/api/weekly-schedule?weekStart=${weekStart}`,\n            nextUrl: {\n                searchParams: {\n                    get: (key)=>key === 'weekStart' ? weekStart : null\n                }\n            }\n        };\n        const response = await weeklyScheduleModule.GET(mockRequest);\n        const result = await response.json();\n        if (result.success && result.data) {\n            const scheduleItems = result.data.scheduleItems || [];\n            console.log(`📂 تم تحميل ${scheduleItems.length} مادة للأسبوع ${weekStart}`);\n            return scheduleItems;\n        } else {\n            console.log('📂 لا توجد بيانات للأسبوع المحدد');\n            return [];\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحميل الجدول:', error);\n        // في حالة فشل تحميل البيانات، إرجاع مصفوفة فارغة بدلاً من null\n        return [];\n    }\n}\n// تحديد لون الخلية حسب نوع المحتوى\nfunction getCellColor(item) {\n    if (!item) {\n        return 'FFFFFFFF'; // أبيض للخلايا الفارغة\n    }\n    // التحقق من كونها مادة مؤقتة\n    if (item.isTemporary || item.type === 'temporary') {\n        console.log('🟠 مادة مؤقتة - لون برتقالي:', item.name || item.title);\n        return 'FFFFA500'; // برتقالي للمواد المؤقتة\n    }\n    // التحقق من كونها إعادة\n    if (item.isRerun || item.type === 'rerun' || item.mediaItem && item.mediaItem.name?.includes('(إعادة)')) {\n        console.log('🔘 إعادة - لون رمادي:', item.mediaItem?.name || item.name);\n        return 'FFC0C0C0'; // رمادي للإعادات\n    }\n    // البرايم بلون ذهبي\n    console.log('🟡 برايم - لون ذهبي:', item.mediaItem?.name || item.name);\n    return 'FFFFD700'; // ذهبي للبرايم\n}\n// تحديد لون النص\nfunction getTextColor(backgroundColor) {\n    if (backgroundColor === 'FFFFFFFF') {\n        return 'FF000000'; // نص أسود على خلفية بيضاء\n    }\n    return 'FF000000'; // نص أسود على باقي الخلفيات\n}\n// تنسيق الوقت للعرض\nfunction formatTime(hour) {\n    return `${hour.toString().padStart(2, '0')}:00`;\n}\n// أسماء الأيام\nfunction getDayName(dayIndex) {\n    const days = [\n        'الأحد',\n        'الاثنين',\n        'الثلاثاء',\n        'الأربعاء',\n        'الخميس',\n        'الجمعة',\n        'السبت'\n    ];\n    return days[dayIndex] || `يوم ${dayIndex}`;\n}\n// دالة تحويل الوقت إلى دقائق\nfunction timeToMinutes(time) {\n    const [hours, minutes] = time.split(':').map(Number);\n    return hours * 60 + minutes;\n}\n// دالة إضافة دقائق للوقت\nfunction addMinutesToTime(time, minutes) {\n    const totalMinutes = timeToMinutes(time) + minutes;\n    const hours = Math.floor(totalMinutes / 60) % 24;\n    const mins = totalMinutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n}\n// دالة توليد الإعادات التلقائية\nfunction generateReruns(scheduleItems, weekStart, tempItems = []) {\n    const reruns = [];\n    console.log(`🔄 توليد الإعادات للأسبوع: ${weekStart}`);\n    // دمج المواد العادية والمؤقتة\n    const allItems = [\n        ...scheduleItems,\n        ...tempItems\n    ];\n    const regularItems = allItems.filter((item)=>!item.isRerun && !item.isTemporary);\n    const temporaryItems = allItems.filter((item)=>!item.isRerun && item.isTemporary);\n    console.log(`📊 المواد: ${regularItems.length} عادية + ${temporaryItems.length} مؤقتة`);\n    // تجميع المواد حسب اليوم لتوليد إعادات متتالية\n    const itemsByDay = new Map();\n    // تجميع المواد العادية حسب اليوم\n    regularItems.forEach((item)=>{\n        const dayOfWeek = item.dayOfWeek;\n        if (!itemsByDay.has(dayOfWeek)) {\n            itemsByDay.set(dayOfWeek, []);\n        }\n        itemsByDay.get(dayOfWeek).push(item);\n    });\n    // تجميع المواد المؤقتة حسب اليوم\n    temporaryItems.forEach((item)=>{\n        const dayOfWeek = item.dayOfWeek;\n        if (!itemsByDay.has(dayOfWeek)) {\n            itemsByDay.set(dayOfWeek, []);\n        }\n        itemsByDay.get(dayOfWeek).push(item);\n    });\n    // ترتيب المواد في كل يوم حسب الوقت\n    itemsByDay.forEach((items, day)=>{\n        items.sort((a, b)=>a.startTime.localeCompare(b.startTime));\n    });\n    // توليد الإعادات لكل يوم\n    itemsByDay.forEach((dayItems, dayOfWeek)=>{\n        if (dayItems.length > 0) {\n            const dayReruns = generateSequentialReruns(dayItems, weekStart, dayOfWeek);\n            reruns.push(...dayReruns);\n        }\n    });\n    return reruns;\n}\n// دالة توليد إعادات متتالية لمواد يوم واحد\nfunction generateSequentialReruns(dayItems, weekStart, dayOfWeek) {\n    const reruns = [];\n    if (dayItems.length === 0) return reruns;\n    console.log(`🔄 توليد إعادات لـ ${dayItems.length} مادة من اليوم ${dayOfWeek}`);\n    // تحديد أوقات الإعادات حسب اليوم\n    let rerunStartTime;\n    let rerunDay;\n    if ([\n        0,\n        1,\n        2,\n        3\n    ].includes(dayOfWeek)) {\n        // الأحد-الأربعاء: الإعادات تبدأ من 00:00 في نفس اليوم\n        rerunStartTime = '00:00';\n        rerunDay = dayOfWeek;\n    } else if ([\n        4,\n        5,\n        6\n    ].includes(dayOfWeek)) {\n        // الخميس-السبت: الإعادات تبدأ من 02:00 في نفس اليوم\n        rerunStartTime = '02:00';\n        rerunDay = dayOfWeek;\n    } else {\n        return reruns;\n    }\n    // إنشاء قائمة مستمرة من المواد\n    function getNextItem(index) {\n        return dayItems[index % dayItems.length];\n    }\n    let currentTime = rerunStartTime;\n    let itemSequenceIndex = 0;\n    // الجزء الأول: حتى 08:00\n    while(timeToMinutes(currentTime) < timeToMinutes('08:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('08:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${rerunDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: rerunDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: weekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                mediaItem: item.mediaItem,\n                originalId: item.id\n            });\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            break;\n        }\n    }\n    // الجزء الثاني: اليوم التالي من 08:00 إلى 18:00\n    let nextDay;\n    if (dayOfWeek === 6) {\n        nextDay = 0; // الأحد\n    } else {\n        nextDay = (rerunDay + 1) % 7;\n    }\n    currentTime = '08:00';\n    while(timeToMinutes(currentTime) < timeToMinutes('18:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('18:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${nextDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: nextDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: weekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                mediaItem: item.mediaItem,\n                originalId: item.id\n            });\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            break;\n        }\n    }\n    return reruns;\n}\nasync function GET(request) {\n    try {\n        console.log('📊 بدء عملية تصدير الخريطة الأسبوعية...');\n        // استخراج معاملات الطلب\n        const url = new URL(request.url);\n        const weekStart = url.searchParams.get('weekStart');\n        if (!weekStart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يجب تحديد تاريخ بداية الأسبوع'\n            }, {\n                status: 400\n            });\n        }\n        // تحميل بيانات الجدول\n        const scheduleData = await loadWeeklySchedule(weekStart);\n        if (!scheduleData || scheduleData.length === 0) {\n            console.log('⚠️ لا توجد بيانات للأسبوع المحدد، سيتم إنشاء ملف فارغ');\n        // لا نرجع خطأ، بل ننشئ ملف فارغ\n        }\n        // تحميل المواد المؤقتة\n        let temporaryItems = [];\n        try {\n            const possibleTempPaths = [\n                path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'data', 'temp-items.json'),\n                path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'temporary-items.json'),\n                path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'temp-data.json')\n            ];\n            for (const tempPath of possibleTempPaths){\n                if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(tempPath)) {\n                    const tempContent = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(tempPath, 'utf8');\n                    const parsedTemp = JSON.parse(tempContent);\n                    // التأكد من أن البيانات مصفوفة\n                    if (Array.isArray(parsedTemp)) {\n                        temporaryItems = parsedTemp;\n                    } else if (parsedTemp && typeof parsedTemp === 'object') {\n                        // إذا كانت البيانات كائن، حاول استخراج المصفوفة\n                        temporaryItems = Object.values(parsedTemp).flat();\n                    }\n                    console.log('📦 تم تحميل', temporaryItems.length, 'مادة مؤقتة من:', tempPath);\n                    break;\n                }\n            }\n            if (temporaryItems.length === 0) {\n                console.log('📦 لا توجد مواد مؤقتة محفوظة');\n            }\n        } catch (error) {\n            console.log('📦 خطأ في تحميل المواد المؤقتة:', error);\n            temporaryItems = []; // تأكد من أن المتغير مصفوفة\n        }\n        // توليد الإعادات التلقائية\n        const reruns = generateReruns(scheduleData, weekStart, temporaryItems);\n        console.log('🔄 تم توليد', reruns.length, 'إعادة');\n        // دمج جميع البيانات (البرايم + الإعادات + المواد المؤقتة)\n        const allScheduleData = [\n            ...scheduleData,\n            ...reruns,\n            ...temporaryItems\n        ];\n        console.log('📊 إجمالي المواد للتصدير:', allScheduleData.length);\n        console.log('📋 بدء إنشاء ملف Excel للخريطة...');\n        // إنشاء ملف Excel\n        const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_1___default().Workbook)();\n        // تعيين خصائص الملف\n        workbook.creator = 'نظام إدارة المواد الإعلامية';\n        workbook.title = 'الخريطة الأسبوعية للبث';\n        workbook.subject = `خريطة الأسبوع ${weekStart}`;\n        workbook.created = new Date();\n        // إنشاء ورقة العمل\n        const worksheet = workbook.addWorksheet('الخريطة الأسبوعية');\n        // تعيين اتجاه الورقة من اليمين لليسار\n        worksheet.views = [\n            {\n                rightToLeft: true,\n                zoomScale: 70\n            }\n        ];\n        // إنشاء الرؤوس مع التواريخ\n        const startDate = new Date(weekStart);\n        const headers = [\n            'الوقت'\n        ];\n        for(let i = 0; i < 7; i++){\n            const currentDate = new Date(startDate);\n            currentDate.setDate(startDate.getDate() + i);\n            const dayName = getDayName(i);\n            const dateStr = currentDate.toLocaleDateString('en-GB', {\n                day: '2-digit',\n                month: '2-digit'\n            });\n            headers.push(`${dayName}\\n${dateStr}`);\n        }\n        worksheet.addRow(headers);\n        // تنسيق الرؤوس\n        const headerRow = worksheet.getRow(1);\n        headerRow.height = 30;\n        headerRow.eachCell((cell)=>{\n            cell.fill = {\n                type: 'pattern',\n                pattern: 'solid',\n                fgColor: {\n                    argb: 'FF4472C4'\n                }\n            };\n            cell.font = {\n                bold: true,\n                color: {\n                    argb: 'FFFFFFFF'\n                },\n                size: 14\n            };\n            cell.alignment = {\n                horizontal: 'center',\n                vertical: 'middle'\n            };\n            cell.border = {\n                top: {\n                    style: 'thin'\n                },\n                left: {\n                    style: 'thin'\n                },\n                bottom: {\n                    style: 'thin'\n                },\n                right: {\n                    style: 'thin'\n                }\n            };\n        });\n        // تحويل البيانات إلى شبكة منظمة حسب اليوم والساعة\n        const scheduleGrid = Array(7).fill(null).map(()=>Array(24).fill(null));\n        // ملء الشبكة بالبيانات\n        if (Array.isArray(allScheduleData)) {\n            allScheduleData.forEach((item)=>{\n                if (item && item.dayOfWeek !== undefined && item.startTime) {\n                    const dayIndex = item.dayOfWeek;\n                    const [hours] = item.startTime.split(':').map(Number);\n                    // تحويل الساعة إلى فهرس في الشبكة (8 صباحاً = فهرس 0)\n                    let hourIndex;\n                    if (hours >= 8) {\n                        hourIndex = hours - 8; // 8-23 -> 0-15\n                    } else {\n                        hourIndex = hours + 16; // 0-7 -> 16-23\n                    }\n                    if (dayIndex >= 0 && dayIndex < 7 && hourIndex >= 0 && hourIndex < 24) {\n                        scheduleGrid[dayIndex][hourIndex] = item;\n                    }\n                }\n            });\n        }\n        console.log('📊 تم تنظيم البيانات في شبكة 7×24');\n        // إضافة البيانات (24 ساعة × 7 أيام)\n        for(let hour = 8; hour < 32; hour++){\n            const displayHour = hour >= 24 ? hour - 24 : hour;\n            const timeLabel = formatTime(displayHour);\n            const rowData = [\n                timeLabel\n            ];\n            // إضافة بيانات كل يوم\n            for(let day = 0; day < 7; day++){\n                const hourIndex = hour - 8; // تحويل إلى فهرس الشبكة\n                const hourData = scheduleGrid[day][hourIndex];\n                let cellContent = '';\n                if (hourData) {\n                    // التعامل مع المواد العادية أولاً\n                    if (hourData.mediaItem && hourData.mediaItem.name) {\n                        cellContent = hourData.mediaItem.name;\n                        // إضافة معلومات إضافية\n                        if (hourData.episodeNumber) {\n                            cellContent += ` - ح${hourData.episodeNumber}`;\n                        }\n                        if (hourData.partNumber) {\n                            cellContent += ` - ج${hourData.partNumber}`;\n                        }\n                        if (hourData.isRerun) {\n                            cellContent += ' (إعادة)';\n                        }\n                    } else if (hourData.isTemporary || hourData.type === 'temporary') {\n                        cellContent = hourData.name || hourData.title || 'مادة مؤقتة';\n                        if (hourData.duration) {\n                            cellContent += ` (${hourData.duration})`;\n                        }\n                    } else if (hourData.name) {\n                        cellContent = hourData.name;\n                        if (hourData.isRerun) {\n                            cellContent += ' (إعادة)';\n                        }\n                    } else if (hourData.mediaItemId) {\n                        cellContent = `مادة ${hourData.mediaItemId}`;\n                        if (hourData.isRerun) {\n                            cellContent += ' (إعادة)';\n                        }\n                    }\n                }\n                rowData.push(cellContent);\n            }\n            const row = worksheet.addRow(rowData);\n            // تنسيق الصف\n            row.eachCell((cell, colNumber)=>{\n                if (colNumber === 1) {\n                    // تنسيق عمود الوقت\n                    cell.fill = {\n                        type: 'pattern',\n                        pattern: 'solid',\n                        fgColor: {\n                            argb: 'FFF0F0F0'\n                        }\n                    };\n                    cell.font = {\n                        bold: true,\n                        size: 12\n                    };\n                } else {\n                    // تنسيق خلايا البيانات\n                    const dayIndex = colNumber - 2;\n                    const hourIndex = hour - 8;\n                    const hourData = scheduleGrid[dayIndex][hourIndex];\n                    const backgroundColor = getCellColor(hourData);\n                    const textColor = getTextColor(backgroundColor);\n                    cell.fill = {\n                        type: 'pattern',\n                        pattern: 'solid',\n                        fgColor: {\n                            argb: backgroundColor\n                        }\n                    };\n                    cell.font = {\n                        color: {\n                            argb: textColor\n                        },\n                        size: 10\n                    };\n                }\n                cell.alignment = {\n                    horizontal: 'center',\n                    vertical: 'middle',\n                    wrapText: true\n                };\n                cell.border = {\n                    top: {\n                        style: 'thin'\n                    },\n                    left: {\n                        style: 'thin'\n                    },\n                    bottom: {\n                        style: 'thin'\n                    },\n                    right: {\n                        style: 'thin'\n                    }\n                };\n            });\n            // تعيين ارتفاع الصف\n            row.height = 25;\n        }\n        // تعيين عرض الأعمدة\n        const columnWidths = [\n            12,\n            20,\n            20,\n            20,\n            20,\n            20,\n            20,\n            20\n        ]; // الوقت + 7 أيام\n        columnWidths.forEach((width, index)=>{\n            worksheet.getColumn(index + 1).width = width;\n        });\n        console.log('✅ تم إنشاء الخريطة الأسبوعية بنجاح');\n        // تحويل إلى buffer\n        const buffer = await workbook.xlsx.writeBuffer();\n        console.log('✅ تم إنشاء ملف Excel بنجاح');\n        // إرسال الملف\n        const fileName = `Weekly_Schedule_${weekStart}.xlsx`;\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n                'Content-Disposition': `attachment; filename=\"${fileName}\"`,\n                'Content-Length': buffer.byteLength.toString(),\n                'Cache-Control': 'no-cache'\n            }\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تصدير الخريطة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تصدير الخريطة'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/export-schedule/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/shared-data.ts":
/*!************************************!*\
  !*** ./src/app/api/shared-data.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMediaItem: () => (/* binding */ addMediaItem),\n/* harmony export */   getAllMediaItems: () => (/* binding */ getAllMediaItems),\n/* harmony export */   getMediaItemById: () => (/* binding */ getMediaItemById),\n/* harmony export */   removeMediaItem: () => (/* binding */ removeMediaItem),\n/* harmony export */   updateMediaItem: () => (/* binding */ updateMediaItem)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n// ملف مشترك للبيانات - يضمن التزامن بين جميع APIs\n\n\n// مسار ملف البيانات المؤقت\nconst DATA_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'temp-data.json');\n// قاعدة بيانات مشتركة للمواد الإعلامية\nlet mediaItems = [];\n// تحميل البيانات من الملف عند بدء التشغيل\nfunction loadData() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(DATA_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(DATA_FILE, 'utf8');\n            mediaItems = JSON.parse(data);\n            console.log(`📂 تم تحميل ${mediaItems.length} مادة من الملف المؤقت`);\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحميل البيانات:', error);\n        mediaItems = [];\n    }\n}\n// حفظ البيانات في الملف\nfunction saveData() {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(DATA_FILE, JSON.stringify(mediaItems, null, 2));\n        console.log(`💾 تم حفظ ${mediaItems.length} مادة في الملف المؤقت`);\n    } catch (error) {\n        console.error('❌ خطأ في حفظ البيانات:', error);\n    }\n}\n// تحميل البيانات عند استيراد الملف\nloadData();\n// دالة لإضافة مادة جديدة\nfunction addMediaItem(item) {\n    mediaItems.push(item);\n    saveData(); // حفظ فوري\n    console.log(`✅ تم إضافة مادة جديدة: ${item.name} (المجموع: ${mediaItems.length})`);\n}\n// دالة لحذف مادة\nfunction removeMediaItem(id) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        const removed = mediaItems.splice(index, 1)[0];\n        saveData(); // حفظ فوري\n        console.log(`🗑️ تم حذف المادة: ${removed.name} (المجموع: ${mediaItems.length})`);\n        return true;\n    }\n    return false;\n}\n// دالة للحصول على جميع المواد\nfunction getAllMediaItems() {\n    return mediaItems;\n}\n// دالة للحصول على مادة بالمعرف\nfunction getMediaItemById(id) {\n    return mediaItems.find((item)=>item.id === id);\n}\n// دالة لتحديث مادة\nfunction updateMediaItem(id, updatedItem) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        mediaItems[index] = {\n            ...mediaItems[index],\n            ...updatedItem\n        };\n        saveData(); // حفظ فوري\n        console.log(`✏️ تم تحديث المادة: ${updatedItem.name} (المعرف: ${id})`);\n        return true;\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/shared-data.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/weekly-schedule/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/weekly-schedule/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared-data */ \"(rsc)/./src/app/api/shared-data.ts\");\n\n\n\n// استيراد البيانات المشتركة\n\n// بيانات الجداول الأسبوعية مع التواريخ\nlet weeklySchedules = new Map();\n// بيانات المواد المؤقتة لكل أسبوع\nlet tempItems = new Map();\n// مسار ملف الحفظ\nconst SCHEDULES_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data', 'weekly-schedules.json');\nconst TEMP_ITEMS_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data', 'temp-items.json');\n// التأكد من وجود مجلد البيانات\nasync function ensureDataDir() {\n    const dataDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data');\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.access(dataDir);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.mkdir(dataDir, {\n            recursive: true\n        });\n    }\n}\n// حفظ البيانات في ملف\nasync function saveSchedulesToFile() {\n    try {\n        await ensureDataDir();\n        const schedulesData = Object.fromEntries(weeklySchedules);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(SCHEDULES_FILE, JSON.stringify(schedulesData, null, 2));\n        console.log('💾 تم حفظ الجداول في الملف');\n    } catch (error) {\n        console.error('❌ خطأ في حفظ الجداول:', error);\n    }\n}\n// حفظ المواد المؤقتة في ملف\nasync function saveTempItemsToFile() {\n    try {\n        await ensureDataDir();\n        const tempData = Object.fromEntries(tempItems);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(TEMP_ITEMS_FILE, JSON.stringify(tempData, null, 2));\n        console.log('💾 تم حفظ المواد المؤقتة في الملف');\n    } catch (error) {\n        console.error('❌ خطأ في حفظ المواد المؤقتة:', error);\n    }\n}\n// تحميل البيانات من الملف\nasync function loadSchedulesFromFile() {\n    try {\n        const data = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(SCHEDULES_FILE, 'utf8');\n        const schedulesData = JSON.parse(data);\n        weeklySchedules = new Map(Object.entries(schedulesData));\n        console.log('📂 تم تحميل الجداول من الملف');\n    } catch (error) {\n        console.log('📂 لا يوجد ملف جداول محفوظ، بدء جديد');\n    }\n}\n// تحميل المواد المؤقتة من الملف\nasync function loadTempItemsFromFile() {\n    try {\n        const data = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(TEMP_ITEMS_FILE, 'utf8');\n        const tempData = JSON.parse(data);\n        tempItems = new Map(Object.entries(tempData));\n        console.log('📂 تم تحميل المواد المؤقتة من الملف');\n    } catch (error) {\n        console.log('📂 لا يوجد ملف مواد مؤقتة محفوظ، بدء جديد');\n    }\n}\n// تحميل البيانات عند بدء التشغيل\nlet dataLoaded = false;\nasync function initializeData() {\n    if (!dataLoaded) {\n        await loadSchedulesFromFile();\n        await loadTempItemsFromFile();\n        dataLoaded = true;\n    }\n}\n// Helper functions\nfunction getWeekStart(date) {\n    const d = new Date(date);\n    const day = d.getDay();\n    const diff = d.getDate() - day;\n    return new Date(d.setDate(diff));\n}\nfunction formatDate(date) {\n    return date.toISOString().split('T')[0];\n}\nfunction timeToMinutes(time) {\n    if (!time || typeof time !== 'string') {\n        console.warn(`⚠️ وقت غير صحيح: ${time}`);\n        return 0;\n    }\n    const [hours, minutes] = time.split(':').map(Number);\n    return hours * 60 + minutes;\n}\n// حساب المدة الإجمالية للمادة\nfunction calculateTotalDuration(segments) {\n    if (!segments || segments.length === 0) return '01:00:00';\n    let totalSeconds = 0;\n    segments.forEach((segment)=>{\n        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n        totalSeconds += hours * 3600 + minutes * 60 + seconds;\n    });\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor(totalSeconds % 3600 / 60);\n    const secs = totalSeconds % 60;\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n}\nfunction addMinutesToTime(timeStr, minutes) {\n    const totalMinutes = timeToMinutes(timeStr) + minutes;\n    const hours = Math.floor(totalMinutes / 60) % 24;\n    const mins = totalMinutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n}\nfunction calculateDuration(startTime, endTime) {\n    const startMinutes = timeToMinutes(startTime);\n    const endMinutes = timeToMinutes(endTime);\n    let duration = endMinutes - startMinutes;\n    if (duration < 0) duration += 24 * 60;\n    return duration;\n}\nfunction isPrimeTime(startTime, dayOfWeek) {\n    const startMinutes = timeToMinutes(startTime);\n    if ([\n        0,\n        1,\n        2,\n        3\n    ].includes(dayOfWeek)) {\n        // الأحد-الأربعاء: 18:00-00:00\n        return startMinutes >= timeToMinutes('18:00');\n    } else if ([\n        4,\n        5,\n        6\n    ].includes(dayOfWeek)) {\n        // الخميس-السبت: 18:00-02:00 (اليوم التالي)\n        return startMinutes >= timeToMinutes('18:00') || startMinutes < timeToMinutes('02:00');\n    }\n    return false;\n}\n// دالة توليد الإعادات التلقائية (متتالية)\nfunction generateReruns(scheduleItems, weekStart, tempItems = []) {\n    const reruns = [];\n    console.log(`🔄 توليد الإعادات للأسبوع: ${weekStart}`);\n    // دمج المواد العادية والمؤقتة\n    const allItems = [\n        ...scheduleItems,\n        ...tempItems\n    ];\n    const regularItems = allItems.filter((item)=>!item.isRerun && !item.isTemporary);\n    const temporaryItems = allItems.filter((item)=>!item.isRerun && item.isTemporary);\n    console.log(`📊 المواد: ${regularItems.length} عادية + ${temporaryItems.length} مؤقتة`);\n    // تجميع المواد حسب اليوم لتوليد إعادات متتالية\n    const itemsByDay = new Map();\n    // إضافة المواد العادية في البرايم تايم\n    regularItems.filter((item)=>isPrimeTime(item.startTime, item.dayOfWeek)).forEach((item)=>{\n        if (!itemsByDay.has(item.dayOfWeek)) {\n            itemsByDay.set(item.dayOfWeek, []);\n        }\n        itemsByDay.get(item.dayOfWeek).push(item);\n    });\n    // إضافة المواد المؤقتة في البرايم تايم (تجاهل المواد المؤقتة في القائمة الجانبية)\n    temporaryItems.filter((item)=>item.startTime && item.dayOfWeek !== undefined && item.startTime !== undefined && isPrimeTime(item.startTime, item.dayOfWeek)).forEach((item)=>{\n        if (!itemsByDay.has(item.dayOfWeek)) {\n            itemsByDay.set(item.dayOfWeek, []);\n        }\n        itemsByDay.get(item.dayOfWeek).push({\n            ...item,\n            isTemporary: true\n        });\n    });\n    // توليد إعادات متتالية لكل يوم\n    itemsByDay.forEach((dayItems, dayOfWeek)=>{\n        console.log(`📅 اليوم ${dayOfWeek}: ${dayItems.length} مادة في البرايم`);\n        // ترتيب المواد حسب وقت البداية في البرايم (مع مراعاة انتقال اليوم)\n        dayItems.sort((a, b)=>{\n            let timeA = timeToMinutes(a.startTime);\n            let timeB = timeToMinutes(b.startTime);\n            // للخميس-السبت: إذا كان الوقت أقل من 02:00، فهو في اليوم التالي\n            if ([\n                4,\n                5,\n                6\n            ].includes(dayOfWeek)) {\n                if (timeA < timeToMinutes('02:00')) timeA += 24 * 60; // إضافة 24 ساعة\n                if (timeB < timeToMinutes('02:00')) timeB += 24 * 60;\n            }\n            return timeA - timeB;\n        });\n        console.log(`📋 ترتيب المواد حسب وقت البث: ${dayItems.map((item, i)=>`${i + 1}:${item.mediaItem?.name || 'مؤقت'}(${item.startTime})`).join(', ')}`);\n        const dayReruns = generateSequentialReruns(dayItems, weekStart, dayOfWeek);\n        reruns.push(...dayReruns);\n    });\n    console.log(`✅ تم توليد ${reruns.length} إعادة إجمالية`);\n    return reruns;\n}\n// دالة توليد إعادات متتالية لمواد يوم واحد (ترتيب مستمر مضمون)\nfunction generateSequentialReruns(dayItems, weekStart, dayOfWeek) {\n    const reruns = [];\n    if (dayItems.length === 0) return reruns;\n    console.log(`🔄 توليد إعادات لـ ${dayItems.length} مادة من اليوم ${dayOfWeek}`);\n    console.log(`📋 ترتيب المواد: ${dayItems.map((item, i)=>`${i + 1}:${item.mediaItem?.name || 'مؤقت'}`).join(', ')}`);\n    // تحديد أوقات الإعادات حسب اليوم\n    let rerunStartTime;\n    let rerunDay;\n    if ([\n        0,\n        1,\n        2,\n        3\n    ].includes(dayOfWeek)) {\n        // الأحد-الأربعاء: الإعادات تبدأ من 00:00 في نفس اليوم\n        rerunStartTime = '00:00';\n        rerunDay = dayOfWeek;\n    } else if ([\n        4,\n        5,\n        6\n    ].includes(dayOfWeek)) {\n        // الخميس-السبت: الإعادات تبدأ من 02:00 في نفس اليوم (بعد انتهاء البرايم)\n        rerunStartTime = '02:00';\n        rerunDay = dayOfWeek;\n    } else {\n        return reruns;\n    }\n    // إنشاء قائمة مستمرة من المواد (تكرار لانهائي)\n    function getNextItem(index) {\n        return dayItems[index % dayItems.length];\n    }\n    let currentTime = rerunStartTime;\n    let itemSequenceIndex = 0; // فهرس التسلسل المستمر - يبدأ من 0 (المادة الأولى)\n    console.log(`🚀 بدء الجزء الأول من ${rerunStartTime} حتى 08:00 - بدءاً من المادة الأولى: ${dayItems[0].mediaItem?.name || 'مؤقت'}`);\n    // الجزء الأول: حتى 08:00\n    while(timeToMinutes(currentTime) < timeToMinutes('08:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('08:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${rerunDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: rerunDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: weekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                rerunPart: 1,\n                rerunCycle: Math.floor(itemSequenceIndex / dayItems.length) + 1,\n                mediaItem: item.mediaItem,\n                originalId: item.id\n            });\n            console.log(`✅ جزء 1: [${itemSequenceIndex}] ${item.mediaItem?.name || 'مؤقت'} (مادة ${itemSequenceIndex % dayItems.length + 1}/${dayItems.length}, دورة ${Math.floor(itemSequenceIndex / dayItems.length) + 1}) ${currentTime}-${rerunEndTime}`);\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            console.log(`⏰ توقف الجزء الأول عند ${currentTime} - المادة التالية: ${item.mediaItem?.name || 'مؤقت'}`);\n            break;\n        }\n    }\n    console.log(`📊 انتهى الجزء الأول: فهرس التسلسل ${itemSequenceIndex}, المادة التالية: ${getNextItem(itemSequenceIndex).mediaItem?.name || 'مؤقت'}`);\n    // حساب اليوم التالي مع مراعاة الأسبوع الجديد\n    let nextDay;\n    let nextWeekStart = weekStart;\n    if (dayOfWeek === 6) {\n        nextDay = 0; // الأحد في الأسبوع التالي\n        const nextWeekDate = new Date(weekStart);\n        nextWeekDate.setDate(nextWeekDate.getDate() + 7);\n        nextWeekStart = formatDate(nextWeekDate);\n        console.log(`🔄 إعادات السبت تذهب للأحد في الأسبوع التالي: ${nextWeekStart}`);\n    } else {\n        nextDay = (rerunDay + 1) % 7;\n    }\n    currentTime = '08:00';\n    console.log(`🚀 بدء الجزء الثاني: العمود ${nextDay} - استكمال من المادة ${getNextItem(itemSequenceIndex).mediaItem?.name || 'مؤقت'} (فهرس ${itemSequenceIndex})`);\n    // الجزء الثاني: استكمال من حيث توقف الجزء الأول\n    while(timeToMinutes(currentTime) < timeToMinutes('18:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('18:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${nextDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: nextDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: nextWeekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                rerunPart: 2,\n                rerunCycle: Math.floor(itemSequenceIndex / dayItems.length) + 1,\n                mediaItem: item.mediaItem,\n                originalId: item.id,\n                isNextWeekRerun: dayOfWeek === 6\n            });\n            console.log(`✅ جزء 2: [${itemSequenceIndex}] ${item.mediaItem?.name || 'مؤقت'} (مادة ${itemSequenceIndex % dayItems.length + 1}/${dayItems.length}, دورة ${Math.floor(itemSequenceIndex / dayItems.length) + 1}) ${currentTime}-${rerunEndTime}`);\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            console.log(`⏰ توقف الجزء الثاني عند ${currentTime} لتجاوز 18:00`);\n            break;\n        }\n    }\n    return reruns;\n}\n// قائمة الإعادات المحذوفة (لترك الحقول فارغة)\nconst deletedReruns = new Map(); // weekStart -> Set of rerun IDs\n// GET - جلب الجدول الأسبوعي\nasync function GET(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const { searchParams } = new URL(request.url);\n        const weekStartParam = searchParams.get('weekStart');\n        const tempItemsParam = searchParams.get('tempItems');\n        const weekStart = weekStartParam ? new Date(weekStartParam) : getWeekStart(new Date());\n        const weekStartStr = formatDate(weekStart);\n        console.log('📅 جلب الجدول الأسبوعي لتاريخ:', weekStartStr);\n        // الحصول على الجدول أو إنشاء جدول فارغ\n        let scheduleItems = weeklySchedules.get(weekStartStr) || [];\n        // الحصول على المواد المؤقتة المحفوظة\n        let weekTempItems = tempItems.get(weekStartStr) || [];\n        console.log(`📦 المواد المؤقتة المحفوظة: ${weekTempItems.length}`);\n        // توليد الإعادات التلقائية\n        console.log('🚀 بدء توليد الإعادات...');\n        const reruns = generateReruns(scheduleItems, weekStartStr, weekTempItems);\n        console.log(`📊 تم توليد ${reruns.length} إعادة`);\n        // فلترة الإعادات المحذوفة\n        const deletedSet = deletedReruns.get(weekStartStr) || new Set();\n        // فصل الإعادات حسب الأسبوع وفلترة المحذوفة\n        const currentWeekReruns = reruns.filter((rerun)=>{\n            if (rerun.weekStart !== weekStartStr) return false;\n            if (deletedSet.has(rerun.id)) return false;\n            // فلترة إضافية: التحقق من عدم وجود مادة أخرى في نفس المكان\n            const hasConflict = [\n                ...scheduleItems,\n                ...weekTempItems\n            ].some((item)=>!item.isRerun && item.dayOfWeek === rerun.dayOfWeek && item.startTime === rerun.startTime);\n            if (hasConflict) {\n                console.log(`🚫 تم تجاهل إعادة ${rerun.mediaItem.name} في ${rerun.startTime} بسبب وجود مادة أخرى`);\n                return false;\n            }\n            return true;\n        });\n        // إضافة إعادات السبت من الأسبوع السابق (تظهر في الأحد)\n        const prevWeekDate = new Date(weekStartStr);\n        prevWeekDate.setDate(prevWeekDate.getDate() - 7);\n        const prevWeekStr = formatDate(prevWeekDate);\n        // توليد إعادات الأسبوع السابق للحصول على إعادات السبت\n        const prevWeekScheduleItems = weeklySchedules.get(prevWeekStr) || [];\n        const prevWeekTempItems = tempItems.get(prevWeekStr) || [];\n        const prevWeekReruns = generateReruns(prevWeekScheduleItems, prevWeekStr, prevWeekTempItems);\n        // فلترة إعادات السبت التي تذهب للأسبوع التالي (الأحد)\n        const saturdayReruns = prevWeekReruns.filter((rerun)=>rerun.isNextWeekRerun && rerun.weekStart === weekStartStr && rerun.dayOfWeek === 0 && !deletedSet.has(rerun.id));\n        console.log(`📅 إعادات السبت من الأسبوع السابق: ${saturdayReruns.length}`);\n        // دمج جميع المواد: عادية + مؤقتة + إعادات\n        const allItems = [\n            ...scheduleItems,\n            ...weekTempItems,\n            ...currentWeekReruns,\n            ...saturdayReruns\n        ];\n        // فلترة المواد الكبيرة للعرض مع إضافة المدة المحسوبة\n        const mediaItems = (0,_shared_data__WEBPACK_IMPORTED_MODULE_3__.getAllMediaItems)();\n        const bigMediaTypes = [\n            'PROGRAM',\n            'SERIES',\n            'MOVIE'\n        ];\n        const availableMedia = mediaItems.filter((item)=>bigMediaTypes.includes(item.type)).map((item)=>({\n                ...item,\n                duration: calculateTotalDuration(item.segments || [])\n            }));\n        // إضافة المواد المؤقتة للاستجابة (فقط المواد في القائمة الجانبية، ليس في الجدول)\n        const currentTempItems = tempItems.get(weekStartStr) || [];\n        const sidebarTempItems = currentTempItems.filter((item)=>!item.dayOfWeek && !item.startTime && !item.endTime);\n        console.log(`📦 إرسال ${sidebarTempItems.length} مادة مؤقتة في الاستجابة (من أصل ${currentTempItems.length})`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                weekStart: weekStartStr,\n                scheduleItems: allItems,\n                availableMedia,\n                tempItems: sidebarTempItems\n            }\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب الجدول:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الجدول الأسبوعي'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مادة للجدول أو حفظ مادة مؤقتة\nasync function POST(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const body = await request.json();\n        const { mediaItemId, dayOfWeek, startTime, endTime, weekStart, episodeNumber, seasonNumber, partNumber, isTemporary, mediaItem: tempMediaItem } = body;\n        // التعامل مع المواد المؤقتة\n        if (isTemporary && tempMediaItem) {\n            // تنظيف وتصحيح البيانات\n            const cleanTempItem = {\n                ...tempMediaItem,\n                name: tempMediaItem.name || tempMediaItem.title || 'مادة مؤقتة',\n                id: tempMediaItem.id || `temp_${Date.now()}`,\n                type: tempMediaItem.type || 'PROGRAM',\n                duration: tempMediaItem.duration || '01:00:00'\n            };\n            console.log('💾 حفظ مادة مؤقتة في API:', cleanTempItem.name);\n            console.log('📋 بيانات المادة المؤقتة المنظفة:', JSON.stringify(cleanTempItem, null, 2));\n            // الحصول على المواد المؤقتة الحالية\n            let currentTempItems = tempItems.get(weekStart) || [];\n            // البحث عن المواد الموجودة في نفس الوقت والمكان لحذف إعاداتها\n            const tempItemsToRemove = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            const regularItemsToRemove = scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n            // حذف الإعادات المرتبطة بجميع المواد التي سيتم استبدالها\n            const allItemsToRemove = [\n                ...tempItemsToRemove,\n                ...regularItemsToRemove\n            ];\n            for (const itemToRemove of allItemsToRemove){\n                if (!deletedReruns.has(weekStart)) {\n                    deletedReruns.set(weekStart, new Set());\n                }\n                const deletedSet = deletedReruns.get(weekStart);\n                // البحث عن جميع الإعادات المرتبطة بهذه المادة\n                const allItems = [\n                    ...scheduleItems,\n                    ...currentTempItems\n                ];\n                const relatedReruns = allItems.filter((item)=>item.isRerun && (item.mediaItemId === itemToRemove.mediaItemId || item.id.includes(itemToRemove.id) || item.originalStartTime === itemToRemove.startTime));\n                relatedReruns.forEach((rerun)=>deletedSet.add(rerun.id));\n                console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة ${itemToRemove.mediaItem?.name || 'مؤقتة'}`);\n            }\n            // حذف أي مواد موجودة في نفس الوقت والمكان\n            currentTempItems = currentTempItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n            // حذف المواد العادية من نفس الوقت والمكان\n            scheduleItems = scheduleItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n            weeklySchedules.set(weekStart, scheduleItems);\n            console.log(`🔄 تم تنظيف المكان للمادة المؤقتة في اليوم ${dayOfWeek} الوقت ${startTime}`);\n            // إضافة المادة المؤقتة الجديدة مع معرف فريد\n            const tempItem = {\n                id: `temp_${Date.now()}`,\n                mediaItemId: cleanTempItem.id,\n                dayOfWeek,\n                startTime,\n                endTime,\n                weekStart,\n                isRerun: false,\n                isTemporary: true,\n                mediaItem: cleanTempItem,\n                createdAt: new Date().toISOString()\n            };\n            console.log('✅ تم إنشاء المادة المؤقتة:', {\n                id: tempItem.id,\n                name: tempItem.mediaItem.name,\n                type: tempItem.mediaItem.type,\n                duration: tempItem.mediaItem.duration\n            });\n            currentTempItems.push(tempItem);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملفات\n            await saveTempItemsToFile();\n            await saveSchedulesToFile();\n            console.log(`✅ تم حفظ المادة المؤقتة. إجمالي المواد المؤقتة: ${currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: tempItem\n            });\n        }\n        if (!mediaItemId || dayOfWeek === undefined || !startTime || !endTime || !weekStart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'جميع البيانات مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود المادة\n        const mediaItem = (0,_shared_data__WEBPACK_IMPORTED_MODULE_3__.getMediaItemById)(mediaItemId);\n        if (!mediaItem) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المادة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        // الحصول على الجدول الحالي\n        let scheduleItems = weeklySchedules.get(weekStart) || [];\n        // التحقق من التداخل\n        const startMinutes = timeToMinutes(startTime);\n        const endMinutes = timeToMinutes(endTime);\n        const conflict = scheduleItems.find((item)=>{\n            if (item.dayOfWeek !== dayOfWeek) return false;\n            const itemStart = timeToMinutes(item.startTime);\n            const itemEnd = timeToMinutes(item.endTime);\n            return startMinutes < itemEnd && endMinutes > itemStart;\n        });\n        if (conflict) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يوجد تداخل في الأوقات'\n            }, {\n                status: 400\n            });\n        }\n        // البحث عن المواد الموجودة في نفس الوقت والمكان لحذف إعاداتها\n        const itemsToRemove = scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        // حذف الإعادات المرتبطة بالمواد التي سيتم استبدالها\n        for (const itemToRemove of itemsToRemove){\n            // إضافة جميع الإعادات المرتبطة بهذه المادة للقائمة المحذوفة\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            const deletedSet = deletedReruns.get(weekStart);\n            // البحث عن جميع الإعادات المرتبطة بهذه المادة\n            const allItems = [\n                ...scheduleItems,\n                ...tempItems.get(weekStart) || []\n            ];\n            const relatedReruns = allItems.filter((item)=>item.isRerun && (item.mediaItemId === itemToRemove.mediaItemId || item.id.includes(itemToRemove.id) || item.originalStartTime === itemToRemove.startTime));\n            relatedReruns.forEach((rerun)=>deletedSet.add(rerun.id));\n            console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة ${itemToRemove.mediaItem?.name}`);\n        }\n        // حذف أي مواد موجودة في نفس الوقت والمكان (بما في ذلك المواد المؤقتة)\n        scheduleItems = scheduleItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        // حذف المواد المؤقتة من نفس الوقت والمكان\n        let currentTempItems = tempItems.get(weekStart) || [];\n        const tempItemsToRemove = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        console.log(`🔍 البحث عن مواد مؤقتة للحذف في اليوم ${dayOfWeek} الوقت ${startTime}: وجد ${tempItemsToRemove.length} مادة`);\n        // حذف إعادات المواد المؤقتة أيضاً\n        for (const tempItem of tempItemsToRemove){\n            console.log(`🗑️ حذف المادة المؤقتة: ${tempItem.mediaItem?.name} (ID: ${tempItem.id})`);\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            // البحث عن إعادات المادة المؤقتة في جميع الأسابيع\n            const allWeeks = Array.from(weeklySchedules.keys());\n            for (const week of allWeeks){\n                const weekItems = weeklySchedules.get(week) || [];\n                const relatedReruns = weekItems.filter((item)=>item.isRerun && item.isTemporary && (item.mediaItemId === tempItem.mediaItemId || item.id.includes(tempItem.id) || item.originalId === tempItem.id || item.mediaItem && item.mediaItem.name === tempItem.mediaItem?.name));\n                relatedReruns.forEach((rerun)=>{\n                    if (!deletedReruns.has(week)) {\n                        deletedReruns.set(week, new Set());\n                    }\n                    deletedReruns.get(week).add(rerun.id);\n                });\n                console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة المؤقتة ${tempItem.mediaItem?.name} في الأسبوع ${week}`);\n            }\n        }\n        // حذف المواد المؤقتة من tempItems نهائياً\n        const beforeCount = currentTempItems.length;\n        const tempItemsToDelete = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        // طباعة تفاصيل المواد التي سيتم حذفها\n        tempItemsToDelete.forEach((item)=>{\n            console.log(`🗑️ سيتم حذف المادة المؤقتة نهائياً: ${item.mediaItem?.name} (ID: ${item.id})`);\n        });\n        currentTempItems = currentTempItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        tempItems.set(weekStart, currentTempItems);\n        // حذف المواد المؤقتة من weeklySchedules أيضاً\n        scheduleItems = scheduleItems.filter((item)=>!(item.isTemporary && item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        weeklySchedules.set(weekStart, scheduleItems);\n        // حذف المواد المؤقتة من جميع الأسابيع في weeklySchedules\n        const allWeeks = Array.from(weeklySchedules.keys());\n        for (const week of allWeeks){\n            let weekItems = weeklySchedules.get(week) || [];\n            const beforeWeekCount = weekItems.length;\n            // حذف المواد المؤقتة والإعادات المرتبطة بها\n            weekItems = weekItems.filter((item)=>{\n                // حذف المواد المؤقتة التي تطابق المواد المحذوفة\n                if (item.isTemporary) {\n                    const shouldDelete = tempItemsToDelete.some((deletedItem)=>item.mediaItem?.name === deletedItem.mediaItem?.name && item.dayOfWeek === deletedItem.dayOfWeek && item.startTime === deletedItem.startTime || item.id === deletedItem.id || item.mediaItemId === deletedItem.mediaItemId);\n                    if (shouldDelete) {\n                        console.log(`🗑️ حذف مادة مؤقتة من weeklySchedules: ${item.mediaItem?.name} (${item.startTime})`);\n                        return false;\n                    }\n                }\n                // حذف الإعادات المرتبطة بالمواد المؤقتة المحذوفة\n                if (item.isRerun && item.isTemporary) {\n                    const shouldDelete = tempItemsToDelete.some((deletedItem)=>item.mediaItem?.name === deletedItem.mediaItem?.name || item.originalId === deletedItem.id || item.mediaItemId === deletedItem.mediaItemId);\n                    if (shouldDelete) {\n                        console.log(`🗑️ حذف إعادة مؤقتة من weeklySchedules: ${item.mediaItem?.name} (${item.startTime})`);\n                        return false;\n                    }\n                }\n                return true;\n            });\n            weeklySchedules.set(week, weekItems);\n            if (beforeWeekCount !== weekItems.length) {\n                console.log(`🗑️ تم حذف ${beforeWeekCount - weekItems.length} مادة/إعادة مؤقتة من الأسبوع ${week}`);\n            }\n        }\n        console.log(`🗑️ تم حذف ${beforeCount - currentTempItems.length} مادة مؤقتة من tempItems نهائياً`);\n        // حفظ المواد المؤقتة المحدثة\n        await saveTempItemsToFile();\n        await saveSchedulesToFile();\n        console.log(`💾 تم حفظ الملفات بعد حذف المواد المؤقتة. المواد المتبقية: ${currentTempItems.length}`);\n        console.log(`🔄 تم تنظيف المكان في اليوم ${dayOfWeek} الوقت ${startTime}`);\n        // إضافة المادة الجديدة مع تفاصيل الحلقة/الجزء\n        const newItem = {\n            id: `schedule_${Date.now()}`,\n            mediaItemId,\n            dayOfWeek,\n            startTime,\n            endTime,\n            weekStart,\n            isRerun: false,\n            // حفظ تفاصيل الحلقة/الجزء على مستوى العنصر\n            episodeNumber,\n            seasonNumber,\n            partNumber,\n            // إنشاء نسخة محدثة من المادة مع التفاصيل\n            mediaItem: {\n                ...mediaItem,\n                episodeNumber: episodeNumber || mediaItem.episodeNumber,\n                seasonNumber: seasonNumber || mediaItem.seasonNumber,\n                partNumber: partNumber || mediaItem.partNumber\n            },\n            createdAt: new Date().toISOString()\n        };\n        scheduleItems.push(newItem);\n        weeklySchedules.set(weekStart, scheduleItems);\n        // حفظ في الملف\n        await saveSchedulesToFile();\n        console.log('✅ تم إضافة المادة:', mediaItem.name);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newItem\n        });\n    } catch (error) {\n        console.error('❌ خطأ في إضافة المادة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة المادة'\n        }, {\n            status: 500\n        });\n    }\n}\n// دعم إضافي للمواد المؤقتة في القائمة الجانبية\nasync function PATCH(request) {\n    try {\n        await initializeData();\n        const body = await request.json();\n        const { action } = body;\n        // حفظ مادة مؤقتة في القائمة الجانبية\n        if (action === 'saveTempToSidebar') {\n            const { tempMedia, weekStart: weekStartParam } = body;\n            const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));\n            console.log(`💾 حفظ مادة مؤقتة في القائمة الجانبية: ${tempMedia.name}`);\n            // إضافة المادة المؤقتة إلى tempItems\n            let currentTempItems = tempItems.get(weekStart) || [];\n            currentTempItems.push(tempMedia);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملف\n            await saveTempItemsToFile();\n            console.log(`✅ تم حفظ المادة المؤقتة في القائمة الجانبية. إجمالي: ${currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حفظ المادة المؤقتة في القائمة الجانبية'\n            });\n        }\n        // حذف مادة مؤقتة من القائمة الجانبية\n        if (action === 'deleteTempFromSidebar') {\n            const { tempMediaId, weekStart: weekStartParam } = body;\n            const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));\n            console.log(`🗑️ حذف مادة مؤقتة من القائمة الجانبية: ${tempMediaId}`);\n            // حذف المادة المؤقتة من tempItems\n            let currentTempItems = tempItems.get(weekStart) || [];\n            const beforeCount = currentTempItems.length;\n            currentTempItems = currentTempItems.filter((item)=>item.id !== tempMediaId);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملف\n            await saveTempItemsToFile();\n            console.log(`✅ تم حذف المادة المؤقتة من القائمة الجانبية. المحذوف: ${beforeCount - currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف المادة المؤقتة من القائمة الجانبية'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'إجراء غير مدعوم'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('❌ خطأ في PATCH:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في العملية'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مادة\nasync function DELETE(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        const weekStart = searchParams.get('weekStart');\n        if (!id || !weekStart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المعرف والتاريخ مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق إذا كانت المادة مؤقتة\n        if (id.startsWith('temp_')) {\n            console.log('🗑️ حذف مادة مؤقتة:', id);\n            // حذف من المواد المؤقتة\n            let currentTempItems = tempItems.get(weekStart) || [];\n            const originalLength = currentTempItems.length;\n            currentTempItems = currentTempItems.filter((item)=>item.id !== id && item.originalId !== id);\n            tempItems.set(weekStart, currentTempItems);\n            // حذف من الجدول الأساسي أيضاً\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            scheduleItems = scheduleItems.filter((item)=>item.id !== id && item.mediaItemId !== id && !(item.mediaItemId && item.mediaItemId.startsWith('temp_') && item.mediaItemId === id));\n            weeklySchedules.set(weekStart, scheduleItems);\n            console.log(`🗑️ حذف المادة المؤقتة من tempItems: ${id}`);\n            console.log(`🗑️ حذف المادة المؤقتة من weeklySchedules: ${id}`);\n            // حفظ في الملفات\n            await saveTempItemsToFile();\n            await saveSchedulesToFile();\n            console.log(`✅ تم حذف المادة المؤقتة نهائياً. المواد المتبقية: ${currentTempItems.length} (كان ${originalLength})`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true\n            });\n        }\n        // التحقق إذا كانت المادة إعادة\n        if (id.startsWith('rerun_')) {\n            // إضافة الإعادة للقائمة المحذوفة\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            deletedReruns.get(weekStart).add(id);\n            console.log('🗑️ تم حذف الإعادة (ترك فارغ):', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف الإعادة بنجاح'\n            });\n        } else {\n            // حذف المادة الأصلية\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            const index = scheduleItems.findIndex((item)=>item.id === id);\n            if (index === -1) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'المادة غير موجودة'\n                }, {\n                    status: 404\n                });\n            }\n            scheduleItems.splice(index, 1);\n            weeklySchedules.set(weekStart, scheduleItems);\n            // حفظ في الملف\n            await saveSchedulesToFile();\n            console.log('🗑️ تم حذف المادة الأصلية:', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف المادة بنجاح'\n            });\n        }\n    } catch (error) {\n        console.error('❌ خطأ في حذف المادة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف المادة'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/weekly-schedule/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "constants":
/*!****************************!*\
  !*** external "constants" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("constants");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "rimraf":
/*!*************************!*\
  !*** external "rimraf" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("rimraf");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/exceljs","vendor-chunks/jszip","vendor-chunks/bluebird","vendor-chunks/unzipper","vendor-chunks/@fast-csv","vendor-chunks/pako","vendor-chunks/fstream","vendor-chunks/uuid","vendor-chunks/readable-stream","vendor-chunks/lazystream","vendor-chunks/archiver-utils","vendor-chunks/duplexer2","vendor-chunks/compress-commons","vendor-chunks/archiver","vendor-chunks/tar-stream","vendor-chunks/readdir-glob","vendor-chunks/graceful-fs","vendor-chunks/zip-stream","vendor-chunks/xmlchars","vendor-chunks/glob","vendor-chunks/dayjs","vendor-chunks/crc32-stream","vendor-chunks/inherits","vendor-chunks/fs.realpath","vendor-chunks/buffer-indexof-polyfill","vendor-chunks/bl","vendor-chunks/binary","vendor-chunks/async","vendor-chunks/wrappy","vendor-chunks/util-deprecate","vendor-chunks/traverse","vendor-chunks/tmp","vendor-chunks/string_decoder","vendor-chunks/saxes","vendor-chunks/safe-buffer","vendor-chunks/process-nextick-args","vendor-chunks/path-is-absolute","vendor-chunks/once","vendor-chunks/normalize-path","vendor-chunks/minimatch","vendor-chunks/lodash.uniq","vendor-chunks/lodash.union","vendor-chunks/lodash.isundefined","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnil","vendor-chunks/lodash.isfunction","vendor-chunks/lodash.isequal","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.groupby","vendor-chunks/lodash.flatten","vendor-chunks/lodash.escaperegexp","vendor-chunks/lodash.difference","vendor-chunks/lodash.defaults","vendor-chunks/listenercount","vendor-chunks/lie","vendor-chunks/inflight","vendor-chunks/immediate","vendor-chunks/fs-constants","vendor-chunks/fast-csv","vendor-chunks/end-of-stream","vendor-chunks/crc-32","vendor-chunks/core-util-is","vendor-chunks/concat-map","vendor-chunks/chainsaw","vendor-chunks/buffers","vendor-chunks/buffer-crc32","vendor-chunks/brace-expansion","vendor-chunks/big-integer","vendor-chunks/balanced-match"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport-schedule%2Froute&page=%2Fapi%2Fexport-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();