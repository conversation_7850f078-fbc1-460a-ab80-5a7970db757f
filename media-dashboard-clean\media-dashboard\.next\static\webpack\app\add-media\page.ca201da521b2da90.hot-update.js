"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const validateTimeFormat = (time)=>{\n        const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;\n        return timeRegex.test(time);\n    };\n    const formatTimeInput = (value)=>{\n        // إزالة أي أحرف غير رقمية أو نقطتين\n        const cleaned = value.replace(/[^\\d:]/g, '');\n        // تقسيم النص إلى أجزاء\n        const parts = cleaned.split(':');\n        // تنسيق كل جزء\n        const hours = parts[0] ? parts[0].padStart(2, '0').slice(0, 2) : '00';\n        const minutes = parts[1] ? parts[1].padStart(2, '0').slice(0, 2) : '00';\n        const seconds = parts[2] ? parts[2].padStart(2, '0').slice(0, 2) : '00';\n        return \"\".concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    };\n    const calculateDuration = (timeIn, timeOut)=>{\n        if (!timeIn || !timeOut || !validateTimeFormat(timeIn) || !validateTimeFormat(timeOut)) {\n            return '00:00:00';\n        }\n        const [inHours, inMinutes, inSeconds] = timeIn.split(':').map(Number);\n        const [outHours, outMinutes, outSeconds] = timeOut.split(':').map(Number);\n        const inTotalSeconds = inHours * 3600 + inMinutes * 60 + inSeconds;\n        const outTotalSeconds = outHours * 3600 + outMinutes * 60 + outSeconds;\n        const durationSeconds = outTotalSeconds - inTotalSeconds;\n        if (durationSeconds <= 0) return '00:00:00';\n        const hours = Math.floor(durationSeconds / 3600);\n        const minutes = Math.floor(durationSeconds % 3600 / 60);\n        const seconds = durationSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const updateSegmentCount = (count)=>{\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            const existingSegment = segments.find((s)=>s.id === i);\n            newSegments.push(existingSegment || {\n                id: i,\n                segmentCode: \"SEG\".concat(i.toString().padStart(3, '0')),\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حفظ المادة الإعلامية بنجاح!', 'success');\n                // إعادة تعيين النموذج\n                setFormData({\n                    name: '',\n                    type: 'PROGRAM',\n                    description: '',\n                    channel: 'DOCUMENTARY',\n                    source: '',\n                    status: 'WAITING',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: ''\n                });\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: 'SEG001',\n                        timeIn: '00:00:00',\n                        timeOut: '00:00:00',\n                        duration: '00:00:00'\n                    }\n                ]);\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            showToast('حدث خطأ أثناء حفظ البيانات', 'error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '800px',\n                    margin: '0 auto',\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    fontWeight: 'bold',\n                                    color: '#333',\n                                    margin: 0,\n                                    background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent'\n                                },\n                                children: \"➕ إضافة مادة إعلامية جديدة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/'),\n                                style: {\n                                    background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '10px',\n                                    padding: '10px 20px',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem'\n                                },\n                                children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #dee2e6'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#495057',\n                                            marginBottom: '20px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة *\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: inputStyle,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 263,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '80px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"المصدر\",\n                                                value: formData.source,\n                                                onChange: (e)=>handleInputChange('source', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.status,\n                                                onChange: (e)=>handleInputChange('status', e.target.value),\n                                                style: inputStyle,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"VALID\",\n                                                        children: \"صالح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_CENSORSHIP\",\n                                                        children: \"مرفوض رقابي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_TECHNICAL\",\n                                                        children: \"مرفوض هندسي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"WAITING\",\n                                                        children: \"في الانتظار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ البداية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ الانتهاء\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الجزء\",\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الموسم\",\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '60px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #90caf9'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#1565c0',\n                                            marginBottom: '10px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83C\\uDFAC السيجمانت\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#1565c0',\n                                            fontSize: '0.9rem',\n                                            marginBottom: '20px',\n                                            fontStyle: 'italic'\n                                        },\n                                        children: \"\\uD83D\\uDCA1 تنسيق الوقت: HH:MM:SS (مثال: 01:30:45 للساعة الواحدة و30 دقيقة و45 ثانية)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '10px',\n                                                    color: '#1565c0',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \"عدد السيجمانت (1-10):\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '10px',\n                                                    flexWrap: 'wrap'\n                                                },\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3,\n                                                    4,\n                                                    5,\n                                                    6,\n                                                    7,\n                                                    8,\n                                                    9,\n                                                    10\n                                                ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>updateSegmentCount(num),\n                                                        style: {\n                                                            background: segmentCount === num ? 'linear-gradient(45deg, #1976d2, #42a5f5)' : '#f8f9fa',\n                                                            color: segmentCount === num ? 'white' : '#495057',\n                                                            border: segmentCount === num ? 'none' : '2px solid #dee2e6',\n                                                            borderRadius: '8px',\n                                                            padding: '8px 16px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '0.9rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: num\n                                                    }, num, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '20px'\n                                        },\n                                        children: segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: 'rgba(255,255,255,0.8)',\n                                                    borderRadius: '10px',\n                                                    padding: '20px',\n                                                    border: '2px solid #90caf9'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        style: {\n                                                            color: '#1565c0',\n                                                            marginBottom: '15px',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: [\n                                                            \"السيجمانت \",\n                                                            segment.id\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'grid',\n                                                            gap: '15px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'grid',\n                                                                gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                                gap: '15px'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"Time In (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 432,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"00:00:00\",\n                                                                            value: segment.timeIn,\n                                                                            onChange: (e)=>{\n                                                                                let newValue = e.target.value;\n                                                                                // السماح فقط بالأرقام والنقطتين\n                                                                                newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                                // تحديد الطول الأقصى\n                                                                                if (newValue.length <= 8) {\n                                                                                    handleSegmentChange(segment.id, 'timeIn', newValue);\n                                                                                    // حساب المدة إذا كان التنسيق صحيح\n                                                                                    if (validateTimeFormat(newValue)) {\n                                                                                        const duration = calculateDuration(newValue, segment.timeOut);\n                                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                                    }\n                                                                                }\n                                                                            },\n                                                                            onBlur: (e)=>{\n                                                                                // تنسيق القيمة عند فقدان التركيز\n                                                                                const formatted = formatTimeInput(e.target.value);\n                                                                                handleSegmentChange(segment.id, 'timeIn', formatted);\n                                                                                const duration = calculateDuration(formatted, segment.timeOut);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            },\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                borderColor: validateTimeFormat(segment.timeIn) ? '#28a745' : '#e0e0e0'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 435,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"Time Out (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 471,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"00:00:00\",\n                                                                            value: segment.timeOut,\n                                                                            onChange: (e)=>{\n                                                                                let newValue = e.target.value;\n                                                                                // السماح فقط بالأرقام والنقطتين\n                                                                                newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                                // تحديد الطول الأقصى\n                                                                                if (newValue.length <= 8) {\n                                                                                    handleSegmentChange(segment.id, 'timeOut', newValue);\n                                                                                    // حساب المدة إذا كان التنسيق صحيح\n                                                                                    if (validateTimeFormat(newValue)) {\n                                                                                        const duration = calculateDuration(segment.timeIn, newValue);\n                                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                                    }\n                                                                                }\n                                                                            },\n                                                                            onBlur: (e)=>{\n                                                                                // تنسيق القيمة عند فقدان التركيز\n                                                                                const formatted = formatTimeInput(e.target.value);\n                                                                                handleSegmentChange(segment.id, 'timeOut', formatted);\n                                                                                const duration = calculateDuration(segment.timeIn, formatted);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            },\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                borderColor: validateTimeFormat(segment.timeOut) ? '#28a745' : '#e0e0e0'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"المدة (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: segment.duration,\n                                                                            readOnly: true,\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                background: '#f8f9fa',\n                                                                                color: '#6c757d'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"كود السيجمانت\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 526,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"SEG001\",\n                                                                            value: segment.segmentCode,\n                                                                            onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                            style: inputStyle\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, segment.id, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        style: {\n                                            background: isSubmitting ? 'linear-gradient(45deg, #6c757d, #adb5bd)' : 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)',\n                                            opacity: isSubmitting ? 0.7 : 1\n                                        },\n                                        children: isSubmitting ? '⏳ جاري الحفظ...' : '💾 حفظ البيانات'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setFormData({\n                                                name: '',\n                                                type: 'PROGRAM',\n                                                description: '',\n                                                channel: 'DOCUMENTARY',\n                                                source: '',\n                                                status: 'WAITING',\n                                                startDate: new Date().toISOString().split('T')[0],\n                                                endDate: '',\n                                                notes: '',\n                                                episodeNumber: '',\n                                                seasonNumber: '',\n                                                partNumber: ''\n                                            });\n                                            setSegmentCount(1);\n                                            setSegments([\n                                                {\n                                                    id: 1,\n                                                    segmentCode: 'SEG001',\n                                                    timeIn: '00:00:00',\n                                                    timeOut: '00:00:00',\n                                                    duration: '00:00:00'\n                                                }\n                                            ]);\n                                        },\n                                        style: {\n                                            background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(220,53,69,0.3)'\n                                        },\n                                        children: \"\\uD83D\\uDDD1️ مسح البيانات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 609,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"zuKwFwf3n4PMZi1YCRQjm69g+B8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});