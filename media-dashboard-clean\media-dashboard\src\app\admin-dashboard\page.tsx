'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useToast } from '@/components/Toast';

interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  lastLogin: string | null;
  roleInfo: {
    name: string;
    description: string;
    permissions: string[];
  };
}

export default function AdminDashboard() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { showToast, ToastContainer } = useToast();
  
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddUser, setShowAddUser] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    name: '',
    email: '',
    phone: '',
    role: 'VIEWER'
  });

  const roles = {
    'ADMIN': { name: 'مدير النظام', color: '#dc3545', icon: '👑' },
    'MEDIA_MANAGER': { name: 'مدير المحتوى', color: '#28a745', icon: '📝' },
    'SCHEDULER': { name: 'مجدول البرامج', color: '#007bff', icon: '📅' },
    'VIEWER': { name: 'مستخدم عرض', color: '#6c757d', icon: '👁️' }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users');
      const result = await response.json();
      
      if (result.success) {
        setUsers(result.users);
      } else {
        showToast('خطأ في جلب بيانات المستخدمين', 'error');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      showToast('خطأ في الاتصال بالخادم', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newUser.username || !newUser.password || !newUser.name) {
      showToast('يرجى ملء جميع الحقول المطلوبة', 'error');
      return;
    }

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUser)
      });

      const result = await response.json();
      
      if (result.success) {
        showToast('تم إنشاء المستخدم بنجاح!', 'success');
        setUsers([...users, result.user]);
        setShowAddUser(false);
        setNewUser({ username: '', password: '', name: '', email: '', phone: '', role: 'VIEWER' });
      } else {
        showToast('خطأ: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Error adding user:', error);
      showToast('خطأ في إنشاء المستخدم', 'error');
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;

    try {
      const response = await fetch(`/api/users?id=${userId}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      
      if (result.success) {
        showToast('تم حذف المستخدم بنجاح!', 'success');
        setUsers(users.filter(u => u.id !== userId));
      } else {
        showToast('خطأ: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      showToast('خطأ في حذف المستخدم', 'error');
    }
  };

  const inputStyle = {
    width: '100%',
    padding: '12px',
    border: '2px solid #e0e0e0',
    borderRadius: '8px',
    fontSize: '1rem',
    fontFamily: 'Cairo, Arial, sans-serif',
    direction: 'rtl' as const,
    outline: 'none'
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '40px',
          textAlign: 'center',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}>
          <h2>⏳ جاري تحميل البيانات...</h2>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard requiredRole="ADMIN">
      <DashboardLayout title="إدارة المستخدمين" subtitle="إضافة وتعديل المستخدمين" icon="👥">

        {/* إحصائيات سريعة */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          marginBottom: '20px'
        }}>
          {Object.entries(roles).map(([roleKey, roleInfo]) => {
            const count = users.filter(u => u.role === roleKey).length;
            return (
              <div key={roleKey} style={{
                background: '#4a5568',
                borderRadius: '15px',
                padding: '25px',
                border: '1px solid #6b7280',
                textAlign: 'center'
              }}>
                <div style={{
                  fontSize: '2.5rem',
                  marginBottom: '10px'
                }}>
                  {roleInfo.icon}
                </div>
                <h3 style={{
                  color: '#f3f4f6',
                  margin: '0 0 5px 0',
                  fontSize: '1.2rem'
                }}>
                  {roleInfo.name}
                </h3>
                <div style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  color: roleInfo.color
                }}>
                  {count}
                </div>
                <p style={{
                  color: '#d1d5db',
                  fontSize: '0.9rem',
                  margin: 0
                }}>
                  مستخدم
                </p>
              </div>
            );
          })}
        </div>

        {/* قسم إدارة المستخدمين */}
        <div style={{
          background: '#4a5568',
          borderRadius: '15px',
          padding: '30px',
          border: '1px solid #6b7280'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '25px'
          }}>
            <h2 style={{
              color: '#f3f4f6',
              fontSize: '1.5rem',
              margin: 0
            }}>
              👥 إدارة المستخدمين ({users.length})
            </h2>
            <button
              onClick={() => setShowAddUser(true)}
              style={{
                background: 'linear-gradient(45deg, #10b981, #059669)',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '12px 20px',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              ➕ إضافة مستخدم جديد
            </button>
          </div>

          {/* نموذج إضافة مستخدم */}
          {showAddUser && (
            <div style={{
              background: '#1f2937',
              borderRadius: '15px',
              padding: '25px',
              marginBottom: '25px',
              border: '1px solid #6b7280'
            }}>
              <h3 style={{ color: '#f3f4f6', marginBottom: '20px' }}>➕ إضافة مستخدم جديد</h3>
              <form onSubmit={handleAddUser}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '15px',
                  marginBottom: '20px'
                }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      اسم المستخدم *
                    </label>
                    <input
                      type="text"
                      placeholder="اسم المستخدم"
                      value={newUser.username}
                      onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      كلمة المرور *
                    </label>
                    <input
                      type="password"
                      placeholder="كلمة المرور"
                      value={newUser.password}
                      onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      placeholder="الاسم الكامل"
                      value={newUser.name}
                      onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      البريد الإلكتروني
                    </label>
                    <input
                      type="email"
                      placeholder="البريد الإلكتروني"
                      value={newUser.email}
                      onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      رقم الهاتف
                    </label>
                    <input
                      type="tel"
                      placeholder="رقم الهاتف"
                      value={newUser.phone}
                      onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      الدور *
                    </label>
                    <select
                      value={newUser.role}
                      onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    >
                      {Object.entries(roles).map(([key, role]) => (
                        <option key={key} value={key}>{role.name}</option>
                      ))}
                    </select>
                  </div>
                </div>
                <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
                  <button
                    type="submit"
                    style={{
                      background: 'linear-gradient(45deg, #10b981, #059669)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '12px 25px',
                      cursor: 'pointer',
                      fontSize: '1rem',
                      fontWeight: 'bold'
                    }}
                  >
                    ✅ إنشاء المستخدم
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddUser(false);
                      setNewUser({ username: '', password: '', name: '', email: '', phone: '', role: 'VIEWER' });
                    }}
                    style={{
                      background: '#6c757d',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '12px 25px',
                      cursor: 'pointer',
                      fontSize: '1rem',
                      fontWeight: 'bold'
                    }}
                  >
                    ❌ إلغاء
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* جدول المستخدمين */}
          <div style={{
            overflowX: 'auto',
            borderRadius: '10px',
            border: '1px solid #6b7280'
          }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              fontSize: '0.9rem'
            }}>
              <thead>
                <tr style={{ background: '#1f2937' }}>
                  <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #6b7280', color: '#f3f4f6' }}>المستخدم</th>
                  <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #6b7280', color: '#f3f4f6' }}>الدور</th>
                  <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #6b7280', color: '#f3f4f6' }}>الحالة</th>
                  <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #6b7280', color: '#f3f4f6' }}>تاريخ الإنشاء</th>
                  <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #6b7280', color: '#f3f4f6' }}>آخر دخول</th>
                  <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #6b7280', color: '#f3f4f6' }}>الإجراءات</th>
                </tr>
              </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id} style={{
                      borderBottom: '1px solid #6b7280',
                      transition: 'background-color 0.2s',
                      background: '#2d3748'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#374151'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#2d3748'}
                    >
                      <td style={{ padding: '15px' }}>
                        <div>
                          <div style={{ fontWeight: 'bold', color: '#f3f4f6', marginBottom: '5px' }}>
                            {user.name}
                          </div>
                          <div style={{ color: '#d1d5db', fontSize: '0.85rem' }}>
                            @{user.username}
                          </div>
                          {user.email && (
                            <div style={{ color: '#a0aec0', fontSize: '0.8rem' }}>
                              📧 {user.email}
                            </div>
                          )}
                          {user.phone && (
                            <div style={{ color: '#a0aec0', fontSize: '0.8rem' }}>
                              📱 {user.phone}
                            </div>
                          )}
                        </div>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <span style={{
                          background: roles[user.role as keyof typeof roles]?.color + '20',
                          color: roles[user.role as keyof typeof roles]?.color,
                          padding: '5px 12px',
                          borderRadius: '20px',
                          fontSize: '0.85rem',
                          fontWeight: 'bold',
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: '5px'
                        }}>
                          {roles[user.role as keyof typeof roles]?.icon}
                          {roles[user.role as keyof typeof roles]?.name}
                        </span>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <span style={{
                          background: user.isActive ? '#28a74520' : '#dc354520',
                          color: user.isActive ? '#28a745' : '#dc3545',
                          padding: '5px 12px',
                          borderRadius: '20px',
                          fontSize: '0.85rem',
                          fontWeight: 'bold'
                        }}>
                          {user.isActive ? '✅ نشط' : '❌ معطل'}
                        </span>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center', color: '#d1d5db' }}>
                        {new Date(user.createdAt).toLocaleDateString('ar-EG')}
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center', color: '#d1d5db' }}>
                        {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-EG') : 'لم يدخل بعد'}
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <div style={{ display: 'flex', gap: '5px', justifyContent: 'center' }}>
                          <button
                            onClick={() => setEditingUser(user)}
                            style={{
                              background: '#007bff',
                              color: 'white',
                              border: 'none',
                              borderRadius: '5px',
                              padding: '5px 10px',
                              cursor: 'pointer',
                              fontSize: '0.8rem'
                            }}
                            title="تعديل"
                          >
                            ✏️
                          </button>
                          {user.id !== '1' && (
                            <button
                              onClick={() => handleDeleteUser(user.id)}
                              style={{
                                background: '#dc3545',
                                color: 'white',
                                border: 'none',
                                borderRadius: '5px',
                                padding: '5px 10px',
                                cursor: 'pointer',
                                fontSize: '0.8rem'
                              }}
                              title="حذف"
                            >
                              🗑️
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {users.length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '40px',
                color: '#6c757d'
              }}>
                <div style={{ fontSize: '3rem', marginBottom: '15px' }}>👥</div>
                <h3>لا توجد مستخدمين</h3>
                <p>ابدأ بإضافة مستخدم جديد</p>
              </div>
            )}
          </div>
        </div>
        <ToastContainer />

        {/* نافذة تعديل المستخدم */}
        {editingUser && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <div style={{
              background: 'white',
              borderRadius: '15px',
              padding: '30px',
              width: '400px',
              maxWidth: '90vw'
            }}>
              <h3 style={{ marginBottom: '20px', color: '#333' }}>تعديل المستخدم</h3>

              <div style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#333' }}>اسم المستخدم:</label>
                <input
                  type="text"
                  value={editingUser.username}
                  onChange={(e) => setEditingUser({...editingUser, username: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '5px',
                    direction: 'rtl'
                  }}
                />
              </div>

              <div style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#333' }}>الاسم الكامل:</label>
                <input
                  type="text"
                  value={editingUser.name}
                  onChange={(e) => setEditingUser({...editingUser, name: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '5px',
                    direction: 'rtl'
                  }}
                />
              </div>

              <div style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#333' }}>البريد الإلكتروني:</label>
                <input
                  type="email"
                  value={editingUser.email || ''}
                  onChange={(e) => setEditingUser({...editingUser, email: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '5px',
                    direction: 'rtl'
                  }}
                />
              </div>

              <div style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#333' }}>رقم الهاتف:</label>
                <input
                  type="tel"
                  value={editingUser.phone || ''}
                  onChange={(e) => setEditingUser({...editingUser, phone: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '5px',
                    direction: 'rtl'
                  }}
                />
              </div>

              <div style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#333' }}>الدور:</label>
                <select
                  value={editingUser.role}
                  onChange={(e) => setEditingUser({...editingUser, role: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '5px',
                    direction: 'rtl'
                  }}
                >
                  <option value="ADMIN">مدير</option>
                  <option value="MEDIA_MANAGER">مدير المواد</option>
                  <option value="SCHEDULER">مجدول</option>
                  <option value="VIEWER">مشاهد</option>
                </select>
              </div>

              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  onClick={() => setEditingUser(null)}
                  style={{
                    background: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    padding: '10px 20px',
                    cursor: 'pointer'
                  }}
                >
                  إلغاء
                </button>
                <button
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/users', {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(editingUser)
                      });

                      if (response.ok) {
                        showToast('تم تحديث المستخدم بنجاح', 'success');
                        setEditingUser(null);
                        fetchUsers();
                      } else {
                        showToast('خطأ في تحديث المستخدم', 'error');
                      }
                    } catch (error) {
                      showToast('خطأ في الاتصال', 'error');
                    }
                  }}
                  style={{
                    background: '#28a745',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    padding: '10px 20px',
                    cursor: 'pointer'
                  }}
                >
                  حفظ
                </button>
              </div>
            </div>
          </div>
        )}
      </DashboardLayout>
    </AuthGuard>
  );
}
