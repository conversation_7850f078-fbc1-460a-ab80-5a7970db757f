'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import { useToast } from '@/components/Toast';

interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  lastLogin: string | null;
  roleInfo: {
    name: string;
    description: string;
    permissions: string[];
  };
}

export default function AdminDashboard() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { showToast, ToastContainer } = useToast();
  
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddUser, setShowAddUser] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    name: '',
    email: '',
    role: 'VIEWER'
  });

  const roles = {
    'ADMIN': { name: 'مدير النظام', color: '#dc3545', icon: '👑' },
    'MEDIA_MANAGER': { name: 'مدير المحتوى', color: '#28a745', icon: '📝' },
    'SCHEDULER': { name: 'مجدول البرامج', color: '#007bff', icon: '📅' },
    'VIEWER': { name: 'مستخدم عرض', color: '#6c757d', icon: '👁️' }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users');
      const result = await response.json();
      
      if (result.success) {
        setUsers(result.users);
      } else {
        showToast('خطأ في جلب بيانات المستخدمين', 'error');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      showToast('خطأ في الاتصال بالخادم', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newUser.username || !newUser.password || !newUser.name) {
      showToast('يرجى ملء جميع الحقول المطلوبة', 'error');
      return;
    }

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUser)
      });

      const result = await response.json();
      
      if (result.success) {
        showToast('تم إنشاء المستخدم بنجاح!', 'success');
        setUsers([...users, result.user]);
        setShowAddUser(false);
        setNewUser({ username: '', password: '', name: '', email: '', role: 'VIEWER' });
      } else {
        showToast('خطأ: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Error adding user:', error);
      showToast('خطأ في إنشاء المستخدم', 'error');
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;

    try {
      const response = await fetch(`/api/users?id=${userId}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      
      if (result.success) {
        showToast('تم حذف المستخدم بنجاح!', 'success');
        setUsers(users.filter(u => u.id !== userId));
      } else {
        showToast('خطأ: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      showToast('خطأ في حذف المستخدم', 'error');
    }
  };

  const inputStyle = {
    width: '100%',
    padding: '12px',
    border: '2px solid #e0e0e0',
    borderRadius: '8px',
    fontSize: '1rem',
    fontFamily: 'Cairo, Arial, sans-serif',
    direction: 'rtl' as const,
    outline: 'none'
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '40px',
          textAlign: 'center',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}>
          <h2>⏳ جاري تحميل البيانات...</h2>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard requiredRole="ADMIN">
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '20px',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {/* Header */}
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            marginBottom: '20px',
            boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <h1 style={{
                fontSize: '2rem',
                fontWeight: 'bold',
                color: '#333',
                margin: '0 0 10px 0',
                background: 'linear-gradient(45deg, #667eea, #764ba2)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                👑 لوحة تحكم المدير
              </h1>
              <p style={{ color: '#6c757d', margin: 0 }}>
                مرحباً {user?.name} - إدارة المستخدمين والصلاحيات
              </p>
            </div>
            <div style={{ display: 'flex', gap: '10px' }}>
              <button
                onClick={() => router.push('/')}
                style={{
                  background: 'linear-gradient(45deg, #28a745, #20c997)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '10px 20px',
                  cursor: 'pointer',
                  fontSize: '0.9rem'
                }}
              >
                🏠 الرئيسية
              </button>
              <button
                onClick={logout}
                style={{
                  background: 'linear-gradient(45deg, #dc3545, #c82333)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '10px 20px',
                  cursor: 'pointer',
                  fontSize: '0.9rem'
                }}
              >
                🚪 تسجيل الخروج
              </button>
            </div>
          </div>

          {/* إحصائيات سريعة */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '20px',
            marginBottom: '20px'
          }}>
            {Object.entries(roles).map(([roleKey, roleInfo]) => {
              const count = users.filter(u => u.role === roleKey).length;
              return (
                <div key={roleKey} style={{
                  background: 'white',
                  borderRadius: '15px',
                  padding: '25px',
                  boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
                  textAlign: 'center',
                  border: `3px solid ${roleInfo.color}20`
                }}>
                  <div style={{
                    fontSize: '2.5rem',
                    marginBottom: '10px'
                  }}>
                    {roleInfo.icon}
                  </div>
                  <h3 style={{
                    color: roleInfo.color,
                    margin: '0 0 5px 0',
                    fontSize: '1.2rem'
                  }}>
                    {roleInfo.name}
                  </h3>
                  <div style={{
                    fontSize: '2rem',
                    fontWeight: 'bold',
                    color: '#333'
                  }}>
                    {count}
                  </div>
                  <p style={{
                    color: '#6c757d',
                    fontSize: '0.9rem',
                    margin: 0
                  }}>
                    مستخدم
                  </p>
                </div>
              );
            })}
          </div>

          {/* قسم إدارة المستخدمين */}
          <div style={{
            background: 'white',
            borderRadius: '20px',
            padding: '30px',
            boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '25px'
            }}>
              <h2 style={{
                color: '#333',
                fontSize: '1.5rem',
                margin: 0
              }}>
                👥 إدارة المستخدمين ({users.length})
              </h2>
              <button
                onClick={() => setShowAddUser(true)}
                style={{
                  background: 'linear-gradient(45deg, #28a745, #20c997)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '12px 20px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold'
                }}
              >
                ➕ إضافة مستخدم جديد
              </button>
            </div>

            {/* نموذج إضافة مستخدم */}
            {showAddUser && (
              <div style={{
                background: '#f8f9fa',
                borderRadius: '15px',
                padding: '25px',
                marginBottom: '25px',
                border: '2px solid #e9ecef'
              }}>
                <h3 style={{ color: '#333', marginBottom: '20px' }}>➕ إضافة مستخدم جديد</h3>
                <form onSubmit={handleAddUser}>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                    gap: '15px',
                    marginBottom: '20px'
                  }}>
                    <input
                      type="text"
                      placeholder="اسم المستخدم *"
                      value={newUser.username}
                      onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                      style={inputStyle}
                      required
                    />
                    <input
                      type="password"
                      placeholder="كلمة المرور *"
                      value={newUser.password}
                      onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                      style={inputStyle}
                      required
                    />
                    <input
                      type="text"
                      placeholder="الاسم الكامل *"
                      value={newUser.name}
                      onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                      style={inputStyle}
                      required
                    />
                    <input
                      type="email"
                      placeholder="البريد الإلكتروني"
                      value={newUser.email}
                      onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                      style={inputStyle}
                    />
                    <select
                      value={newUser.role}
                      onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                      style={inputStyle}
                    >
                      {Object.entries(roles).map(([key, role]) => (
                        <option key={key} value={key}>{role.name}</option>
                      ))}
                    </select>
                  </div>
                  <div style={{ display: 'flex', gap: '10px' }}>
                    <button
                      type="submit"
                      style={{
                        background: 'linear-gradient(45deg, #28a745, #20c997)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '8px',
                        padding: '10px 20px',
                        cursor: 'pointer'
                      }}
                    >
                      ✅ إنشاء المستخدم
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowAddUser(false)}
                      style={{
                        background: '#6c757d',
                        color: 'white',
                        border: 'none',
                        borderRadius: '8px',
                        padding: '10px 20px',
                        cursor: 'pointer'
                      }}
                    >
                      ❌ إلغاء
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* جدول المستخدمين */}
            <div style={{
              overflowX: 'auto',
              borderRadius: '10px',
              border: '1px solid #e9ecef'
            }}>
              <table style={{
                width: '100%',
                borderCollapse: 'collapse',
                fontSize: '0.9rem'
              }}>
                <thead>
                  <tr style={{ background: '#f8f9fa' }}>
                    <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #dee2e6' }}>المستخدم</th>
                    <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>الدور</th>
                    <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>الحالة</th>
                    <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>تاريخ الإنشاء</th>
                    <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>آخر دخول</th>
                    <th style={{ padding: '15px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id} style={{
                      borderBottom: '1px solid #e9ecef',
                      transition: 'background-color 0.2s'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f8f9fa'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <td style={{ padding: '15px' }}>
                        <div>
                          <div style={{ fontWeight: 'bold', color: '#333', marginBottom: '5px' }}>
                            {user.name}
                          </div>
                          <div style={{ color: '#6c757d', fontSize: '0.85rem' }}>
                            @{user.username}
                          </div>
                          {user.email && (
                            <div style={{ color: '#6c757d', fontSize: '0.8rem' }}>
                              📧 {user.email}
                            </div>
                          )}
                        </div>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <span style={{
                          background: roles[user.role as keyof typeof roles]?.color + '20',
                          color: roles[user.role as keyof typeof roles]?.color,
                          padding: '5px 12px',
                          borderRadius: '20px',
                          fontSize: '0.85rem',
                          fontWeight: 'bold',
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: '5px'
                        }}>
                          {roles[user.role as keyof typeof roles]?.icon}
                          {roles[user.role as keyof typeof roles]?.name}
                        </span>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <span style={{
                          background: user.isActive ? '#28a74520' : '#dc354520',
                          color: user.isActive ? '#28a745' : '#dc3545',
                          padding: '5px 12px',
                          borderRadius: '20px',
                          fontSize: '0.85rem',
                          fontWeight: 'bold'
                        }}>
                          {user.isActive ? '✅ نشط' : '❌ معطل'}
                        </span>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center', color: '#6c757d' }}>
                        {new Date(user.createdAt).toLocaleDateString('ar-EG')}
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center', color: '#6c757d' }}>
                        {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-EG') : 'لم يدخل بعد'}
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <div style={{ display: 'flex', gap: '5px', justifyContent: 'center' }}>
                          <button
                            onClick={() => setEditingUser(user)}
                            style={{
                              background: '#007bff',
                              color: 'white',
                              border: 'none',
                              borderRadius: '5px',
                              padding: '5px 10px',
                              cursor: 'pointer',
                              fontSize: '0.8rem'
                            }}
                            title="تعديل"
                          >
                            ✏️
                          </button>
                          {user.id !== '1' && (
                            <button
                              onClick={() => handleDeleteUser(user.id)}
                              style={{
                                background: '#dc3545',
                                color: 'white',
                                border: 'none',
                                borderRadius: '5px',
                                padding: '5px 10px',
                                cursor: 'pointer',
                                fontSize: '0.8rem'
                              }}
                              title="حذف"
                            >
                              🗑️
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {users.length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '40px',
                color: '#6c757d'
              }}>
                <div style={{ fontSize: '3rem', marginBottom: '15px' }}>👥</div>
                <h3>لا توجد مستخدمين</h3>
                <p>ابدأ بإضافة مستخدم جديد</p>
              </div>
            )}
          </div>
        </div>
        <ToastContainer />
      </div>
    </AuthGuard>
  );
}
