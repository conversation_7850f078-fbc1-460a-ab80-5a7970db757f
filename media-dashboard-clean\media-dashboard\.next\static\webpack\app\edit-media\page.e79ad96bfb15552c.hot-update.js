"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit-media/page",{

/***/ "(app-pages-browser)/./src/app/edit-media/page.tsx":
/*!*************************************!*\
  !*** ./src/app/edit-media/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction EditMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const mediaId = searchParams.get('id');\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        description: '',\n        channel: '',\n        source: '',\n        status: '',\n        startDate: '',\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: ''\n    });\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '',\n            duration: '00:00:00'\n        }\n    ]);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // جلب بيانات المادة للتعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditMediaPage.useEffect\": ()=>{\n            if (mediaId) {\n                fetchMediaData();\n            } else {\n                router.push('/media-list');\n            }\n        }\n    }[\"EditMediaPage.useEffect\"], [\n        mediaId\n    ]);\n    const fetchMediaData = async ()=>{\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(mediaId));\n            const result = await response.json();\n            if (result.success && result.data) {\n                var _media_episodeNumber, _media_seasonNumber, _media_partNumber;\n                const media = result.data;\n                setFormData({\n                    name: media.name || '',\n                    type: media.type || '',\n                    description: media.description || '',\n                    channel: media.channel || '',\n                    source: media.source || '',\n                    status: media.status || '',\n                    startDate: media.startDate ? media.startDate.split('T')[0] : '',\n                    endDate: media.endDate ? media.endDate.split('T')[0] : '',\n                    notes: media.notes || '',\n                    episodeNumber: ((_media_episodeNumber = media.episodeNumber) === null || _media_episodeNumber === void 0 ? void 0 : _media_episodeNumber.toString()) || '',\n                    seasonNumber: ((_media_seasonNumber = media.seasonNumber) === null || _media_seasonNumber === void 0 ? void 0 : _media_seasonNumber.toString()) || '',\n                    partNumber: ((_media_partNumber = media.partNumber) === null || _media_partNumber === void 0 ? void 0 : _media_partNumber.toString()) || '',\n                    hardDiskNumber: media.hardDiskNumber || ''\n                });\n                if (media.segments && media.segments.length > 0) {\n                    setSegments(media.segments.map((seg, index)=>({\n                            id: index + 1,\n                            segmentCode: seg.segmentCode || \"SEG\".concat((index + 1).toString().padStart(3, '0')),\n                            timeIn: seg.timeIn || '00:00:00',\n                            timeOut: seg.timeOut || '',\n                            duration: seg.duration || '00:00:00'\n                        })));\n                }\n            } else {\n                showToast('خطأ في جلب بيانات المادة', 'error');\n                router.push('/media-list');\n            }\n        } catch (error) {\n            console.error('Error fetching media:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n            router.push('/media-list');\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n            requiredPermissions: [\n                'MEDIA_UPDATE'\n            ],\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: \"تحديث مادة إعلامية\",\n                subtitle: \"جاري تحميل البيانات...\",\n                icon: \"✏️\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        padding: '50px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                fontSize: '2rem',\n                                marginBottom: '20px'\n                            },\n                            children: \"⏳\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                color: '#a0aec0'\n                            },\n                            children: \"جاري تحميل بيانات المادة...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(mediaId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...formData,\n                    segments: segments.map((seg)=>({\n                            segmentCode: seg.segmentCode,\n                            timeIn: seg.timeIn,\n                            timeOut: seg.timeOut,\n                            duration: seg.duration\n                        }))\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم تحديث المادة بنجاح', 'success');\n                setTimeout(()=>{\n                    router.push('/media-list');\n                }, 1500);\n            } else {\n                showToast('خطأ في تحديث المادة: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error updating media:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const addSegment = ()=>{\n        const newSegment = {\n            id: segments.length + 1,\n            segmentCode: \"SEG\".concat((segments.length + 1).toString().padStart(3, '0')),\n            timeIn: '00:00:00',\n            timeOut: '',\n            duration: '00:00:00'\n        };\n        setSegments([\n            ...segments,\n            newSegment\n        ]);\n    };\n    const removeSegment = (id)=>{\n        if (segments.length > 1) {\n            setSegments(segments.filter((seg)=>seg.id !== id));\n        }\n    };\n    const updateSegment = (id, field, value)=>{\n        setSegments(segments.map((seg)=>seg.id === id ? {\n                ...seg,\n                [field]: value\n            } : seg));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: [\n            'MEDIA_UPDATE'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            title: \"تحديث مادة إعلامية\",\n            subtitle: \"تعديل بيانات المادة الإعلامية\",\n            icon: \"✏️\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    style: {\n                        maxWidth: '800px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#4a5568',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                marginBottom: '25px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        marginBottom: '20px',\n                                        fontSize: '1.3rem'\n                                    },\n                                    children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n                                        gap: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    style: {\n                                                        display: 'block',\n                                                        marginBottom: '8px',\n                                                        color: '#f3f4f6',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"اسم المادة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            name: e.target.value\n                                                        }),\n                                                    required: true,\n                                                    style: {\n                                                        width: '100%',\n                                                        padding: '12px',\n                                                        border: '2px solid #6b7280',\n                                                        borderRadius: '8px',\n                                                        fontSize: '1rem',\n                                                        direction: 'rtl',\n                                                        color: '#333',\n                                                        background: 'white'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    style: {\n                                                        display: 'block',\n                                                        marginBottom: '8px',\n                                                        color: '#f3f4f6',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"نوع المادة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.type,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            type: e.target.value\n                                                        }),\n                                                    required: true,\n                                                    style: {\n                                                        width: '100%',\n                                                        padding: '12px',\n                                                        border: '2px solid #6b7280',\n                                                        borderRadius: '8px',\n                                                        fontSize: '1rem',\n                                                        direction: 'rtl',\n                                                        color: '#333',\n                                                        background: 'white'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"اختر نوع المادة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"PROGRAM\",\n                                                            children: \"برنامج\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"SERIES\",\n                                                            children: \"مسلسل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"MOVIE\",\n                                                            children: \"فيلم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"SONG\",\n                                                            children: \"أغنية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"STING\",\n                                                            children: \"Sting\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"FILL_IN\",\n                                                            children: \"Fill IN\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"FILLER\",\n                                                            children: \"Filler\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"PROMO\",\n                                                            children: \"Promo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#4a5568',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                marginBottom: '25px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: '#f3f4f6',\n                                                fontSize: '1.3rem',\n                                                margin: 0\n                                            },\n                                            children: \"\\uD83C\\uDFAC السيجمانت\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: addSegment,\n                                            style: {\n                                                background: 'linear-gradient(45deg, #10b981, #059669)',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                padding: '8px 16px',\n                                                cursor: 'pointer',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"➕ إضافة سيجمنت\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                segments.map((segment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            padding: '20px',\n                                            marginBottom: '15px',\n                                            border: '1px solid #6b7280'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    justifyContent: 'space-between',\n                                                    alignItems: 'center',\n                                                    marginBottom: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        style: {\n                                                            color: '#f3f4f6',\n                                                            margin: 0\n                                                        },\n                                                        children: [\n                                                            \"سيجمنت #\",\n                                                            segment.id\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    segments.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>removeSegment(segment.id),\n                                                        style: {\n                                                            background: '#ef4444',\n                                                            color: 'white',\n                                                            border: 'none',\n                                                            borderRadius: '6px',\n                                                            padding: '6px 12px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '0.8rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDDD1️ حذف\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#d1d5db',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"كود السيجمنت\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: segment.segmentCode,\n                                                                onChange: (e)=>updateSegment(segment.id, 'segmentCode', e.target.value),\n                                                                style: {\n                                                                    width: '100%',\n                                                                    padding: '10px',\n                                                                    border: '1px solid #6b7280',\n                                                                    borderRadius: '6px',\n                                                                    fontSize: '0.9rem',\n                                                                    direction: 'rtl',\n                                                                    color: '#333',\n                                                                    background: 'white'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#d1d5db',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"وقت البداية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"00:00:00\",\n                                                                value: segment.timeIn,\n                                                                onChange: (e)=>updateSegment(segment.id, 'timeIn', e.target.value),\n                                                                style: {\n                                                                    width: '100%',\n                                                                    padding: '10px',\n                                                                    border: '1px solid #6b7280',\n                                                                    borderRadius: '6px',\n                                                                    fontSize: '0.9rem',\n                                                                    direction: 'ltr',\n                                                                    color: '#333',\n                                                                    background: 'white'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#d1d5db',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"وقت النهاية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"00:00:00\",\n                                                                value: segment.timeOut,\n                                                                onChange: (e)=>updateSegment(segment.id, 'timeOut', e.target.value),\n                                                                style: {\n                                                                    width: '100%',\n                                                                    padding: '10px',\n                                                                    border: '1px solid #6b7280',\n                                                                    borderRadius: '6px',\n                                                                    fontSize: '0.9rem',\n                                                                    direction: 'ltr',\n                                                                    color: '#333',\n                                                                    background: 'white'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#d1d5db',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"المدة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: \"00:00:00\",\n                                                                value: segment.duration,\n                                                                onChange: (e)=>updateSegment(segment.id, 'duration', e.target.value),\n                                                                style: {\n                                                                    width: '100%',\n                                                                    padding: '10px',\n                                                                    border: '1px solid #6b7280',\n                                                                    borderRadius: '6px',\n                                                                    fontSize: '0.9rem',\n                                                                    direction: 'ltr',\n                                                                    color: '#333',\n                                                                    background: 'white'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, segment.id, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '15px',\n                                justifyContent: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: ()=>router.push('/media-list'),\n                                    style: {\n                                        background: '#6c757d',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '12px 30px',\n                                        fontSize: '1rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    style: {\n                                        background: isSubmitting ? '#6c757d' : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '12px 30px',\n                                        fontSize: '1rem',\n                                        cursor: isSubmitting ? 'not-allowed' : 'pointer'\n                                    },\n                                    children: isSubmitting ? '⏳ جاري الحفظ...' : '💾 حفظ التعديلات'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n            lineNumber: 183,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(EditMediaPage, \"9sixu6lvWM6OFtUjgnxZ2QpHtU8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = EditMediaPage;\nvar _c;\n$RefreshReg$(_c, \"EditMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZWRpdC1tZWRpYS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ2lCO0FBQ1Y7QUFDUTtBQUNiO0FBVS9CLFNBQVNPOztJQUN0QixNQUFNQyxTQUFTTiwwREFBU0E7SUFDeEIsTUFBTU8sZUFBZU4sZ0VBQWVBO0lBQ3BDLE1BQU1PLFVBQVVELGFBQWFFLEdBQUcsQ0FBQztJQUNqQyxNQUFNLEVBQUVDLFNBQVMsRUFBRUMsY0FBYyxFQUFFLEdBQUdQLDJEQUFRQTtJQUU5QyxNQUFNLENBQUNRLFVBQVVDLFlBQVksR0FBR2YsK0NBQVFBLENBQUM7UUFDdkNnQixNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxTQUFTO1FBQ1RDLFFBQVE7UUFDUkMsUUFBUTtRQUNSQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsT0FBTztRQUNQQyxlQUFlO1FBQ2ZDLGNBQWM7UUFDZEMsWUFBWTtRQUNaQyxnQkFBZ0I7SUFDbEI7SUFFQSxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBRzlCLCtDQUFRQSxDQUFZO1FBQ2xEO1lBQ0UrQixJQUFJO1lBQ0pDLGFBQWE7WUFDYkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFVBQVU7UUFDWjtLQUNEO0lBRUQsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR3JDLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ3NDLFNBQVNDLFdBQVcsR0FBR3ZDLCtDQUFRQSxDQUFDO0lBRXZDLDRCQUE0QjtJQUM1QkMsZ0RBQVNBO21DQUFDO1lBQ1IsSUFBSVMsU0FBUztnQkFDWDhCO1lBQ0YsT0FBTztnQkFDTGhDLE9BQU9pQyxJQUFJLENBQUM7WUFDZDtRQUNGO2tDQUFHO1FBQUMvQjtLQUFRO0lBRVosTUFBTThCLGlCQUFpQjtRQUNyQixJQUFJO1lBQ0YsTUFBTUUsV0FBVyxNQUFNQyxNQUFNLGlCQUF5QixPQUFSakM7WUFDOUMsTUFBTWtDLFNBQVMsTUFBTUYsU0FBU0csSUFBSTtZQUVsQyxJQUFJRCxPQUFPRSxPQUFPLElBQUlGLE9BQU9HLElBQUksRUFBRTtvQkFZaEJDLHNCQUNEQSxxQkFDRkE7Z0JBYmQsTUFBTUEsUUFBUUosT0FBT0csSUFBSTtnQkFDekJoQyxZQUFZO29CQUNWQyxNQUFNZ0MsTUFBTWhDLElBQUksSUFBSTtvQkFDcEJDLE1BQU0rQixNQUFNL0IsSUFBSSxJQUFJO29CQUNwQkMsYUFBYThCLE1BQU05QixXQUFXLElBQUk7b0JBQ2xDQyxTQUFTNkIsTUFBTTdCLE9BQU8sSUFBSTtvQkFDMUJDLFFBQVE0QixNQUFNNUIsTUFBTSxJQUFJO29CQUN4QkMsUUFBUTJCLE1BQU0zQixNQUFNLElBQUk7b0JBQ3hCQyxXQUFXMEIsTUFBTTFCLFNBQVMsR0FBRzBCLE1BQU0xQixTQUFTLENBQUMyQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBRztvQkFDN0QxQixTQUFTeUIsTUFBTXpCLE9BQU8sR0FBR3lCLE1BQU16QixPQUFPLENBQUMwQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsR0FBRztvQkFDdkR6QixPQUFPd0IsTUFBTXhCLEtBQUssSUFBSTtvQkFDdEJDLGVBQWV1QixFQUFBQSx1QkFBQUEsTUFBTXZCLGFBQWEsY0FBbkJ1QiwyQ0FBQUEscUJBQXFCRSxRQUFRLE9BQU07b0JBQ2xEeEIsY0FBY3NCLEVBQUFBLHNCQUFBQSxNQUFNdEIsWUFBWSxjQUFsQnNCLDBDQUFBQSxvQkFBb0JFLFFBQVEsT0FBTTtvQkFDaER2QixZQUFZcUIsRUFBQUEsb0JBQUFBLE1BQU1yQixVQUFVLGNBQWhCcUIsd0NBQUFBLGtCQUFrQkUsUUFBUSxPQUFNO29CQUM1Q3RCLGdCQUFnQm9CLE1BQU1wQixjQUFjLElBQUk7Z0JBQzFDO2dCQUVBLElBQUlvQixNQUFNbkIsUUFBUSxJQUFJbUIsTUFBTW5CLFFBQVEsQ0FBQ3NCLE1BQU0sR0FBRyxHQUFHO29CQUMvQ3JCLFlBQVlrQixNQUFNbkIsUUFBUSxDQUFDdUIsR0FBRyxDQUFDLENBQUNDLEtBQVVDLFFBQW1COzRCQUMzRHZCLElBQUl1QixRQUFROzRCQUNadEIsYUFBYXFCLElBQUlyQixXQUFXLElBQUksTUFBOEMsT0FBeEMsQ0FBQ3NCLFFBQVEsR0FBR0osUUFBUSxHQUFHSyxRQUFRLENBQUMsR0FBRzs0QkFDekV0QixRQUFRb0IsSUFBSXBCLE1BQU0sSUFBSTs0QkFDdEJDLFNBQVNtQixJQUFJbkIsT0FBTyxJQUFJOzRCQUN4QkMsVUFBVWtCLElBQUlsQixRQUFRLElBQUk7d0JBQzVCO2dCQUNGO1lBQ0YsT0FBTztnQkFDTHZCLFVBQVUsNEJBQTRCO2dCQUN0Q0osT0FBT2lDLElBQUksQ0FBQztZQUNkO1FBQ0YsRUFBRSxPQUFPZSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDNUMsVUFBVSwwQkFBMEI7WUFDcENKLE9BQU9pQyxJQUFJLENBQUM7UUFDZCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUEsSUFBSUQsU0FBUztRQUNYLHFCQUNFLDhEQUFDbEMsNERBQVNBO1lBQUNzRCxxQkFBcUI7Z0JBQUM7YUFBZTtzQkFDOUMsNEVBQUNyRCxtRUFBZUE7Z0JBQUNzRCxPQUFNO2dCQUFxQkMsVUFBUztnQkFBeUJDLE1BQUs7MEJBQ2pGLDRFQUFDQztvQkFBSUMsT0FBTzt3QkFBRUMsV0FBVzt3QkFBVUMsU0FBUztvQkFBTzs7c0NBQ2pELDhEQUFDSDs0QkFBSUMsT0FBTztnQ0FBRUcsVUFBVTtnQ0FBUUMsY0FBYzs0QkFBTztzQ0FBRzs7Ozs7O3NDQUN4RCw4REFBQ0w7NEJBQUlDLE9BQU87Z0NBQUVLLE9BQU87NEJBQVU7c0NBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFLNUM7SUFFQSxNQUFNQyxlQUFlLE9BQU9DO1FBQzFCQSxFQUFFQyxjQUFjO1FBQ2hCbEMsZ0JBQWdCO1FBRWhCLElBQUk7WUFDRixNQUFNSyxXQUFXLE1BQU1DLE1BQU0saUJBQXlCLE9BQVJqQyxVQUFXO2dCQUN2RDhELFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQixHQUFHOUQsUUFBUTtvQkFDWGUsVUFBVUEsU0FBU3VCLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBUTs0QkFDN0JyQixhQUFhcUIsSUFBSXJCLFdBQVc7NEJBQzVCQyxRQUFRb0IsSUFBSXBCLE1BQU07NEJBQ2xCQyxTQUFTbUIsSUFBSW5CLE9BQU87NEJBQ3BCQyxVQUFVa0IsSUFBSWxCLFFBQVE7d0JBQ3hCO2dCQUNGO1lBQ0Y7WUFFQSxNQUFNUyxTQUFTLE1BQU1GLFNBQVNHLElBQUk7WUFFbEMsSUFBSUQsT0FBT0UsT0FBTyxFQUFFO2dCQUNsQmxDLFVBQVUseUJBQXlCO2dCQUNuQ2lFLFdBQVc7b0JBQ1RyRSxPQUFPaUMsSUFBSSxDQUFDO2dCQUNkLEdBQUc7WUFDTCxPQUFPO2dCQUNMN0IsVUFBVSwwQkFBMEJnQyxPQUFPWSxLQUFLLEVBQUU7WUFDcEQ7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkM1QyxVQUFVLDBCQUEwQjtRQUN0QyxTQUFVO1lBQ1J5QixnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBLE1BQU15QyxhQUFhO1FBQ2pCLE1BQU1DLGFBQXNCO1lBQzFCaEQsSUFBSUYsU0FBU3NCLE1BQU0sR0FBRztZQUN0Qm5CLGFBQWEsTUFBd0QsT0FBbEQsQ0FBQ0gsU0FBU3NCLE1BQU0sR0FBRyxHQUFHRCxRQUFRLEdBQUdLLFFBQVEsQ0FBQyxHQUFHO1lBQ2hFdEIsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFVBQVU7UUFDWjtRQUNBTCxZQUFZO2VBQUlEO1lBQVVrRDtTQUFXO0lBQ3ZDO0lBRUEsTUFBTUMsZ0JBQWdCLENBQUNqRDtRQUNyQixJQUFJRixTQUFTc0IsTUFBTSxHQUFHLEdBQUc7WUFDdkJyQixZQUFZRCxTQUFTb0QsTUFBTSxDQUFDNUIsQ0FBQUEsTUFBT0EsSUFBSXRCLEVBQUUsS0FBS0E7UUFDaEQ7SUFDRjtJQUVBLE1BQU1tRCxnQkFBZ0IsQ0FBQ25ELElBQVlvRCxPQUFzQkM7UUFDdkR0RCxZQUFZRCxTQUFTdUIsR0FBRyxDQUFDQyxDQUFBQSxNQUN2QkEsSUFBSXRCLEVBQUUsS0FBS0EsS0FBSztnQkFBRSxHQUFHc0IsR0FBRztnQkFBRSxDQUFDOEIsTUFBTSxFQUFFQztZQUFNLElBQUkvQjtJQUVqRDtJQUVBLHFCQUNFLDhEQUFDakQsNERBQVNBO1FBQUNzRCxxQkFBcUI7WUFBQztTQUFlO2tCQUM5Qyw0RUFBQ3JELG1FQUFlQTtZQUFDc0QsT0FBTTtZQUFxQkMsVUFBUztZQUFnQ0MsTUFBSzs7OEJBQ3hGLDhEQUFDd0I7b0JBQUtDLFVBQVVqQjtvQkFBY04sT0FBTzt3QkFBRXdCLFVBQVU7d0JBQVNDLFFBQVE7b0JBQVM7O3NDQUV6RSw4REFBQzFCOzRCQUFJQyxPQUFPO2dDQUNWMEIsWUFBWTtnQ0FDWkMsY0FBYztnQ0FDZHpCLFNBQVM7Z0NBQ1RFLGNBQWM7Z0NBQ2R3QixRQUFROzRCQUNWOzs4Q0FDRSw4REFBQ0M7b0NBQUc3QixPQUFPO3dDQUFFSyxPQUFPO3dDQUFXRCxjQUFjO3dDQUFRRCxVQUFVO29DQUFTOzhDQUFHOzs7Ozs7OENBSTNFLDhEQUFDSjtvQ0FBSUMsT0FBTzt3Q0FBRThCLFNBQVM7d0NBQVFDLHFCQUFxQjt3Q0FBd0NDLEtBQUs7b0NBQU87O3NEQUN0Ryw4REFBQ2pDOzs4REFDQyw4REFBQ2tDO29EQUFNakMsT0FBTzt3REFBRThCLFNBQVM7d0RBQVMxQixjQUFjO3dEQUFPQyxPQUFPO3dEQUFXNkIsWUFBWTtvREFBTzs4REFBRzs7Ozs7OzhEQUcvRiw4REFBQ0M7b0RBQ0NqRixNQUFLO29EQUNMbUUsT0FBT3RFLFNBQVNFLElBQUk7b0RBQ3BCbUYsVUFBVSxDQUFDN0IsSUFBTXZELFlBQVk7NERBQUMsR0FBR0QsUUFBUTs0REFBRUUsTUFBTXNELEVBQUU4QixNQUFNLENBQUNoQixLQUFLO3dEQUFBO29EQUMvRGlCLFFBQVE7b0RBQ1J0QyxPQUFPO3dEQUNMdUMsT0FBTzt3REFDUHJDLFNBQVM7d0RBQ1QwQixRQUFRO3dEQUNSRCxjQUFjO3dEQUNkeEIsVUFBVTt3REFDVnFDLFdBQVc7d0RBQ1huQyxPQUFPO3dEQUNQcUIsWUFBWTtvREFDZDs7Ozs7Ozs7Ozs7O3NEQUlKLDhEQUFDM0I7OzhEQUNDLDhEQUFDa0M7b0RBQU1qQyxPQUFPO3dEQUFFOEIsU0FBUzt3REFBUzFCLGNBQWM7d0RBQU9DLE9BQU87d0RBQVc2QixZQUFZO29EQUFPOzhEQUFHOzs7Ozs7OERBRy9GLDhEQUFDTztvREFDQ3BCLE9BQU90RSxTQUFTRyxJQUFJO29EQUNwQmtGLFVBQVUsQ0FBQzdCLElBQU12RCxZQUFZOzREQUFDLEdBQUdELFFBQVE7NERBQUVHLE1BQU1xRCxFQUFFOEIsTUFBTSxDQUFDaEIsS0FBSzt3REFBQTtvREFDL0RpQixRQUFRO29EQUNSdEMsT0FBTzt3REFDTHVDLE9BQU87d0RBQ1ByQyxTQUFTO3dEQUNUMEIsUUFBUTt3REFDUkQsY0FBYzt3REFDZHhCLFVBQVU7d0RBQ1ZxQyxXQUFXO3dEQUNYbkMsT0FBTzt3REFDUHFCLFlBQVk7b0RBQ2Q7O3NFQUVBLDhEQUFDZ0I7NERBQU9yQixPQUFNO3NFQUFHOzs7Ozs7c0VBQ2pCLDhEQUFDcUI7NERBQU9yQixPQUFNO3NFQUFVOzs7Ozs7c0VBQ3hCLDhEQUFDcUI7NERBQU9yQixPQUFNO3NFQUFTOzs7Ozs7c0VBQ3ZCLDhEQUFDcUI7NERBQU9yQixPQUFNO3NFQUFROzs7Ozs7c0VBQ3RCLDhEQUFDcUI7NERBQU9yQixPQUFNO3NFQUFPOzs7Ozs7c0VBQ3JCLDhEQUFDcUI7NERBQU9yQixPQUFNO3NFQUFROzs7Ozs7c0VBQ3RCLDhEQUFDcUI7NERBQU9yQixPQUFNO3NFQUFVOzs7Ozs7c0VBQ3hCLDhEQUFDcUI7NERBQU9yQixPQUFNO3NFQUFTOzs7Ozs7c0VBQ3ZCLDhEQUFDcUI7NERBQU9yQixPQUFNO3NFQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTzlCLDhEQUFDdEI7NEJBQUlDLE9BQU87Z0NBQ1YwQixZQUFZO2dDQUNaQyxjQUFjO2dDQUNkekIsU0FBUztnQ0FDVEUsY0FBYztnQ0FDZHdCLFFBQVE7NEJBQ1Y7OzhDQUNFLDhEQUFDN0I7b0NBQUlDLE9BQU87d0NBQUU4QixTQUFTO3dDQUFRYSxnQkFBZ0I7d0NBQWlCQyxZQUFZO3dDQUFVeEMsY0FBYztvQ0FBTzs7c0RBQ3pHLDhEQUFDeUI7NENBQUc3QixPQUFPO2dEQUFFSyxPQUFPO2dEQUFXRixVQUFVO2dEQUFVc0IsUUFBUTs0Q0FBRTtzREFBRzs7Ozs7O3NEQUdoRSw4REFBQ29COzRDQUNDM0YsTUFBSzs0Q0FDTDRGLFNBQVMvQjs0Q0FDVGYsT0FBTztnREFDTDBCLFlBQVk7Z0RBQ1pyQixPQUFPO2dEQUNQdUIsUUFBUTtnREFDUkQsY0FBYztnREFDZHpCLFNBQVM7Z0RBQ1Q2QyxRQUFRO2dEQUNSNUMsVUFBVTs0Q0FDWjtzREFDRDs7Ozs7Ozs7Ozs7O2dDQUtGckMsU0FBU3VCLEdBQUcsQ0FBQyxDQUFDMkQsU0FBU3pELHNCQUN0Qiw4REFBQ1E7d0NBQXFCQyxPQUFPOzRDQUMzQjBCLFlBQVk7NENBQ1pDLGNBQWM7NENBQ2R6QixTQUFTOzRDQUNURSxjQUFjOzRDQUNkd0IsUUFBUTt3Q0FDVjs7MERBQ0UsOERBQUM3QjtnREFBSUMsT0FBTztvREFBRThCLFNBQVM7b0RBQVFhLGdCQUFnQjtvREFBaUJDLFlBQVk7b0RBQVV4QyxjQUFjO2dEQUFPOztrRUFDekcsOERBQUM2Qzt3REFBR2pELE9BQU87NERBQUVLLE9BQU87NERBQVdvQixRQUFRO3dEQUFFOzs0REFBRzs0REFBU3VCLFFBQVFoRixFQUFFOzs7Ozs7O29EQUM5REYsU0FBU3NCLE1BQU0sR0FBRyxtQkFDakIsOERBQUN5RDt3REFDQzNGLE1BQUs7d0RBQ0w0RixTQUFTLElBQU03QixjQUFjK0IsUUFBUWhGLEVBQUU7d0RBQ3ZDZ0MsT0FBTzs0REFDTDBCLFlBQVk7NERBQ1pyQixPQUFPOzREQUNQdUIsUUFBUTs0REFDUkQsY0FBYzs0REFDZHpCLFNBQVM7NERBQ1Q2QyxRQUFROzREQUNSNUMsVUFBVTt3REFDWjtrRUFDRDs7Ozs7Ozs7Ozs7OzBEQU1MLDhEQUFDSjtnREFBSUMsT0FBTztvREFBRThCLFNBQVM7b0RBQVFDLHFCQUFxQjtvREFBd0NDLEtBQUs7Z0RBQU87O2tFQUN0Ryw4REFBQ2pDOzswRUFDQyw4REFBQ2tDO2dFQUFNakMsT0FBTztvRUFBRThCLFNBQVM7b0VBQVMxQixjQUFjO29FQUFPQyxPQUFPO29FQUFXRixVQUFVO2dFQUFTOzBFQUFHOzs7Ozs7MEVBRy9GLDhEQUFDZ0M7Z0VBQ0NqRixNQUFLO2dFQUNMbUUsT0FBTzJCLFFBQVEvRSxXQUFXO2dFQUMxQm1FLFVBQVUsQ0FBQzdCLElBQU1ZLGNBQWM2QixRQUFRaEYsRUFBRSxFQUFFLGVBQWV1QyxFQUFFOEIsTUFBTSxDQUFDaEIsS0FBSztnRUFDeEVyQixPQUFPO29FQUNMdUMsT0FBTztvRUFDUHJDLFNBQVM7b0VBQ1QwQixRQUFRO29FQUNSRCxjQUFjO29FQUNkeEIsVUFBVTtvRUFDVnFDLFdBQVc7b0VBQ1huQyxPQUFPO29FQUNQcUIsWUFBWTtnRUFDZDs7Ozs7Ozs7Ozs7O2tFQUlKLDhEQUFDM0I7OzBFQUNDLDhEQUFDa0M7Z0VBQU1qQyxPQUFPO29FQUFFOEIsU0FBUztvRUFBUzFCLGNBQWM7b0VBQU9DLE9BQU87b0VBQVdGLFVBQVU7Z0VBQVM7MEVBQUc7Ozs7OzswRUFHL0YsOERBQUNnQztnRUFDQ2pGLE1BQUs7Z0VBQ0xnRyxhQUFZO2dFQUNaN0IsT0FBTzJCLFFBQVE5RSxNQUFNO2dFQUNyQmtFLFVBQVUsQ0FBQzdCLElBQU1ZLGNBQWM2QixRQUFRaEYsRUFBRSxFQUFFLFVBQVV1QyxFQUFFOEIsTUFBTSxDQUFDaEIsS0FBSztnRUFDbkVyQixPQUFPO29FQUNMdUMsT0FBTztvRUFDUHJDLFNBQVM7b0VBQ1QwQixRQUFRO29FQUNSRCxjQUFjO29FQUNkeEIsVUFBVTtvRUFDVnFDLFdBQVc7b0VBQ1huQyxPQUFPO29FQUNQcUIsWUFBWTtnRUFDZDs7Ozs7Ozs7Ozs7O2tFQUlKLDhEQUFDM0I7OzBFQUNDLDhEQUFDa0M7Z0VBQU1qQyxPQUFPO29FQUFFOEIsU0FBUztvRUFBUzFCLGNBQWM7b0VBQU9DLE9BQU87b0VBQVdGLFVBQVU7Z0VBQVM7MEVBQUc7Ozs7OzswRUFHL0YsOERBQUNnQztnRUFDQ2pGLE1BQUs7Z0VBQ0xnRyxhQUFZO2dFQUNaN0IsT0FBTzJCLFFBQVE3RSxPQUFPO2dFQUN0QmlFLFVBQVUsQ0FBQzdCLElBQU1ZLGNBQWM2QixRQUFRaEYsRUFBRSxFQUFFLFdBQVd1QyxFQUFFOEIsTUFBTSxDQUFDaEIsS0FBSztnRUFDcEVyQixPQUFPO29FQUNMdUMsT0FBTztvRUFDUHJDLFNBQVM7b0VBQ1QwQixRQUFRO29FQUNSRCxjQUFjO29FQUNkeEIsVUFBVTtvRUFDVnFDLFdBQVc7b0VBQ1huQyxPQUFPO29FQUNQcUIsWUFBWTtnRUFDZDs7Ozs7Ozs7Ozs7O2tFQUlKLDhEQUFDM0I7OzBFQUNDLDhEQUFDa0M7Z0VBQU1qQyxPQUFPO29FQUFFOEIsU0FBUztvRUFBUzFCLGNBQWM7b0VBQU9DLE9BQU87b0VBQVdGLFVBQVU7Z0VBQVM7MEVBQUc7Ozs7OzswRUFHL0YsOERBQUNnQztnRUFDQ2pGLE1BQUs7Z0VBQ0xnRyxhQUFZO2dFQUNaN0IsT0FBTzJCLFFBQVE1RSxRQUFRO2dFQUN2QmdFLFVBQVUsQ0FBQzdCLElBQU1ZLGNBQWM2QixRQUFRaEYsRUFBRSxFQUFFLFlBQVl1QyxFQUFFOEIsTUFBTSxDQUFDaEIsS0FBSztnRUFDckVyQixPQUFPO29FQUNMdUMsT0FBTztvRUFDUHJDLFNBQVM7b0VBQ1QwQixRQUFRO29FQUNSRCxjQUFjO29FQUNkeEIsVUFBVTtvRUFDVnFDLFdBQVc7b0VBQ1huQyxPQUFPO29FQUNQcUIsWUFBWTtnRUFDZDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt1Q0FoSEVzQixRQUFRaEYsRUFBRTs7Ozs7Ozs7Ozs7c0NBeUh4Qiw4REFBQytCOzRCQUFJQyxPQUFPO2dDQUFFOEIsU0FBUztnQ0FBUUUsS0FBSztnQ0FBUVcsZ0JBQWdCOzRCQUFTOzs4Q0FDbkUsOERBQUNFO29DQUNDM0YsTUFBSztvQ0FDTDRGLFNBQVMsSUFBTXJHLE9BQU9pQyxJQUFJLENBQUM7b0NBQzNCc0IsT0FBTzt3Q0FDTDBCLFlBQVk7d0NBQ1pyQixPQUFPO3dDQUNQdUIsUUFBUTt3Q0FDUkQsY0FBYzt3Q0FDZHpCLFNBQVM7d0NBQ1RDLFVBQVU7d0NBQ1Y0QyxRQUFRO29DQUNWOzhDQUNEOzs7Ozs7OENBR0QsOERBQUNGO29DQUNDM0YsTUFBSztvQ0FDTGlHLFVBQVU5RTtvQ0FDVjJCLE9BQU87d0NBQ0wwQixZQUFZckQsZUFDUixZQUNBO3dDQUNKZ0MsT0FBTzt3Q0FDUHVCLFFBQVE7d0NBQ1JELGNBQWM7d0NBQ2R6QixTQUFTO3dDQUNUQyxVQUFVO3dDQUNWNEMsUUFBUTFFLGVBQWUsZ0JBQWdCO29DQUN6Qzs4Q0FFQ0EsZUFBZSxvQkFBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJMUMsOERBQUN2Qjs7Ozs7Ozs7Ozs7Ozs7OztBQUlUO0dBMWF3Qk47O1FBQ1BMLHNEQUFTQTtRQUNIQyw0REFBZUE7UUFFRUcsdURBQVFBOzs7S0FKeEJDIiwic291cmNlcyI6WyJEOlxcRG9jIGRhdGFiYXNlXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxhcHBcXGVkaXQtbWVkaWFcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IEF1dGhHdWFyZCB9IGZyb20gJ0AvY29tcG9uZW50cy9BdXRoR3VhcmQnO1xuaW1wb3J0IERhc2hib2FyZExheW91dCBmcm9tICdAL2NvbXBvbmVudHMvRGFzaGJvYXJkTGF5b3V0JztcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9jb21wb25lbnRzL1RvYXN0JztcblxuaW50ZXJmYWNlIFNlZ21lbnQge1xuICBpZDogbnVtYmVyO1xuICBzZWdtZW50Q29kZTogc3RyaW5nO1xuICB0aW1lSW46IHN0cmluZztcbiAgdGltZU91dDogc3RyaW5nO1xuICBkdXJhdGlvbjogc3RyaW5nO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFZGl0TWVkaWFQYWdlKCkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKCk7XG4gIGNvbnN0IG1lZGlhSWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdpZCcpO1xuICBjb25zdCB7IHNob3dUb2FzdCwgVG9hc3RDb250YWluZXIgfSA9IHVzZVRvYXN0KCk7XG5cbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XG4gICAgbmFtZTogJycsXG4gICAgdHlwZTogJycsXG4gICAgZGVzY3JpcHRpb246ICcnLFxuICAgIGNoYW5uZWw6ICcnLFxuICAgIHNvdXJjZTogJycsXG4gICAgc3RhdHVzOiAnJyxcbiAgICBzdGFydERhdGU6ICcnLFxuICAgIGVuZERhdGU6ICcnLFxuICAgIG5vdGVzOiAnJyxcbiAgICBlcGlzb2RlTnVtYmVyOiAnJyxcbiAgICBzZWFzb25OdW1iZXI6ICcnLFxuICAgIHBhcnROdW1iZXI6ICcnLFxuICAgIGhhcmREaXNrTnVtYmVyOiAnJyxcbiAgfSk7XG5cbiAgY29uc3QgW3NlZ21lbnRzLCBzZXRTZWdtZW50c10gPSB1c2VTdGF0ZTxTZWdtZW50W10+KFtcbiAgICB7XG4gICAgICBpZDogMSxcbiAgICAgIHNlZ21lbnRDb2RlOiAnU0VHMDAxJyxcbiAgICAgIHRpbWVJbjogJzAwOjAwOjAwJyxcbiAgICAgIHRpbWVPdXQ6ICcnLFxuICAgICAgZHVyYXRpb246ICcwMDowMDowMCdcbiAgICB9XG4gIF0pO1xuXG4gIGNvbnN0IFtpc1N1Ym1pdHRpbmcsIHNldElzU3VibWl0dGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIC8vINis2YTYqCDYqNmK2KfZhtin2Kog2KfZhNmF2KfYr9ipINmE2YTYqti52K/ZitmEXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG1lZGlhSWQpIHtcbiAgICAgIGZldGNoTWVkaWFEYXRhKCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJvdXRlci5wdXNoKCcvbWVkaWEtbGlzdCcpO1xuICAgIH1cbiAgfSwgW21lZGlhSWRdKTtcblxuICBjb25zdCBmZXRjaE1lZGlhRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9tZWRpYT9pZD0ke21lZGlhSWR9YCk7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2VzcyAmJiByZXN1bHQuZGF0YSkge1xuICAgICAgICBjb25zdCBtZWRpYSA9IHJlc3VsdC5kYXRhO1xuICAgICAgICBzZXRGb3JtRGF0YSh7XG4gICAgICAgICAgbmFtZTogbWVkaWEubmFtZSB8fCAnJyxcbiAgICAgICAgICB0eXBlOiBtZWRpYS50eXBlIHx8ICcnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBtZWRpYS5kZXNjcmlwdGlvbiB8fCAnJyxcbiAgICAgICAgICBjaGFubmVsOiBtZWRpYS5jaGFubmVsIHx8ICcnLFxuICAgICAgICAgIHNvdXJjZTogbWVkaWEuc291cmNlIHx8ICcnLFxuICAgICAgICAgIHN0YXR1czogbWVkaWEuc3RhdHVzIHx8ICcnLFxuICAgICAgICAgIHN0YXJ0RGF0ZTogbWVkaWEuc3RhcnREYXRlID8gbWVkaWEuc3RhcnREYXRlLnNwbGl0KCdUJylbMF0gOiAnJyxcbiAgICAgICAgICBlbmREYXRlOiBtZWRpYS5lbmREYXRlID8gbWVkaWEuZW5kRGF0ZS5zcGxpdCgnVCcpWzBdIDogJycsXG4gICAgICAgICAgbm90ZXM6IG1lZGlhLm5vdGVzIHx8ICcnLFxuICAgICAgICAgIGVwaXNvZGVOdW1iZXI6IG1lZGlhLmVwaXNvZGVOdW1iZXI/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICAgICAgc2Vhc29uTnVtYmVyOiBtZWRpYS5zZWFzb25OdW1iZXI/LnRvU3RyaW5nKCkgfHwgJycsXG4gICAgICAgICAgcGFydE51bWJlcjogbWVkaWEucGFydE51bWJlcj8udG9TdHJpbmcoKSB8fCAnJyxcbiAgICAgICAgICBoYXJkRGlza051bWJlcjogbWVkaWEuaGFyZERpc2tOdW1iZXIgfHwgJycsXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmIChtZWRpYS5zZWdtZW50cyAmJiBtZWRpYS5zZWdtZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgc2V0U2VnbWVudHMobWVkaWEuc2VnbWVudHMubWFwKChzZWc6IGFueSwgaW5kZXg6IG51bWJlcikgPT4gKHtcbiAgICAgICAgICAgIGlkOiBpbmRleCArIDEsXG4gICAgICAgICAgICBzZWdtZW50Q29kZTogc2VnLnNlZ21lbnRDb2RlIHx8IGBTRUckeyhpbmRleCArIDEpLnRvU3RyaW5nKCkucGFkU3RhcnQoMywgJzAnKX1gLFxuICAgICAgICAgICAgdGltZUluOiBzZWcudGltZUluIHx8ICcwMDowMDowMCcsXG4gICAgICAgICAgICB0aW1lT3V0OiBzZWcudGltZU91dCB8fCAnJyxcbiAgICAgICAgICAgIGR1cmF0aW9uOiBzZWcuZHVyYXRpb24gfHwgJzAwOjAwOjAwJ1xuICAgICAgICAgIH0pKSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNob3dUb2FzdCgn2K7Yt9ijINmB2Yog2KzZhNioINio2YrYp9mG2KfYqiDYp9mE2YXYp9iv2KknLCAnZXJyb3InKTtcbiAgICAgICAgcm91dGVyLnB1c2goJy9tZWRpYS1saXN0Jyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIG1lZGlhOicsIGVycm9yKTtcbiAgICAgIHNob3dUb2FzdCgn2K7Yt9ijINmB2Yog2KfZhNin2KrYtdin2YQg2KjYp9mE2K7Yp9iv2YUnLCAnZXJyb3InKTtcbiAgICAgIHJvdXRlci5wdXNoKCcvbWVkaWEtbGlzdCcpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPEF1dGhHdWFyZCByZXF1aXJlZFBlcm1pc3Npb25zPXtbJ01FRElBX1VQREFURSddfT5cbiAgICAgICAgPERhc2hib2FyZExheW91dCB0aXRsZT1cItiq2K3Yr9mK2Ksg2YXYp9iv2Kkg2KXYudmE2KfZhdmK2KlcIiBzdWJ0aXRsZT1cItis2KfYsdmKINiq2K3ZhdmK2YQg2KfZhNio2YrYp9mG2KfYqi4uLlwiIGljb249XCLinI/vuI9cIj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicsIHBhZGRpbmc6ICc1MHB4JyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICcycmVtJywgbWFyZ2luQm90dG9tOiAnMjBweCcgfX0+4o+zPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnI2EwYWVjMCcgfX0+2KzYp9ix2Yog2KrYrdmF2YrZhCDYqNmK2KfZhtin2Kog2KfZhNmF2KfYr9ipLi4uPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvRGFzaGJvYXJkTGF5b3V0PlxuICAgICAgPC9BdXRoR3VhcmQ+XG4gICAgKTtcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgc2V0SXNTdWJtaXR0aW5nKHRydWUpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvbWVkaWE/aWQ9JHttZWRpYUlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICAuLi5mb3JtRGF0YSxcbiAgICAgICAgICBzZWdtZW50czogc2VnbWVudHMubWFwKHNlZyA9PiAoe1xuICAgICAgICAgICAgc2VnbWVudENvZGU6IHNlZy5zZWdtZW50Q29kZSxcbiAgICAgICAgICAgIHRpbWVJbjogc2VnLnRpbWVJbixcbiAgICAgICAgICAgIHRpbWVPdXQ6IHNlZy50aW1lT3V0LFxuICAgICAgICAgICAgZHVyYXRpb246IHNlZy5kdXJhdGlvblxuICAgICAgICAgIH0pKVxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzaG93VG9hc3QoJ9iq2YUg2KrYrdiv2YrYqyDYp9mE2YXYp9iv2Kkg2KjZhtis2KfYrScsICdzdWNjZXNzJyk7XG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIHJvdXRlci5wdXNoKCcvbWVkaWEtbGlzdCcpO1xuICAgICAgICB9LCAxNTAwKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNob3dUb2FzdCgn2K7Yt9ijINmB2Yog2KrYrdiv2YrYqyDYp9mE2YXYp9iv2Kk6ICcgKyByZXN1bHQuZXJyb3IsICdlcnJvcicpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBtZWRpYTonLCBlcnJvcik7XG4gICAgICBzaG93VG9hc3QoJ9iu2LfYoyDZgdmKINin2YTYp9iq2LXYp9mEINio2KfZhNiu2KfYr9mFJywgJ2Vycm9yJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzU3VibWl0dGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGFkZFNlZ21lbnQgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3U2VnbWVudDogU2VnbWVudCA9IHtcbiAgICAgIGlkOiBzZWdtZW50cy5sZW5ndGggKyAxLFxuICAgICAgc2VnbWVudENvZGU6IGBTRUckeyhzZWdtZW50cy5sZW5ndGggKyAxKS50b1N0cmluZygpLnBhZFN0YXJ0KDMsICcwJyl9YCxcbiAgICAgIHRpbWVJbjogJzAwOjAwOjAwJyxcbiAgICAgIHRpbWVPdXQ6ICcnLFxuICAgICAgZHVyYXRpb246ICcwMDowMDowMCdcbiAgICB9O1xuICAgIHNldFNlZ21lbnRzKFsuLi5zZWdtZW50cywgbmV3U2VnbWVudF0pO1xuICB9O1xuXG4gIGNvbnN0IHJlbW92ZVNlZ21lbnQgPSAoaWQ6IG51bWJlcikgPT4ge1xuICAgIGlmIChzZWdtZW50cy5sZW5ndGggPiAxKSB7XG4gICAgICBzZXRTZWdtZW50cyhzZWdtZW50cy5maWx0ZXIoc2VnID0+IHNlZy5pZCAhPT0gaWQpKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdXBkYXRlU2VnbWVudCA9IChpZDogbnVtYmVyLCBmaWVsZDoga2V5b2YgU2VnbWVudCwgdmFsdWU6IHN0cmluZykgPT4ge1xuICAgIHNldFNlZ21lbnRzKHNlZ21lbnRzLm1hcChzZWcgPT5cbiAgICAgIHNlZy5pZCA9PT0gaWQgPyB7IC4uLnNlZywgW2ZpZWxkXTogdmFsdWUgfSA6IHNlZ1xuICAgICkpO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEF1dGhHdWFyZCByZXF1aXJlZFBlcm1pc3Npb25zPXtbJ01FRElBX1VQREFURSddfT5cbiAgICAgIDxEYXNoYm9hcmRMYXlvdXQgdGl0bGU9XCLYqtit2K/ZitirINmF2KfYr9ipINil2LnZhNin2YXZitipXCIgc3VidGl0bGU9XCLYqti52K/ZitmEINio2YrYp9mG2KfYqiDYp9mE2YXYp9iv2Kkg2KfZhNil2LnZhNin2YXZitipXCIgaWNvbj1cIuKcj++4j1wiPlxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBzdHlsZT17eyBtYXhXaWR0aDogJzgwMHB4JywgbWFyZ2luOiAnMCBhdXRvJyB9fT5cbiAgICAgICAgICB7Lyog2YXYudmE2YjZhdin2Kog2KPYs9in2LPZitipICovfVxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjNGE1NTY4JyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzE1cHgnLFxuICAgICAgICAgICAgcGFkZGluZzogJzI1cHgnLFxuICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMjVweCcsXG4gICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgIzZiNzI4MCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxoMiBzdHlsZT17eyBjb2xvcjogJyNmM2Y0ZjYnLCBtYXJnaW5Cb3R0b206ICcyMHB4JywgZm9udFNpemU6ICcxLjNyZW0nIH19PlxuICAgICAgICAgICAgICDwn5OdINin2YTZhdi52YTZiNmF2KfYqiDYp9mE2KPYs9in2LPZitipXG4gICAgICAgICAgICA8L2gyPlxuXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogJ3JlcGVhdChhdXRvLWZpdCwgbWlubWF4KDMwMHB4LCAxZnIpKScsIGdhcDogJzIwcHgnIH19PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snLCBtYXJnaW5Cb3R0b206ICc4cHgnLCBjb2xvcjogJyNmM2Y0ZjYnLCBmb250V2VpZ2h0OiAnYm9sZCcgfX0+XG4gICAgICAgICAgICAgICAgICDYp9iz2YUg2KfZhNmF2KfYr9ipICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLm5hbWV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKHsuLi5mb3JtRGF0YSwgbmFtZTogZS50YXJnZXQudmFsdWV9KX1cbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzJweCBzb2xpZCAjNmI3MjgwJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICAgICAgZGlyZWN0aW9uOiAncnRsJyxcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMzMzJyxcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3doaXRlJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snLCBtYXJnaW5Cb3R0b206ICc4cHgnLCBjb2xvcjogJyNmM2Y0ZjYnLCBmb250V2VpZ2h0OiAnYm9sZCcgfX0+XG4gICAgICAgICAgICAgICAgICDZhtmI2Lkg2KfZhNmF2KfYr9ipICpcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50eXBlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSh7Li4uZm9ybURhdGEsIHR5cGU6IGUudGFyZ2V0LnZhbHVlfSl9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEycHgnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICcycHggc29saWQgIzZiNzI4MCcsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGRpcmVjdGlvbjogJ3J0bCcsXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzMzMycsXG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd3aGl0ZSdcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPtin2K7YqtixINmG2YjYuSDYp9mE2YXYp9iv2Kk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJQUk9HUkFNXCI+2KjYsdmG2KfZhdisPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiU0VSSUVTXCI+2YXYs9mE2LPZhDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIk1PVklFXCI+2YHZitmE2YU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJTT05HXCI+2KPYutmG2YrYqTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlNUSU5HXCI+U3Rpbmc8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJGSUxMX0lOXCI+RmlsbCBJTjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkZJTExFUlwiPkZpbGxlcjwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBST01PXCI+UHJvbW88L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDYp9mE2LPZitis2YXYp9mG2KogKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJyM0YTU1NjgnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTVweCcsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMjVweCcsXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcyNXB4JyxcbiAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjNmI3MjgwJ1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBtYXJnaW5Cb3R0b206ICcyMHB4JyB9fT5cbiAgICAgICAgICAgICAgPGgyIHN0eWxlPXt7IGNvbG9yOiAnI2YzZjRmNicsIGZvbnRTaXplOiAnMS4zcmVtJywgbWFyZ2luOiAwIH19PlxuICAgICAgICAgICAgICAgIPCfjqwg2KfZhNiz2YrYrNmF2KfZhtiqXG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXthZGRTZWdtZW50fVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMTBiOTgxLCAjMDU5NjY5KScsXG4gICAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICc4cHggMTZweCcsXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDinpUg2KXYttin2YHYqSDYs9mK2KzZhdmG2KpcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAge3NlZ21lbnRzLm1hcCgoc2VnbWVudCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e3NlZ21lbnQuaWR9IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyMxZjI5MzcnLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzEwcHgnLFxuICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcyMHB4JyxcbiAgICAgICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcxNXB4JyxcbiAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgIzZiNzI4MCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBtYXJnaW5Cb3R0b206ICcxNXB4JyB9fT5cbiAgICAgICAgICAgICAgICAgIDxoMyBzdHlsZT17eyBjb2xvcjogJyNmM2Y0ZjYnLCBtYXJnaW46IDAgfX0+2LPZitis2YXZhtiqICN7c2VnbWVudC5pZH08L2gzPlxuICAgICAgICAgICAgICAgICAge3NlZ21lbnRzLmxlbmd0aCA+IDEgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlU2VnbWVudChzZWdtZW50LmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyNlZjQ0NDQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnNnB4IDEycHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuOHJlbSdcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg8J+Xke+4jyDYrdiw2YFcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZ3JpZCcsIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICdyZXBlYXQoYXV0by1maXQsIG1pbm1heCgyMDBweCwgMWZyKSknLCBnYXA6ICcxNXB4JyB9fT5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snLCBtYXJnaW5Cb3R0b206ICc1cHgnLCBjb2xvcjogJyNkMWQ1ZGInLCBmb250U2l6ZTogJzAuOXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAg2YPZiNivINin2YTYs9mK2KzZhdmG2KpcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWdtZW50LnNlZ21lbnRDb2RlfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlU2VnbWVudChzZWdtZW50LmlkLCAnc2VnbWVudENvZGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMTAwJScsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMTBweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgIzZiNzI4MCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgZGlyZWN0aW9uOiAncnRsJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzMzMycsXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgc3R5bGU9e3sgZGlzcGxheTogJ2Jsb2NrJywgbWFyZ2luQm90dG9tOiAnNXB4JywgY29sb3I6ICcjZDFkNWRiJywgZm9udFNpemU6ICcwLjlyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgINmI2YLYqiDYp9mE2KjYr9in2YrYqVxuICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwMDowMDowMFwiXG4gICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlZ21lbnQudGltZUlufVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlU2VnbWVudChzZWdtZW50LmlkLCAndGltZUluJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM2YjcyODAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpcmVjdGlvbjogJ2x0cicsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyMzMzMnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3doaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGRpc3BsYXk6ICdibG9jaycsIG1hcmdpbkJvdHRvbTogJzVweCcsIGNvbG9yOiAnI2QxZDVkYicsIGZvbnRTaXplOiAnMC45cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICDZiNmC2Kog2KfZhNmG2YfYp9mK2KlcbiAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiMDA6MDA6MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWdtZW50LnRpbWVPdXR9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVTZWdtZW50KHNlZ21lbnQuaWQsICd0aW1lT3V0JywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM2YjcyODAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpcmVjdGlvbjogJ2x0cicsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyMzMzMnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3doaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGRpc3BsYXk6ICdibG9jaycsIG1hcmdpbkJvdHRvbTogJzVweCcsIGNvbG9yOiAnI2QxZDVkYicsIGZvbnRTaXplOiAnMC45cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICDYp9mE2YXYr9ipXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjAwOjAwOjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VnbWVudC5kdXJhdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVNlZ21lbnQoc2VnbWVudC5pZCwgJ2R1cmF0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM2YjcyODAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpcmVjdGlvbjogJ2x0cicsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyMzMzMnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3doaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDYo9iy2LHYp9ixINin2YTYrdmB2LggKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzE1cHgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL21lZGlhLWxpc3QnKX1cbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzZjNzU3ZCcsXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTBweCcsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMzBweCcsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJ1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDYpdmE2LrYp9ihXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogaXNTdWJtaXR0aW5nXG4gICAgICAgICAgICAgICAgICA/ICcjNmM3NTdkJ1xuICAgICAgICAgICAgICAgICAgOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzEwYjk4MSAwJSwgIzA1OTY2OSAxMDAlKScsXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTBweCcsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMzBweCcsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICBjdXJzb3I6IGlzU3VibWl0dGluZyA/ICdub3QtYWxsb3dlZCcgOiAncG9pbnRlcidcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzU3VibWl0dGluZyA/ICfij7Mg2KzYp9ix2Yog2KfZhNit2YHYuC4uLicgOiAn8J+SviDYrdmB2Lgg2KfZhNiq2LnYr9mK2YTYp9iqJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvcm0+XG4gICAgICAgIDxUb2FzdENvbnRhaW5lciAvPlxuICAgICAgPC9EYXNoYm9hcmRMYXlvdXQ+XG4gICAgPC9BdXRoR3VhcmQ+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VTZWFyY2hQYXJhbXMiLCJBdXRoR3VhcmQiLCJEYXNoYm9hcmRMYXlvdXQiLCJ1c2VUb2FzdCIsIkVkaXRNZWRpYVBhZ2UiLCJyb3V0ZXIiLCJzZWFyY2hQYXJhbXMiLCJtZWRpYUlkIiwiZ2V0Iiwic2hvd1RvYXN0IiwiVG9hc3RDb250YWluZXIiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwibmFtZSIsInR5cGUiLCJkZXNjcmlwdGlvbiIsImNoYW5uZWwiLCJzb3VyY2UiLCJzdGF0dXMiLCJzdGFydERhdGUiLCJlbmREYXRlIiwibm90ZXMiLCJlcGlzb2RlTnVtYmVyIiwic2Vhc29uTnVtYmVyIiwicGFydE51bWJlciIsImhhcmREaXNrTnVtYmVyIiwic2VnbWVudHMiLCJzZXRTZWdtZW50cyIsImlkIiwic2VnbWVudENvZGUiLCJ0aW1lSW4iLCJ0aW1lT3V0IiwiZHVyYXRpb24iLCJpc1N1Ym1pdHRpbmciLCJzZXRJc1N1Ym1pdHRpbmciLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImZldGNoTWVkaWFEYXRhIiwicHVzaCIsInJlc3BvbnNlIiwiZmV0Y2giLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsImRhdGEiLCJtZWRpYSIsInNwbGl0IiwidG9TdHJpbmciLCJsZW5ndGgiLCJtYXAiLCJzZWciLCJpbmRleCIsInBhZFN0YXJ0IiwiZXJyb3IiLCJjb25zb2xlIiwicmVxdWlyZWRQZXJtaXNzaW9ucyIsInRpdGxlIiwic3VidGl0bGUiLCJpY29uIiwiZGl2Iiwic3R5bGUiLCJ0ZXh0QWxpZ24iLCJwYWRkaW5nIiwiZm9udFNpemUiLCJtYXJnaW5Cb3R0b20iLCJjb2xvciIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInNldFRpbWVvdXQiLCJhZGRTZWdtZW50IiwibmV3U2VnbWVudCIsInJlbW92ZVNlZ21lbnQiLCJmaWx0ZXIiLCJ1cGRhdGVTZWdtZW50IiwiZmllbGQiLCJ2YWx1ZSIsImZvcm0iLCJvblN1Ym1pdCIsIm1heFdpZHRoIiwibWFyZ2luIiwiYmFja2dyb3VuZCIsImJvcmRlclJhZGl1cyIsImJvcmRlciIsImgyIiwiZGlzcGxheSIsImdyaWRUZW1wbGF0ZUNvbHVtbnMiLCJnYXAiLCJsYWJlbCIsImZvbnRXZWlnaHQiLCJpbnB1dCIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwicmVxdWlyZWQiLCJ3aWR0aCIsImRpcmVjdGlvbiIsInNlbGVjdCIsIm9wdGlvbiIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJjdXJzb3IiLCJzZWdtZW50IiwiaDMiLCJwbGFjZWhvbGRlciIsImRpc2FibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/edit-media/page.tsx\n"));

/***/ })

});