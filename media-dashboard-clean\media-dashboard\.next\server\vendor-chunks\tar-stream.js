/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tar-stream";
exports.ids = ["vendor-chunks/tar-stream"];
exports.modules = {

/***/ "(rsc)/./node_modules/tar-stream/extract.js":
/*!********************************************!*\
  !*** ./node_modules/tar-stream/extract.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var util = __webpack_require__(/*! util */ \"util\")\nvar bl = __webpack_require__(/*! bl */ \"(rsc)/./node_modules/bl/bl.js\")\nvar headers = __webpack_require__(/*! ./headers */ \"(rsc)/./node_modules/tar-stream/headers.js\")\n\nvar Writable = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\").Writable)\nvar PassThrough = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\").PassThrough)\n\nvar noop = function () {}\n\nvar overflow = function (size) {\n  size &= 511\n  return size && 512 - size\n}\n\nvar emptyStream = function (self, offset) {\n  var s = new Source(self, offset)\n  s.end()\n  return s\n}\n\nvar mixinPax = function (header, pax) {\n  if (pax.path) header.name = pax.path\n  if (pax.linkpath) header.linkname = pax.linkpath\n  if (pax.size) header.size = parseInt(pax.size, 10)\n  header.pax = pax\n  return header\n}\n\nvar Source = function (self, offset) {\n  this._parent = self\n  this.offset = offset\n  PassThrough.call(this, { autoDestroy: false })\n}\n\nutil.inherits(Source, PassThrough)\n\nSource.prototype.destroy = function (err) {\n  this._parent.destroy(err)\n}\n\nvar Extract = function (opts) {\n  if (!(this instanceof Extract)) return new Extract(opts)\n  Writable.call(this, opts)\n\n  opts = opts || {}\n\n  this._offset = 0\n  this._buffer = bl()\n  this._missing = 0\n  this._partial = false\n  this._onparse = noop\n  this._header = null\n  this._stream = null\n  this._overflow = null\n  this._cb = null\n  this._locked = false\n  this._destroyed = false\n  this._pax = null\n  this._paxGlobal = null\n  this._gnuLongPath = null\n  this._gnuLongLinkPath = null\n\n  var self = this\n  var b = self._buffer\n\n  var oncontinue = function () {\n    self._continue()\n  }\n\n  var onunlock = function (err) {\n    self._locked = false\n    if (err) return self.destroy(err)\n    if (!self._stream) oncontinue()\n  }\n\n  var onstreamend = function () {\n    self._stream = null\n    var drain = overflow(self._header.size)\n    if (drain) self._parse(drain, ondrain)\n    else self._parse(512, onheader)\n    if (!self._locked) oncontinue()\n  }\n\n  var ondrain = function () {\n    self._buffer.consume(overflow(self._header.size))\n    self._parse(512, onheader)\n    oncontinue()\n  }\n\n  var onpaxglobalheader = function () {\n    var size = self._header.size\n    self._paxGlobal = headers.decodePax(b.slice(0, size))\n    b.consume(size)\n    onstreamend()\n  }\n\n  var onpaxheader = function () {\n    var size = self._header.size\n    self._pax = headers.decodePax(b.slice(0, size))\n    if (self._paxGlobal) self._pax = Object.assign({}, self._paxGlobal, self._pax)\n    b.consume(size)\n    onstreamend()\n  }\n\n  var ongnulongpath = function () {\n    var size = self._header.size\n    this._gnuLongPath = headers.decodeLongPath(b.slice(0, size), opts.filenameEncoding)\n    b.consume(size)\n    onstreamend()\n  }\n\n  var ongnulonglinkpath = function () {\n    var size = self._header.size\n    this._gnuLongLinkPath = headers.decodeLongPath(b.slice(0, size), opts.filenameEncoding)\n    b.consume(size)\n    onstreamend()\n  }\n\n  var onheader = function () {\n    var offset = self._offset\n    var header\n    try {\n      header = self._header = headers.decode(b.slice(0, 512), opts.filenameEncoding, opts.allowUnknownFormat)\n    } catch (err) {\n      self.emit('error', err)\n    }\n    b.consume(512)\n\n    if (!header) {\n      self._parse(512, onheader)\n      oncontinue()\n      return\n    }\n    if (header.type === 'gnu-long-path') {\n      self._parse(header.size, ongnulongpath)\n      oncontinue()\n      return\n    }\n    if (header.type === 'gnu-long-link-path') {\n      self._parse(header.size, ongnulonglinkpath)\n      oncontinue()\n      return\n    }\n    if (header.type === 'pax-global-header') {\n      self._parse(header.size, onpaxglobalheader)\n      oncontinue()\n      return\n    }\n    if (header.type === 'pax-header') {\n      self._parse(header.size, onpaxheader)\n      oncontinue()\n      return\n    }\n\n    if (self._gnuLongPath) {\n      header.name = self._gnuLongPath\n      self._gnuLongPath = null\n    }\n\n    if (self._gnuLongLinkPath) {\n      header.linkname = self._gnuLongLinkPath\n      self._gnuLongLinkPath = null\n    }\n\n    if (self._pax) {\n      self._header = header = mixinPax(header, self._pax)\n      self._pax = null\n    }\n\n    self._locked = true\n\n    if (!header.size || header.type === 'directory') {\n      self._parse(512, onheader)\n      self.emit('entry', header, emptyStream(self, offset), onunlock)\n      return\n    }\n\n    self._stream = new Source(self, offset)\n\n    self.emit('entry', header, self._stream, onunlock)\n    self._parse(header.size, onstreamend)\n    oncontinue()\n  }\n\n  this._onheader = onheader\n  this._parse(512, onheader)\n}\n\nutil.inherits(Extract, Writable)\n\nExtract.prototype.destroy = function (err) {\n  if (this._destroyed) return\n  this._destroyed = true\n\n  if (err) this.emit('error', err)\n  this.emit('close')\n  if (this._stream) this._stream.emit('close')\n}\n\nExtract.prototype._parse = function (size, onparse) {\n  if (this._destroyed) return\n  this._offset += size\n  this._missing = size\n  if (onparse === this._onheader) this._partial = false\n  this._onparse = onparse\n}\n\nExtract.prototype._continue = function () {\n  if (this._destroyed) return\n  var cb = this._cb\n  this._cb = noop\n  if (this._overflow) this._write(this._overflow, undefined, cb)\n  else cb()\n}\n\nExtract.prototype._write = function (data, enc, cb) {\n  if (this._destroyed) return\n\n  var s = this._stream\n  var b = this._buffer\n  var missing = this._missing\n  if (data.length) this._partial = true\n\n  // we do not reach end-of-chunk now. just forward it\n\n  if (data.length < missing) {\n    this._missing -= data.length\n    this._overflow = null\n    if (s) return s.write(data, cb)\n    b.append(data)\n    return cb()\n  }\n\n  // end-of-chunk. the parser should call cb.\n\n  this._cb = cb\n  this._missing = 0\n\n  var overflow = null\n  if (data.length > missing) {\n    overflow = data.slice(missing)\n    data = data.slice(0, missing)\n  }\n\n  if (s) s.end(data)\n  else b.append(data)\n\n  this._overflow = overflow\n  this._onparse()\n}\n\nExtract.prototype._final = function (cb) {\n  if (this._partial) return this.destroy(new Error('Unexpected end of data'))\n  cb()\n}\n\nmodule.exports = Extract\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tar-stream/extract.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tar-stream/headers.js":
/*!********************************************!*\
  !*** ./node_modules/tar-stream/headers.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("var alloc = Buffer.alloc\n\nvar ZEROS = '0000000000000000000'\nvar SEVENS = '7777777777777777777'\nvar ZERO_OFFSET = '0'.charCodeAt(0)\nvar USTAR_MAGIC = Buffer.from('ustar\\x00', 'binary')\nvar USTAR_VER = Buffer.from('00', 'binary')\nvar GNU_MAGIC = Buffer.from('ustar\\x20', 'binary')\nvar GNU_VER = Buffer.from('\\x20\\x00', 'binary')\nvar MASK = parseInt('7777', 8)\nvar MAGIC_OFFSET = 257\nvar VERSION_OFFSET = 263\n\nvar clamp = function (index, len, defaultValue) {\n  if (typeof index !== 'number') return defaultValue\n  index = ~~index // Coerce to integer.\n  if (index >= len) return len\n  if (index >= 0) return index\n  index += len\n  if (index >= 0) return index\n  return 0\n}\n\nvar toType = function (flag) {\n  switch (flag) {\n    case 0:\n      return 'file'\n    case 1:\n      return 'link'\n    case 2:\n      return 'symlink'\n    case 3:\n      return 'character-device'\n    case 4:\n      return 'block-device'\n    case 5:\n      return 'directory'\n    case 6:\n      return 'fifo'\n    case 7:\n      return 'contiguous-file'\n    case 72:\n      return 'pax-header'\n    case 55:\n      return 'pax-global-header'\n    case 27:\n      return 'gnu-long-link-path'\n    case 28:\n    case 30:\n      return 'gnu-long-path'\n  }\n\n  return null\n}\n\nvar toTypeflag = function (flag) {\n  switch (flag) {\n    case 'file':\n      return 0\n    case 'link':\n      return 1\n    case 'symlink':\n      return 2\n    case 'character-device':\n      return 3\n    case 'block-device':\n      return 4\n    case 'directory':\n      return 5\n    case 'fifo':\n      return 6\n    case 'contiguous-file':\n      return 7\n    case 'pax-header':\n      return 72\n  }\n\n  return 0\n}\n\nvar indexOf = function (block, num, offset, end) {\n  for (; offset < end; offset++) {\n    if (block[offset] === num) return offset\n  }\n  return end\n}\n\nvar cksum = function (block) {\n  var sum = 8 * 32\n  for (var i = 0; i < 148; i++) sum += block[i]\n  for (var j = 156; j < 512; j++) sum += block[j]\n  return sum\n}\n\nvar encodeOct = function (val, n) {\n  val = val.toString(8)\n  if (val.length > n) return SEVENS.slice(0, n) + ' '\n  else return ZEROS.slice(0, n - val.length) + val + ' '\n}\n\n/* Copied from the node-tar repo and modified to meet\n * tar-stream coding standard.\n *\n * Source: https://github.com/npm/node-tar/blob/51b6627a1f357d2eb433e7378e5f05e83b7aa6cd/lib/header.js#L349\n */\nfunction parse256 (buf) {\n  // first byte MUST be either 80 or FF\n  // 80 for positive, FF for 2's comp\n  var positive\n  if (buf[0] === 0x80) positive = true\n  else if (buf[0] === 0xFF) positive = false\n  else return null\n\n  // build up a base-256 tuple from the least sig to the highest\n  var tuple = []\n  for (var i = buf.length - 1; i > 0; i--) {\n    var byte = buf[i]\n    if (positive) tuple.push(byte)\n    else tuple.push(0xFF - byte)\n  }\n\n  var sum = 0\n  var l = tuple.length\n  for (i = 0; i < l; i++) {\n    sum += tuple[i] * Math.pow(256, i)\n  }\n\n  return positive ? sum : -1 * sum\n}\n\nvar decodeOct = function (val, offset, length) {\n  val = val.slice(offset, offset + length)\n  offset = 0\n\n  // If prefixed with 0x80 then parse as a base-256 integer\n  if (val[offset] & 0x80) {\n    return parse256(val)\n  } else {\n    // Older versions of tar can prefix with spaces\n    while (offset < val.length && val[offset] === 32) offset++\n    var end = clamp(indexOf(val, 32, offset, val.length), val.length, val.length)\n    while (offset < end && val[offset] === 0) offset++\n    if (end === offset) return 0\n    return parseInt(val.slice(offset, end).toString(), 8)\n  }\n}\n\nvar decodeStr = function (val, offset, length, encoding) {\n  return val.slice(offset, indexOf(val, 0, offset, offset + length)).toString(encoding)\n}\n\nvar addLength = function (str) {\n  var len = Buffer.byteLength(str)\n  var digits = Math.floor(Math.log(len) / Math.log(10)) + 1\n  if (len + digits >= Math.pow(10, digits)) digits++\n\n  return (len + digits) + str\n}\n\nexports.decodeLongPath = function (buf, encoding) {\n  return decodeStr(buf, 0, buf.length, encoding)\n}\n\nexports.encodePax = function (opts) { // TODO: encode more stuff in pax\n  var result = ''\n  if (opts.name) result += addLength(' path=' + opts.name + '\\n')\n  if (opts.linkname) result += addLength(' linkpath=' + opts.linkname + '\\n')\n  var pax = opts.pax\n  if (pax) {\n    for (var key in pax) {\n      result += addLength(' ' + key + '=' + pax[key] + '\\n')\n    }\n  }\n  return Buffer.from(result)\n}\n\nexports.decodePax = function (buf) {\n  var result = {}\n\n  while (buf.length) {\n    var i = 0\n    while (i < buf.length && buf[i] !== 32) i++\n    var len = parseInt(buf.slice(0, i).toString(), 10)\n    if (!len) return result\n\n    var b = buf.slice(i + 1, len - 1).toString()\n    var keyIndex = b.indexOf('=')\n    if (keyIndex === -1) return result\n    result[b.slice(0, keyIndex)] = b.slice(keyIndex + 1)\n\n    buf = buf.slice(len)\n  }\n\n  return result\n}\n\nexports.encode = function (opts) {\n  var buf = alloc(512)\n  var name = opts.name\n  var prefix = ''\n\n  if (opts.typeflag === 5 && name[name.length - 1] !== '/') name += '/'\n  if (Buffer.byteLength(name) !== name.length) return null // utf-8\n\n  while (Buffer.byteLength(name) > 100) {\n    var i = name.indexOf('/')\n    if (i === -1) return null\n    prefix += prefix ? '/' + name.slice(0, i) : name.slice(0, i)\n    name = name.slice(i + 1)\n  }\n\n  if (Buffer.byteLength(name) > 100 || Buffer.byteLength(prefix) > 155) return null\n  if (opts.linkname && Buffer.byteLength(opts.linkname) > 100) return null\n\n  buf.write(name)\n  buf.write(encodeOct(opts.mode & MASK, 6), 100)\n  buf.write(encodeOct(opts.uid, 6), 108)\n  buf.write(encodeOct(opts.gid, 6), 116)\n  buf.write(encodeOct(opts.size, 11), 124)\n  buf.write(encodeOct((opts.mtime.getTime() / 1000) | 0, 11), 136)\n\n  buf[156] = ZERO_OFFSET + toTypeflag(opts.type)\n\n  if (opts.linkname) buf.write(opts.linkname, 157)\n\n  USTAR_MAGIC.copy(buf, MAGIC_OFFSET)\n  USTAR_VER.copy(buf, VERSION_OFFSET)\n  if (opts.uname) buf.write(opts.uname, 265)\n  if (opts.gname) buf.write(opts.gname, 297)\n  buf.write(encodeOct(opts.devmajor || 0, 6), 329)\n  buf.write(encodeOct(opts.devminor || 0, 6), 337)\n\n  if (prefix) buf.write(prefix, 345)\n\n  buf.write(encodeOct(cksum(buf), 6), 148)\n\n  return buf\n}\n\nexports.decode = function (buf, filenameEncoding, allowUnknownFormat) {\n  var typeflag = buf[156] === 0 ? 0 : buf[156] - ZERO_OFFSET\n\n  var name = decodeStr(buf, 0, 100, filenameEncoding)\n  var mode = decodeOct(buf, 100, 8)\n  var uid = decodeOct(buf, 108, 8)\n  var gid = decodeOct(buf, 116, 8)\n  var size = decodeOct(buf, 124, 12)\n  var mtime = decodeOct(buf, 136, 12)\n  var type = toType(typeflag)\n  var linkname = buf[157] === 0 ? null : decodeStr(buf, 157, 100, filenameEncoding)\n  var uname = decodeStr(buf, 265, 32)\n  var gname = decodeStr(buf, 297, 32)\n  var devmajor = decodeOct(buf, 329, 8)\n  var devminor = decodeOct(buf, 337, 8)\n\n  var c = cksum(buf)\n\n  // checksum is still initial value if header was null.\n  if (c === 8 * 32) return null\n\n  // valid checksum\n  if (c !== decodeOct(buf, 148, 8)) throw new Error('Invalid tar header. Maybe the tar is corrupted or it needs to be gunzipped?')\n\n  if (USTAR_MAGIC.compare(buf, MAGIC_OFFSET, MAGIC_OFFSET + 6) === 0) {\n    // ustar (posix) format.\n    // prepend prefix, if present.\n    if (buf[345]) name = decodeStr(buf, 345, 155, filenameEncoding) + '/' + name\n  } else if (GNU_MAGIC.compare(buf, MAGIC_OFFSET, MAGIC_OFFSET + 6) === 0 &&\n             GNU_VER.compare(buf, VERSION_OFFSET, VERSION_OFFSET + 2) === 0) {\n    // 'gnu'/'oldgnu' format. Similar to ustar, but has support for incremental and\n    // multi-volume tarballs.\n  } else {\n    if (!allowUnknownFormat) {\n      throw new Error('Invalid tar header: unknown format.')\n    }\n  }\n\n  // to support old tar versions that use trailing / to indicate dirs\n  if (typeflag === 0 && name && name[name.length - 1] === '/') typeflag = 5\n\n  return {\n    name,\n    mode,\n    uid,\n    gid,\n    size,\n    mtime: new Date(1000 * mtime),\n    type,\n    linkname,\n    uname,\n    gname,\n    devmajor,\n    devminor\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tar-stream/headers.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tar-stream/index.js":
/*!******************************************!*\
  !*** ./node_modules/tar-stream/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.extract = __webpack_require__(/*! ./extract */ \"(rsc)/./node_modules/tar-stream/extract.js\")\nexports.pack = __webpack_require__(/*! ./pack */ \"(rsc)/./node_modules/tar-stream/pack.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdGFyLXN0cmVhbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxvR0FBc0M7QUFDdEMsMkZBQWdDIiwic291cmNlcyI6WyJEOlxcRG9jIGRhdGFiYXNlXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFx0YXItc3RyZWFtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnRzLmV4dHJhY3QgPSByZXF1aXJlKCcuL2V4dHJhY3QnKVxuZXhwb3J0cy5wYWNrID0gcmVxdWlyZSgnLi9wYWNrJylcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tar-stream/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/tar-stream/pack.js":
/*!*****************************************!*\
  !*** ./node_modules/tar-stream/pack.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var constants = __webpack_require__(/*! fs-constants */ \"(rsc)/./node_modules/fs-constants/index.js\")\nvar eos = __webpack_require__(/*! end-of-stream */ \"(rsc)/./node_modules/end-of-stream/index.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/inherits/inherits.js\")\nvar alloc = Buffer.alloc\n\nvar Readable = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\").Readable)\nvar Writable = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\").Writable)\nvar StringDecoder = (__webpack_require__(/*! string_decoder */ \"string_decoder\").StringDecoder)\n\nvar headers = __webpack_require__(/*! ./headers */ \"(rsc)/./node_modules/tar-stream/headers.js\")\n\nvar DMODE = parseInt('755', 8)\nvar FMODE = parseInt('644', 8)\n\nvar END_OF_TAR = alloc(1024)\n\nvar noop = function () {}\n\nvar overflow = function (self, size) {\n  size &= 511\n  if (size) self.push(END_OF_TAR.slice(0, 512 - size))\n}\n\nfunction modeToType (mode) {\n  switch (mode & constants.S_IFMT) {\n    case constants.S_IFBLK: return 'block-device'\n    case constants.S_IFCHR: return 'character-device'\n    case constants.S_IFDIR: return 'directory'\n    case constants.S_IFIFO: return 'fifo'\n    case constants.S_IFLNK: return 'symlink'\n  }\n\n  return 'file'\n}\n\nvar Sink = function (to) {\n  Writable.call(this)\n  this.written = 0\n  this._to = to\n  this._destroyed = false\n}\n\ninherits(Sink, Writable)\n\nSink.prototype._write = function (data, enc, cb) {\n  this.written += data.length\n  if (this._to.push(data)) return cb()\n  this._to._drain = cb\n}\n\nSink.prototype.destroy = function () {\n  if (this._destroyed) return\n  this._destroyed = true\n  this.emit('close')\n}\n\nvar LinkSink = function () {\n  Writable.call(this)\n  this.linkname = ''\n  this._decoder = new StringDecoder('utf-8')\n  this._destroyed = false\n}\n\ninherits(LinkSink, Writable)\n\nLinkSink.prototype._write = function (data, enc, cb) {\n  this.linkname += this._decoder.write(data)\n  cb()\n}\n\nLinkSink.prototype.destroy = function () {\n  if (this._destroyed) return\n  this._destroyed = true\n  this.emit('close')\n}\n\nvar Void = function () {\n  Writable.call(this)\n  this._destroyed = false\n}\n\ninherits(Void, Writable)\n\nVoid.prototype._write = function (data, enc, cb) {\n  cb(new Error('No body allowed for this entry'))\n}\n\nVoid.prototype.destroy = function () {\n  if (this._destroyed) return\n  this._destroyed = true\n  this.emit('close')\n}\n\nvar Pack = function (opts) {\n  if (!(this instanceof Pack)) return new Pack(opts)\n  Readable.call(this, opts)\n\n  this._drain = noop\n  this._finalized = false\n  this._finalizing = false\n  this._destroyed = false\n  this._stream = null\n}\n\ninherits(Pack, Readable)\n\nPack.prototype.entry = function (header, buffer, callback) {\n  if (this._stream) throw new Error('already piping an entry')\n  if (this._finalized || this._destroyed) return\n\n  if (typeof buffer === 'function') {\n    callback = buffer\n    buffer = null\n  }\n\n  if (!callback) callback = noop\n\n  var self = this\n\n  if (!header.size || header.type === 'symlink') header.size = 0\n  if (!header.type) header.type = modeToType(header.mode)\n  if (!header.mode) header.mode = header.type === 'directory' ? DMODE : FMODE\n  if (!header.uid) header.uid = 0\n  if (!header.gid) header.gid = 0\n  if (!header.mtime) header.mtime = new Date()\n\n  if (typeof buffer === 'string') buffer = Buffer.from(buffer)\n  if (Buffer.isBuffer(buffer)) {\n    header.size = buffer.length\n    this._encode(header)\n    var ok = this.push(buffer)\n    overflow(self, header.size)\n    if (ok) process.nextTick(callback)\n    else this._drain = callback\n    return new Void()\n  }\n\n  if (header.type === 'symlink' && !header.linkname) {\n    var linkSink = new LinkSink()\n    eos(linkSink, function (err) {\n      if (err) { // stream was closed\n        self.destroy()\n        return callback(err)\n      }\n\n      header.linkname = linkSink.linkname\n      self._encode(header)\n      callback()\n    })\n\n    return linkSink\n  }\n\n  this._encode(header)\n\n  if (header.type !== 'file' && header.type !== 'contiguous-file') {\n    process.nextTick(callback)\n    return new Void()\n  }\n\n  var sink = new Sink(this)\n\n  this._stream = sink\n\n  eos(sink, function (err) {\n    self._stream = null\n\n    if (err) { // stream was closed\n      self.destroy()\n      return callback(err)\n    }\n\n    if (sink.written !== header.size) { // corrupting tar\n      self.destroy()\n      return callback(new Error('size mismatch'))\n    }\n\n    overflow(self, header.size)\n    if (self._finalizing) self.finalize()\n    callback()\n  })\n\n  return sink\n}\n\nPack.prototype.finalize = function () {\n  if (this._stream) {\n    this._finalizing = true\n    return\n  }\n\n  if (this._finalized) return\n  this._finalized = true\n  this.push(END_OF_TAR)\n  this.push(null)\n}\n\nPack.prototype.destroy = function (err) {\n  if (this._destroyed) return\n  this._destroyed = true\n\n  if (err) this.emit('error', err)\n  this.emit('close')\n  if (this._stream && this._stream.destroy) this._stream.destroy()\n}\n\nPack.prototype._encode = function (header) {\n  if (!header.pax) {\n    var buf = headers.encode(header)\n    if (buf) {\n      this.push(buf)\n      return\n    }\n  }\n  this._encodePax(header)\n}\n\nPack.prototype._encodePax = function (header) {\n  var paxHeader = headers.encodePax({\n    name: header.name,\n    linkname: header.linkname,\n    pax: header.pax\n  })\n\n  var newHeader = {\n    name: 'PaxHeader',\n    mode: header.mode,\n    uid: header.uid,\n    gid: header.gid,\n    size: paxHeader.length,\n    mtime: header.mtime,\n    type: 'pax-header',\n    linkname: header.linkname && 'PaxHeader',\n    uname: header.uname,\n    gname: header.gname,\n    devmajor: header.devmajor,\n    devminor: header.devminor\n  }\n\n  this.push(headers.encode(newHeader))\n  this.push(paxHeader)\n  overflow(this, paxHeader.length)\n\n  newHeader.size = header.size\n  newHeader.type = header.type\n  this.push(headers.encode(newHeader))\n}\n\nPack.prototype._read = function (n) {\n  var drain = this._drain\n  this._drain = noop\n  drain()\n}\n\nmodule.exports = Pack\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/tar-stream/pack.js\n");

/***/ })

};
;