/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/export/route";
exports.ids = ["app/api/export/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport%2Froute&page=%2Fapi%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport%2Froute&page=%2Fapi%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_export_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/export/route.ts */ \"(rsc)/./src/app/api/export/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/export/route\",\n        pathname: \"/api/export\",\n        filename: \"route\",\n        bundlePath: \"app/api/export/route\"\n    },\n    resolvedPagePath: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\export\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_export_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport%2Froute&page=%2Fapi%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/export/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/export/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! exceljs */ \"(rsc)/./node_modules/exceljs/excel.js\");\n/* harmony import */ var exceljs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(exceljs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// تحميل البيانات من الملف\nfunction loadMediaData() {\n    try {\n        // محاولة تحميل من الملف المؤقت أولاً\n        const tempFilePath = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'temp-data.json');\n        console.log(`🔍 البحث عن الملف المؤقت في: ${tempFilePath}`);\n        if (fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(tempFilePath)) {\n            const fileContent = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(tempFilePath, 'utf8');\n            const data = JSON.parse(fileContent);\n            console.log(`📂 تم تحميل ${data.length} مادة من الملف المؤقت`);\n            return data;\n        } else {\n            console.log('📂 الملف المؤقت غير موجود');\n        }\n        console.log('📂 لم يتم العثور على أي ملفات بيانات');\n        return [];\n    } catch (error) {\n        console.error('❌ خطأ في تحميل البيانات:', error);\n        return [];\n    }\n}\n// تحديد لون الخلفية حسب الحالة\nfunction getBackgroundColor(status, expiryDate) {\n    console.log('🎨 تحديد اللون للحالة:', status, 'تاريخ الانتهاء:', expiryDate);\n    // التحقق من انتهاء التاريخ\n    if (expiryDate) {\n        const expiry = new Date(expiryDate);\n        const now = new Date();\n        if (expiry < now) {\n            console.log('🔴 مادة منتهية الصلاحية - لون أحمر');\n            return 'FFFF0000'; // أحمر - منتهي الصلاحية\n        }\n    }\n    // تطبيع الحالة للمقارنة\n    const normalizedStatus = status?.toLowerCase().trim();\n    console.log('📝 الحالة المطبعة:', normalizedStatus);\n    // حسب الحالة\n    if (normalizedStatus === 'rejected' || normalizedStatus === 'مرفوض' || normalizedStatus === 'مرفوض رقابي' || normalizedStatus === 'rejected_censorship') {\n        console.log('🔴 مادة مرفوضة رقابياً - لون أحمر');\n        return 'FFFF0000'; // أحمر - مرفوض\n    } else if (normalizedStatus === 'engineering_rejected' || normalizedStatus === 'رفض هندسي' || normalizedStatus === 'مرفوض هندسي' || normalizedStatus === 'rejected_technical') {\n        console.log('⚫ رفض هندسي - لون أسود');\n        return 'FF000000'; // أسود - رفض هندسي\n    } else if (normalizedStatus === 'pending' || normalizedStatus === 'انتظار' || normalizedStatus === 'في الانتظار' || normalizedStatus === 'waiting') {\n        console.log('🔘 في الانتظار - لون رمادي');\n        return 'FF808080'; // رمادي - انتظار\n    } else {\n        console.log('🟢 مادة موافقة - لون أخضر');\n        return 'FF90EE90'; // أخضر فاتح - موافق (افتراضي)\n    }\n}\n// تحديد لون النص حسب خلفية\nfunction getTextColor(backgroundColor) {\n    if (backgroundColor === 'FF000000') {\n        return 'FFFFFFFF'; // نص أبيض على خلفية سوداء\n    }\n    return 'FF000000'; // نص أسود على باقي الخلفيات\n}\n// تنسيق البيانات للتصدير - كل سيجمنت في صف منفصل\nfunction formatDataForExport(mediaData, mediaType) {\n    const formattedData = [];\n    let segmentCounter = 1;\n    mediaData.forEach((item)=>{\n        const segments = item.segments && item.segments.length > 0 ? item.segments : [\n            {}\n        ];\n        segments.forEach((segment)=>{\n            // البيانات الأساسية المشتركة حسب الترتيب الجديد\n            const baseData = {\n                'كود السيجمنت': segment?.code || `SEG${segmentCounter.toString().padStart(3, '0')}`,\n                'نوع المادة': getTypeLabel(item.type) || '',\n                'اسم المادة': item.name || '',\n                'URL': item.source || '',\n                'رقم الهارد': item.hardNumber || '',\n                'Dur': segment?.duration || '00:00:00',\n                'TC Out': segment?.timeOut || '00:00:00',\n                'TC In': segment?.timeIn || '00:00:00'\n            };\n            // إضافة أعمدة خاصة حسب نوع المادة\n            let specificColumns = {};\n            if (mediaType === 'MOVIE') {\n                // للأفلام: رقم الجزء بدلاً من رقم الحلقة\n                specificColumns = {\n                    'رقم الجزء': item.partNumber || item.episodeNumber || ''\n                };\n            } else if (mediaType === 'SERIES') {\n                // للمسلسلات: رقم الحلقة + رقم الجزء\n                specificColumns = {\n                    'رقم الحلقة': item.episodeNumber || '',\n                    'رقم الجزء': item.partNumber || ''\n                };\n            } else if (mediaType === 'PROGRAM') {\n                // للبرامج: رقم الحلقة + الموسم\n                specificColumns = {\n                    'رقم الحلقة': item.episodeNumber || '',\n                    'الموسم': item.season || ''\n                };\n            } else {\n                // للأنواع الأخرى: رقم الحلقة فقط\n                specificColumns = {\n                    'رقم الحلقة': item.episodeNumber || ''\n                };\n            }\n            // البيانات النهائية\n            const finalData = {\n                ...baseData,\n                ...specificColumns,\n                'الملاحظات': item.notes || item.description || '',\n                'الحالة': getStatusLabel(item.status) || 'موافق',\n                'تاريخ البداية': item.startDate || '',\n                'تاريخ الانتهاء': item.endDate || ''\n            };\n            formattedData.push(finalData);\n            segmentCounter++;\n        });\n    });\n    return formattedData;\n}\n// دوال مساعدة للتسميات\nfunction getTypeLabel(type) {\n    const types = {\n        PROGRAM: 'برنامج',\n        SERIES: 'مسلسل',\n        MOVIE: 'فيلم',\n        SONG: 'أغنية',\n        STING: 'Sting',\n        FILL_IN: 'Fill IN',\n        FILLER: 'Filler',\n        PROMO: 'Promo'\n    };\n    return types[type] || type;\n}\nfunction getStatusLabel(status) {\n    const statuses = {\n        VALID: 'موافق',\n        REJECTED_CENSORSHIP: 'مرفوض رقابي',\n        REJECTED_TECHNICAL: 'مرفوض هندسي',\n        WAITING: 'في الانتظار'\n    };\n    return statuses[status] || status;\n}\nfunction getChannelLabel(channel) {\n    const channels = {\n        DOCUMENTARY: 'الوثائقية',\n        NEWS: 'الأخبار',\n        OTHER: 'أخرى'\n    };\n    return channels[channel] || channel;\n}\nasync function GET() {\n    try {\n        console.log('📊 بدء عملية تصدير قاعدة البيانات...');\n        // تحميل البيانات\n        const mediaData = loadMediaData();\n        if (mediaData.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا توجد بيانات للتصدير'\n            });\n        }\n        // تجميع البيانات حسب النوع\n        const groupedData = {};\n        mediaData.forEach((item)=>{\n            const type = item.type || 'غير محدد';\n            if (!groupedData[type]) {\n                groupedData[type] = [];\n            }\n            groupedData[type].push(item);\n        });\n        console.log('📋 أنواع المواد المكتشفة:', Object.keys(groupedData));\n        // إنشاء ملف Excel باستخدام ExcelJS\n        const workbook = new (exceljs__WEBPACK_IMPORTED_MODULE_1___default().Workbook)();\n        // تعيين خصائص الملف\n        workbook.creator = 'نظام إدارة المواد الإعلامية';\n        workbook.title = 'قاعدة بيانات المواد الإعلامية';\n        workbook.subject = 'تصدير شامل لجميع المواد';\n        workbook.created = new Date();\n        // إضافة ورقة عمل لكل نوع مادة\n        for (const type of Object.keys(groupedData)){\n            const typeData = groupedData[type];\n            const formattedData = formatDataForExport(typeData, type);\n            // إنشاء ورقة عمل\n            const worksheet = workbook.addWorksheet(getTypeLabel(type));\n            // تعيين اتجاه الورقة من اليمين لليسار\n            worksheet.views = [\n                {\n                    rightToLeft: true,\n                    zoomScale: 70\n                }\n            ];\n            // إضافة الرؤوس حسب الترتيب الجديد من الصورة\n            let headers = [\n                'كود السيجمنت',\n                'نوع المادة',\n                'اسم المادة',\n                'URL',\n                'رقم الهارد',\n                'Dur',\n                'TC Out',\n                'TC In'\n            ];\n            // إضافة أعمدة خاصة حسب نوع المادة\n            if (type === 'MOVIE') {\n                headers.push('رقم الجزء');\n            } else if (type === 'SERIES') {\n                headers.push('رقم الحلقة', 'رقم الجزء');\n            } else if (type === 'PROGRAM') {\n                headers.push('رقم الحلقة', 'الموسم');\n            } else {\n                headers.push('رقم الحلقة');\n            }\n            // إضافة باقي الأعمدة\n            headers.push('الملاحظات', 'الحالة', 'تاريخ البداية', 'تاريخ الانتهاء');\n            worksheet.addRow(headers);\n            // تنسيق الرؤوس\n            const headerRow = worksheet.getRow(1);\n            headerRow.height = 25;\n            headerRow.eachCell((cell)=>{\n                cell.fill = {\n                    type: 'pattern',\n                    pattern: 'solid',\n                    fgColor: {\n                        argb: 'FF4472C4'\n                    }\n                };\n                cell.font = {\n                    bold: true,\n                    color: {\n                        argb: 'FFFFFFFF'\n                    },\n                    size: 12\n                };\n                cell.alignment = {\n                    horizontal: 'center',\n                    vertical: 'middle'\n                };\n                cell.border = {\n                    top: {\n                        style: 'thin'\n                    },\n                    left: {\n                        style: 'thin'\n                    },\n                    bottom: {\n                        style: 'thin'\n                    },\n                    right: {\n                        style: 'thin'\n                    }\n                };\n            });\n            // إضافة البيانات\n            formattedData.forEach((item)=>{\n                // إنشاء صف البيانات الأساسية حسب الترتيب الجديد\n                let rowData = [\n                    item['كود السيجمنت'],\n                    item['نوع المادة'],\n                    item['اسم المادة'],\n                    item['URL'],\n                    item['رقم الهارد'],\n                    item['Dur'],\n                    item['TC Out'],\n                    item['TC In']\n                ];\n                // إضافة الأعمدة الخاصة حسب نوع المادة\n                if (type === 'MOVIE') {\n                    rowData.push(item['رقم الجزء'] || '');\n                } else if (type === 'SERIES') {\n                    rowData.push(item['رقم الحلقة'] || '', item['رقم الجزء'] || '');\n                } else if (type === 'PROGRAM') {\n                    rowData.push(item['رقم الحلقة'] || '', item['الموسم'] || '');\n                } else {\n                    rowData.push(item['رقم الحلقة'] || '');\n                }\n                // إضافة باقي البيانات\n                rowData.push(item['الملاحظات'], item['الحالة'], item['تاريخ البداية'], item['تاريخ الانتهاء']);\n                const row = worksheet.addRow(rowData);\n                // تنسيق البيانات\n                const backgroundColor = getBackgroundColor(item['الحالة'], item['تاريخ الانتهاء']);\n                const textColor = getTextColor(backgroundColor);\n                row.eachCell((cell)=>{\n                    cell.fill = {\n                        type: 'pattern',\n                        pattern: 'solid',\n                        fgColor: {\n                            argb: backgroundColor\n                        }\n                    };\n                    cell.font = {\n                        color: {\n                            argb: textColor\n                        },\n                        size: 10\n                    };\n                    cell.alignment = {\n                        horizontal: 'center',\n                        vertical: 'middle'\n                    };\n                    cell.border = {\n                        top: {\n                            style: 'thin'\n                        },\n                        left: {\n                            style: 'thin'\n                        },\n                        bottom: {\n                            style: 'thin'\n                        },\n                        right: {\n                            style: 'thin'\n                        }\n                    };\n                });\n            });\n            // تعيين عرض الأعمدة حسب الترتيب الجديد\n            let columnWidths = [\n                15,\n                12,\n                25,\n                20,\n                12,\n                10,\n                10,\n                10\n            ]; // الأعمدة الأساسية\n            // إضافة عرض للأعمدة الخاصة\n            if (type === 'MOVIE') {\n                columnWidths.push(12); // رقم الجزء\n            } else if (type === 'SERIES') {\n                columnWidths.push(12, 12); // رقم الحلقة، رقم الجزء\n            } else if (type === 'PROGRAM') {\n                columnWidths.push(12, 12); // رقم الحلقة، الموسم\n            } else {\n                columnWidths.push(12); // رقم الحلقة\n            }\n            // إضافة عرض باقي الأعمدة\n            columnWidths.push(20, 12, 15, 15); // الملاحظات، الحالة، تاريخ البداية، تاريخ الانتهاء\n            columnWidths.forEach((width, index)=>{\n                worksheet.getColumn(index + 1).width = width;\n            });\n            console.log(`✅ تم إنشاء تاب \"${type}\" مع ${typeData.length} مادة`);\n        }\n        // تحويل إلى buffer\n        const buffer = await workbook.xlsx.writeBuffer();\n        console.log('✅ تم إنشاء ملف Excel بنجاح');\n        // إرسال الملف\n        const fileName = `Media_Database_${new Date().toISOString().split('T')[0]}.xlsx`;\n        return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(buffer, {\n            status: 200,\n            headers: {\n                'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n                'Content-Disposition': `attachment; filename=\"${fileName}\"`,\n                'Content-Length': buffer.byteLength.toString(),\n                'Cache-Control': 'no-cache'\n            }\n        });\n    } catch (error) {\n        console.error('❌ خطأ في تصدير البيانات:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تصدير البيانات'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/export/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "constants":
/*!****************************!*\
  !*** external "constants" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("constants");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "rimraf":
/*!*************************!*\
  !*** external "rimraf" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("rimraf");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/exceljs","vendor-chunks/jszip","vendor-chunks/bluebird","vendor-chunks/unzipper","vendor-chunks/@fast-csv","vendor-chunks/pako","vendor-chunks/fstream","vendor-chunks/uuid","vendor-chunks/readable-stream","vendor-chunks/lazystream","vendor-chunks/archiver-utils","vendor-chunks/duplexer2","vendor-chunks/compress-commons","vendor-chunks/archiver","vendor-chunks/tar-stream","vendor-chunks/readdir-glob","vendor-chunks/graceful-fs","vendor-chunks/zip-stream","vendor-chunks/xmlchars","vendor-chunks/glob","vendor-chunks/dayjs","vendor-chunks/crc32-stream","vendor-chunks/inherits","vendor-chunks/fs.realpath","vendor-chunks/buffer-indexof-polyfill","vendor-chunks/bl","vendor-chunks/binary","vendor-chunks/async","vendor-chunks/wrappy","vendor-chunks/util-deprecate","vendor-chunks/traverse","vendor-chunks/tmp","vendor-chunks/string_decoder","vendor-chunks/saxes","vendor-chunks/safe-buffer","vendor-chunks/process-nextick-args","vendor-chunks/path-is-absolute","vendor-chunks/once","vendor-chunks/normalize-path","vendor-chunks/minimatch","vendor-chunks/lodash.uniq","vendor-chunks/lodash.union","vendor-chunks/lodash.isundefined","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnil","vendor-chunks/lodash.isfunction","vendor-chunks/lodash.isequal","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.groupby","vendor-chunks/lodash.flatten","vendor-chunks/lodash.escaperegexp","vendor-chunks/lodash.difference","vendor-chunks/lodash.defaults","vendor-chunks/listenercount","vendor-chunks/lie","vendor-chunks/inflight","vendor-chunks/immediate","vendor-chunks/fs-constants","vendor-chunks/fast-csv","vendor-chunks/end-of-stream","vendor-chunks/crc-32","vendor-chunks/core-util-is","vendor-chunks/concat-map","vendor-chunks/chainsaw","vendor-chunks/buffers","vendor-chunks/buffer-crc32","vendor-chunks/brace-expansion","vendor-chunks/big-integer","vendor-chunks/balanced-match"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fexport%2Froute&page=%2Fapi%2Fexport%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fexport%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();