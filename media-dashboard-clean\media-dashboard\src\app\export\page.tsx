'use client';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function ExportPage() {
  const router = useRouter();
  const [selectedFormat, setSelectedFormat] = useState('excel');
  const [selectedData, setSelectedData] = useState('all');
  const [isExporting, setIsExporting] = useState(false);

  const exportFormats = [
    { value: 'excel', label: 'Excel (.xlsx)', icon: '📊', description: 'ملف Excel مع دعم RTL' },
    { value: 'pdf', label: 'PDF', icon: '📄', description: 'ملف PDF للطباعة' },
    { value: 'csv', label: 'CSV', icon: '📋', description: 'ملف CSV للبيانات' },
    { value: 'json', label: 'JSON', icon: '🔧', description: 'ملف JSON للمطورين' },
  ];

  const dataTypes = [
    { value: 'all', label: 'جميع البيانات', description: 'تصدير كامل للنظام' },
    { value: 'media', label: 'المواد الإعلامية', description: 'قائمة المواد فقط' },
    { value: 'schedule', label: 'الخريطة البرامجية', description: 'الجدول الأسبوعي' },
    { value: 'playlist', label: 'جدول الإذاعة', description: 'الجدول اليومي' },
  ];

  const handleExport = async () => {
    setIsExporting(true);

    // محاكاة عملية التصدير
    setTimeout(() => {
      setIsExporting(false);
      alert(`تم تصدير ${dataTypes.find(d => d.value === selectedData)?.label} بصيغة ${exportFormats.find(f => f.value === selectedFormat)?.label} بنجاح!`);
    }, 3000);
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        maxWidth: '800px',
        margin: '0 auto',
        background: 'white',
        borderRadius: '20px',
        padding: '40px',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '30px' }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            color: '#333',
            margin: 0,
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            📤 تصدير البيانات
          </h1>
          <button
            onClick={() => router.push('/')}
            style={{
              background: 'linear-gradient(45deg, #6c757d, #495057)',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '10px 20px',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            🏠 العودة للرئيسية
          </button>
        </div>

        <p style={{ color: '#666', marginBottom: '30px', textAlign: 'center' }}>
          اختر نوع البيانات وصيغة التصدير المطلوبة
        </p>

        {/* اختيار نوع البيانات */}
        <div style={{
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          borderRadius: '15px',
          padding: '25px',
          marginBottom: '25px',
          border: '1px solid #dee2e6'
        }}>
          <h2 style={{ color: '#495057', marginBottom: '20px', fontSize: '1.3rem' }}>📋 نوع البيانات</h2>

          <div style={{ display: 'grid', gap: '15px' }}>
            {dataTypes.map((type) => (
              <div key={type.value} style={{
                background: selectedData === type.value ? '#e3f2fd' : 'white',
                border: `2px solid ${selectedData === type.value ? '#1976d2' : '#dee2e6'}`,
                borderRadius: '10px',
                padding: '15px',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onClick={() => setSelectedData(type.value)}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                  <input
                    type="radio"
                    name="dataType"
                    value={type.value}
                    checked={selectedData === type.value}
                    onChange={() => setSelectedData(type.value)}
                    style={{ marginLeft: '10px' }}
                  />
                  <div>
                    <h4 style={{ margin: '0 0 5px 0', color: '#333' }}>{type.label}</h4>
                    <p style={{ margin: 0, color: '#666', fontSize: '0.9rem' }}>{type.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* اختيار صيغة التصدير */}
        <div style={{
          background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
          borderRadius: '15px',
          padding: '25px',
          marginBottom: '25px',
          border: '1px solid #90caf9'
        }}>
          <h2 style={{ color: '#1565c0', marginBottom: '20px', fontSize: '1.3rem' }}>📊 صيغة التصدير</h2>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            {exportFormats.map((format) => (
              <div key={format.value} style={{
                background: selectedFormat === format.value ? '#1976d2' : 'white',
                color: selectedFormat === format.value ? 'white' : '#333',
                border: `2px solid ${selectedFormat === format.value ? '#1976d2' : '#dee2e6'}`,
                borderRadius: '10px',
                padding: '20px',
                cursor: 'pointer',
                textAlign: 'center',
                transition: 'all 0.3s ease'
              }}
              onClick={() => setSelectedFormat(format.value)}
              >
                <div style={{ fontSize: '2rem', marginBottom: '10px' }}>{format.icon}</div>
                <h4 style={{ margin: '0 0 5px 0' }}>{format.label}</h4>
                <p style={{ margin: 0, fontSize: '0.8rem', opacity: 0.8 }}>{format.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* معاينة التصدير */}
        <div style={{
          background: 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)',
          borderRadius: '15px',
          padding: '25px',
          marginBottom: '25px',
          border: '1px solid #ffeaa7'
        }}>
          <h2 style={{ color: '#856404', marginBottom: '20px', fontSize: '1.3rem' }}>👁️ معاينة التصدير</h2>

          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '20px' }}>
            <div>
              <p style={{ margin: '0 0 5px 0', color: '#856404' }}>
                <strong>نوع البيانات:</strong> {dataTypes.find(d => d.value === selectedData)?.label}
              </p>
              <p style={{ margin: '0 0 5px 0', color: '#856404' }}>
                <strong>صيغة التصدير:</strong> {exportFormats.find(f => f.value === selectedFormat)?.label}
              </p>
              <p style={{ margin: 0, color: '#856404' }}>
                <strong>التاريخ:</strong> {new Date().toLocaleDateString('ar-SA')}
              </p>
            </div>
            <div style={{ fontSize: '3rem' }}>
              {exportFormats.find(f => f.value === selectedFormat)?.icon}
            </div>
          </div>
        </div>

        {/* شريط التقدم */}
        {isExporting && (
          <div style={{
            background: 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #c3e6cb',
            textAlign: 'center'
          }}>
            <h3 style={{ color: '#155724', marginBottom: '15px' }}>⏳ جاري التصدير...</h3>
            <div style={{
              width: '100%',
              height: '10px',
              background: '#f8f9fa',
              borderRadius: '5px',
              overflow: 'hidden',
              marginBottom: '10px'
            }}>
              <div style={{
                width: '100%',
                height: '100%',
                background: 'linear-gradient(45deg, #28a745, #20c997)',
                animation: 'progress 3s ease-in-out'
              }}></div>
            </div>
            <p style={{ color: '#155724', margin: 0 }}>يرجى الانتظار...</p>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button
            onClick={handleExport}
            disabled={isExporting}
            style={{
              background: isExporting ? '#6c757d' : 'linear-gradient(45deg, #28a745, #20c997)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 40px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              cursor: isExporting ? 'not-allowed' : 'pointer',
              boxShadow: '0 4px 15px rgba(40,167,69,0.3)',
              opacity: isExporting ? 0.7 : 1
            }}
          >
            {isExporting ? '⏳ جاري التصدير...' : '📤 بدء التصدير'}
          </button>

          <button
            onClick={() => alert('سيتم إضافة ميزة الجدولة قريباً')}
            style={{
              background: 'linear-gradient(45deg, #007bff, #0056b3)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 40px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(0,123,255,0.3)',
            }}
          >
            ⏰ جدولة التصدير
          </button>
        </div>

        {/* معلومات إضافية */}
        <div style={{
          marginTop: '30px',
          padding: '20px',
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          borderRadius: '15px',
          border: '1px solid #dee2e6',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#495057', marginBottom: '10px' }}>ℹ️ معلومات مهمة</h3>
          <div style={{ color: '#6c757d', lineHeight: '1.6' }}>
            <p style={{ margin: '5px 0' }}>• ملفات Excel تدعم اتجاه RTL للنصوص العربية</p>
            <p style={{ margin: '5px 0' }}>• ملفات PDF محسنة للطباعة مع تنسيق عربي</p>
            <p style={{ margin: '5px 0' }}>• يمكن جدولة التصدير التلقائي يومياً أو أسبوعياً</p>
            <p style={{ margin: '5px 0' }}>• جميع الملفات المصدرة تحتوي على طابع زمني</p>
          </div>
        </div>
      </div>
    </div>
  );
}