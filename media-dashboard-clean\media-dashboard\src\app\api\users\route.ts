import { NextRequest, NextResponse } from 'next/server';

// تعريف الأدوار والصلاحيات
export const ROLES = {
  ADMIN: {
    name: 'مدير النظام',
    permissions: ['ALL'],
    description: 'صلاحيات كاملة لجميع أجزاء النظام'
  },
  MEDIA_MANAGER: {
    name: 'مدير المحتوى',
    permissions: ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE'],
    description: 'إدارة المواد الإعلامية (إضافة، تعديل، حذف)'
  },
  SCHEDULER: {
    name: 'مجدول البرامج',
    permissions: ['SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', 'MEDIA_READ'],
    description: 'إدارة الجداول الإذاعية والخريطة البرامجية'
  },
  VIEWER: {
    name: 'مستخدم عرض',
    permissions: ['MEDIA_READ', 'SCHEDULE_READ'],
    description: 'عرض المحتوى فقط بدون إمكانية التعديل'
  }
};

// بيانات المستخدمين المؤقتة
let users = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123',
    name: 'مدير النظام الرئيسي',
    email: '<EMAIL>',
    role: 'ADMIN',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '2',
    username: 'media_manager',
    password: 'media123',
    name: 'أحمد محمد - مدير المحتوى',
    email: '<EMAIL>',
    role: 'MEDIA_MANAGER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '3',
    username: 'scheduler',
    password: 'schedule123',
    name: 'فاطمة علي - مجدولة البرامج',
    email: '<EMAIL>',
    role: 'SCHEDULER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  },
  {
    id: '4',
    username: 'viewer',
    password: 'view123',
    name: 'محمد سالم - مستخدم عرض',
    email: '<EMAIL>',
    role: 'VIEWER',
    isActive: true,
    createdAt: new Date().toISOString(),
    lastLogin: null
  }
];

// GET - الحصول على قائمة المستخدمين
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const role = url.searchParams.get('role');

    let filteredUsers = users;
    
    if (role) {
      filteredUsers = users.filter(user => user.role === role);
    }

    // إزالة كلمات المرور من النتائج
    const usersWithoutPasswords = filteredUsers.map(user => {
      const { password, ...userWithoutPassword } = user;
      return {
        ...userWithoutPassword,
        roleInfo: ROLES[user.role as keyof typeof ROLES]
      };
    });

    return NextResponse.json({
      success: true,
      users: usersWithoutPasswords,
      roles: ROLES,
      totalUsers: filteredUsers.length
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات المستخدمين'
    }, { status: 500 });
  }
}

// POST - إضافة مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();
    
    const { username, password, name, email, role } = userData;

    // التحقق من البيانات المطلوبة
    if (!username || !password || !name || !role) {
      return NextResponse.json({
        success: false,
        error: 'جميع الحقول مطلوبة'
      }, { status: 400 });
    }

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    if (users.find(user => user.username === username)) {
      return NextResponse.json({
        success: false,
        error: 'اسم المستخدم موجود مسبقاً'
      }, { status: 400 });
    }

    // التحقق من صحة الدور
    if (!ROLES[role as keyof typeof ROLES]) {
      return NextResponse.json({
        success: false,
        error: 'دور المستخدم غير صحيح'
      }, { status: 400 });
    }

    // إنشاء المستخدم الجديد
    const newUser = {
      id: Date.now().toString(),
      username,
      password,
      name,
      email: email || '',
      role,
      isActive: true,
      createdAt: new Date().toISOString(),
      lastLogin: null
    };

    users.push(newUser);

    // إرجاع المستخدم بدون كلمة المرور
    const { password: _, ...userWithoutPassword } = newUser;

    return NextResponse.json({
      success: true,
      user: {
        ...userWithoutPassword,
        roleInfo: ROLES[role as keyof typeof ROLES]
      },
      message: 'تم إنشاء المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء المستخدم'
    }, { status: 500 });
  }
}

// PUT - تحديث مستخدم
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('id');
    const userData = await request.json();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'معرف المستخدم مطلوب'
      }, { status: 400 });
    }

    const userIndex = users.findIndex(user => user.id === userId);
    
    if (userIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'المستخدم غير موجود'
      }, { status: 404 });
    }

    // تحديث بيانات المستخدم
    users[userIndex] = {
      ...users[userIndex],
      ...userData,
      id: userId // التأكد من عدم تغيير المعرف
    };

    const { password: _, ...userWithoutPassword } = users[userIndex];

    return NextResponse.json({
      success: true,
      user: {
        ...userWithoutPassword,
        roleInfo: ROLES[users[userIndex].role as keyof typeof ROLES]
      },
      message: 'تم تحديث المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث المستخدم'
    }, { status: 500 });
  }
}

// DELETE - حذف مستخدم
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('id');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'معرف المستخدم مطلوب'
      }, { status: 400 });
    }

    const userIndex = users.findIndex(user => user.id === userId);
    
    if (userIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'المستخدم غير موجود'
      }, { status: 404 });
    }

    // منع حذف المدير الرئيسي
    if (users[userIndex].role === 'ADMIN' && users[userIndex].id === '1') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف المدير الرئيسي'
      }, { status: 403 });
    }

    users.splice(userIndex, 1);

    return NextResponse.json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في حذف المستخدم'
    }, { status: 500 });
  }
}
