import { NextRequest, NextResponse } from 'next/server';
import {
  getAllUsers,
  getUserById,
  getUserByUsername,
  addUser,
  updateUser,
  deleteUser,
  ROLES
} from '../shared-users';

// GET - الحصول على قائمة المستخدمين
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const role = url.searchParams.get('role');

    let filteredUsers = getAllUsers();

    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role);
    }

    // إزالة كلمات المرور من النتائج
    const usersWithoutPasswords = filteredUsers.map(user => {
      const { password, ...userWithoutPassword } = user;
      return {
        ...userWithoutPassword,
        roleInfo: ROLES[user.role as keyof typeof ROLES]
      };
    });

    return NextResponse.json({
      success: true,
      users: usersWithoutPasswords,
      roles: ROLES,
      totalUsers: filteredUsers.length
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات المستخدمين'
    }, { status: 500 });
  }
}

// POST - إضافة مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();

    const { username, password, name, email, role, phone } = userData;

    // التحقق من البيانات المطلوبة
    if (!username || !password || !name || !role) {
      return NextResponse.json({
        success: false,
        error: 'جميع الحقول مطلوبة'
      }, { status: 400 });
    }

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    if (getUserByUsername(username)) {
      return NextResponse.json({
        success: false,
        error: 'اسم المستخدم موجود مسبقاً'
      }, { status: 400 });
    }

    // التحقق من صحة الدور
    if (!ROLES[role as keyof typeof ROLES]) {
      return NextResponse.json({
        success: false,
        error: 'دور المستخدم غير صحيح'
      }, { status: 400 });
    }

    // إنشاء المستخدم الجديد
    const newUser = {
      id: Date.now().toString(),
      username,
      password,
      name,
      email: email || '',
      phone: phone || '',
      role,
      isActive: true,
      createdAt: new Date().toISOString(),
      lastLogin: null
    };

    // إضافة المستخدم باستخدام الملف المشترك
    const success = addUser(newUser);

    if (!success) {
      return NextResponse.json({
        success: false,
        error: 'فشل في إضافة المستخدم'
      }, { status: 500 });
    }

    // إرجاع المستخدم بدون كلمة المرور
    const { password: _, ...userWithoutPassword } = newUser;

    return NextResponse.json({
      success: true,
      user: {
        ...userWithoutPassword,
        roleInfo: ROLES[role as keyof typeof ROLES]
      },
      message: 'تم إنشاء المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء المستخدم'
    }, { status: 500 });
  }
}

// PUT - تحديث مستخدم
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('id');
    const userData = await request.json();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'معرف المستخدم مطلوب'
      }, { status: 400 });
    }

    const existingUser = getUserById(userId);

    if (!existingUser) {
      return NextResponse.json({
        success: false,
        error: 'المستخدم غير موجود'
      }, { status: 404 });
    }

    // تحديث بيانات المستخدم باستخدام الملف المشترك
    const success = updateUser(userId, userData);

    if (!success) {
      return NextResponse.json({
        success: false,
        error: 'فشل في تحديث المستخدم'
      }, { status: 500 });
    }

    // الحصول على المستخدم المحدث
    const updatedUser = getUserById(userId);
    const { password: _, ...userWithoutPassword } = updatedUser;

    return NextResponse.json({
      success: true,
      user: {
        ...userWithoutPassword,
        roleInfo: ROLES[updatedUser.role as keyof typeof ROLES]
      },
      message: 'تم تحديث المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث المستخدم'
    }, { status: 500 });
  }
}

// DELETE - حذف مستخدم
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('id');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'معرف المستخدم مطلوب'
      }, { status: 400 });
    }

    const existingUser = getUserById(userId);

    if (!existingUser) {
      return NextResponse.json({
        success: false,
        error: 'المستخدم غير موجود'
      }, { status: 404 });
    }

    // منع حذف المدير الرئيسي
    if (existingUser.role === 'ADMIN' && existingUser.id === '1') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف المدير الرئيسي'
      }, { status: 403 });
    }

    // حذف المستخدم باستخدام الملف المشترك
    const success = deleteUser(userId);

    if (!success) {
      return NextResponse.json({
        success: false,
        error: 'فشل في حذف المستخدم'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في حذف المستخدم'
    }, { status: 500 });
  }
}
