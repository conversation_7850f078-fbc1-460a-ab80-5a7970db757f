'use client';
import { useState, useEffect } from 'react';

export default function DebugPage() {
  const [status, setStatus] = useState('بدء التحميل...');
  const [data, setData] = useState(null);

  useEffect(() => {
    setStatus('جاري جلب البيانات...');
    
    fetch('/api/weekly-schedule?weekStart=2024-12-15')
      .then(response => {
        setStatus('تم استلام الاستجابة...');
        return response.json();
      })
      .then(result => {
        setStatus('تم تحليل البيانات...');
        setData(result);
      })
      .catch(error => {
        setStatus('خطأ: ' + error.message);
      });
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🔍 صفحة التشخيص</h1>
      <p><strong>الحالة:</strong> {status}</p>
      
      {data && (
        <div>
          <h3>📊 البيانات المستلمة:</h3>
          <pre style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '5px',
            overflow: 'auto',
            maxHeight: '400px'
          }}>
            {JSON.stringify(data, null, 2)}
          </pre>
        </div>
      )}
      
      <div style={{ marginTop: '20px' }}>
        <button 
          onClick={() => window.location.href = '/weekly-schedule'}
          style={{
            padding: '10px 20px',
            background: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          🔄 العودة للخريطة الأسبوعية
        </button>
      </div>
    </div>
  );
}
