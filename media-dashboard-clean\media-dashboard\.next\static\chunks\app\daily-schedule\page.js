/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/daily-schedule/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdaily-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdaily-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/daily-schedule/page.tsx */ \"(app-pages-browser)/./src/app/daily-schedule/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFpbHktc2NoZWR1bGUlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFpSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcRG9jIGRhdGFiYXNlXFxcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcXFxtZWRpYS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYWlseS1zY2hlZHVsZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdaily-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxzR0FBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLGdCQUFnQjtBQUNwQixJQUFJLGNBQWM7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHIiwic291cmNlcyI6WyJEOlxcRG9jIGRhdGFiYXNlXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGNqc1xccmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgUmVhY3RcbiAqIHJlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qc1xuICpcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuXCJ1c2Ugc3RyaWN0XCI7XG5cInByb2R1Y3Rpb25cIiAhPT0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgJiZcbiAgKGZ1bmN0aW9uICgpIHtcbiAgICBmdW5jdGlvbiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSkge1xuICAgICAgaWYgKG51bGwgPT0gdHlwZSkgcmV0dXJuIG51bGw7XG4gICAgICBpZiAoXCJmdW5jdGlvblwiID09PSB0eXBlb2YgdHlwZSlcbiAgICAgICAgcmV0dXJuIHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0VcbiAgICAgICAgICA/IG51bGxcbiAgICAgICAgICA6IHR5cGUuZGlzcGxheU5hbWUgfHwgdHlwZS5uYW1lIHx8IG51bGw7XG4gICAgICBpZiAoXCJzdHJpbmdcIiA9PT0gdHlwZW9mIHR5cGUpIHJldHVybiB0eXBlO1xuICAgICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICAgIGNhc2UgUkVBQ1RfRlJBR01FTlRfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJGcmFnbWVudFwiO1xuICAgICAgICBjYXNlIFJFQUNUX1BST0ZJTEVSX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiUHJvZmlsZXJcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVFJJQ1RfTU9ERV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN0cmljdE1vZGVcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN1c3BlbnNlXCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfU1VTUEVOU0VfTElTVF9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIlN1c3BlbnNlTGlzdFwiO1xuICAgICAgICBjYXNlIFJFQUNUX0FDVElWSVRZX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiQWN0aXZpdHlcIjtcbiAgICAgIH1cbiAgICAgIGlmIChcIm9iamVjdFwiID09PSB0eXBlb2YgdHlwZSlcbiAgICAgICAgc3dpdGNoIChcbiAgICAgICAgICAoXCJudW1iZXJcIiA9PT0gdHlwZW9mIHR5cGUudGFnICYmXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBcIlJlY2VpdmVkIGFuIHVuZXhwZWN0ZWQgb2JqZWN0IGluIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSgpLiBUaGlzIGlzIGxpa2VseSBhIGJ1ZyBpbiBSZWFjdC4gUGxlYXNlIGZpbGUgYW4gaXNzdWUuXCJcbiAgICAgICAgICAgICksXG4gICAgICAgICAgdHlwZS4kJHR5cGVvZilcbiAgICAgICAgKSB7XG4gICAgICAgICAgY2FzZSBSRUFDVF9QT1JUQUxfVFlQRTpcbiAgICAgICAgICAgIHJldHVybiBcIlBvcnRhbFwiO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfQ09OVEVYVF9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuICh0eXBlLmRpc3BsYXlOYW1lIHx8IFwiQ29udGV4dFwiKSArIFwiLlByb3ZpZGVyXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9DT05TVU1FUl9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuICh0eXBlLl9jb250ZXh0LmRpc3BsYXlOYW1lIHx8IFwiQ29udGV4dFwiKSArIFwiLkNvbnN1bWVyXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFOlxuICAgICAgICAgICAgdmFyIGlubmVyVHlwZSA9IHR5cGUucmVuZGVyO1xuICAgICAgICAgICAgdHlwZSA9IHR5cGUuZGlzcGxheU5hbWU7XG4gICAgICAgICAgICB0eXBlIHx8XG4gICAgICAgICAgICAgICgodHlwZSA9IGlubmVyVHlwZS5kaXNwbGF5TmFtZSB8fCBpbm5lclR5cGUubmFtZSB8fCBcIlwiKSxcbiAgICAgICAgICAgICAgKHR5cGUgPSBcIlwiICE9PSB0eXBlID8gXCJGb3J3YXJkUmVmKFwiICsgdHlwZSArIFwiKVwiIDogXCJGb3J3YXJkUmVmXCIpKTtcbiAgICAgICAgICAgIHJldHVybiB0eXBlO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfTUVNT19UWVBFOlxuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgKGlubmVyVHlwZSA9IHR5cGUuZGlzcGxheU5hbWUgfHwgbnVsbCksXG4gICAgICAgICAgICAgIG51bGwgIT09IGlubmVyVHlwZVxuICAgICAgICAgICAgICAgID8gaW5uZXJUeXBlXG4gICAgICAgICAgICAgICAgOiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZS50eXBlKSB8fCBcIk1lbW9cIlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICBjYXNlIFJFQUNUX0xBWllfVFlQRTpcbiAgICAgICAgICAgIGlubmVyVHlwZSA9IHR5cGUuX3BheWxvYWQ7XG4gICAgICAgICAgICB0eXBlID0gdHlwZS5faW5pdDtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIHJldHVybiBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZShpbm5lclR5cGUpKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKHgpIHt9XG4gICAgICAgIH1cbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICBmdW5jdGlvbiB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpIHtcbiAgICAgIHJldHVybiBcIlwiICsgdmFsdWU7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGNoZWNrS2V5U3RyaW5nQ29lcmNpb24odmFsdWUpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSk7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSAhMTtcbiAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gITA7XG4gICAgICB9XG4gICAgICBpZiAoSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0KSB7XG4gICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9IGNvbnNvbGU7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX3RlbXBfY29uc3QgPSBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQuZXJyb3I7XG4gICAgICAgIHZhciBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQkanNjb21wJDAgPVxuICAgICAgICAgIChcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiBTeW1ib2wgJiZcbiAgICAgICAgICAgIFN5bWJvbC50b1N0cmluZ1RhZyAmJlxuICAgICAgICAgICAgdmFsdWVbU3ltYm9sLnRvU3RyaW5nVGFnXSkgfHxcbiAgICAgICAgICB2YWx1ZS5jb25zdHJ1Y3Rvci5uYW1lIHx8XG4gICAgICAgICAgXCJPYmplY3RcIjtcbiAgICAgICAgSlNDb21waWxlcl90ZW1wX2NvbnN0LmNhbGwoXG4gICAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0LFxuICAgICAgICAgIFwiVGhlIHByb3ZpZGVkIGtleSBpcyBhbiB1bnN1cHBvcnRlZCB0eXBlICVzLiBUaGlzIHZhbHVlIG11c3QgYmUgY29lcmNlZCB0byBhIHN0cmluZyBiZWZvcmUgdXNpbmcgaXQgaGVyZS5cIixcbiAgICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQkanNjb21wJDBcbiAgICAgICAgKTtcbiAgICAgICAgcmV0dXJuIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSk7XG4gICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIGdldFRhc2tOYW1lKHR5cGUpIHtcbiAgICAgIGlmICh0eXBlID09PSBSRUFDVF9GUkFHTUVOVF9UWVBFKSByZXR1cm4gXCI8PlwiO1xuICAgICAgaWYgKFxuICAgICAgICBcIm9iamVjdFwiID09PSB0eXBlb2YgdHlwZSAmJlxuICAgICAgICBudWxsICE9PSB0eXBlICYmXG4gICAgICAgIHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0xBWllfVFlQRVxuICAgICAgKVxuICAgICAgICByZXR1cm4gXCI8Li4uPlwiO1xuICAgICAgdHJ5IHtcbiAgICAgICAgdmFyIG5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodHlwZSk7XG4gICAgICAgIHJldHVybiBuYW1lID8gXCI8XCIgKyBuYW1lICsgXCI+XCIgOiBcIjwuLi4+XCI7XG4gICAgICB9IGNhdGNoICh4KSB7XG4gICAgICAgIHJldHVybiBcIjwuLi4+XCI7XG4gICAgICB9XG4gICAgfVxuICAgIGZ1bmN0aW9uIGdldE93bmVyKCkge1xuICAgICAgdmFyIGRpc3BhdGNoZXIgPSBSZWFjdFNoYXJlZEludGVybmFscy5BO1xuICAgICAgcmV0dXJuIG51bGwgPT09IGRpc3BhdGNoZXIgPyBudWxsIDogZGlzcGF0Y2hlci5nZXRPd25lcigpO1xuICAgIH1cbiAgICBmdW5jdGlvbiBVbmtub3duT3duZXIoKSB7XG4gICAgICByZXR1cm4gRXJyb3IoXCJyZWFjdC1zdGFjay10b3AtZnJhbWVcIik7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGhhc1ZhbGlkS2V5KGNvbmZpZykge1xuICAgICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCBcImtleVwiKSkge1xuICAgICAgICB2YXIgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihjb25maWcsIFwia2V5XCIpLmdldDtcbiAgICAgICAgaWYgKGdldHRlciAmJiBnZXR0ZXIuaXNSZWFjdFdhcm5pbmcpIHJldHVybiAhMTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB2b2lkIDAgIT09IGNvbmZpZy5rZXk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGRlZmluZUtleVByb3BXYXJuaW5nR2V0dGVyKHByb3BzLCBkaXNwbGF5TmFtZSkge1xuICAgICAgZnVuY3Rpb24gd2FybkFib3V0QWNjZXNzaW5nS2V5KCkge1xuICAgICAgICBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93biB8fFxuICAgICAgICAgICgoc3BlY2lhbFByb3BLZXlXYXJuaW5nU2hvd24gPSAhMCksXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgIFwiJXM6IGBrZXlgIGlzIG5vdCBhIHByb3AuIFRyeWluZyB0byBhY2Nlc3MgaXQgd2lsbCByZXN1bHQgaW4gYHVuZGVmaW5lZGAgYmVpbmcgcmV0dXJuZWQuIElmIHlvdSBuZWVkIHRvIGFjY2VzcyB0aGUgc2FtZSB2YWx1ZSB3aXRoaW4gdGhlIGNoaWxkIGNvbXBvbmVudCwgeW91IHNob3VsZCBwYXNzIGl0IGFzIGEgZGlmZmVyZW50IHByb3AuIChodHRwczovL3JlYWN0LmRldi9saW5rL3NwZWNpYWwtcHJvcHMpXCIsXG4gICAgICAgICAgICBkaXNwbGF5TmFtZVxuICAgICAgICAgICkpO1xuICAgICAgfVxuICAgICAgd2FybkFib3V0QWNjZXNzaW5nS2V5LmlzUmVhY3RXYXJuaW5nID0gITA7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocHJvcHMsIFwia2V5XCIsIHtcbiAgICAgICAgZ2V0OiB3YXJuQWJvdXRBY2Nlc3NpbmdLZXksXG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITBcbiAgICAgIH0pO1xuICAgIH1cbiAgICBmdW5jdGlvbiBlbGVtZW50UmVmR2V0dGVyV2l0aERlcHJlY2F0aW9uV2FybmluZygpIHtcbiAgICAgIHZhciBjb21wb25lbnROYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHRoaXMudHlwZSk7XG4gICAgICBkaWRXYXJuQWJvdXRFbGVtZW50UmVmW2NvbXBvbmVudE5hbWVdIHx8XG4gICAgICAgICgoZGlkV2FybkFib3V0RWxlbWVudFJlZltjb21wb25lbnROYW1lXSA9ICEwKSxcbiAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICBcIkFjY2Vzc2luZyBlbGVtZW50LnJlZiB3YXMgcmVtb3ZlZCBpbiBSZWFjdCAxOS4gcmVmIGlzIG5vdyBhIHJlZ3VsYXIgcHJvcC4gSXQgd2lsbCBiZSByZW1vdmVkIGZyb20gdGhlIEpTWCBFbGVtZW50IHR5cGUgaW4gYSBmdXR1cmUgcmVsZWFzZS5cIlxuICAgICAgICApKTtcbiAgICAgIGNvbXBvbmVudE5hbWUgPSB0aGlzLnByb3BzLnJlZjtcbiAgICAgIHJldHVybiB2b2lkIDAgIT09IGNvbXBvbmVudE5hbWUgPyBjb21wb25lbnROYW1lIDogbnVsbDtcbiAgICB9XG4gICAgZnVuY3Rpb24gUmVhY3RFbGVtZW50KFxuICAgICAgdHlwZSxcbiAgICAgIGtleSxcbiAgICAgIHNlbGYsXG4gICAgICBzb3VyY2UsXG4gICAgICBvd25lcixcbiAgICAgIHByb3BzLFxuICAgICAgZGVidWdTdGFjayxcbiAgICAgIGRlYnVnVGFza1xuICAgICkge1xuICAgICAgc2VsZiA9IHByb3BzLnJlZjtcbiAgICAgIHR5cGUgPSB7XG4gICAgICAgICQkdHlwZW9mOiBSRUFDVF9FTEVNRU5UX1RZUEUsXG4gICAgICAgIHR5cGU6IHR5cGUsXG4gICAgICAgIGtleToga2V5LFxuICAgICAgICBwcm9wczogcHJvcHMsXG4gICAgICAgIF9vd25lcjogb3duZXJcbiAgICAgIH07XG4gICAgICBudWxsICE9PSAodm9pZCAwICE9PSBzZWxmID8gc2VsZiA6IG51bGwpXG4gICAgICAgID8gT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwicmVmXCIsIHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICAgICAgZ2V0OiBlbGVtZW50UmVmR2V0dGVyV2l0aERlcHJlY2F0aW9uV2FybmluZ1xuICAgICAgICAgIH0pXG4gICAgICAgIDogT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwicmVmXCIsIHsgZW51bWVyYWJsZTogITEsIHZhbHVlOiBudWxsIH0pO1xuICAgICAgdHlwZS5fc3RvcmUgPSB7fTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLl9zdG9yZSwgXCJ2YWxpZGF0ZWRcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogMFxuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdJbmZvXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IG51bGxcbiAgICAgIH0pO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUsIFwiX2RlYnVnU3RhY2tcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogZGVidWdTdGFja1xuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdUYXNrXCIsIHtcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMSxcbiAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgIHdyaXRhYmxlOiAhMCxcbiAgICAgICAgdmFsdWU6IGRlYnVnVGFza1xuICAgICAgfSk7XG4gICAgICBPYmplY3QuZnJlZXplICYmIChPYmplY3QuZnJlZXplKHR5cGUucHJvcHMpLCBPYmplY3QuZnJlZXplKHR5cGUpKTtcbiAgICAgIHJldHVybiB0eXBlO1xuICAgIH1cbiAgICBmdW5jdGlvbiBqc3hERVZJbXBsKFxuICAgICAgdHlwZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIG1heWJlS2V5LFxuICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgIHNvdXJjZSxcbiAgICAgIHNlbGYsXG4gICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgZGVidWdUYXNrXG4gICAgKSB7XG4gICAgICB2YXIgY2hpbGRyZW4gPSBjb25maWcuY2hpbGRyZW47XG4gICAgICBpZiAodm9pZCAwICE9PSBjaGlsZHJlbilcbiAgICAgICAgaWYgKGlzU3RhdGljQ2hpbGRyZW4pXG4gICAgICAgICAgaWYgKGlzQXJyYXlJbXBsKGNoaWxkcmVuKSkge1xuICAgICAgICAgICAgZm9yIChcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA9IDA7XG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4gPCBjaGlsZHJlbi5sZW5ndGg7XG4gICAgICAgICAgICAgIGlzU3RhdGljQ2hpbGRyZW4rK1xuICAgICAgICAgICAgKVxuICAgICAgICAgICAgICB2YWxpZGF0ZUNoaWxkS2V5cyhjaGlsZHJlbltpc1N0YXRpY0NoaWxkcmVuXSk7XG4gICAgICAgICAgICBPYmplY3QuZnJlZXplICYmIE9iamVjdC5mcmVlemUoY2hpbGRyZW4pO1xuICAgICAgICAgIH0gZWxzZVxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICAgXCJSZWFjdC5qc3g6IFN0YXRpYyBjaGlsZHJlbiBzaG91bGQgYWx3YXlzIGJlIGFuIGFycmF5LiBZb3UgYXJlIGxpa2VseSBleHBsaWNpdGx5IGNhbGxpbmcgUmVhY3QuanN4cyBvciBSZWFjdC5qc3hERVYuIFVzZSB0aGUgQmFiZWwgdHJhbnNmb3JtIGluc3RlYWQuXCJcbiAgICAgICAgICAgICk7XG4gICAgICAgIGVsc2UgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW4pO1xuICAgICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCBcImtleVwiKSkge1xuICAgICAgICBjaGlsZHJlbiA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKTtcbiAgICAgICAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhjb25maWcpLmZpbHRlcihmdW5jdGlvbiAoaykge1xuICAgICAgICAgIHJldHVybiBcImtleVwiICE9PSBrO1xuICAgICAgICB9KTtcbiAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA9XG4gICAgICAgICAgMCA8IGtleXMubGVuZ3RoXG4gICAgICAgICAgICA/IFwie2tleTogc29tZUtleSwgXCIgKyBrZXlzLmpvaW4oXCI6IC4uLiwgXCIpICsgXCI6IC4uLn1cIlxuICAgICAgICAgICAgOiBcIntrZXk6IHNvbWVLZXl9XCI7XG4gICAgICAgIGRpZFdhcm5BYm91dEtleVNwcmVhZFtjaGlsZHJlbiArIGlzU3RhdGljQ2hpbGRyZW5dIHx8XG4gICAgICAgICAgKChrZXlzID1cbiAgICAgICAgICAgIDAgPCBrZXlzLmxlbmd0aCA/IFwie1wiICsga2V5cy5qb2luKFwiOiAuLi4sIFwiKSArIFwiOiAuLi59XCIgOiBcInt9XCIpLFxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAnQSBwcm9wcyBvYmplY3QgY29udGFpbmluZyBhIFwia2V5XCIgcHJvcCBpcyBiZWluZyBzcHJlYWQgaW50byBKU1g6XFxuICBsZXQgcHJvcHMgPSAlcztcXG4gIDwlcyB7Li4ucHJvcHN9IC8+XFxuUmVhY3Qga2V5cyBtdXN0IGJlIHBhc3NlZCBkaXJlY3RseSB0byBKU1ggd2l0aG91dCB1c2luZyBzcHJlYWQ6XFxuICBsZXQgcHJvcHMgPSAlcztcXG4gIDwlcyBrZXk9e3NvbWVLZXl9IHsuLi5wcm9wc30gLz4nLFxuICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgICAgICAgIGNoaWxkcmVuLFxuICAgICAgICAgICAga2V5cyxcbiAgICAgICAgICAgIGNoaWxkcmVuXG4gICAgICAgICAgKSxcbiAgICAgICAgICAoZGlkV2FybkFib3V0S2V5U3ByZWFkW2NoaWxkcmVuICsgaXNTdGF0aWNDaGlsZHJlbl0gPSAhMCkpO1xuICAgICAgfVxuICAgICAgY2hpbGRyZW4gPSBudWxsO1xuICAgICAgdm9pZCAwICE9PSBtYXliZUtleSAmJlxuICAgICAgICAoY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihtYXliZUtleSksIChjaGlsZHJlbiA9IFwiXCIgKyBtYXliZUtleSkpO1xuICAgICAgaGFzVmFsaWRLZXkoY29uZmlnKSAmJlxuICAgICAgICAoY2hlY2tLZXlTdHJpbmdDb2VyY2lvbihjb25maWcua2V5KSwgKGNoaWxkcmVuID0gXCJcIiArIGNvbmZpZy5rZXkpKTtcbiAgICAgIGlmIChcImtleVwiIGluIGNvbmZpZykge1xuICAgICAgICBtYXliZUtleSA9IHt9O1xuICAgICAgICBmb3IgKHZhciBwcm9wTmFtZSBpbiBjb25maWcpXG4gICAgICAgICAgXCJrZXlcIiAhPT0gcHJvcE5hbWUgJiYgKG1heWJlS2V5W3Byb3BOYW1lXSA9IGNvbmZpZ1twcm9wTmFtZV0pO1xuICAgICAgfSBlbHNlIG1heWJlS2V5ID0gY29uZmlnO1xuICAgICAgY2hpbGRyZW4gJiZcbiAgICAgICAgZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIoXG4gICAgICAgICAgbWF5YmVLZXksXG4gICAgICAgICAgXCJmdW5jdGlvblwiID09PSB0eXBlb2YgdHlwZVxuICAgICAgICAgICAgPyB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBcIlVua25vd25cIlxuICAgICAgICAgICAgOiB0eXBlXG4gICAgICAgICk7XG4gICAgICByZXR1cm4gUmVhY3RFbGVtZW50KFxuICAgICAgICB0eXBlLFxuICAgICAgICBjaGlsZHJlbixcbiAgICAgICAgc2VsZixcbiAgICAgICAgc291cmNlLFxuICAgICAgICBnZXRPd25lcigpLFxuICAgICAgICBtYXliZUtleSxcbiAgICAgICAgZGVidWdTdGFjayxcbiAgICAgICAgZGVidWdUYXNrXG4gICAgICApO1xuICAgIH1cbiAgICBmdW5jdGlvbiB2YWxpZGF0ZUNoaWxkS2V5cyhub2RlKSB7XG4gICAgICBcIm9iamVjdFwiID09PSB0eXBlb2Ygbm9kZSAmJlxuICAgICAgICBudWxsICE9PSBub2RlICYmXG4gICAgICAgIG5vZGUuJCR0eXBlb2YgPT09IFJFQUNUX0VMRU1FTlRfVFlQRSAmJlxuICAgICAgICBub2RlLl9zdG9yZSAmJlxuICAgICAgICAobm9kZS5fc3RvcmUudmFsaWRhdGVkID0gMSk7XG4gICAgfVxuICAgIHZhciBSZWFjdCA9IHJlcXVpcmUoXCJuZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3RcIiksXG4gICAgICBSRUFDVF9FTEVNRU5UX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QudHJhbnNpdGlvbmFsLmVsZW1lbnRcIiksXG4gICAgICBSRUFDVF9QT1JUQUxfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5wb3J0YWxcIiksXG4gICAgICBSRUFDVF9GUkFHTUVOVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmZyYWdtZW50XCIpLFxuICAgICAgUkVBQ1RfU1RSSUNUX01PREVfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdHJpY3RfbW9kZVwiKSxcbiAgICAgIFJFQUNUX1BST0ZJTEVSX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QucHJvZmlsZXJcIik7XG4gICAgU3ltYm9sLmZvcihcInJlYWN0LnByb3ZpZGVyXCIpO1xuICAgIHZhciBSRUFDVF9DT05TVU1FUl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmNvbnN1bWVyXCIpLFxuICAgICAgUkVBQ1RfQ09OVEVYVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmNvbnRleHRcIiksXG4gICAgICBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LmZvcndhcmRfcmVmXCIpLFxuICAgICAgUkVBQ1RfU1VTUEVOU0VfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdXNwZW5zZVwiKSxcbiAgICAgIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5zdXNwZW5zZV9saXN0XCIpLFxuICAgICAgUkVBQ1RfTUVNT19UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0Lm1lbW9cIiksXG4gICAgICBSRUFDVF9MQVpZX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QubGF6eVwiKSxcbiAgICAgIFJFQUNUX0FDVElWSVRZX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuYWN0aXZpdHlcIiksXG4gICAgICBSRUFDVF9DTElFTlRfUkVGRVJFTkNFID0gU3ltYm9sLmZvcihcInJlYWN0LmNsaWVudC5yZWZlcmVuY2VcIiksXG4gICAgICBSZWFjdFNoYXJlZEludGVybmFscyA9XG4gICAgICAgIFJlYWN0Ll9fQ0xJRU5UX0lOVEVSTkFMU19ET19OT1RfVVNFX09SX1dBUk5fVVNFUlNfVEhFWV9DQU5OT1RfVVBHUkFERSxcbiAgICAgIGhhc093blByb3BlcnR5ID0gT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eSxcbiAgICAgIGlzQXJyYXlJbXBsID0gQXJyYXkuaXNBcnJheSxcbiAgICAgIGNyZWF0ZVRhc2sgPSBjb25zb2xlLmNyZWF0ZVRhc2tcbiAgICAgICAgPyBjb25zb2xlLmNyZWF0ZVRhc2tcbiAgICAgICAgOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICB9O1xuICAgIFJlYWN0ID0ge1xuICAgICAgXCJyZWFjdC1zdGFjay1ib3R0b20tZnJhbWVcIjogZnVuY3Rpb24gKGNhbGxTdGFja0ZvckVycm9yKSB7XG4gICAgICAgIHJldHVybiBjYWxsU3RhY2tGb3JFcnJvcigpO1xuICAgICAgfVxuICAgIH07XG4gICAgdmFyIHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duO1xuICAgIHZhciBkaWRXYXJuQWJvdXRFbGVtZW50UmVmID0ge307XG4gICAgdmFyIHVua25vd25Pd25lckRlYnVnU3RhY2sgPSBSZWFjdFtcInJlYWN0LXN0YWNrLWJvdHRvbS1mcmFtZVwiXS5iaW5kKFxuICAgICAgUmVhY3QsXG4gICAgICBVbmtub3duT3duZXJcbiAgICApKCk7XG4gICAgdmFyIHVua25vd25Pd25lckRlYnVnVGFzayA9IGNyZWF0ZVRhc2soZ2V0VGFza05hbWUoVW5rbm93bk93bmVyKSk7XG4gICAgdmFyIGRpZFdhcm5BYm91dEtleVNwcmVhZCA9IHt9O1xuICAgIGV4cG9ydHMuRnJhZ21lbnQgPSBSRUFDVF9GUkFHTUVOVF9UWVBFO1xuICAgIGV4cG9ydHMuanN4REVWID0gZnVuY3Rpb24gKFxuICAgICAgdHlwZSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIG1heWJlS2V5LFxuICAgICAgaXNTdGF0aWNDaGlsZHJlbixcbiAgICAgIHNvdXJjZSxcbiAgICAgIHNlbGZcbiAgICApIHtcbiAgICAgIHZhciB0cmFja0FjdHVhbE93bmVyID1cbiAgICAgICAgMWU0ID4gUmVhY3RTaGFyZWRJbnRlcm5hbHMucmVjZW50bHlDcmVhdGVkT3duZXJTdGFja3MrKztcbiAgICAgIHJldHVybiBqc3hERVZJbXBsKFxuICAgICAgICB0eXBlLFxuICAgICAgICBjb25maWcsXG4gICAgICAgIG1heWJlS2V5LFxuICAgICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgICBzb3VyY2UsXG4gICAgICAgIHNlbGYsXG4gICAgICAgIHRyYWNrQWN0dWFsT3duZXJcbiAgICAgICAgICA/IEVycm9yKFwicmVhY3Qtc3RhY2stdG9wLWZyYW1lXCIpXG4gICAgICAgICAgOiB1bmtub3duT3duZXJEZWJ1Z1N0YWNrLFxuICAgICAgICB0cmFja0FjdHVhbE93bmVyID8gY3JlYXRlVGFzayhnZXRUYXNrTmFtZSh0eXBlKSkgOiB1bmtub3duT3duZXJEZWJ1Z1Rhc2tcbiAgICAgICk7XG4gICAgfTtcbiAgfSkoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css":
/*!***************************************************!*\
  !*** ./src/app/daily-schedule/daily-schedule.css ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"617977ecb9c0\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFpbHktc2NoZWR1bGUvZGFpbHktc2NoZWR1bGUuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxkYWlseS1zY2hlZHVsZVxcZGFpbHktc2NoZWR1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjE3OTc3ZWNiOWMwXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/daily-schedule/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/daily-schedule/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DailySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _daily_schedule_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./daily-schedule.css */ \"(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DailySchedulePage() {\n    _s();\n    const { user } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gridRows, setGridRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [weeklySchedule, setWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWeeklySchedule, setShowWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تهيئة التاريخ الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            setSelectedDate(today.toISOString().split('T')[0]);\n        }\n    }[\"DailySchedulePage.useEffect\"], []);\n    // جلب البيانات عند تغيير التاريخ\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (selectedDate) {\n                fetchScheduleData();\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        selectedDate\n    ]);\n    // جلب بيانات الجدول الإذاعي\n    const fetchScheduleData = async ()=>{\n        setLoading(true);\n        try {\n            console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);\n            const response = await fetch(\"/api/daily-schedule?date=\".concat(selectedDate));\n            const data = await response.json();\n            if (data.success) {\n                var _data_data_availableMedia;\n                setScheduleItems(data.data.scheduleItems);\n                setAvailableMedia(data.data.availableMedia || []);\n                setGridRows(data.data.scheduleRows || []);\n                if (data.fromSavedFile) {\n                    console.log('📂 تم تحميل جدول محفوظ مسبقاً');\n                    console.log('💾 تاريخ الحفظ:', data.savedAt);\n                    console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);\n                } else {\n                    console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');\n                    console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');\n                }\n                console.log('📦 المواد المتاحة:', ((_data_data_availableMedia = data.data.availableMedia) === null || _data_data_availableMedia === void 0 ? void 0 : _data_data_availableMedia.length) || 0);\n                // عرض عينة من المواد المتاحة\n                if (data.data.availableMedia && data.data.availableMedia.length > 0) {\n                    console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));\n                }\n            }\n            // جلب الجدول الأسبوعي للمراجعة\n            await fetchWeeklySchedule();\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // جلب الجدول الأسبوعي للمراجعة\n    const fetchWeeklySchedule = async ()=>{\n        try {\n            const date = new Date(selectedDate);\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const data = await response.json();\n            console.log('📊 استجابة API للجدول الأسبوعي:', data);\n            if (data.success && data.data) {\n                // استخراج مواد اليوم المحدد من scheduleItems\n                const dayOfWeek = new Date(selectedDate).getDay();\n                const daySchedule = [];\n                console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);\n                console.log('📦 البيانات المتاحة:', data.data);\n                // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)\n                if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {\n                    console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);\n                    const dayItems = data.data.scheduleItems.filter((item)=>{\n                        var _item_mediaItem;\n                        console.log('🔍 فحص المادة:', (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);\n                        return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء\n                    }).sort((a, b)=>{\n                        // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59\n                        const timeA = a.startTime;\n                        const timeB = b.startTime;\n                        const getTimeOrder = (time)=>{\n                            const hour = parseInt(time.split(':')[0]);\n                            if (hour >= 8 && hour < 18) return 1; // صباح ومساء\n                            if (hour >= 18) return 2; // برايم تايم\n                            return 3; // منتصف الليل والفجر\n                        };\n                        const orderA = getTimeOrder(timeA);\n                        const orderB = getTimeOrder(timeB);\n                        if (orderA !== orderB) return orderA - orderB;\n                        return timeA.localeCompare(timeB);\n                    });\n                    console.log('✅ مواد اليوم المفلترة:', dayItems.length);\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3;\n                        const scheduleItem = {\n                            time: item.startTime,\n                            name: ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'مادة غير محددة',\n                            episodeNumber: item.episodeNumber || ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.episodeNumber),\n                            partNumber: item.partNumber || ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.partNumber),\n                            seasonNumber: item.seasonNumber || ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.seasonNumber),\n                            isRerun: item.isRerun || false\n                        };\n                        daySchedule.push(scheduleItem);\n                        console.log('📺 إضافة مادة للخريطة:', scheduleItem);\n                    });\n                } else {\n                    console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');\n                }\n                setWeeklySchedule(daySchedule);\n                console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');\n            } else {\n                console.log('❌ فشل في جلب البيانات أو البيانات فارغة');\n                setWeeklySchedule([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);\n            setWeeklySchedule([]);\n        }\n    };\n    // فلترة المواد المتاحة\n    const filteredMedia = availableMedia.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesType = filterType === 'ALL' || item.type === filterType;\n        return matchesSearch && matchesType;\n    });\n    // أنواع المواد للفلترة\n    const mediaTypes = [\n        'ALL',\n        'PROGRAM',\n        'SERIES',\n        'MOVIE',\n        'PROMO',\n        'STING',\n        'FILL_IN',\n        'FILLER'\n    ];\n    // إضافة صف فارغ\n    const addEmptyRow = (afterIndex)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        const newRow = {\n            id: \"empty_\".concat(Date.now()),\n            type: 'empty',\n            canDelete: true\n        };\n        newRows.splice(afterIndex + 1, 0, newRow);\n        setGridRows(newRows);\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // إضافة صفوف فارغة متعددة\n    const addMultipleEmptyRows = function(afterIndex) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        for(let i = 0; i < count; i++){\n            const newRow = {\n                id: \"empty_\".concat(Date.now(), \"_\").concat(i),\n                type: 'empty',\n                canDelete: true\n            };\n            newRows.splice(afterIndex + 1 + i, 0, newRow);\n        }\n        setGridRows(newRows);\n        console.log(\"✅ تم إضافة \".concat(count, \" صف فارغ\"));\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // التحقق من الحاجة لإضافة صفوف فارغة\n    const checkAndAddEmptyRows = (currentIndex)=>{\n        const currentRows = gridRows;\n        const nextEmptyIndex = currentRows.findIndex((row, index)=>index > currentIndex && row.type === 'empty');\n        // إذا لم توجد صفوف فارغة بعد الفاصل الحالي\n        if (nextEmptyIndex === -1) {\n            console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 5 صفوف');\n            addMultipleEmptyRows(currentIndex, 5);\n        } else {\n            console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);\n        }\n    };\n    // حذف صف فارغ\n    const deleteRow = (rowId)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        // استعادة موضع التمرير بعد الحذف\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 100);\n    };\n    // حذف سيجمنت\n    const deleteSegment = (rowId)=>{\n        if (confirm('هل أنت متأكد من حذف هذا السيجمنت؟')) {\n            const newRows = gridRows.filter((row)=>row.id !== rowId);\n            recalculateTimes(newRows);\n            console.log('🗑️ تم حذف السيجمنت');\n        }\n    };\n    // حذف فاصل بدون تأكيد\n    const deleteFiller = (rowId)=>{\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        console.log('🗑️ تم حذف الفاصل');\n    };\n    // تحريك صف لأعلى\n    const moveRowUp = (index)=>{\n        if (index <= 0) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index - 1], newRows[index]] = [\n            newRows[index],\n            newRows[index - 1]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬆️ تم تحريك الصف لأعلى');\n    };\n    // تحريك صف لأسفل\n    const moveRowDown = (index)=>{\n        if (index >= gridRows.length - 1) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index], newRows[index + 1]] = [\n            newRows[index + 1],\n            newRows[index]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬇️ تم تحريك الصف لأسفل');\n    };\n    // معالجة سحب الصفوف داخل الجدول\n    const handleRowDragStart = (e, index)=>{\n        e.dataTransfer.setData('text/plain', index.toString());\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    // معالجة إسقاط الصفوف داخل الجدول\n    const handleRowDrop = (e, targetIndex)=>{\n        e.preventDefault();\n        const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));\n        if (sourceIndex === targetIndex) return;\n        const newRows = [\n            ...gridRows\n        ];\n        const [movedRow] = newRows.splice(sourceIndex, 1);\n        newRows.splice(targetIndex, 0, movedRow);\n        recalculateTimes(newRows);\n        console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);\n    };\n    // معالجة إسقاط المواد\n    const handleDrop = (e, rowIndex)=>{\n        e.preventDefault();\n        try {\n            var _mediaData_segments, _mediaData_segments1;\n            // محاولة الحصول على البيانات بطرق مختلفة\n            let mediaData;\n            const jsonData = e.dataTransfer.getData('application/json');\n            const textData = e.dataTransfer.getData('text/plain');\n            if (jsonData) {\n                mediaData = JSON.parse(jsonData);\n            } else if (textData) {\n                try {\n                    mediaData = JSON.parse(textData);\n                } catch (e) {\n                    console.error('❌ لا يمكن تحليل البيانات المسحوبة');\n                    return;\n                }\n            } else {\n                console.error('❌ لا توجد بيانات مسحوبة');\n                return;\n            }\n            // التحقق من أن الصف فارغ\n            const targetRow = gridRows[rowIndex];\n            if (targetRow.type !== 'empty') {\n                alert('يمكن إسقاط المواد في الصفوف الفارغة فقط');\n                return;\n            }\n            console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);\n            // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر\n            if (!mediaData || typeof mediaData !== 'object') {\n                console.error('❌ بيانات المادة غير صحيحة:', mediaData);\n                console.error('❌ نوع البيانات:', typeof mediaData);\n                return;\n            }\n            // التأكد من وجود الاسم\n            const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';\n            const itemType = mediaData.type || 'UNKNOWN';\n            const itemId = mediaData.id || Date.now().toString();\n            console.log('📥 معلومات المادة:', {\n                name: itemName,\n                type: itemType,\n                id: itemId,\n                segments: ((_mediaData_segments = mediaData.segments) === null || _mediaData_segments === void 0 ? void 0 : _mediaData_segments.length) || 0\n            });\n            // تحديد نوع المادة المسحوبة\n            let dragItemType = 'filler';\n            let itemContent = itemName;\n            // إضافة تفاصيل المادة\n            const details = [];\n            if (mediaData.episodeNumber) details.push(\"ح\".concat(mediaData.episodeNumber));\n            if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(\"م\".concat(mediaData.seasonNumber));\n            if (mediaData.partNumber) details.push(\"ج\".concat(mediaData.partNumber));\n            const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n            // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات\n            if ([\n                'PROMO',\n                'STING',\n                'FILLER',\n                'FILL_IN'\n            ].includes(itemType)) {\n                dragItemType = 'filler';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n            } else {\n                dragItemType = 'segment';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" (مادة إضافية)\");\n            }\n            // حساب المدة الحقيقية للمادة\n            let itemDuration = '00:01:00'; // مدة افتراضية\n            console.log('🔍 تحليل مدة المادة:', {\n                name: itemName,\n                hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),\n                segmentsCount: ((_mediaData_segments1 = mediaData.segments) === null || _mediaData_segments1 === void 0 ? void 0 : _mediaData_segments1.length) || 0,\n                hasDuration: !!mediaData.duration,\n                directDuration: mediaData.duration\n            });\n            if (mediaData.segments && mediaData.segments.length > 0) {\n                // حساب إجمالي مدة جميع السيجمنتات\n                let totalSeconds = 0;\n                mediaData.segments.forEach((segment, index)=>{\n                    if (segment.duration) {\n                        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                        const segmentSeconds = hours * 3600 + minutes * 60 + seconds;\n                        totalSeconds += segmentSeconds;\n                        console.log(\"  \\uD83D\\uDCFA سيجمنت \".concat(index + 1, \": \").concat(segment.duration, \" (\").concat(segmentSeconds, \" ثانية)\"));\n                    }\n                });\n                if (totalSeconds > 0) {\n                    const hours = Math.floor(totalSeconds / 3600);\n                    const minutes = Math.floor(totalSeconds % 3600 / 60);\n                    const secs = totalSeconds % 60;\n                    itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                }\n                console.log('📊 حساب مدة المادة من السيجمنتات:', {\n                    name: itemName,\n                    segments: mediaData.segments.length,\n                    totalSeconds,\n                    finalDuration: itemDuration\n                });\n            } else if (mediaData.duration) {\n                // استخدام المدة المباشرة إذا كانت موجودة\n                itemDuration = mediaData.duration;\n                console.log('📊 استخدام مدة مباشرة:', itemDuration);\n            } else {\n                console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);\n            }\n            // إنشاء كود للمادة المسحوبة\n            let itemCode = '';\n            if (mediaData.segmentCode) {\n                itemCode = mediaData.segmentCode;\n            } else if (mediaData.id) {\n                itemCode = \"\".concat(itemType, \"_\").concat(mediaData.id);\n            } else {\n                itemCode = \"\".concat(itemType, \"_\").concat(Date.now().toString().slice(-6));\n            }\n            // إنشاء صف جديد مع المدة الحقيقية\n            const newRow = {\n                id: \"dropped_\".concat(Date.now()),\n                type: dragItemType,\n                content: itemContent,\n                mediaItemId: itemId,\n                segmentCode: itemCode,\n                duration: itemDuration,\n                canDelete: true\n            };\n            // التأكد من أن الصف المستهدف فارغ\n            if (gridRows[rowIndex].type !== 'empty') {\n                console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);\n                return;\n            }\n            // استبدال الصف الفارغ مباشرة\n            const newRows = [\n                ...gridRows\n            ];\n            newRows[rowIndex] = newRow;\n            console.log('✅ تم إضافة المادة:', {\n                name: itemName,\n                type: dragItemType,\n                duration: itemDuration,\n                position: rowIndex,\n                content: itemContent,\n                beforeType: gridRows[rowIndex].type,\n                afterType: newRow.type\n            });\n            // حفظ موضع التمرير الحالي\n            const gridBody = document.querySelector('.grid-body');\n            const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n            // تحديث الصفوف مباشرة\n            setGridRows(newRows);\n            // إعادة حساب الأوقات بعد تأخير قصير\n            setTimeout(()=>{\n                recalculateTimes(newRows);\n                // التحقق من الحاجة لإضافة صفوف فارغة\n                checkAndAddEmptyRows(rowIndex);\n                // استعادة موضع التمرير بعد التحديث\n                setTimeout(()=>{\n                    if (gridBody) {\n                        gridBody.scrollTop = currentScrollTop;\n                        console.log('📍 تم استعادة موضع التمرير:', currentScrollTop);\n                    }\n                }, 100);\n            }, 50);\n        } catch (error) {\n            console.error('❌ خطأ في إسقاط المادة:', error);\n        }\n    };\n    // إعادة حساب الأوقات مثل Excel\n    const recalculateTimes = (rows)=>{\n        const newRows = [\n            ...rows\n        ];\n        let currentTime = '08:00:00'; // نقطة البداية بالثواني\n        let hasFillers = false; // هل تم إضافة فواصل؟\n        // التحقق من وجود فواصل\n        hasFillers = rows.some((row)=>row.type === 'filler');\n        console.log('🔄 بدء إعادة حساب الأوقات من 08:00:00', hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');\n        for(let i = 0; i < newRows.length; i++){\n            const row = newRows[i];\n            if (row.type === 'segment' || row.type === 'filler') {\n                // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل\n                if (i === 0 || hasFillers) {\n                    newRows[i] = {\n                        ...row,\n                        time: currentTime\n                    };\n                } else {\n                    newRows[i] = {\n                        ...row,\n                        time: undefined\n                    };\n                }\n                if (row.duration) {\n                    // حساب الوقت التالي بناءً على المدة\n                    const nextTime = calculateNextTime(currentTime, row.duration);\n                    console.log(\"⏰ \".concat(row.type, ': \"').concat(row.content, '\" - من ').concat(currentTime, \" إلى \").concat(nextTime, \" (مدة: \").concat(row.duration, \")\"));\n                    currentTime = nextTime;\n                }\n            } else if (row.type === 'empty') {\n                // الصفوف الفارغة لا تؤثر على الوقت\n                newRows[i] = {\n                    ...row,\n                    time: undefined\n                };\n            }\n            // التحقق من الوصول لوقت مادة أساسية\n            if (row.originalStartTime && hasFillers) {\n                const targetMinutes = timeToMinutes(row.originalStartTime);\n                const currentMinutes = timeToMinutes(currentTime);\n                const difference = targetMinutes - currentMinutes;\n                if (Math.abs(difference) > 5) {\n                    console.log('⚠️ انحراف زمني: المادة \"'.concat(row.content, '\" مجدولة في ').concat(row.originalStartTime, \" لكن ستدخل في \").concat(currentTime, \" (فرق: \").concat(difference, \" دقيقة)\"));\n                } else {\n                    console.log('✅ توقيت صحيح: المادة \"'.concat(row.content, '\" ستدخل في ').concat(currentTime, \" (مجدولة: \").concat(row.originalStartTime, \")\"));\n                }\n            }\n        }\n        console.log(\"\\uD83C\\uDFC1 انتهاء الحساب - الوقت النهائي: \".concat(currentTime));\n        // حفظ موضع التمرير قبل التحديث\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        // تحديث الصفوف دائماً لضمان التحديث الصحيح\n        setGridRows(newRows);\n        // استعادة موضع التمرير بعد التحديث\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // تحويل الوقت إلى دقائق\n    const timeToMinutes = (time)=>{\n        const [hours, minutes] = time.split(':').map(Number);\n        return hours * 60 + minutes;\n    };\n    // حساب المدة الإجمالية للمادة بدقة\n    const calculateTotalDuration = (item)=>{\n        if (item.segments && item.segments.length > 0) {\n            let totalSeconds = 0;\n            item.segments.forEach((segment)=>{\n                if (segment.duration) {\n                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                }\n            });\n            if (totalSeconds > 0) {\n                const hours = Math.floor(totalSeconds / 3600);\n                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                const secs = totalSeconds % 60;\n                return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n            }\n        }\n        return item.duration || '00:01:00';\n    };\n    // حفظ تعديلات الجدول\n    const saveScheduleChanges = async ()=>{\n        try {\n            console.log('💾 بدء حفظ التعديلات...');\n            console.log('📅 التاريخ:', selectedDate);\n            console.log('📝 عدد الصفوف:', gridRows.length);\n            const response = await fetch('/api/daily-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    date: selectedDate,\n                    scheduleRows: gridRows\n                })\n            });\n            const result = await response.json();\n            if (response.ok && result.success) {\n                console.log('✅ تم حفظ تعديلات الجدول الإذاعي بنجاح');\n                alert('✅ تم حفظ التعديلات بنجاح!');\n            } else {\n                console.error('❌ فشل في حفظ التعديلات:', result.error);\n                alert('❌ فشل في حفظ التعديلات: ' + result.error);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ التعديلات:', error);\n            alert('❌ خطأ في الاتصال بالخادم');\n        }\n    };\n    // تصدير الجدول الإذاعي إلى Excel\n    const exportDailySchedule = async ()=>{\n        try {\n            console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');\n            if (!selectedDate) {\n                alert('يرجى تحديد التاريخ أولاً');\n                return;\n            }\n            const response = await fetch(\"/api/export-daily-schedule-new?date=\".concat(selectedDate));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"Daily_Schedule_\".concat(selectedDate, \".xlsx\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('✅ تم تصدير الجدول الإذاعي بنجاح');\n            alert('✅ تم تصدير الجدول بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في تصدير الجدول:', error);\n            alert('❌ فشل في تصدير الجدول: ' + error.message);\n        }\n    };\n    // حساب الوقت التالي بناءً على المدة (بدقة الثواني)\n    const calculateNextTime = (startTime, duration)=>{\n        // تحليل وقت البداية\n        const startParts = startTime.split(':');\n        const startHours = parseInt(startParts[0]);\n        const startMins = parseInt(startParts[1]);\n        const startSecs = parseInt(startParts[2] || '0');\n        // تحليل المدة\n        const [durHours, durMins, durSecs] = duration.split(':').map(Number);\n        // حساب إجمالي الثواني\n        let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;\n        totalSeconds += durHours * 3600 + durMins * 60 + durSecs;\n        // تحويل إلى ساعات ودقائق وثواني\n        const hours = Math.floor(totalSeconds / 3600) % 24;\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: \"جدول الإذاعة اليومي\",\n            subtitle: \"البرامج المجدولة اليوم\",\n            icon: \"\\uD83D\\uDCCA\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"schedule-controls\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"date-selector\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"schedule-date\",\n                                    children: \"اختر التاريخ:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    id: \"schedule-date\",\n                                    type: \"date\",\n                                    value: selectedDate,\n                                    onChange: (e)=>setSelectedDate(e.target.value),\n                                    className: \"glass-input\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"header-buttons\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: saveScheduleChanges,\n                                    className: \"glass-button primary\",\n                                    children: \"\\uD83D\\uDCBE حفظ التعديلات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 739,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: exportDailySchedule,\n                                    className: \"glass-button export\",\n                                    style: {\n                                        background: 'linear-gradient(45deg, #17a2b8, #138496)',\n                                        color: 'white'\n                                    },\n                                    children: \"\\uD83D\\uDCCA تصدير Excel\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowWeeklySchedule(!showWeeklySchedule),\n                                    className: \"glass-button\",\n                                    children: showWeeklySchedule ? '📋 إخفاء الخريطة' : '📅 عرض الخريطة'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                    lineNumber: 726,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"schedule-content\",\n                    children: [\n                        showWeeklySchedule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"weekly-sidebar\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"sidebar-title\",\n                                    children: \"الخريطة البرامجية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"weekly-schedule-list\",\n                                    children: Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? weeklySchedule.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"weekly-item\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"weekly-time\",\n                                                    children: item.time\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"weekly-content\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"weekly-name\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 779,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"weekly-details\",\n                                                            children: [\n                                                                \"ح\",\n                                                                item.episodeNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"weekly-details\",\n                                                            children: [\n                                                                \"ج\",\n                                                                item.partNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 784,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"weekly-status\",\n                                                    children: item.isRerun ? '🔄' : '🎯'\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 787,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 19\n                                        }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"no-data\",\n                                        children: \"لا توجد بيانات للخريطة البرامجية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 771,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"media-sidebar\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"sidebar-title\",\n                                    children: \"المواد المتاحة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"sidebar-controls\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: filterType,\n                                            onChange: (e)=>setFilterType(e.target.value),\n                                            className: \"filter-select\",\n                                            children: mediaTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: type,\n                                                    children: type === 'ALL' ? 'جميع الأنواع' : type\n                                                }, type, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 811,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 805,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"بحث في المواد...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"search-input\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 817,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 804,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"media-list\",\n                                    children: filteredMedia.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"media-item \".concat(item.type.toLowerCase()),\n                                            draggable: true,\n                                            onDragStart: (e)=>{\n                                                e.dataTransfer.setData('application/json', JSON.stringify(item));\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"media-name\",\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"media-details\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"media-type\",\n                                                            children: item.type\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"media-duration\",\n                                                            children: calculateTotalDuration(item)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 840,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"media-info\",\n                                                    children: [\n                                                        item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"info-tag\",\n                                                            children: [\n                                                                \"ح\",\n                                                                item.episodeNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 42\n                                                        }, this),\n                                                        item.seasonNumber && item.seasonNumber > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"info-tag\",\n                                                            children: [\n                                                                \"م\",\n                                                                item.seasonNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 844,\n                                                            columnNumber: 66\n                                                        }, this),\n                                                        item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"info-tag\",\n                                                            children: [\n                                                                \"ج\",\n                                                                item.partNumber\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 845,\n                                                            columnNumber: 39\n                                                        }, this),\n                                                        item.segments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"info-tag\",\n                                                            children: [\n                                                                item.segments.length,\n                                                                \" سيجمنت\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 846,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, item.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 15\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 827,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 800,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"schedule-grid\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid-header\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"code-column\",\n                                            children: \"الكود\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"time-column\",\n                                            children: \"الوقت\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"content-column\",\n                                            children: \"المحتوى\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"duration-column\",\n                                            children: \"المدة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 859,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"status-column\",\n                                            children: \"الحالة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"actions-column\",\n                                            children: \"إجراءات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 861,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 855,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid-body\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"loading\",\n                                        children: \"جاري التحميل...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 866,\n                                        columnNumber: 15\n                                    }, this) : gridRows.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid-row \".concat(row.type, \" \").concat(row.isRerun ? 'rerun' : '', \" \").concat(row.isTemporary ? 'temporary' : ''),\n                                            draggable: row.type === 'filler' || row.type === 'empty',\n                                            onDragStart: (e)=>handleRowDragStart(e, index),\n                                            onDrop: (e)=>handleRowDrop(e, index),\n                                            onDragOver: (e)=>e.preventDefault(),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"code-cell\",\n                                                    children: row.type === 'segment' || row.type === 'filler' ? row.segmentCode || row.mediaItemId || \"\".concat(row.type.toUpperCase(), \"_\").concat(row.id.slice(-6)) : ''\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"time-cell\",\n                                                    children: row.time || ''\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"content-cell\",\n                                                    onDrop: (e)=>handleDrop(e, index),\n                                                    onDragOver: (e)=>e.preventDefault(),\n                                                    children: row.content || ''\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 886,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"duration-cell\",\n                                                    children: row.duration || ''\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"status-cell\",\n                                                    children: [\n                                                        row.type === 'segment' && row.isTemporary && '🟣 مؤقت',\n                                                        row.type === 'segment' && !row.isRerun && !row.isTemporary && (row.originalStartTime ? Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ? '⚠️ انحراف' : '✅ دقيق' : '🎯 أساسي'),\n                                                        row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة',\n                                                        row.type === 'filler' && '📺 فاصل',\n                                                        row.type === 'empty' && '⚪ فارغ'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"actions-cell\",\n                                                    children: [\n                                                        row.type === 'empty' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn add-row\",\n                                                                    title: \"إضافة صف\",\n                                                                    onClick: ()=>addEmptyRow(index),\n                                                                    children: \"➕\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn add-multiple-rows\",\n                                                                    title: \"إضافة 5 صفوف\",\n                                                                    onClick: ()=>addMultipleEmptyRows(index, 5),\n                                                                    children: \"➕➕\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 918,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                row.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn delete-row\",\n                                                                    title: \"حذف صف\",\n                                                                    onClick: ()=>deleteRow(row.id),\n                                                                    children: \"➖\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true),\n                                                        row.type === 'filler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn move-up\",\n                                                                    title: \"تحريك لأعلى\",\n                                                                    onClick: ()=>moveRowUp(index),\n                                                                    disabled: index === 0,\n                                                                    children: \"⬆️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 938,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn move-down\",\n                                                                    title: \"تحريك لأسفل\",\n                                                                    onClick: ()=>moveRowDown(index),\n                                                                    disabled: index === gridRows.length - 1,\n                                                                    children: \"⬇️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"action-btn delete-row\",\n                                                                    title: \"حذف فاصل\",\n                                                                    onClick: ()=>{\n                                                                        // تحويل الفاصل إلى صف فارغ\n                                                                        const newRows = [\n                                                                            ...gridRows\n                                                                        ];\n                                                                        newRows[index] = {\n                                                                            id: \"empty_\".concat(Date.now()),\n                                                                            type: 'empty',\n                                                                            canDelete: true\n                                                                        };\n                                                                        // إعادة حساب الأوقات\n                                                                        recalculateTimes(newRows);\n                                                                    },\n                                                                    children: \"\\uD83D\\uDDD1️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 954,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true),\n                                                        row.type === 'segment' && row.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"action-btn replace-temp\",\n                                                            title: \"استبدال بمادة حقيقية\",\n                                                            onClick: ()=>{\n                                                                alert('💡 لاستبدال المادة المؤقتة:\\n\\n1. أضف المادة الحقيقية لقاعدة البيانات\\n2. احذف المادة المؤقتة\\n3. اسحب المادة الجديدة من القائمة الجانبية');\n                                                            },\n                                                            style: {\n                                                                color: '#9c27b0'\n                                                            },\n                                                            children: \"\\uD83D\\uDD04\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 975,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        row.type === 'segment' && row.canDelete && !row.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"action-btn delete-row\",\n                                                            title: \"حذف سيجمنت\",\n                                                            onClick: ()=>deleteSegment(row.id),\n                                                            children: \"❌\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 987,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 908,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, row.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 864,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                    lineNumber: 768,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n            lineNumber: 723,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n        lineNumber: 722,\n        columnNumber: 5\n    }, this);\n}\n_s(DailySchedulePage, \"l78MCoamIEbt5T/8CCdwPLkgwyk=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = DailySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"DailySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction AuthGuard(param) {\n    let { children, requiredPermissions = [], requiredRole, fallbackComponent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        // المدير له صلاحيات كاملة\n        if (user.role === 'ADMIN') {\n            return true;\n        }\n        // التحقق من الدور المطلوب\n        if (role && user.role !== role) {\n            return false;\n        }\n        // التحقق من الصلاحيات المطلوبة\n        if (permissions.length > 0) {\n            const userPermissions = getUserPermissions(user.role);\n            return permissions.every((permission)=>userPermissions.includes(permission) || userPermissions.includes('ALL'));\n        }\n        return true;\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user === null || user === void 0 ? void 0 : user.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user === null || user === void 0 ? void 0 : user.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AuthGuard, \"fqF8YvhaHbrPIfzSUHTCm0cPUfQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthGuard;\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        if (user.role === 'ADMIN') return true;\n        const userPermissions = getUserPermissions(user.role);\n        return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN',\n        isMediaManager: (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER',\n        isScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER',\n        isViewer: (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER'\n    };\n}\n_s1(useAuth, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AuthGuard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction DashboardLayout(param) {\n    let { children, title = 'لوحة التحكم', subtitle = 'إدارة النظام', icon = '📊', requiredPermissions, requiredRole } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const timer = setInterval({\n                \"DashboardLayout.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"DashboardLayout.useEffect.timer\"], 1000);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>clearInterval(timer)\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: false,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'جدول الإذاعة اليومي',\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: requiredPermissions,\n        requiredRole: requiredRole,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        fontSize: '1.2rem',\n                                        fontWeight: '900',\n                                        fontFamily: 'Arial, sans-serif',\n                                        gap: '5px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '900',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"X\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '1rem',\n                                                fontWeight: '300'\n                                            },\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '800',\n                                                letterSpacing: '1px'\n                                            },\n                                            children: \"Prime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        marginRight: sidebarOpen ? '280px' : '0',\n                        transition: 'margin-right 0.3s ease'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"متصل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: currentTime.toLocaleTimeString('ar-EG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '20px',\n                                padding: '25px',\n                                border: '1px solid #4a5568',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                                maxWidth: '1000px',\n                                margin: '0 auto'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 55,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"KNYedtH3V/oElqzTesyis3gRu2I=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DashboardLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction Sidebar(param) {\n    let { isOpen, onToggle } = param;\n    var _user_name;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const menuItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'إدارة المستخدمين',\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null,\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            path: '/statistics',\n            permission: null,\n            adminOnly: true\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    right: isOpen ? 0 : '-280px',\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    borderLeft: '1px solid #2d3748',\n                    transition: 'right 0.3s ease',\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            fontSize: '1.5rem',\n                                            fontWeight: '900',\n                                            fontFamily: 'Arial, sans-serif',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '900',\n                                                    fontSize: '1.8rem'\n                                                },\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#6c757d',\n                                                    fontSize: '1.2rem',\n                                                    fontWeight: '300'\n                                                },\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '800',\n                                                    letterSpacing: '1px'\n                                                },\n                                                children: \"Prime\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '1.1rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: \"نظام الإدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: \"المحتوى الإعلامي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                style: {\n                                    background: 'transparent',\n                                    border: 'none',\n                                    color: '#a0aec0',\n                                    fontSize: '1.2rem',\n                                    cursor: 'pointer',\n                                    padding: '5px'\n                                },\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    marginBottom: '10px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '35px',\n                                            height: '35px',\n                                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                            borderRadius: '50%',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            color: 'white',\n                                            fontSize: '1rem',\n                                            fontWeight: 'bold'\n                                        },\n                                        children: (user === null || user === void 0 ? void 0 : (_user_name = user.name) === null || _user_name === void 0 ? void 0 : _user_name.charAt(0)) || 'U'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: (user === null || user === void 0 ? void 0 : user.name) || 'مستخدم'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: [\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && '👑 مدير النظام',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER' && '📝 مدير المحتوى',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER' && '📅 مجدول البرامج',\n                                                    (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER' && '👁️ مستخدم عرض'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '5px',\n                                    color: '#68d391',\n                                    fontSize: '0.8rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '6px',\n                                            height: '6px',\n                                            background: '#68d391',\n                                            borderRadius: '50%'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"متصل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#4299e1' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    padding: '12px 20px',\n                                    textAlign: 'right',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    fontSize: '0.9rem',\n                                    transition: 'all 0.2s',\n                                    borderRight: isActive ? '3px solid #4299e1' : '3px solid transparent'\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = '#2d3748';\n                                        e.target.style.color = 'white';\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '1.1rem'\n                                        },\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    // إضافة وظيفة تسجيل الخروج هنا\n                                    localStorage.removeItem('user');\n                                    localStorage.removeItem('token');\n                                    router.push('/login');\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    padding: '10px',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    gap: '8px',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '15px'\n                                },\n                                children: \"\\uD83D\\uDEAA تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    color: '#6c757d',\n                                    fontSize: '0.75rem',\n                                    fontFamily: 'Arial, sans-serif',\n                                    direction: 'ltr'\n                                },\n                                children: \"Powered By Mahmoud Ismail\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"F0XCIKCOXO7Aj/2LRHqPM/x8tuo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1NpZGViYXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUV5RDtBQUNuQjtBQU92QixTQUFTRyxRQUFRLEtBQWtDO1FBQWxDLEVBQUVDLE1BQU0sRUFBRUMsUUFBUSxFQUFnQixHQUFsQztRQTRMakJDOztJQTNMYixNQUFNQyxTQUFTUCwwREFBU0E7SUFDeEIsTUFBTVEsV0FBV1AsNERBQVdBO0lBQzVCLE1BQU0sRUFBRUssSUFBSSxFQUFFRyxhQUFhLEVBQUUsR0FBR1AsbURBQU9BO0lBRXZDLE1BQU1RLFlBQVk7UUFDaEI7WUFDRUMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtRQUNkO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtRQUNkO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtRQUNkO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtRQUNkO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtZQUNaQyxXQUFXO1FBQ2I7UUFDQTtZQUNFSixNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxZQUFZO1lBQ1pDLFdBQVc7UUFDYjtLQUVEO0lBRUQsTUFBTUMsb0JBQW9CTixVQUFVTyxNQUFNLENBQUNDLENBQUFBO1FBQ3pDLElBQUlBLEtBQUtILFNBQVMsSUFBSVQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNYSxJQUFJLE1BQUssU0FBUyxPQUFPO1FBQ3JELElBQUlELEtBQUtKLFVBQVUsSUFBSSxDQUFDTCxjQUFjUyxLQUFLSixVQUFVLEdBQUcsT0FBTztRQUMvRCxPQUFPO0lBQ1Q7SUFFQSxxQkFDRTs7WUFFR1Ysd0JBQ0MsOERBQUNnQjtnQkFDQ0MsT0FBTztvQkFDTEMsVUFBVTtvQkFDVkMsS0FBSztvQkFDTEMsTUFBTTtvQkFDTkMsT0FBTztvQkFDUEMsUUFBUTtvQkFDUkMsWUFBWTtvQkFDWkMsUUFBUTtvQkFDUkMsU0FBU0MsT0FBT0MsVUFBVSxJQUFJLE1BQU0sVUFBVTtnQkFDaEQ7Z0JBQ0FDLFNBQVMzQjs7Ozs7OzBCQUtiLDhEQUFDZTtnQkFDQ0MsT0FBTztvQkFDTEMsVUFBVTtvQkFDVkMsS0FBSztvQkFDTEUsT0FBT3JCLFNBQVMsSUFBSTtvQkFDcEI2QixPQUFPO29CQUNQQyxRQUFRO29CQUNSUCxZQUFZO29CQUNaUSxZQUFZO29CQUNaQyxZQUFZO29CQUNaUixRQUFRO29CQUNSQyxTQUFTO29CQUNUUSxlQUFlO29CQUNmQyxZQUFZO2dCQUNkOztrQ0FHQSw4REFBQ2xCO3dCQUFJQyxPQUFPOzRCQUNWa0IsU0FBUzs0QkFDVEMsY0FBYzs0QkFDZFgsU0FBUzs0QkFDVFksWUFBWTs0QkFDWkMsZ0JBQWdCO3dCQUNsQjs7MENBQ0UsOERBQUN0QjtnQ0FBSUMsT0FBTztvQ0FBRVEsU0FBUztvQ0FBUVksWUFBWTtvQ0FBVUUsS0FBSztnQ0FBTzs7a0RBQy9ELDhEQUFDdkI7d0NBQUlDLE9BQU87NENBQ1ZRLFNBQVM7NENBQ1RZLFlBQVk7NENBQ1pHLFVBQVU7NENBQ1ZDLFlBQVk7NENBQ1pQLFlBQVk7NENBQ1pLLEtBQUs7d0NBQ1A7OzBEQUNFLDhEQUFDRztnREFBS3pCLE9BQU87b0RBQ1hNLFlBQVk7b0RBQ1pvQixzQkFBc0I7b0RBQ3RCQyxxQkFBcUI7b0RBQ3JCSCxZQUFZO29EQUNaRCxVQUFVO2dEQUNaOzBEQUFHOzs7Ozs7MERBR0gsOERBQUNFO2dEQUFLekIsT0FBTztvREFDWDRCLE9BQU87b0RBQ1BMLFVBQVU7b0RBQ1ZDLFlBQVk7Z0RBQ2Q7MERBQUc7Ozs7OzswREFHSCw4REFBQ0M7Z0RBQUt6QixPQUFPO29EQUNYTSxZQUFZO29EQUNab0Isc0JBQXNCO29EQUN0QkMscUJBQXFCO29EQUNyQkgsWUFBWTtvREFDWkssZUFBZTtnREFDakI7MERBQUc7Ozs7Ozs7Ozs7OztrREFJTCw4REFBQzlCOzswREFDQyw4REFBQytCO2dEQUFHOUIsT0FBTztvREFDVDRCLE9BQU87b0RBQ1BHLFFBQVE7b0RBQ1JSLFVBQVU7b0RBQ1ZDLFlBQVk7Z0RBQ2Q7MERBQUc7Ozs7OzswREFHSCw4REFBQ1E7Z0RBQUVoQyxPQUFPO29EQUNSNEIsT0FBTztvREFDUEcsUUFBUTtvREFDUlIsVUFBVTtnREFDWjswREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtQLDhEQUFDVTtnQ0FDQ3RCLFNBQVMzQjtnQ0FDVGdCLE9BQU87b0NBQ0xNLFlBQVk7b0NBQ1o0QixRQUFRO29DQUNSTixPQUFPO29DQUNQTCxVQUFVO29DQUNWWSxRQUFRO29DQUNSakIsU0FBUztnQ0FDWDswQ0FDRDs7Ozs7Ozs7Ozs7O2tDQU1ILDhEQUFDbkI7d0JBQUlDLE9BQU87NEJBQ1ZrQixTQUFTOzRCQUNUQyxjQUFjO3dCQUNoQjs7MENBQ0UsOERBQUNwQjtnQ0FBSUMsT0FBTztvQ0FDVlEsU0FBUztvQ0FDVFksWUFBWTtvQ0FDWkUsS0FBSztvQ0FDTGMsY0FBYztnQ0FDaEI7O2tEQUNFLDhEQUFDckM7d0NBQUlDLE9BQU87NENBQ1ZZLE9BQU87NENBQ1BDLFFBQVE7NENBQ1JQLFlBQVk7NENBQ1orQixjQUFjOzRDQUNkN0IsU0FBUzs0Q0FDVFksWUFBWTs0Q0FDWkMsZ0JBQWdCOzRDQUNoQk8sT0FBTzs0Q0FDUEwsVUFBVTs0Q0FDVkMsWUFBWTt3Q0FDZDtrREFDR3ZDLENBQUFBLGlCQUFBQSw0QkFBQUEsYUFBQUEsS0FBTUssSUFBSSxjQUFWTCxpQ0FBQUEsV0FBWXFELE1BQU0sQ0FBQyxPQUFNOzs7Ozs7a0RBRTVCLDhEQUFDdkM7OzBEQUNDLDhEQUFDaUM7Z0RBQUVoQyxPQUFPO29EQUNSNEIsT0FBTztvREFDUEcsUUFBUTtvREFDUlIsVUFBVTtvREFDVkMsWUFBWTtnREFDZDswREFDR3ZDLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTUssSUFBSSxLQUFJOzs7Ozs7MERBRWpCLDhEQUFDMEM7Z0RBQUVoQyxPQUFPO29EQUNSNEIsT0FBTztvREFDUEcsUUFBUTtvREFDUlIsVUFBVTtnREFDWjs7b0RBQ0d0QyxDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1hLElBQUksTUFBSyxXQUFXO29EQUMxQmIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNYSxJQUFJLE1BQUssbUJBQW1CO29EQUNsQ2IsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNYSxJQUFJLE1BQUssZUFBZTtvREFDOUJiLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWEsSUFBSSxNQUFLLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSWxDLDhEQUFDQztnQ0FBSUMsT0FBTztvQ0FDVlEsU0FBUztvQ0FDVFksWUFBWTtvQ0FDWkUsS0FBSztvQ0FDTE0sT0FBTztvQ0FDUEwsVUFBVTtnQ0FDWjs7a0RBQ0UsOERBQUN4Qjt3Q0FBSUMsT0FBTzs0Q0FDVlksT0FBTzs0Q0FDUEMsUUFBUTs0Q0FDUlAsWUFBWTs0Q0FDWitCLGNBQWM7d0NBQ2hCOzs7Ozs7b0NBQVM7Ozs7Ozs7Ozs7Ozs7a0NBTWIsOERBQUN0Qzt3QkFBSUMsT0FBTzs0QkFDVnVDLE1BQU07NEJBQ05yQixTQUFTOzRCQUNUc0IsV0FBVzt3QkFDYjtrQ0FDRzdDLGtCQUFrQjhDLEdBQUcsQ0FBQyxDQUFDNUMsTUFBTTZDOzRCQUM1QixNQUFNQyxXQUFXeEQsYUFBYVUsS0FBS0wsSUFBSTs0QkFDdkMscUJBQ0UsOERBQUN5QztnQ0FFQ3RCLFNBQVM7b0NBQ1B6QixPQUFPMEQsSUFBSSxDQUFDL0MsS0FBS0wsSUFBSTtvQ0FDckIsSUFBSWlCLE9BQU9DLFVBQVUsSUFBSSxLQUFLO3dDQUM1QjFCO29DQUNGO2dDQUNGO2dDQUNBZ0IsT0FBTztvQ0FDTFksT0FBTztvQ0FDUE4sWUFBWXFDLFdBQVcsWUFBWTtvQ0FDbkNmLE9BQU9lLFdBQVcsVUFBVTtvQ0FDNUJULFFBQVE7b0NBQ1JoQixTQUFTO29DQUNUMkIsV0FBVztvQ0FDWFYsUUFBUTtvQ0FDUjNCLFNBQVM7b0NBQ1RZLFlBQVk7b0NBQ1pFLEtBQUs7b0NBQ0xDLFVBQVU7b0NBQ1ZSLFlBQVk7b0NBQ1orQixhQUFhSCxXQUFXLHNCQUFzQjtnQ0FDaEQ7Z0NBQ0FJLGNBQWMsQ0FBQ0M7b0NBQ2IsSUFBSSxDQUFDTCxVQUFVO3dDQUNiSyxFQUFFQyxNQUFNLENBQUNqRCxLQUFLLENBQUNNLFVBQVUsR0FBRzt3Q0FDNUIwQyxFQUFFQyxNQUFNLENBQUNqRCxLQUFLLENBQUM0QixLQUFLLEdBQUc7b0NBQ3pCO2dDQUNGO2dDQUNBc0IsY0FBYyxDQUFDRjtvQ0FDYixJQUFJLENBQUNMLFVBQVU7d0NBQ2JLLEVBQUVDLE1BQU0sQ0FBQ2pELEtBQUssQ0FBQ00sVUFBVSxHQUFHO3dDQUM1QjBDLEVBQUVDLE1BQU0sQ0FBQ2pELEtBQUssQ0FBQzRCLEtBQUssR0FBRztvQ0FDekI7Z0NBQ0Y7O2tEQUVBLDhEQUFDSDt3Q0FBS3pCLE9BQU87NENBQUV1QixVQUFVO3dDQUFTO2tEQUFJMUIsS0FBS04sSUFBSTs7Ozs7O29DQUM5Q00sS0FBS1AsSUFBSTs7K0JBcENMb0Q7Ozs7O3dCQXVDWDs7Ozs7O2tDQUlGLDhEQUFDM0M7d0JBQUlDLE9BQU87NEJBQ1ZrQixTQUFTOzRCQUNUaUMsV0FBVzt3QkFDYjs7MENBQ0UsOERBQUNsQjtnQ0FDQ3RCLFNBQVM7b0NBQ1AsK0JBQStCO29DQUMvQnlDLGFBQWFDLFVBQVUsQ0FBQztvQ0FDeEJELGFBQWFDLFVBQVUsQ0FBQztvQ0FDeEJuRSxPQUFPMEQsSUFBSSxDQUFDO2dDQUNkO2dDQUNBNUMsT0FBTztvQ0FDTFksT0FBTztvQ0FDUE4sWUFBWTtvQ0FDWnNCLE9BQU87b0NBQ1BNLFFBQVE7b0NBQ1JHLGNBQWM7b0NBQ2RuQixTQUFTO29DQUNUaUIsUUFBUTtvQ0FDUjNCLFNBQVM7b0NBQ1RZLFlBQVk7b0NBQ1pDLGdCQUFnQjtvQ0FDaEJDLEtBQUs7b0NBQ0xDLFVBQVU7b0NBQ1ZDLFlBQVk7b0NBQ1pZLGNBQWM7Z0NBQ2hCOzBDQUNEOzs7Ozs7MENBS0QsOERBQUNyQztnQ0FBSUMsT0FBTztvQ0FDVjZDLFdBQVc7b0NBQ1hqQixPQUFPO29DQUNQTCxVQUFVO29DQUNWTixZQUFZO29DQUNacUMsV0FBVztnQ0FDYjswQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPYjtHQXRVd0J4RTs7UUFDUEgsc0RBQVNBO1FBQ1BDLHdEQUFXQTtRQUNJQywrQ0FBT0E7OztLQUhqQkMiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxzcmNcXGNvbXBvbmVudHNcXFNpZGViYXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnLi9BdXRoR3VhcmQnO1xuXG5pbnRlcmZhY2UgU2lkZWJhclByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvblRvZ2dsZTogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2lkZWJhcih7IGlzT3Blbiwgb25Ub2dnbGUgfTogU2lkZWJhclByb3BzKSB7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKCk7XG4gIGNvbnN0IHsgdXNlciwgaGFzUGVybWlzc2lvbiB9ID0gdXNlQXV0aCgpO1xuXG4gIGNvbnN0IG1lbnVJdGVtcyA9IFtcbiAgICB7XG4gICAgICBuYW1lOiAn2YTZiNit2Kkg2KfZhNiq2K3Zg9mFJyxcbiAgICAgIGljb246ICfwn5OKJyxcbiAgICAgIHBhdGg6ICcvZGFzaGJvYXJkJyxcbiAgICAgIHBlcm1pc3Npb246IG51bGxcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYp9mE2YXZiNin2K8g2KfZhNil2LnZhNin2YXZitipJyxcbiAgICAgIGljb246ICfwn46sJyxcbiAgICAgIHBhdGg6ICcvbWVkaWEtbGlzdCcsXG4gICAgICBwZXJtaXNzaW9uOiAnTUVESUFfUkVBRCdcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYpdi22KfZgdipINmF2KfYr9ipJyxcbiAgICAgIGljb246ICfinpUnLFxuICAgICAgcGF0aDogJy9hZGQtbWVkaWEnLFxuICAgICAgcGVybWlzc2lvbjogJ01FRElBX0NSRUFURSdcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYp9mE2K7YsdmK2LfYqSDYp9mE2KjYsdin2YXYrNmK2KknLFxuICAgICAgaWNvbjogJ/Cfk4UnLFxuICAgICAgcGF0aDogJy93ZWVrbHktc2NoZWR1bGUnLFxuICAgICAgcGVybWlzc2lvbjogJ1NDSEVEVUxFX1JFQUQnXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAn2KXYr9in2LHYqSDYp9mE2YXYs9iq2K7Yr9mF2YrZhicsXG4gICAgICBpY29uOiAn8J+RpScsXG4gICAgICBwYXRoOiAnL2FkbWluLWRhc2hib2FyZCcsXG4gICAgICBwZXJtaXNzaW9uOiBudWxsLFxuICAgICAgYWRtaW5Pbmx5OiB0cnVlXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAn2KfZhNil2K3Ytdin2KbZitin2KonLFxuICAgICAgaWNvbjogJ/Cfk4gnLFxuICAgICAgcGF0aDogJy9zdGF0aXN0aWNzJyxcbiAgICAgIHBlcm1pc3Npb246IG51bGwsXG4gICAgICBhZG1pbk9ubHk6IHRydWVcbiAgICB9LFxuXG4gIF07XG5cbiAgY29uc3QgZmlsdGVyZWRNZW51SXRlbXMgPSBtZW51SXRlbXMuZmlsdGVyKGl0ZW0gPT4ge1xuICAgIGlmIChpdGVtLmFkbWluT25seSAmJiB1c2VyPy5yb2xlICE9PSAnQURNSU4nKSByZXR1cm4gZmFsc2U7XG4gICAgaWYgKGl0ZW0ucGVybWlzc2lvbiAmJiAhaGFzUGVybWlzc2lvbihpdGVtLnBlcm1pc3Npb24pKSByZXR1cm4gZmFsc2U7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0pO1xuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiDYrtmE2YHZitipINi02YHYp9mB2Kkg2YTZhNmF2YjYqNin2YrZhCAqL31cbiAgICAgIHtpc09wZW4gJiYgKFxuICAgICAgICA8ZGl2XG4gICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLFxuICAgICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgICAgbGVmdDogMCxcbiAgICAgICAgICAgIHJpZ2h0OiAwLFxuICAgICAgICAgICAgYm90dG9tOiAwLFxuICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMCwgMCwgMCwgMC41KScsXG4gICAgICAgICAgICB6SW5kZXg6IDk5OCxcbiAgICAgICAgICAgIGRpc3BsYXk6IHdpbmRvdy5pbm5lcldpZHRoIDw9IDc2OCA/ICdibG9jaycgOiAnbm9uZSdcbiAgICAgICAgICB9fVxuICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlfVxuICAgICAgICAvPlxuICAgICAgKX1cblxuICAgICAgey8qINin2YTYtNix2YrYtyDYp9mE2KzYp9mG2KjZiiAqL31cbiAgICAgIDxkaXZcbiAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICBwb3NpdGlvbjogJ2ZpeGVkJyxcbiAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgcmlnaHQ6IGlzT3BlbiA/IDAgOiAnLTI4MHB4JyxcbiAgICAgICAgICB3aWR0aDogJzI4MHB4JyxcbiAgICAgICAgICBoZWlnaHQ6ICcxMDB2aCcsXG4gICAgICAgICAgYmFja2dyb3VuZDogJyMxYTFkMjknLFxuICAgICAgICAgIGJvcmRlckxlZnQ6ICcxcHggc29saWQgIzJkMzc0OCcsXG4gICAgICAgICAgdHJhbnNpdGlvbjogJ3JpZ2h0IDAuM3MgZWFzZScsXG4gICAgICAgICAgekluZGV4OiA5OTksXG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLFxuICAgICAgICAgIGZvbnRGYW1pbHk6ICdDYWlybywgQXJpYWwsIHNhbnMtc2VyaWYnXG4gICAgICAgIH19XG4gICAgICA+XG4gICAgICAgIHsvKiDYsdij2LMg2KfZhNi02LHZiti3INin2YTYrNin2YbYqNmKICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzIwcHgnLFxuICAgICAgICAgIGJvcmRlckJvdHRvbTogJzFweCBzb2xpZCAjMmQzNzQ4JyxcbiAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzEycHgnIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzEuNXJlbScsXG4gICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc5MDAnLFxuICAgICAgICAgICAgICBmb250RmFtaWx5OiAnQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgICAgICAgICAgICBnYXA6ICc4cHgnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZmZDcwMCAwJSwgI2ZmZWQ0ZSA1MCUsICNmZmQ3MDAgMTAwJSknLFxuICAgICAgICAgICAgICAgIFdlYmtpdEJhY2tncm91bmRDbGlwOiAndGV4dCcsXG4gICAgICAgICAgICAgICAgV2Via2l0VGV4dEZpbGxDb2xvcjogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnOTAwJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuOHJlbSdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgWFxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgY29sb3I6ICcjNmM3NTdkJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuMnJlbScsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzMwMCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgLVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSknLFxuICAgICAgICAgICAgICAgIFdlYmtpdEJhY2tncm91bmRDbGlwOiAndGV4dCcsXG4gICAgICAgICAgICAgICAgV2Via2l0VGV4dEZpbGxDb2xvcjogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnODAwJyxcbiAgICAgICAgICAgICAgICBsZXR0ZXJTcGFjaW5nOiAnMXB4J1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICBQcmltZVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgIG1hcmdpbjogMCxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuMXJlbScsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgINmG2LjYp9mFINin2YTYpdiv2KfYsdipXG4gICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgIDxwIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgY29sb3I6ICcjYTBhZWMwJyxcbiAgICAgICAgICAgICAgICBtYXJnaW46IDAsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjhyZW0nXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgINin2YTZhdit2KrZiNmJINin2YTYpdi52YTYp9mF2YpcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25Ub2dnbGV9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgY29sb3I6ICcjYTBhZWMwJyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjJyZW0nLFxuICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgcGFkZGluZzogJzVweCdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAg4pyVXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDZhdi52YTZiNmF2KfYqiDYp9mE2YXYs9iq2K7Yr9mFICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzIwcHgnLFxuICAgICAgICAgIGJvcmRlckJvdHRvbTogJzFweCBzb2xpZCAjMmQzNzQ4J1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgIGdhcDogJzEycHgnLFxuICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMTBweCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgd2lkdGg6ICczNXB4JyxcbiAgICAgICAgICAgICAgaGVpZ2h0OiAnMzVweCcsXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoNDVkZWcsICM2NjdlZWEsICM3NjRiYTIpJyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnXG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAge3VzZXI/Lm5hbWU/LmNoYXJBdCgwKSB8fCAnVSd9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxwIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgbWFyZ2luOiAwLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAge3VzZXI/Lm5hbWUgfHwgJ9mF2LPYqtiu2K/ZhSd9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPHAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBjb2xvcjogJyNhMGFlYzAnLFxuICAgICAgICAgICAgICAgIG1hcmdpbjogMCxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuOHJlbSdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAge3VzZXI/LnJvbGUgPT09ICdBRE1JTicgJiYgJ/CfkZEg2YXYr9mK2LEg2KfZhNmG2LjYp9mFJ31cbiAgICAgICAgICAgICAgICB7dXNlcj8ucm9sZSA9PT0gJ01FRElBX01BTkFHRVInICYmICfwn5OdINmF2K/ZitixINin2YTZhdit2KrZiNmJJ31cbiAgICAgICAgICAgICAgICB7dXNlcj8ucm9sZSA9PT0gJ1NDSEVEVUxFUicgJiYgJ/Cfk4Ug2YXYrNiv2YjZhCDYp9mE2KjYsdin2YXYrCd9XG4gICAgICAgICAgICAgICAge3VzZXI/LnJvbGUgPT09ICdWSUVXRVInICYmICfwn5GB77iPINmF2LPYqtiu2K/ZhSDYudix2LYnfVxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgIGdhcDogJzVweCcsXG4gICAgICAgICAgICBjb2xvcjogJyM2OGQzOTEnLFxuICAgICAgICAgICAgZm9udFNpemU6ICcwLjhyZW0nXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIHdpZHRoOiAnNnB4JyxcbiAgICAgICAgICAgICAgaGVpZ2h0OiAnNnB4JyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyM2OGQzOTEnLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnXG4gICAgICAgICAgICB9fT48L2Rpdj5cbiAgICAgICAgICAgINmF2KrYtdmEXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDZgtin2KbZhdipINin2YTYqtmG2YLZhCAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIGZsZXg6IDEsXG4gICAgICAgICAgcGFkZGluZzogJzIwcHggMCcsXG4gICAgICAgICAgb3ZlcmZsb3dZOiAnYXV0bydcbiAgICAgICAgfX0+XG4gICAgICAgICAge2ZpbHRlcmVkTWVudUl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGlzQWN0aXZlID0gcGF0aG5hbWUgPT09IGl0ZW0ucGF0aDtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKGl0ZW0ucGF0aCk7XG4gICAgICAgICAgICAgICAgICBpZiAod2luZG93LmlubmVyV2lkdGggPD0gNzY4KSB7XG4gICAgICAgICAgICAgICAgICAgIG9uVG9nZ2xlKCk7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGlzQWN0aXZlID8gJyM0Mjk5ZTEnIDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBpc0FjdGl2ZSA/ICd3aGl0ZScgOiAnI2EwYWVjMCcsXG4gICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxMnB4IDIwcHgnLFxuICAgICAgICAgICAgICAgICAgdGV4dEFsaWduOiAncmlnaHQnLFxuICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgIGdhcDogJzEycHgnLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nLFxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJpZ2h0OiBpc0FjdGl2ZSA/ICczcHggc29saWQgIzQyOTllMScgOiAnM3B4IHNvbGlkIHRyYW5zcGFyZW50J1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgaWYgKCFpc0FjdGl2ZSkge1xuICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJyMyZDM3NDgnO1xuICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5jb2xvciA9ICd3aGl0ZSc7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAoIWlzQWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAndHJhbnNwYXJlbnQnO1xuICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5jb2xvciA9ICcjYTBhZWMwJztcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFNpemU6ICcxLjFyZW0nIH19PntpdGVtLmljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKTtcbiAgICAgICAgICB9KX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qINij2LPZgdmEINin2YTYtNix2YrYtyDYp9mE2KzYp9mG2KjZiiAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIHBhZGRpbmc6ICcyMHB4JyxcbiAgICAgICAgICBib3JkZXJUb3A6ICcxcHggc29saWQgIzJkMzc0OCdcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAvLyDYpdi22KfZgdipINmI2LjZitmB2Kkg2KrYs9is2YrZhCDYp9mE2K7YsdmI2Kwg2YfZhtinXG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd0b2tlbicpO1xuICAgICAgICAgICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgI2Y1NjU2NSwgI2U1M2UzZSknLFxuICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcxMHB4JyxcbiAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgZ2FwOiAnOHB4JyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nLFxuICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzE1cHgnXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIPCfmqog2KrYs9is2YrZhCDYp9mE2K7YsdmI2KxcbiAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIHsvKiDZhti1INin2YTZhdi32YjYsSAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgICAgICAgICAgY29sb3I6ICcjNmM3NTdkJyxcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMC43NXJlbScsXG4gICAgICAgICAgICBmb250RmFtaWx5OiAnQXJpYWwsIHNhbnMtc2VyaWYnLFxuICAgICAgICAgICAgZGlyZWN0aW9uOiAnbHRyJ1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgUG93ZXJlZCBCeSBNYWhtb3VkIElzbWFpbFxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVJvdXRlciIsInVzZVBhdGhuYW1lIiwidXNlQXV0aCIsIlNpZGViYXIiLCJpc09wZW4iLCJvblRvZ2dsZSIsInVzZXIiLCJyb3V0ZXIiLCJwYXRobmFtZSIsImhhc1Blcm1pc3Npb24iLCJtZW51SXRlbXMiLCJuYW1lIiwiaWNvbiIsInBhdGgiLCJwZXJtaXNzaW9uIiwiYWRtaW5Pbmx5IiwiZmlsdGVyZWRNZW51SXRlbXMiLCJmaWx0ZXIiLCJpdGVtIiwicm9sZSIsImRpdiIsInN0eWxlIiwicG9zaXRpb24iLCJ0b3AiLCJsZWZ0IiwicmlnaHQiLCJib3R0b20iLCJiYWNrZ3JvdW5kIiwiekluZGV4IiwiZGlzcGxheSIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJvbkNsaWNrIiwid2lkdGgiLCJoZWlnaHQiLCJib3JkZXJMZWZ0IiwidHJhbnNpdGlvbiIsImZsZXhEaXJlY3Rpb24iLCJmb250RmFtaWx5IiwicGFkZGluZyIsImJvcmRlckJvdHRvbSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImdhcCIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsInNwYW4iLCJXZWJraXRCYWNrZ3JvdW5kQ2xpcCIsIldlYmtpdFRleHRGaWxsQ29sb3IiLCJjb2xvciIsImxldHRlclNwYWNpbmciLCJoMyIsIm1hcmdpbiIsInAiLCJidXR0b24iLCJib3JkZXIiLCJjdXJzb3IiLCJtYXJnaW5Cb3R0b20iLCJib3JkZXJSYWRpdXMiLCJjaGFyQXQiLCJmbGV4Iiwib3ZlcmZsb3dZIiwibWFwIiwiaW5kZXgiLCJpc0FjdGl2ZSIsInB1c2giLCJ0ZXh0QWxpZ24iLCJib3JkZXJSaWdodCIsIm9uTW91c2VFbnRlciIsImUiLCJ0YXJnZXQiLCJvbk1vdXNlTGVhdmUiLCJib3JkZXJUb3AiLCJsb2NhbFN0b3JhZ2UiLCJyZW1vdmVJdGVtIiwiZGlyZWN0aW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdaily-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);