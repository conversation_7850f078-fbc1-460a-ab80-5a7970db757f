/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/daily-schedule/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdaily-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdaily-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/daily-schedule/page.tsx */ \"(app-pages-browser)/./src/app/daily-schedule/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFpbHktc2NoZWR1bGUlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDRMQUFpSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcRG9jIGRhdGFiYXNlXFxcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcXFxtZWRpYS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYWlseS1zY2hlZHVsZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdaily-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css":
/*!***************************************************!*\
  !*** ./src/app/daily-schedule/daily-schedule.css ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a89ad030ddd8\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFpbHktc2NoZWR1bGUvZGFpbHktc2NoZWR1bGUuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxkYWlseS1zY2hlZHVsZVxcZGFpbHktc2NoZWR1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYTg5YWQwMzBkZGQ4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/daily-schedule/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/daily-schedule/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DailySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _daily_schedule_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./daily-schedule.css */ \"(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n// Mock user context for now\nconst useAuth = ()=>({\n        user: {\n            username: 'مستخدم',\n            role: 'مدير'\n        }\n    });\nfunction DailySchedulePage() {\n    _s();\n    const { user } = useAuth();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [gridRows, setGridRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [weeklySchedule, setWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showWeeklySchedule, setShowWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تهيئة التاريخ الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            setSelectedDate(today.toISOString().split('T')[0]);\n        }\n    }[\"DailySchedulePage.useEffect\"], []);\n    // جلب البيانات عند تغيير التاريخ\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (selectedDate) {\n                fetchScheduleData();\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        selectedDate\n    ]);\n    // جلب بيانات الجدول الإذاعي\n    const fetchScheduleData = async ()=>{\n        setLoading(true);\n        try {\n            console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);\n            const response = await fetch(\"/api/daily-schedule?date=\".concat(selectedDate));\n            const data = await response.json();\n            if (data.success) {\n                var _data_data_availableMedia;\n                setScheduleItems(data.data.scheduleItems);\n                setAvailableMedia(data.data.availableMedia || []);\n                setGridRows(data.data.scheduleRows || []);\n                if (data.fromSavedFile) {\n                    console.log('📂 تم تحميل جدول محفوظ مسبقاً');\n                    console.log('💾 تاريخ الحفظ:', data.savedAt);\n                    console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);\n                } else {\n                    console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');\n                    console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');\n                }\n                console.log('📦 المواد المتاحة:', ((_data_data_availableMedia = data.data.availableMedia) === null || _data_data_availableMedia === void 0 ? void 0 : _data_data_availableMedia.length) || 0);\n                // عرض عينة من المواد المتاحة\n                if (data.data.availableMedia && data.data.availableMedia.length > 0) {\n                    console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));\n                }\n            }\n            // جلب الجدول الأسبوعي للمراجعة\n            await fetchWeeklySchedule();\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // جلب الجدول الأسبوعي للمراجعة\n    const fetchWeeklySchedule = async ()=>{\n        try {\n            const date = new Date(selectedDate);\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const data = await response.json();\n            console.log('📊 استجابة API للجدول الأسبوعي:', data);\n            if (data.success && data.data) {\n                // استخراج مواد اليوم المحدد من scheduleItems\n                const dayOfWeek = new Date(selectedDate).getDay();\n                const daySchedule = [];\n                console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);\n                console.log('📦 البيانات المتاحة:', data.data);\n                // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)\n                if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {\n                    console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);\n                    const dayItems = data.data.scheduleItems.filter((item)=>{\n                        var _item_mediaItem;\n                        console.log('🔍 فحص المادة:', (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);\n                        return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء\n                    }).sort((a, b)=>{\n                        // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59\n                        const timeA = a.startTime;\n                        const timeB = b.startTime;\n                        const getTimeOrder = (time)=>{\n                            const hour = parseInt(time.split(':')[0]);\n                            if (hour >= 8 && hour < 18) return 1; // صباح ومساء\n                            if (hour >= 18) return 2; // برايم تايم\n                            return 3; // منتصف الليل والفجر\n                        };\n                        const orderA = getTimeOrder(timeA);\n                        const orderB = getTimeOrder(timeB);\n                        if (orderA !== orderB) return orderA - orderB;\n                        return timeA.localeCompare(timeB);\n                    });\n                    console.log('✅ مواد اليوم المفلترة:', dayItems.length);\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3;\n                        const scheduleItem = {\n                            time: item.startTime,\n                            name: ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'مادة غير محددة',\n                            episodeNumber: item.episodeNumber || ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.episodeNumber),\n                            partNumber: item.partNumber || ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.partNumber),\n                            seasonNumber: item.seasonNumber || ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.seasonNumber),\n                            isRerun: item.isRerun || false\n                        };\n                        daySchedule.push(scheduleItem);\n                        console.log('📺 إضافة مادة للخريطة:', scheduleItem);\n                    });\n                } else {\n                    console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');\n                }\n                setWeeklySchedule(daySchedule);\n                console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');\n            } else {\n                console.log('❌ فشل في جلب البيانات أو البيانات فارغة');\n                setWeeklySchedule([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);\n            setWeeklySchedule([]);\n        }\n    };\n    // فلترة المواد المتاحة\n    const filteredMedia = availableMedia.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesType = filterType === 'ALL' || item.type === filterType;\n        return matchesSearch && matchesType;\n    });\n    // أنواع المواد للفلترة\n    const mediaTypes = [\n        'ALL',\n        'PROGRAM',\n        'SERIES',\n        'MOVIE',\n        'PROMO',\n        'STING',\n        'FILL_IN',\n        'FILLER'\n    ];\n    // إضافة صف فارغ\n    const addEmptyRow = (afterIndex)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        const newRow = {\n            id: \"empty_\".concat(Date.now()),\n            type: 'empty',\n            canDelete: true\n        };\n        newRows.splice(afterIndex + 1, 0, newRow);\n        setGridRows(newRows);\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // إضافة صفوف فارغة متعددة\n    const addMultipleEmptyRows = function(afterIndex) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        for(let i = 0; i < count; i++){\n            const newRow = {\n                id: \"empty_\".concat(Date.now(), \"_\").concat(i),\n                type: 'empty',\n                canDelete: true\n            };\n            newRows.splice(afterIndex + 1 + i, 0, newRow);\n        }\n        setGridRows(newRows);\n        console.log(\"✅ تم إضافة \".concat(count, \" صف فارغ\"));\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // التحقق من الحاجة لإضافة صفوف فارغة\n    const checkAndAddEmptyRows = (currentIndex)=>{\n        const currentRows = gridRows;\n        const nextEmptyIndex = currentRows.findIndex((row, index)=>index > currentIndex && row.type === 'empty');\n        // إذا لم توجد صفوف فارغة بعد الفاصل الحالي\n        if (nextEmptyIndex === -1) {\n            console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 5 صفوف');\n            addMultipleEmptyRows(currentIndex, 5);\n        } else {\n            console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);\n        }\n    };\n    // حذف صف فارغ\n    const deleteRow = (rowId)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        // استعادة موضع التمرير بعد الحذف\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 100);\n    };\n    // حذف سيجمنت\n    const deleteSegment = (rowId)=>{\n        if (confirm('هل أنت متأكد من حذف هذا السيجمنت؟')) {\n            const newRows = gridRows.filter((row)=>row.id !== rowId);\n            recalculateTimes(newRows);\n            console.log('🗑️ تم حذف السيجمنت');\n        }\n    };\n    // حذف فاصل بدون تأكيد\n    const deleteFiller = (rowId)=>{\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        console.log('🗑️ تم حذف الفاصل');\n    };\n    // تحريك صف لأعلى\n    const moveRowUp = (index)=>{\n        if (index <= 0) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index - 1], newRows[index]] = [\n            newRows[index],\n            newRows[index - 1]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬆️ تم تحريك الصف لأعلى');\n    };\n    // تحريك صف لأسفل\n    const moveRowDown = (index)=>{\n        if (index >= gridRows.length - 1) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index], newRows[index + 1]] = [\n            newRows[index + 1],\n            newRows[index]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬇️ تم تحريك الصف لأسفل');\n    };\n    // معالجة سحب الصفوف داخل الجدول\n    const handleRowDragStart = (e, index)=>{\n        e.dataTransfer.setData('text/plain', index.toString());\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    // معالجة إسقاط الصفوف داخل الجدول\n    const handleRowDrop = (e, targetIndex)=>{\n        e.preventDefault();\n        const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));\n        if (sourceIndex === targetIndex) return;\n        const newRows = [\n            ...gridRows\n        ];\n        const [movedRow] = newRows.splice(sourceIndex, 1);\n        newRows.splice(targetIndex, 0, movedRow);\n        recalculateTimes(newRows);\n        console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);\n    };\n    // معالجة إسقاط المواد\n    const handleDrop = (e, rowIndex)=>{\n        e.preventDefault();\n        try {\n            var _mediaData_segments, _mediaData_segments1;\n            // محاولة الحصول على البيانات بطرق مختلفة\n            let mediaData;\n            const jsonData = e.dataTransfer.getData('application/json');\n            const textData = e.dataTransfer.getData('text/plain');\n            if (jsonData) {\n                mediaData = JSON.parse(jsonData);\n            } else if (textData) {\n                try {\n                    mediaData = JSON.parse(textData);\n                } catch (e) {\n                    console.error('❌ لا يمكن تحليل البيانات المسحوبة');\n                    return;\n                }\n            } else {\n                console.error('❌ لا توجد بيانات مسحوبة');\n                return;\n            }\n            // التحقق من أن الصف فارغ\n            const targetRow = gridRows[rowIndex];\n            if (targetRow.type !== 'empty') {\n                alert('يمكن إسقاط المواد في الصفوف الفارغة فقط');\n                return;\n            }\n            console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);\n            // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر\n            if (!mediaData || typeof mediaData !== 'object') {\n                console.error('❌ بيانات المادة غير صحيحة:', mediaData);\n                console.error('❌ نوع البيانات:', typeof mediaData);\n                return;\n            }\n            // التأكد من وجود الاسم\n            const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';\n            const itemType = mediaData.type || 'UNKNOWN';\n            const itemId = mediaData.id || Date.now().toString();\n            console.log('📥 معلومات المادة:', {\n                name: itemName,\n                type: itemType,\n                id: itemId,\n                segments: ((_mediaData_segments = mediaData.segments) === null || _mediaData_segments === void 0 ? void 0 : _mediaData_segments.length) || 0\n            });\n            // تحديد نوع المادة المسحوبة\n            let dragItemType = 'filler';\n            let itemContent = itemName;\n            // إضافة تفاصيل المادة\n            const details = [];\n            if (mediaData.episodeNumber) details.push(\"ح\".concat(mediaData.episodeNumber));\n            if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(\"م\".concat(mediaData.seasonNumber));\n            if (mediaData.partNumber) details.push(\"ج\".concat(mediaData.partNumber));\n            const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n            // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات\n            if ([\n                'PROMO',\n                'STING',\n                'FILLER',\n                'FILL_IN'\n            ].includes(itemType)) {\n                dragItemType = 'filler';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n            } else {\n                dragItemType = 'segment';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" (مادة إضافية)\");\n            }\n            // حساب المدة الحقيقية للمادة\n            let itemDuration = '00:01:00'; // مدة افتراضية\n            console.log('🔍 تحليل مدة المادة:', {\n                name: itemName,\n                hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),\n                segmentsCount: ((_mediaData_segments1 = mediaData.segments) === null || _mediaData_segments1 === void 0 ? void 0 : _mediaData_segments1.length) || 0,\n                hasDuration: !!mediaData.duration,\n                directDuration: mediaData.duration\n            });\n            if (mediaData.segments && mediaData.segments.length > 0) {\n                // حساب إجمالي مدة جميع السيجمنتات\n                let totalSeconds = 0;\n                mediaData.segments.forEach((segment, index)=>{\n                    if (segment.duration) {\n                        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                        const segmentSeconds = hours * 3600 + minutes * 60 + seconds;\n                        totalSeconds += segmentSeconds;\n                        console.log(\"  \\uD83D\\uDCFA سيجمنت \".concat(index + 1, \": \").concat(segment.duration, \" (\").concat(segmentSeconds, \" ثانية)\"));\n                    }\n                });\n                if (totalSeconds > 0) {\n                    const hours = Math.floor(totalSeconds / 3600);\n                    const minutes = Math.floor(totalSeconds % 3600 / 60);\n                    const secs = totalSeconds % 60;\n                    itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                }\n                console.log('📊 حساب مدة المادة من السيجمنتات:', {\n                    name: itemName,\n                    segments: mediaData.segments.length,\n                    totalSeconds,\n                    finalDuration: itemDuration\n                });\n            } else if (mediaData.duration) {\n                // استخدام المدة المباشرة إذا كانت موجودة\n                itemDuration = mediaData.duration;\n                console.log('📊 استخدام مدة مباشرة:', itemDuration);\n            } else {\n                console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);\n            }\n            // إنشاء كود للمادة المسحوبة\n            let itemCode = '';\n            if (mediaData.segmentCode) {\n                itemCode = mediaData.segmentCode;\n            } else if (mediaData.id) {\n                itemCode = \"\".concat(itemType, \"_\").concat(mediaData.id);\n            } else {\n                itemCode = \"\".concat(itemType, \"_\").concat(Date.now().toString().slice(-6));\n            }\n            // إنشاء صف جديد مع المدة الحقيقية\n            const newRow = {\n                id: \"dropped_\".concat(Date.now()),\n                type: dragItemType,\n                content: itemContent,\n                mediaItemId: itemId,\n                segmentCode: itemCode,\n                duration: itemDuration,\n                canDelete: true\n            };\n            // التأكد من أن الصف المستهدف فارغ\n            if (gridRows[rowIndex].type !== 'empty') {\n                console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);\n                return;\n            }\n            // استبدال الصف الفارغ مباشرة\n            const newRows = [\n                ...gridRows\n            ];\n            newRows[rowIndex] = newRow;\n            console.log('✅ تم إضافة المادة:', {\n                name: itemName,\n                type: dragItemType,\n                duration: itemDuration,\n                position: rowIndex,\n                content: itemContent,\n                beforeType: gridRows[rowIndex].type,\n                afterType: newRow.type\n            });\n            // حفظ موضع التمرير الحالي\n            const gridBody = document.querySelector('.grid-body');\n            const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n            // تحديث الصفوف مباشرة\n            setGridRows(newRows);\n            // إعادة حساب الأوقات بعد تأخير قصير\n            setTimeout(()=>{\n                recalculateTimes(newRows);\n                // التحقق من الحاجة لإضافة صفوف فارغة\n                checkAndAddEmptyRows(rowIndex);\n                // استعادة موضع التمرير بعد التحديث\n                setTimeout(()=>{\n                    if (gridBody) {\n                        gridBody.scrollTop = currentScrollTop;\n                        console.log('📍 تم استعادة موضع التمرير:', currentScrollTop);\n                    }\n                }, 100);\n            }, 50);\n        } catch (error) {\n            console.error('❌ خطأ في إسقاط المادة:', error);\n        }\n    };\n    // إعادة حساب الأوقات مثل Excel\n    const recalculateTimes = (rows)=>{\n        const newRows = [\n            ...rows\n        ];\n        let currentTime = '08:00:00'; // نقطة البداية بالثواني\n        let hasFillers = false; // هل تم إضافة فواصل؟\n        // التحقق من وجود فواصل\n        hasFillers = rows.some((row)=>row.type === 'filler');\n        console.log('🔄 بدء إعادة حساب الأوقات من 08:00:00', hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');\n        for(let i = 0; i < newRows.length; i++){\n            const row = newRows[i];\n            if (row.type === 'segment' || row.type === 'filler') {\n                // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل\n                if (i === 0 || hasFillers) {\n                    newRows[i] = {\n                        ...row,\n                        time: currentTime\n                    };\n                } else {\n                    newRows[i] = {\n                        ...row,\n                        time: undefined\n                    };\n                }\n                if (row.duration) {\n                    // حساب الوقت التالي بناءً على المدة\n                    const nextTime = calculateNextTime(currentTime, row.duration);\n                    console.log(\"⏰ \".concat(row.type, ': \"').concat(row.content, '\" - من ').concat(currentTime, \" إلى \").concat(nextTime, \" (مدة: \").concat(row.duration, \")\"));\n                    currentTime = nextTime;\n                }\n            } else if (row.type === 'empty') {\n                // الصفوف الفارغة لا تؤثر على الوقت\n                newRows[i] = {\n                    ...row,\n                    time: undefined\n                };\n            }\n            // التحقق من الوصول لوقت مادة أساسية\n            if (row.originalStartTime && hasFillers) {\n                const targetMinutes = timeToMinutes(row.originalStartTime);\n                const currentMinutes = timeToMinutes(currentTime);\n                const difference = targetMinutes - currentMinutes;\n                if (Math.abs(difference) > 5) {\n                    console.log('⚠️ انحراف زمني: المادة \"'.concat(row.content, '\" مجدولة في ').concat(row.originalStartTime, \" لكن ستدخل في \").concat(currentTime, \" (فرق: \").concat(difference, \" دقيقة)\"));\n                } else {\n                    console.log('✅ توقيت صحيح: المادة \"'.concat(row.content, '\" ستدخل في ').concat(currentTime, \" (مجدولة: \").concat(row.originalStartTime, \")\"));\n                }\n            }\n        }\n        console.log(\"\\uD83C\\uDFC1 انتهاء الحساب - الوقت النهائي: \".concat(currentTime));\n        // حفظ موضع التمرير قبل التحديث\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        // تحديث الصفوف دائماً لضمان التحديث الصحيح\n        setGridRows(newRows);\n        // استعادة موضع التمرير بعد التحديث\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // تحويل الوقت إلى دقائق\n    const timeToMinutes = (time)=>{\n        const [hours, minutes] = time.split(':').map(Number);\n        return hours * 60 + minutes;\n    };\n    // حساب المدة الإجمالية للمادة بدقة\n    const calculateTotalDuration = (item)=>{\n        if (item.segments && item.segments.length > 0) {\n            let totalSeconds = 0;\n            item.segments.forEach((segment)=>{\n                if (segment.duration) {\n                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                }\n            });\n            if (totalSeconds > 0) {\n                const hours = Math.floor(totalSeconds / 3600);\n                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                const secs = totalSeconds % 60;\n                return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n            }\n        }\n        return item.duration || '00:01:00';\n    };\n    // حفظ تعديلات الجدول\n    const saveScheduleChanges = async ()=>{\n        try {\n            console.log('💾 بدء حفظ التعديلات...');\n            console.log('📅 التاريخ:', selectedDate);\n            console.log('📝 عدد الصفوف:', gridRows.length);\n            const response = await fetch('/api/daily-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    date: selectedDate,\n                    scheduleRows: gridRows\n                })\n            });\n            const result = await response.json();\n            if (response.ok && result.success) {\n                console.log('✅ تم حفظ تعديلات الجدول الإذاعي بنجاح');\n                alert('✅ تم حفظ التعديلات بنجاح!');\n            } else {\n                console.error('❌ فشل في حفظ التعديلات:', result.error);\n                alert('❌ فشل في حفظ التعديلات: ' + result.error);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ التعديلات:', error);\n            alert('❌ خطأ في الاتصال بالخادم');\n        }\n    };\n    // تصدير الجدول الإذاعي إلى Excel\n    const exportDailySchedule = async ()=>{\n        try {\n            console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');\n            if (!selectedDate) {\n                alert('يرجى تحديد التاريخ أولاً');\n                return;\n            }\n            const response = await fetch(\"/api/export-daily-schedule-new?date=\".concat(selectedDate));\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"Daily_Schedule_\".concat(selectedDate, \".xlsx\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('✅ تم تصدير الجدول الإذاعي بنجاح');\n            alert('✅ تم تصدير الجدول بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في تصدير الجدول:', error);\n            alert('❌ فشل في تصدير الجدول: ' + error.message);\n        }\n    };\n    // حساب الوقت التالي بناءً على المدة (بدقة الثواني)\n    const calculateNextTime = (startTime, duration)=>{\n        // تحليل وقت البداية\n        const startParts = startTime.split(':');\n        const startHours = parseInt(startParts[0]);\n        const startMins = parseInt(startParts[1]);\n        const startSecs = parseInt(startParts[2] || '0');\n        // تحليل المدة\n        const [durHours, durMins, durSecs] = duration.split(':').map(Number);\n        // حساب إجمالي الثواني\n        let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;\n        totalSeconds += durHours * 3600 + durMins * 60 + durSecs;\n        // تحويل إلى ساعات ودقائق وثواني\n        const hours = Math.floor(totalSeconds / 3600) % 24;\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"daily-schedule-container\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"schedule-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"header-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"header-title\",\n                            children: \"الجدول الإذاعي اليومي\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"user-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"user-name\",\n                                    children: user === null || user === void 0 ? void 0 : user.username\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"user-role\",\n                                    children: user === null || user === void 0 ? void 0 : user.role\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 732,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                    lineNumber: 728,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                lineNumber: 727,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"schedule-controls\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"date-selector\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"schedule-date\",\n                                children: \"اختر التاريخ:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                id: \"schedule-date\",\n                                type: \"date\",\n                                value: selectedDate,\n                                onChange: (e)=>setSelectedDate(e.target.value),\n                                className: \"glass-input\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 741,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                        lineNumber: 739,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-buttons\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: saveScheduleChanges,\n                                className: \"glass-button primary\",\n                                children: \"\\uD83D\\uDCBE حفظ التعديلات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 751,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: exportDailySchedule,\n                                className: \"glass-button export\",\n                                style: {\n                                    background: 'linear-gradient(45deg, #17a2b8, #138496)',\n                                    color: 'white'\n                                },\n                                children: \"\\uD83D\\uDCCA تصدير Excel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 758,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowWeeklySchedule(!showWeeklySchedule),\n                                className: \"glass-button\",\n                                children: showWeeklySchedule ? '📋 إخفاء الخريطة' : '📅 عرض الخريطة'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 769,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.href = '/weekly-schedule',\n                                className: \"glass-button\",\n                                children: \"\\uD83D\\uDD19 العودة للخريطة البرامجية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 776,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                        lineNumber: 750,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                lineNumber: 738,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"schedule-content\",\n                children: [\n                    showWeeklySchedule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"weekly-sidebar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"sidebar-title\",\n                                children: \"الخريطة البرامجية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 789,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"weekly-schedule-list\",\n                                children: Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? weeklySchedule.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"weekly-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"weekly-time\",\n                                                children: item.time\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"weekly-content\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"weekly-name\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"weekly-details\",\n                                                        children: [\n                                                            \"ح\",\n                                                            item.episodeNumber\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"weekly-details\",\n                                                        children: [\n                                                            \"ج\",\n                                                            item.partNumber\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"weekly-status\",\n                                                children: item.isRerun ? '🔄' : '🎯'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 19\n                                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"no-data\",\n                                    children: \"لا توجد بيانات للخريطة البرامجية\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                        lineNumber: 788,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"media-sidebar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"sidebar-title\",\n                                children: \"المواد المتاحة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 818,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sidebar-controls\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: filterType,\n                                        onChange: (e)=>setFilterType(e.target.value),\n                                        className: \"filter-select\",\n                                        children: mediaTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: type,\n                                                children: type === 'ALL' ? 'جميع الأنواع' : type\n                                            }, type, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 822,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"بحث في المواد...\",\n                                        value: searchTerm,\n                                        onChange: (e)=>setSearchTerm(e.target.value),\n                                        className: \"search-input\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"media-list\",\n                                children: filteredMedia.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"media-item \".concat(item.type.toLowerCase()),\n                                        draggable: true,\n                                        onDragStart: (e)=>{\n                                            e.dataTransfer.setData('application/json', JSON.stringify(item));\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"media-name\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 854,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"media-details\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"media-type\",\n                                                        children: item.type\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"media-duration\",\n                                                        children: calculateTotalDuration(item)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"media-info\",\n                                                children: [\n                                                    item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"info-tag\",\n                                                        children: [\n                                                            \"ح\",\n                                                            item.episodeNumber\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 860,\n                                                        columnNumber: 42\n                                                    }, this),\n                                                    item.seasonNumber && item.seasonNumber > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"info-tag\",\n                                                        children: [\n                                                            \"م\",\n                                                            item.seasonNumber\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 861,\n                                                        columnNumber: 66\n                                                    }, this),\n                                                    item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"info-tag\",\n                                                        children: [\n                                                            \"ج\",\n                                                            item.partNumber\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 39\n                                                    }, this),\n                                                    item.segments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"info-tag\",\n                                                        children: [\n                                                            item.segments.length,\n                                                            \" سيجمنت\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 37\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 859,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                        lineNumber: 817,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"schedule-grid\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid-header\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"code-column\",\n                                        children: \"الكود\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 873,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"time-column\",\n                                        children: \"الوقت\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 874,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"content-column\",\n                                        children: \"المحتوى\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"duration-column\",\n                                        children: \"المدة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 876,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"status-column\",\n                                        children: \"الحالة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"actions-column\",\n                                        children: \"إجراءات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 878,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid-body\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"loading\",\n                                    children: \"جاري التحميل...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 883,\n                                    columnNumber: 15\n                                }, this) : gridRows.map((row, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid-row \".concat(row.type, \" \").concat(row.isRerun ? 'rerun' : '', \" \").concat(row.isTemporary ? 'temporary' : ''),\n                                        draggable: row.type === 'filler' || row.type === 'empty',\n                                        onDragStart: (e)=>handleRowDragStart(e, index),\n                                        onDrop: (e)=>handleRowDrop(e, index),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"code-cell\",\n                                                children: row.type === 'segment' || row.type === 'filler' ? row.segmentCode || row.mediaItemId || \"\".concat(row.type.toUpperCase(), \"_\").concat(row.id.slice(-6)) : ''\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"time-cell\",\n                                                children: row.time || ''\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"content-cell\",\n                                                onDrop: (e)=>handleDrop(e, index),\n                                                onDragOver: (e)=>e.preventDefault(),\n                                                children: row.content || ''\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 903,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"duration-cell\",\n                                                children: row.duration || ''\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 910,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"status-cell\",\n                                                children: [\n                                                    row.type === 'segment' && row.isTemporary && '🟣 مؤقت',\n                                                    row.type === 'segment' && !row.isRerun && !row.isTemporary && (row.originalStartTime ? Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ? '⚠️ انحراف' : '✅ دقيق' : '🎯 أساسي'),\n                                                    row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة',\n                                                    row.type === 'filler' && '📺 فاصل',\n                                                    row.type === 'empty' && '⚪ فارغ'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"actions-cell\",\n                                                children: [\n                                                    row.type === 'empty' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"action-btn add-row\",\n                                                                title: \"إضافة صف\",\n                                                                onClick: ()=>addEmptyRow(index),\n                                                                children: \"➕\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 928,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"action-btn add-multiple-rows\",\n                                                                title: \"إضافة 5 صفوف\",\n                                                                onClick: ()=>addMultipleEmptyRows(index, 5),\n                                                                children: \"➕➕\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            row.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"action-btn delete-row\",\n                                                                title: \"حذف صف\",\n                                                                onClick: ()=>deleteRow(row.id),\n                                                                children: \"➖\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 943,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    row.type === 'filler' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"action-btn move-up\",\n                                                                title: \"تحريك لأعلى\",\n                                                                onClick: ()=>moveRowUp(index),\n                                                                disabled: index === 0,\n                                                                children: \"⬆️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 955,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"action-btn move-down\",\n                                                                title: \"تحريك لأسفل\",\n                                                                onClick: ()=>moveRowDown(index),\n                                                                disabled: index === gridRows.length - 1,\n                                                                children: \"⬇️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: \"action-btn delete-row\",\n                                                                title: \"حذف فاصل\",\n                                                                onClick: ()=>{\n                                                                    // تحويل الفاصل إلى صف فارغ\n                                                                    const newRows = [\n                                                                        ...gridRows\n                                                                    ];\n                                                                    newRows[index] = {\n                                                                        id: \"empty_\".concat(Date.now()),\n                                                                        type: 'empty',\n                                                                        canDelete: true\n                                                                    };\n                                                                    // إعادة حساب الأوقات\n                                                                    recalculateTimes(newRows);\n                                                                },\n                                                                children: \"\\uD83D\\uDDD1️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 971,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true),\n                                                    row.type === 'segment' && row.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"action-btn replace-temp\",\n                                                        title: \"استبدال بمادة حقيقية\",\n                                                        onClick: ()=>{\n                                                            alert('💡 لاستبدال المادة المؤقتة:\\n\\n1. أضف المادة الحقيقية لقاعدة البيانات\\n2. احذف المادة المؤقتة\\n3. اسحب المادة الجديدة من القائمة الجانبية');\n                                                        },\n                                                        style: {\n                                                            color: '#9c27b0'\n                                                        },\n                                                        children: \"\\uD83D\\uDD04\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    row.type === 'segment' && row.canDelete && !row.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"action-btn delete-row\",\n                                                        title: \"حذف سيجمنت\",\n                                                        onClick: ()=>deleteSegment(row.id),\n                                                        children: \"❌\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, row.id, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                lineNumber: 881,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                        lineNumber: 871,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                lineNumber: 785,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n        lineNumber: 725,\n        columnNumber: 5\n    }, this);\n}\n_s(DailySchedulePage, \"l78MCoamIEbt5T/8CCdwPLkgwyk=\", false, function() {\n    return [\n        useAuth\n    ];\n});\n_c = DailySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"DailySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdaily-schedule%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);