"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/media-list/page",{

/***/ "(app-pages-browser)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MediaListPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction MediaListPage() {\n    _s();\n    const [mediaItems, setMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            fetchMediaItems();\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            filterAndSortItems();\n        }\n    }[\"MediaListPage.useEffect\"], [\n        mediaItems,\n        searchTerm,\n        selectedType,\n        selectedStatus,\n        sortBy\n    ]);\n    const fetchMediaItems = async ()=>{\n        try {\n            const response = await fetch('/api/media');\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(result.data);\n            } else {\n                setError(result.error);\n            }\n        } catch (error) {\n            console.error('Error fetching media items:', error);\n            setError('فشل في جلب المواد الإعلامية');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterAndSortItems = ()=>{\n        let filtered = [\n            ...mediaItems\n        ];\n        // البحث بالاسم\n        if (searchTerm) {\n            filtered = filtered.filter((item)=>item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // فلترة بالنوع\n        if (selectedType !== 'ALL') {\n            filtered = filtered.filter((item)=>item.type === selectedType);\n        }\n        // فلترة بالحالة\n        if (selectedStatus !== 'ALL') {\n            filtered = filtered.filter((item)=>item.status === selectedStatus);\n        }\n        // الترتيب\n        switch(sortBy){\n            case 'newest':\n                filtered.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                break;\n            case 'oldest':\n                filtered.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n                break;\n            case 'name':\n                filtered.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n                break;\n            case 'type':\n                filtered.sort((a, b)=>a.type.localeCompare(b.type));\n                break;\n        }\n        setFilteredItems(filtered);\n    };\n    const deleteMediaItem = async (id)=>{\n        if (!confirm('هل أنت متأكد من حذف هذه المادة؟')) return;\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(id), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(mediaItems.filter((item)=>item.id !== id));\n                alert('تم حذف المادة بنجاح');\n            } else {\n                alert('فشل في حذف المادة: ' + result.error);\n            }\n        } catch (error) {\n            console.error('Error deleting media item:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    const exportToExcel = async ()=>{\n        setIsExporting(true);\n        try {\n            console.log('🚀 بدء تصدير قاعدة البيانات...');\n            const response = await fetch('/api/export');\n            if (!response.ok) {\n                throw new Error('فشل في تصدير البيانات');\n            }\n            // الحصول على الملف كـ blob\n            const blob = await response.blob();\n            // إنشاء رابط التحميل\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            // تحديد اسم الملف\n            const fileName = \"Media_Database_\".concat(new Date().toISOString().split('T')[0], \".xlsx\");\n            link.download = fileName;\n            // تحميل الملف\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            // تنظيف الذاكرة\n            window.URL.revokeObjectURL(url);\n            console.log('✅ تم تصدير قاعدة البيانات بنجاح');\n            alert('✅ تم تصدير قاعدة البيانات بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في التصدير:', error);\n            alert('❌ فشل في تصدير قاعدة البيانات. يرجى المحاولة مرة أخرى.');\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const getTypeLabel = (type)=>{\n        const types = {\n            PROGRAM: 'برنامج',\n            SERIES: 'مسلسل',\n            MOVIE: 'فيلم',\n            SONG: 'أغنية',\n            STING: 'Sting',\n            FILL_IN: 'Fill IN',\n            FILLER: 'Filler',\n            PROMO: 'Promo'\n        };\n        return types[type] || type;\n    };\n    const getStatusLabel = (status)=>{\n        const statuses = {\n            VALID: 'صالح',\n            REJECTED_CENSORSHIP: 'مرفوض رقابي',\n            REJECTED_TECHNICAL: 'مرفوض هندسي',\n            WAITING: 'في الانتظار'\n        };\n        return statuses[status] || status;\n    };\n    const getChannelLabel = (channel)=>{\n        const channels = {\n            DOCUMENTARY: 'الوثائقية',\n            NEWS: 'الأخبار',\n            OTHER: 'أخرى'\n        };\n        return channels[channel] || channel;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: \"⏳ جاري التحميل...\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 209,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"❌ خطأ: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 230,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: '#1a1d29',\n            padding: '20px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '30px',\n                            marginBottom: '30px',\n                            boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                            textAlign: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    color: '#2c3e50',\n                                    marginBottom: '20px',\n                                    fontSize: '2.5rem',\n                                    fontWeight: 'bold'\n                                },\n                                children: \"\\uD83D\\uDCDA قائمة المواد الإعلامية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center',\n                                    flexWrap: 'wrap'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/add-media\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            textDecoration: 'none',\n                                            fontWeight: 'bold',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)'\n                                        },\n                                        children: \"➕ إضافة مادة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: exportToExcel,\n                                        disabled: isExporting,\n                                        style: {\n                                            background: isExporting ? 'linear-gradient(45deg, #6c757d, #5a6268)' : 'linear-gradient(45deg, #17a2b8, #138496)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            border: 'none',\n                                            fontWeight: 'bold',\n                                            cursor: isExporting ? 'not-allowed' : 'pointer',\n                                            boxShadow: '0 4px 15px rgba(23,162,184,0.3)',\n                                            fontSize: '1rem'\n                                        },\n                                        children: isExporting ? '⏳ جاري التصدير...' : '📊 تصدير Excel'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                            color: 'white',\n                                            padding: '12px 25px',\n                                            borderRadius: '25px',\n                                            textDecoration: 'none',\n                                            fontWeight: 'bold',\n                                            boxShadow: '0 4px 15px rgba(0,123,255,0.3)'\n                                        },\n                                        children: \"\\uD83C\\uDFE0 الرئيسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            boxShadow: '0 8px 25px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#2c3e50',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: \"\\uD83D\\uDD0D البحث والفلترة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"البحث بالاسم أو الوصف:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"ابحث عن مادة إعلامية...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"نوع المادة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedType,\n                                                onChange: (e)=>setSelectedType(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ALL\",\n                                                        children: \"جميع الأنواع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"PROGRAM\",\n                                                        children: \"برنامج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SERIES\",\n                                                        children: \"مسلسل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"MOVIE\",\n                                                        children: \"فيلم\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"SONG\",\n                                                        children: \"أغنية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"STING\",\n                                                        children: \"Sting\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FILL_IN\",\n                                                        children: \"Fill IN\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"FILLER\",\n                                                        children: \"Filler\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"PROMO\",\n                                                        children: \"Promo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"الحالة:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"ALL\",\n                                                        children: \"جميع الحالات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"VALID\",\n                                                        children: \"صالح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_CENSORSHIP\",\n                                                        children: \"مرفوض رقابي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_TECHNICAL\",\n                                                        children: \"مرفوض هندسي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"WAITING\",\n                                                        children: \"في الانتظار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#495057',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"ترتيب حسب:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: sortBy,\n                                                onChange: (e)=>setSortBy(e.target.value),\n                                                style: {\n                                                    width: '100%',\n                                                    padding: '10px',\n                                                    border: '2px solid #e0e0e0',\n                                                    borderRadius: '8px',\n                                                    fontSize: '1rem',\n                                                    direction: 'rtl'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"newest\",\n                                                        children: \"الأحدث أولاً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"oldest\",\n                                                        children: \"الأقدم أولاً\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"name\",\n                                                        children: \"الاسم (أ-ي)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"type\",\n                                                        children: \"النوع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '15px',\n                                    padding: '10px',\n                                    background: '#f8f9fa',\n                                    borderRadius: '8px',\n                                    textAlign: 'center',\n                                    color: '#6c757d'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA عرض \",\n                                    filteredItems.length,\n                                    \" من أصل \",\n                                    mediaItems.length,\n                                    \" مادة إعلامية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this),\n                    filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'rgba(255,255,255,0.95)',\n                            borderRadius: '20px',\n                            padding: '50px',\n                            textAlign: 'center',\n                            boxShadow: '0 10px 30px rgba(0,0,0,0.2)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#6c757d',\n                                    fontSize: '1.5rem'\n                                },\n                                children: \"\\uD83D\\uDCED لا توجد مواد إعلامية محفوظة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: '#6c757d',\n                                    marginTop: '10px'\n                                },\n                                children: \"ابدأ بإضافة مادة إعلامية جديدة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gap: '20px'\n                        },\n                        children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'rgba(255,255,255,0.95)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    boxShadow: '0 8px 25px rgba(0,0,0,0.1)',\n                                    border: '1px solid #e9ecef'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr auto',\n                                        gap: '20px',\n                                        alignItems: 'start'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#2c3e50',\n                                                        marginBottom: '15px',\n                                                        fontSize: '1.4rem'\n                                                    },\n                                                    children: item.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                        gap: '15px',\n                                                        marginBottom: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"النوع:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getTypeLabel(item.type)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"القناة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getChannelLabel(item.channel)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"الحالة:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                getStatusLabel(item.status)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"عدد السيجمانت:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \" \",\n                                                                item.segments.length\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#6c757d',\n                                                        marginBottom: '10px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"الوصف:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \" \",\n                                                        item.description\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 23\n                                                }, this),\n                                                item.segments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"السيجمانت:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'grid',\n                                                                gap: '8px',\n                                                                marginTop: '8px'\n                                                            },\n                                                            children: item.segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        background: '#f8f9fa',\n                                                                        padding: '8px 12px',\n                                                                        borderRadius: '8px',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: [\n                                                                                \"#\",\n                                                                                segment.segmentNumber\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                            lineNumber: 501,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        \" -\",\n                                                                        segment.code && \" \".concat(segment.code, \" - \"),\n                                                                        segment.timeIn,\n                                                                        \" → \",\n                                                                        segment.timeOut,\n                                                                        \" (\",\n                                                                        segment.duration,\n                                                                        \")\"\n                                                                    ]\n                                                                }, segment.id, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                gap: '10px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        // توجيه لصفحة التعديل مع معرف المادة\n                                                        window.location.href = \"/edit-media?id=\".concat(item.id);\n                                                    },\n                                                    style: {\n                                                        background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '8px 16px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '0.9rem',\n                                                        marginBottom: '5px'\n                                                    },\n                                                    children: \"✏️ تعديل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>deleteMediaItem(item.id),\n                                                    style: {\n                                                        background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '8px 16px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDDD1️ حذف\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 17\n                                }, this)\n                            }, item.id, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 454,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    bottom: '20px',\n                    left: '20px',\n                    color: '#6c757d',\n                    fontSize: '0.75rem',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: 'ltr'\n                },\n                children: \"Powered By Mahmoud Ismail\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 554,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n        lineNumber: 236,\n        columnNumber: 5\n    }, this);\n}\n_s(MediaListPage, \"EP5OjVEHjSPUFe6kTv2ugGTcOAs=\");\n_c = MediaListPage;\nvar _c;\n$RefreshReg$(_c, \"MediaListPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/media-list/page.tsx\n"));

/***/ })

});