import React from 'react';

interface StatsCardProps {
  icon: string;
  title: string;
  value: string | number;
  subtitle: string;
  badge?: string;
  badgeColor?: string;
  gradient: string;
}

export default function StatsCard({ 
  icon, 
  title, 
  value, 
  subtitle, 
  badge, 
  badgeColor = '#68d391',
  gradient 
}: StatsCardProps) {
  return (
    <div 
      style={{
        background: '#2d3748',
        borderRadius: '12px',
        padding: '25px',
        border: '1px solid #4a5568',
        position: 'relative',
        overflow: 'hidden',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        transform: 'translateZ(0)'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-8px) scale(1.02)';
        e.currentTarget.style.boxShadow = '0 20px 40px rgba(255, 215, 0, 0.3), 0 0 0 2px rgba(255, 215, 0, 0.5)';
        e.currentTarget.style.borderColor = '#ffd700';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0) scale(1)';
        e.currentTarget.style.boxShadow = 'none';
        e.currentTarget.style.borderColor = '#4a5568';
      }}
    >
      {badge && (
        <div style={{
          position: 'absolute',
          top: '15px',
          left: '15px',
          background: badgeColor,
          color: 'white',
          padding: '4px 8px',
          borderRadius: '6px',
          fontSize: '0.8rem',
          fontWeight: 'bold'
        }}>
          {badge}
        </div>
      )}
      
      <div style={{
        width: '50px',
        height: '50px',
        background: gradient,
        borderRadius: '12px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '1.5rem',
        marginBottom: '15px',
        margin: badge ? '0 auto 15px auto' : '0 0 15px 0'
      }}>
        {icon}
      </div>
      
      <div style={{
        fontSize: '2.5rem',
        fontWeight: 'bold',
        color: 'white',
        marginBottom: '5px',
        textAlign: badge ? 'center' : 'left'
      }}>
        {typeof value === 'number' ? value.toLocaleString() : value}
      </div>
      
      <div style={{
        color: 'white',
        fontWeight: 'bold',
        fontSize: '1rem',
        marginBottom: '5px',
        textAlign: badge ? 'center' : 'left'
      }}>
        {title}
      </div>
      
      <div style={{
        color: '#a0aec0',
        fontSize: '0.9rem',
        textAlign: badge ? 'center' : 'left'
      }}>
        {subtitle}
      </div>
    </div>
  );
}
