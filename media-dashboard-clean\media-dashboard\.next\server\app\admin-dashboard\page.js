/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/admin-dashboard/page";
exports.ids = ["app/admin-dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin-dashboard%2Fpage&page=%2Fadmin-dashboard%2Fpage&appPaths=%2Fadmin-dashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin-dashboard%2Fpage.tsx&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin-dashboard%2Fpage&page=%2Fadmin-dashboard%2Fpage&appPaths=%2Fadmin-dashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin-dashboard%2Fpage.tsx&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin-dashboard/page.tsx */ \"(rsc)/./src/app/admin-dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'admin-dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/admin-dashboard/page\",\n        pathname: \"/admin-dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin-dashboard%2Fpage&page=%2Fadmin-dashboard%2Fpage&appPaths=%2Fadmin-dashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin-dashboard%2Fpage.tsx&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDRG9jJTIwZGF0YWJhc2UlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTJKO0FBQzNKO0FBQ0EsME9BQThKO0FBQzlKO0FBQ0EsME9BQThKO0FBQzlKO0FBQ0Esb1JBQW9MO0FBQ3BMO0FBQ0Esd09BQTZKO0FBQzdKO0FBQ0EsNFBBQXdLO0FBQ3hLO0FBQ0Esa1FBQTJLO0FBQzNLO0FBQ0Esc1FBQTRLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin-dashboard/page.tsx */ \"(rsc)/./src/app/admin-dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluLWRhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW4tZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/admin-dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin-dashboard/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Doc database\\media-dashboard-clean\\media-dashboard\\src\\app\\admin-dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2b7bd8a9d60a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcRG9jIGRhdGFiYXNlXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmI3YmQ4YTlkNjBhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"نظام إدارة المحتوى الإعلامي\",\n    description: \"نظام متكامل لإدارة المحتوى الإعلامي والخريطة البرامجية\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap\",\n                    rel: \"stylesheet\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                style: {\n                    margin: 0,\n                    padding: 0,\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3VCO0FBRWhCLE1BQU1BLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsS0FBSTs7MEJBQ2xCLDhEQUFDQzswQkFDQyw0RUFBQ0M7b0JBQUtDLE1BQUs7b0JBQXVGQyxLQUFJOzs7Ozs7Ozs7OzswQkFFeEcsOERBQUNDO2dCQUFLQyxPQUFPO29CQUFFQyxRQUFRO29CQUFHQyxTQUFTO29CQUFHQyxZQUFZO2dCQUEyQjswQkFDMUVaOzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCLZhti42KfZhSDYpdiv2KfYsdipINin2YTZhdit2KrZiNmJINin2YTYpdi52YTYp9mF2YpcIixcbiAgZGVzY3JpcHRpb246IFwi2YbYuNin2YUg2YXYqtmD2KfZhdmEINmE2KXYr9in2LHYqSDYp9mE2YXYrdiq2YjZiSDYp9mE2KXYudmE2KfZhdmKINmI2KfZhNiu2LHZiti32Kkg2KfZhNio2LHYp9mF2KzZitipXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxsaW5rIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUNhaXJvOndnaHRAMzAwOzQwMDs1MDA7NjAwOzcwMCZkaXNwbGF5PXN3YXBcIiByZWw9XCJzdHlsZXNoZWV0XCIgLz5cbiAgICAgIDwvaGVhZD5cbiAgICAgIDxib2R5IHN0eWxlPXt7IG1hcmdpbjogMCwgcGFkZGluZzogMCwgZm9udEZhbWlseTogJ0NhaXJvLCBBcmlhbCwgc2Fucy1zZXJpZicgfX0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJkaXIiLCJoZWFkIiwibGluayIsImhyZWYiLCJyZWwiLCJib2R5Iiwic3R5bGUiLCJtYXJnaW4iLCJwYWRkaW5nIiwiZm9udEZhbWlseSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin-dashboard/page.tsx */ \"(ssr)/./src/app/admin-dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluLWRhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW4tZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/admin-dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin-dashboard/page.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Toast */ \"(ssr)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction AdminDashboard() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddUser, setShowAddUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        phone: '',\n        role: 'VIEWER'\n    });\n    const roles = {\n        'ADMIN': {\n            name: 'مدير النظام',\n            color: '#dc3545',\n            icon: '👑'\n        },\n        'MEDIA_MANAGER': {\n            name: 'مدير المحتوى',\n            color: '#28a745',\n            icon: '📝'\n        },\n        'SCHEDULER': {\n            name: 'مجدول البرامج',\n            color: '#007bff',\n            icon: '📅'\n        },\n        'VIEWER': {\n            name: 'مستخدم عرض',\n            color: '#6c757d',\n            icon: '👁️'\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchUsers();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch('/api/users');\n            const result = await response.json();\n            if (result.success) {\n                setUsers(result.users);\n            } else {\n                showToast('خطأ في جلب بيانات المستخدمين', 'error');\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddUser = async (e)=>{\n        e.preventDefault();\n        if (!newUser.username || !newUser.password || !newUser.name) {\n            showToast('يرجى ملء جميع الحقول المطلوبة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch('/api/users', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newUser)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم إنشاء المستخدم بنجاح!', 'success');\n                setUsers([\n                    ...users,\n                    result.user\n                ]);\n                setShowAddUser(false);\n                setNewUser({\n                    username: '',\n                    password: '',\n                    name: '',\n                    email: '',\n                    phone: '',\n                    role: 'VIEWER'\n                });\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error adding user:', error);\n            showToast('خطأ في إنشاء المستخدم', 'error');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;\n        try {\n            const response = await fetch(`/api/users?id=${userId}`, {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حذف المستخدم بنجاح!', 'success');\n                setUsers(users.filter((u)=>u.id !== userId));\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error deleting user:', error);\n            showToast('خطأ في حذف المستخدم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredRole: \"ADMIN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            title: \"إدارة المستخدمين\",\n            subtitle: \"إضافة وتعديل المستخدمين\",\n            icon: \"\\uD83D\\uDC65\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                        gap: '20px',\n                        marginBottom: '20px'\n                    },\n                    children: Object.entries(roles).map(([roleKey, roleInfo])=>{\n                        const count = users.filter((u)=>u.role === roleKey).length;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#4a5568',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                border: '1px solid #6b7280',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2.5rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: roleInfo.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        margin: '0 0 5px 0',\n                                        fontSize: '1.2rem'\n                                    },\n                                    children: roleInfo.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        fontWeight: 'bold',\n                                        color: roleInfo.color\n                                    },\n                                    children: count\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#d1d5db',\n                                        fontSize: '0.9rem',\n                                        margin: 0\n                                    },\n                                    children: \"مستخدم\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, roleKey, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: '30px',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                marginBottom: '25px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        fontSize: '1.5rem',\n                                        margin: 0\n                                    },\n                                    children: [\n                                        \"\\uD83D\\uDC65 إدارة المستخدمين (\",\n                                        users.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddUser(true),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #10b981, #059669)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '12px 20px',\n                                        cursor: 'pointer',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold'\n                                    },\n                                    children: \"➕ إضافة مستخدم جديد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        showAddUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#1f2937',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                marginBottom: '25px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        marginBottom: '20px'\n                                    },\n                                    children: \"➕ إضافة مستخدم جديد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleAddUser,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                gap: '15px',\n                                                marginBottom: '20px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"اسم المستخدم *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"اسم المستخدم\",\n                                                            value: newUser.username,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    username: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"كلمة المرور *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            placeholder: \"كلمة المرور\",\n                                                            value: newUser.password,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    password: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"الاسم الكامل *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"الاسم الكامل\",\n                                                            value: newUser.name,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    name: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"البريد الإلكتروني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            placeholder: \"البريد الإلكتروني\",\n                                                            value: newUser.email,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    email: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"رقم الهاتف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            placeholder: \"رقم الهاتف\",\n                                                            value: newUser.phone,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    phone: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"الدور *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: newUser.role,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    role: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            children: Object.entries(roles).map(([key, role])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: role.name\n                                                                }, key, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '10px',\n                                                justifyContent: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    style: {\n                                                        background: 'linear-gradient(45deg, #10b981, #059669)',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"✅ إنشاء المستخدم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        setShowAddUser(false);\n                                                        setNewUser({\n                                                            username: '',\n                                                            password: '',\n                                                            name: '',\n                                                            email: '',\n                                                            phone: '',\n                                                            role: 'VIEWER'\n                                                        });\n                                                    },\n                                                    style: {\n                                                        background: '#6c757d',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"❌ إلغاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                overflowX: 'auto',\n                                borderRadius: '10px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                style: {\n                                    width: '100%',\n                                    borderCollapse: 'collapse',\n                                    fontSize: '0.9rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            style: {\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'right',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"المستخدم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"الدور\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"تاريخ الإنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"آخر دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"الإجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    borderBottom: '1px solid #6b7280',\n                                                    transition: 'background-color 0.2s',\n                                                    background: '#2d3748'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#374151',\n                                                onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = '#2d3748',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontWeight: 'bold',\n                                                                        color: '#f3f4f6',\n                                                                        marginBottom: '5px'\n                                                                    },\n                                                                    children: user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        color: '#d1d5db',\n                                                                        fontSize: '0.85rem'\n                                                                    },\n                                                                    children: [\n                                                                        \"@\",\n                                                                        user.username\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                user.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        color: '#a0aec0',\n                                                                        fontSize: '0.8rem'\n                                                                    },\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCE7 \",\n                                                                        user.email\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                user.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        color: '#a0aec0',\n                                                                        fontSize: '0.8rem'\n                                                                    },\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCF1 \",\n                                                                        user.phone\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                background: roles[user.role]?.color + '20',\n                                                                color: roles[user.role]?.color,\n                                                                padding: '5px 12px',\n                                                                borderRadius: '20px',\n                                                                fontSize: '0.85rem',\n                                                                fontWeight: 'bold',\n                                                                display: 'inline-flex',\n                                                                alignItems: 'center',\n                                                                gap: '5px'\n                                                            },\n                                                            children: [\n                                                                roles[user.role]?.icon,\n                                                                roles[user.role]?.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                background: user.isActive ? '#28a74520' : '#dc354520',\n                                                                color: user.isActive ? '#28a745' : '#dc3545',\n                                                                padding: '5px 12px',\n                                                                borderRadius: '20px',\n                                                                fontSize: '0.85rem',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: user.isActive ? '✅ نشط' : '❌ معطل'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: new Date(user.createdAt).toLocaleDateString('en-GB')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('en-GB') : 'لم يدخل بعد'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                gap: '5px',\n                                                                justifyContent: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        console.log('تعديل المستخدم:', user);\n                                                                        setEditingUser(user);\n                                                                    },\n                                                                    style: {\n                                                                        background: '#007bff',\n                                                                        color: 'white',\n                                                                        border: 'none',\n                                                                        borderRadius: '5px',\n                                                                        padding: '5px 10px',\n                                                                        cursor: 'pointer',\n                                                                        fontSize: '0.8rem'\n                                                                    },\n                                                                    title: \"تعديل\",\n                                                                    children: \"✏️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                user.id !== '1' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDeleteUser(user.id),\n                                                                    style: {\n                                                                        background: '#dc3545',\n                                                                        color: 'white',\n                                                                        border: 'none',\n                                                                        borderRadius: '5px',\n                                                                        padding: '5px 10px',\n                                                                        cursor: 'pointer',\n                                                                        fontSize: '0.8rem'\n                                                                    },\n                                                                    title: \"حذف\",\n                                                                    children: \"\\uD83D\\uDDD1️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, user.id, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this),\n                        users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center',\n                                padding: '40px',\n                                color: '#6c757d'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        marginBottom: '15px'\n                                    },\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"لا توجد مستخدمين\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"ابدأ بإضافة مستخدم جديد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 557,\n                    columnNumber: 9\n                }, this),\n                editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.5)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'white',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            width: '400px',\n                            maxWidth: '90vw'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: '20px',\n                                    color: '#333'\n                                },\n                                children: [\n                                    \"تعديل المستخدم: \",\n                                    editingUser?.name || 'غير محدد'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"اسم المستخدم:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingUser?.username || '',\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                username: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 584,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"الاسم الكامل:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingUser?.name || '',\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                name: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"البريد الإلكتروني:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: editingUser.email || '',\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                email: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"رقم الهاتف:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        value: editingUser.phone || '',\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                phone: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"الدور:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 649,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingUser?.role || '',\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                role: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"ADMIN\",\n                                                children: \"مدير\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MEDIA_MANAGER\",\n                                                children: \"مدير المواد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 662,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SCHEDULER\",\n                                                children: \"مجدول\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 663,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"VIEWER\",\n                                                children: \"مشاهد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 650,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '10px',\n                                    justifyContent: 'flex-end'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingUser(null),\n                                        style: {\n                                            background: '#6c757d',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            padding: '10px 20px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            try {\n                                                const response = await fetch(`/api/users?id=${editingUser.id}`, {\n                                                    method: 'PUT',\n                                                    headers: {\n                                                        'Content-Type': 'application/json'\n                                                    },\n                                                    body: JSON.stringify(editingUser)\n                                                });\n                                                const result = await response.json();\n                                                if (response.ok && result.success) {\n                                                    showToast('تم تحديث المستخدم بنجاح', 'success');\n                                                    setEditingUser(null);\n                                                    fetchUsers();\n                                                } else {\n                                                    showToast(`خطأ في تحديث المستخدم: ${result.error || 'خطأ غير معروف'}`, 'error');\n                                                }\n                                            } catch (error) {\n                                                console.error('خطأ في تحديث المستخدم:', error);\n                                                showToast('خطأ في الاتصال', 'error');\n                                            }\n                                        },\n                                        style: {\n                                            background: '#28a745',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            padding: '10px 20px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"حفظ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 668,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 561,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/admin-dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \n\n\nfunction AuthGuard({ children, requiredPermissions = [], requiredRole, fallbackComponent }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        // المدير له صلاحيات كاملة\n        if (user.role === 'ADMIN') {\n            return true;\n        }\n        // التحقق من الدور المطلوب\n        if (role && user.role !== role) {\n            return false;\n        }\n        // التحقق من الصلاحيات المطلوبة\n        if (permissions.length > 0) {\n            const userPermissions = getUserPermissions(user.role);\n            return permissions.every((permission)=>userPermissions.includes(permission) || userPermissions.includes('ALL'));\n        }\n        return true;\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user?.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user?.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        if (user.role === 'ADMIN') return true;\n        const userPermissions = getUserPermissions(user.role);\n        return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: user?.role === 'ADMIN',\n        isMediaManager: user?.role === 'MEDIA_MANAGER',\n        isScheduler: user?.role === 'SCHEDULER',\n        isViewer: user?.role === 'VIEWER'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children, title = 'لوحة التحكم', subtitle = 'إدارة النظام', icon = '📊', requiredPermissions, requiredRole, fullWidth = false }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const timer = setInterval({\n                \"DashboardLayout.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"DashboardLayout.useEffect.timer\"], 1000);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>clearInterval(timer)\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: false,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'جدول الإذاعة اليومي',\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: requiredPermissions,\n        requiredRole: requiredRole,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        fontSize: '1.2rem',\n                                        fontWeight: '900',\n                                        fontFamily: 'Arial, sans-serif',\n                                        gap: '5px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '900',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"X\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '1rem',\n                                                fontWeight: '300'\n                                            },\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '800',\n                                                letterSpacing: '1px'\n                                            },\n                                            children: \"Prime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        marginRight: sidebarOpen ? '280px' : '0',\n                        transition: 'margin-right 0.3s ease'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"متصل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: currentTime.toLocaleTimeString('ar-EG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '20px',\n                                padding: '25px',\n                                border: '1px solid #4a5568',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                                maxWidth: fullWidth ? 'none' : '1000px',\n                                margin: '0 auto',\n                                width: fullWidth ? '100%' : 'auto'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Sidebar({ isOpen, onToggle }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const menuItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'جدول الإذاعة اليومي',\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'إدارة المستخدمين',\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null,\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            path: '/statistics',\n            permission: null,\n            adminOnly: true\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    right: isOpen ? 0 : '-280px',\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    borderLeft: '1px solid #2d3748',\n                    transition: 'right 0.3s ease',\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            fontSize: '1.5rem',\n                                            fontWeight: '900',\n                                            fontFamily: 'Arial, sans-serif',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '900',\n                                                    fontSize: '1.8rem'\n                                                },\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#6c757d',\n                                                    fontSize: '1.2rem',\n                                                    fontWeight: '300'\n                                                },\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '800',\n                                                    letterSpacing: '1px'\n                                                },\n                                                children: \"Prime\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '1.1rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: \"نظام الإدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: \"المحتوى الإعلامي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                style: {\n                                    background: 'transparent',\n                                    border: 'none',\n                                    color: '#a0aec0',\n                                    fontSize: '1.2rem',\n                                    cursor: 'pointer',\n                                    padding: '5px'\n                                },\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    marginBottom: '10px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '35px',\n                                            height: '35px',\n                                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                            borderRadius: '50%',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            color: 'white',\n                                            fontSize: '1rem',\n                                            fontWeight: 'bold'\n                                        },\n                                        children: user?.name?.charAt(0) || 'U'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: user?.name || 'مستخدم'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: [\n                                                    user?.role === 'ADMIN' && '👑 مدير النظام',\n                                                    user?.role === 'MEDIA_MANAGER' && '📝 مدير المحتوى',\n                                                    user?.role === 'SCHEDULER' && '📅 مجدول البرامج',\n                                                    user?.role === 'VIEWER' && '👁️ مستخدم عرض'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '5px',\n                                    color: '#68d391',\n                                    fontSize: '0.8rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '6px',\n                                            height: '6px',\n                                            background: '#68d391',\n                                            borderRadius: '50%'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"متصل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#4299e1' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    padding: '12px 20px',\n                                    textAlign: 'right',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    fontSize: '0.9rem',\n                                    transition: 'all 0.2s',\n                                    borderRight: isActive ? '3px solid #4299e1' : '3px solid transparent'\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = '#2d3748';\n                                        e.target.style.color = 'white';\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '1.1rem'\n                                        },\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    // إضافة وظيفة تسجيل الخروج هنا\n                                    localStorage.removeItem('user');\n                                    localStorage.removeItem('token');\n                                    router.push('/login');\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    padding: '10px',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    gap: '8px',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '15px'\n                                },\n                                children: \"\\uD83D\\uDEAA تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    color: '#6c757d',\n                                    fontSize: '0.75rem',\n                                    fontFamily: 'Arial, sans-serif',\n                                    direction: 'ltr'\n                                },\n                                children: \"Powered By Mahmoud Ismail\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \n\nfunction Toast({ message, type, duration = 3000, onClose }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن\n                }\n            }[\"Toast.useEffect.timer\"], duration);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        duration,\n        onClose\n    ]);\n    const getToastStyles = ()=>{\n        const baseStyles = {\n            position: 'relative',\n            padding: '15px 20px',\n            borderRadius: '10px',\n            color: 'white',\n            fontWeight: 'bold',\n            fontSize: '1rem',\n            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n            transform: isVisible ? 'translateX(0)' : 'translateX(100%)',\n            transition: 'transform 0.3s ease, opacity 0.3s ease',\n            opacity: isVisible ? 1 : 0,\n            minWidth: '300px',\n            maxWidth: '500px',\n            direction: 'rtl',\n            fontFamily: 'Cairo, Arial, sans-serif'\n        };\n        const typeStyles = {\n            success: {\n                background: 'linear-gradient(45deg, #28a745, #20c997)'\n            },\n            error: {\n                background: 'linear-gradient(45deg, #dc3545, #c82333)'\n            },\n            warning: {\n                background: 'linear-gradient(45deg, #ffc107, #e0a800)'\n            },\n            info: {\n                background: 'linear-gradient(45deg, #007bff, #0056b3)'\n            }\n        };\n        return {\n            ...baseStyles,\n            ...typeStyles[type]\n        };\n    };\n    const getIcon = ()=>{\n        const icons = {\n            success: '✅',\n            error: '❌',\n            warning: '⚠️',\n            info: 'ℹ️'\n        };\n        return icons[type];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: getToastStyles(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '10px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        fontSize: '1.2rem'\n                    },\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setIsVisible(false);\n                        setTimeout(onClose, 300);\n                    },\n                    style: {\n                        background: 'rgba(255,255,255,0.2)',\n                        border: 'none',\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '25px',\n                        height: '25px',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        marginLeft: 'auto'\n                    },\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n// Hook لاستخدام Toast\nfunction useToast() {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (message, type = 'info')=>{\n        // التحقق من عدم وجود رسالة مشابهة\n        const existingToast = toasts.find((toast)=>toast.message === message && toast.type === type);\n        if (existingToast) {\n            return; // لا تضيف رسالة مكررة\n        }\n        // إنشاء ID فريد باستخدام timestamp + random number\n        const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        // حد أقصى 5 رسائل في نفس الوقت\n        setToasts((prev)=>prev.slice(-4)); // احتفظ بآخر 4 + الجديدة = 5\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const ToastContainer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: 'fixed',\n                top: '20px',\n                right: '20px',\n                zIndex: 1000,\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '10px'\n            },\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                    message: toast.message,\n                    type: toast.type,\n                    onClose: ()=>removeToast(toast.id)\n                }, toast.id, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 129,\n            columnNumber: 5\n        }, this);\n    return {\n        showToast,\n        ToastContainer\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Toast.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadmin-dashboard%2Fpage&page=%2Fadmin-dashboard%2Fpage&appPaths=%2Fadmin-dashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin-dashboard%2Fpage.tsx&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();