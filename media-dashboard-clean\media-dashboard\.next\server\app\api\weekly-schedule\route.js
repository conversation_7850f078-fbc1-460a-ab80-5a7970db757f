/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/weekly-schedule/route";
exports.ids = ["app/api/weekly-schedule/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fweekly-schedule%2Froute&page=%2Fapi%2Fweekly-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fweekly-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fweekly-schedule%2Froute&page=%2Fapi%2Fweekly-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fweekly-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_weekly_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/weekly-schedule/route.ts */ \"(rsc)/./src/app/api/weekly-schedule/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/weekly-schedule/route\",\n        pathname: \"/api/weekly-schedule\",\n        filename: \"route\",\n        bundlePath: \"app/api/weekly-schedule/route\"\n    },\n    resolvedPagePath: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\weekly-schedule\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Doc_database_media_dashboard_clean_media_dashboard_src_app_api_weekly_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fweekly-schedule%2Froute&page=%2Fapi%2Fweekly-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fweekly-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/shared-data.ts":
/*!************************************!*\
  !*** ./src/app/api/shared-data.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMediaItem: () => (/* binding */ addMediaItem),\n/* harmony export */   getAllMediaItems: () => (/* binding */ getAllMediaItems),\n/* harmony export */   getMediaItemById: () => (/* binding */ getMediaItemById),\n/* harmony export */   removeMediaItem: () => (/* binding */ removeMediaItem),\n/* harmony export */   updateMediaItem: () => (/* binding */ updateMediaItem)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n// ملف مشترك للبيانات - يضمن التزامن بين جميع APIs\n\n\n// مسار ملف البيانات المؤقت\nconst DATA_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'temp-data.json');\n// قاعدة بيانات مشتركة للمواد الإعلامية\nlet mediaItems = [];\n// تحميل البيانات من الملف عند بدء التشغيل\nfunction loadData() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(DATA_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(DATA_FILE, 'utf8');\n            mediaItems = JSON.parse(data);\n            console.log(`📂 تم تحميل ${mediaItems.length} مادة من الملف المؤقت`);\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحميل البيانات:', error);\n        mediaItems = [];\n    }\n}\n// حفظ البيانات في الملف\nfunction saveData() {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(DATA_FILE, JSON.stringify(mediaItems, null, 2));\n        console.log(`💾 تم حفظ ${mediaItems.length} مادة في الملف المؤقت`);\n    } catch (error) {\n        console.error('❌ خطأ في حفظ البيانات:', error);\n    }\n}\n// تحميل البيانات عند استيراد الملف\nloadData();\n// دالة لإضافة مادة جديدة\nfunction addMediaItem(item) {\n    mediaItems.push(item);\n    saveData(); // حفظ فوري\n    console.log(`✅ تم إضافة مادة جديدة: ${item.name} (المجموع: ${mediaItems.length})`);\n}\n// دالة لحذف مادة\nfunction removeMediaItem(id) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        const removed = mediaItems.splice(index, 1)[0];\n        saveData(); // حفظ فوري\n        console.log(`🗑️ تم حذف المادة: ${removed.name} (المجموع: ${mediaItems.length})`);\n        return true;\n    }\n    return false;\n}\n// دالة للحصول على جميع المواد\nfunction getAllMediaItems() {\n    return mediaItems;\n}\n// دالة للحصول على مادة بالمعرف\nfunction getMediaItemById(id) {\n    return mediaItems.find((item)=>item.id === id);\n}\n// دالة لتحديث مادة\nfunction updateMediaItem(id, updatedItem) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        mediaItems[index] = {\n            ...mediaItems[index],\n            ...updatedItem\n        };\n        saveData(); // حفظ فوري\n        console.log(`✏️ تم تحديث المادة: ${updatedItem.name} (المعرف: ${id})`);\n        return true;\n    }\n    return false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/shared-data.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/weekly-schedule/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/weekly-schedule/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared-data */ \"(rsc)/./src/app/api/shared-data.ts\");\n\n\n\n// استيراد البيانات المشتركة\n\n// بيانات الجداول الأسبوعية مع التواريخ\nlet weeklySchedules = new Map();\n// بيانات المواد المؤقتة لكل أسبوع\nlet tempItems = new Map();\n// مسار ملف الحفظ\nconst SCHEDULES_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data', 'weekly-schedules.json');\nconst TEMP_ITEMS_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data', 'temp-items.json');\n// التأكد من وجود مجلد البيانات\nasync function ensureDataDir() {\n    const dataDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data');\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.access(dataDir);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.mkdir(dataDir, {\n            recursive: true\n        });\n    }\n}\n// حفظ البيانات في ملف\nasync function saveSchedulesToFile() {\n    try {\n        await ensureDataDir();\n        const schedulesData = Object.fromEntries(weeklySchedules);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(SCHEDULES_FILE, JSON.stringify(schedulesData, null, 2));\n        console.log('💾 تم حفظ الجداول في الملف');\n    } catch (error) {\n        console.error('❌ خطأ في حفظ الجداول:', error);\n    }\n}\n// حفظ المواد المؤقتة في ملف\nasync function saveTempItemsToFile() {\n    try {\n        await ensureDataDir();\n        const tempData = Object.fromEntries(tempItems);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(TEMP_ITEMS_FILE, JSON.stringify(tempData, null, 2));\n        console.log('💾 تم حفظ المواد المؤقتة في الملف');\n    } catch (error) {\n        console.error('❌ خطأ في حفظ المواد المؤقتة:', error);\n    }\n}\n// تحميل البيانات من الملف\nasync function loadSchedulesFromFile() {\n    try {\n        const data = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(SCHEDULES_FILE, 'utf8');\n        const schedulesData = JSON.parse(data);\n        weeklySchedules = new Map(Object.entries(schedulesData));\n        console.log('📂 تم تحميل الجداول من الملف');\n    } catch (error) {\n        console.log('📂 لا يوجد ملف جداول محفوظ، بدء جديد');\n    }\n}\n// تحميل المواد المؤقتة من الملف\nasync function loadTempItemsFromFile() {\n    try {\n        const data = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(TEMP_ITEMS_FILE, 'utf8');\n        const tempData = JSON.parse(data);\n        tempItems = new Map(Object.entries(tempData));\n        console.log('📂 تم تحميل المواد المؤقتة من الملف');\n    } catch (error) {\n        console.log('📂 لا يوجد ملف مواد مؤقتة محفوظ، بدء جديد');\n    }\n}\n// تحميل البيانات عند بدء التشغيل\nlet dataLoaded = false;\nasync function initializeData() {\n    if (!dataLoaded) {\n        await loadSchedulesFromFile();\n        await loadTempItemsFromFile();\n        dataLoaded = true;\n    }\n}\n// Helper functions\nfunction getWeekStart(date) {\n    const d = new Date(date);\n    const day = d.getDay();\n    const diff = d.getDate() - day;\n    return new Date(d.setDate(diff));\n}\nfunction formatDate(date) {\n    return date.toISOString().split('T')[0];\n}\nfunction timeToMinutes(time) {\n    if (!time || typeof time !== 'string') {\n        console.warn(`⚠️ وقت غير صحيح: ${time}`);\n        return 0;\n    }\n    const [hours, minutes] = time.split(':').map(Number);\n    return hours * 60 + minutes;\n}\n// حساب المدة الإجمالية للمادة\nfunction calculateTotalDuration(segments) {\n    if (!segments || segments.length === 0) return '01:00:00';\n    let totalSeconds = 0;\n    segments.forEach((segment)=>{\n        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n        totalSeconds += hours * 3600 + minutes * 60 + seconds;\n    });\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor(totalSeconds % 3600 / 60);\n    const secs = totalSeconds % 60;\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n}\nfunction addMinutesToTime(timeStr, minutes) {\n    const totalMinutes = timeToMinutes(timeStr) + minutes;\n    const hours = Math.floor(totalMinutes / 60) % 24;\n    const mins = totalMinutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n}\nfunction calculateDuration(startTime, endTime) {\n    const startMinutes = timeToMinutes(startTime);\n    const endMinutes = timeToMinutes(endTime);\n    let duration = endMinutes - startMinutes;\n    if (duration < 0) duration += 24 * 60;\n    return duration;\n}\nfunction isPrimeTime(startTime, dayOfWeek) {\n    const startMinutes = timeToMinutes(startTime);\n    if ([\n        0,\n        1,\n        2,\n        3\n    ].includes(dayOfWeek)) {\n        // الأحد-الأربعاء: 18:00-00:00\n        return startMinutes >= timeToMinutes('18:00');\n    } else if ([\n        4,\n        5,\n        6\n    ].includes(dayOfWeek)) {\n        // الخميس-السبت: 18:00-02:00 (اليوم التالي)\n        return startMinutes >= timeToMinutes('18:00') || startMinutes < timeToMinutes('02:00');\n    }\n    return false;\n}\n// دالة توليد الإعادات التلقائية (متتالية)\nfunction generateReruns(scheduleItems, weekStart, tempItems = []) {\n    const reruns = [];\n    console.log(`🔄 توليد الإعادات للأسبوع: ${weekStart}`);\n    // دمج المواد العادية والمؤقتة مع فلترة المواد المحذوفة\n    const permanentDeletedSet = deletedReruns.get('permanent_temp_deletions') || new Set();\n    const allItems = [\n        ...scheduleItems,\n        ...tempItems\n    ];\n    const regularItems = allItems.filter((item)=>!item.isRerun && !item.isTemporary && !permanentDeletedSet.has(item.mediaItemId || item.id));\n    const temporaryItems = allItems.filter((item)=>!item.isRerun && item.isTemporary && !permanentDeletedSet.has(item.mediaItemId || item.id));\n    console.log(`📊 المواد: ${regularItems.length} عادية + ${temporaryItems.length} مؤقتة (بعد فلترة المحذوفة)`);\n    // التحقق من وجود مواد للإعادة\n    if (regularItems.length === 0 && temporaryItems.length === 0) {\n        console.log('⚠️ لا توجد مواد لتوليد إعادات منها');\n        return reruns;\n    }\n    // تجميع المواد حسب اليوم لتوليد إعادات متتالية\n    const itemsByDay = new Map();\n    // إضافة المواد العادية في البرايم تايم\n    regularItems.filter((item)=>isPrimeTime(item.startTime, item.dayOfWeek)).forEach((item)=>{\n        if (!itemsByDay.has(item.dayOfWeek)) {\n            itemsByDay.set(item.dayOfWeek, []);\n        }\n        itemsByDay.get(item.dayOfWeek).push(item);\n    });\n    // إضافة المواد المؤقتة في البرايم تايم (تجاهل المواد المؤقتة في القائمة الجانبية)\n    temporaryItems.filter((item)=>item.startTime && item.dayOfWeek !== undefined && item.startTime !== undefined && isPrimeTime(item.startTime, item.dayOfWeek)).forEach((item)=>{\n        if (!itemsByDay.has(item.dayOfWeek)) {\n            itemsByDay.set(item.dayOfWeek, []);\n        }\n        itemsByDay.get(item.dayOfWeek).push({\n            ...item,\n            isTemporary: true\n        });\n    });\n    // توليد إعادات متتالية لكل يوم\n    itemsByDay.forEach((dayItems, dayOfWeek)=>{\n        console.log(`📅 اليوم ${dayOfWeek}: ${dayItems.length} مادة في البرايم`);\n        // ترتيب المواد حسب وقت البداية في البرايم (مع مراعاة انتقال اليوم)\n        dayItems.sort((a, b)=>{\n            let timeA = timeToMinutes(a.startTime);\n            let timeB = timeToMinutes(b.startTime);\n            // للخميس-السبت: إذا كان الوقت أقل من 02:00، فهو في اليوم التالي\n            if ([\n                4,\n                5,\n                6\n            ].includes(dayOfWeek)) {\n                if (timeA < timeToMinutes('02:00')) timeA += 24 * 60; // إضافة 24 ساعة\n                if (timeB < timeToMinutes('02:00')) timeB += 24 * 60;\n            }\n            return timeA - timeB;\n        });\n        console.log(`📋 ترتيب المواد حسب وقت البث: ${dayItems.map((item, i)=>`${i + 1}:${item.mediaItem?.name || 'مؤقت'}(${item.startTime})`).join(', ')}`);\n        const dayReruns = generateSequentialReruns(dayItems, weekStart, dayOfWeek);\n        reruns.push(...dayReruns);\n    });\n    console.log(`✅ تم توليد ${reruns.length} إعادة إجمالية`);\n    return reruns;\n}\n// دالة توليد إعادات متتالية لمواد يوم واحد (ترتيب مستمر مضمون)\nfunction generateSequentialReruns(dayItems, weekStart, dayOfWeek) {\n    const reruns = [];\n    if (dayItems.length === 0) return reruns;\n    console.log(`🔄 توليد إعادات لـ ${dayItems.length} مادة من اليوم ${dayOfWeek}`);\n    console.log(`📋 ترتيب المواد: ${dayItems.map((item, i)=>`${i + 1}:${item.mediaItem?.name || 'مؤقت'}`).join(', ')}`);\n    // تحديد أوقات الإعادات حسب اليوم\n    let rerunStartTime;\n    let rerunDay;\n    if ([\n        0,\n        1,\n        2,\n        3\n    ].includes(dayOfWeek)) {\n        // الأحد-الأربعاء: الإعادات تبدأ من 00:00 في نفس اليوم\n        rerunStartTime = '00:00';\n        rerunDay = dayOfWeek;\n    } else if ([\n        4,\n        5,\n        6\n    ].includes(dayOfWeek)) {\n        // الخميس-السبت: الإعادات تبدأ من 02:00 في نفس اليوم (بعد انتهاء البرايم)\n        rerunStartTime = '02:00';\n        rerunDay = dayOfWeek;\n    } else {\n        return reruns;\n    }\n    // إنشاء قائمة مستمرة من المواد (تكرار لانهائي)\n    function getNextItem(index) {\n        return dayItems[index % dayItems.length];\n    }\n    let currentTime = rerunStartTime;\n    let itemSequenceIndex = 0; // فهرس التسلسل المستمر - يبدأ من 0 (المادة الأولى)\n    console.log(`🚀 بدء الجزء الأول من ${rerunStartTime} حتى 08:00 - بدءاً من المادة الأولى: ${dayItems[0].mediaItem?.name || 'مؤقت'}`);\n    // الجزء الأول: حتى 08:00\n    while(timeToMinutes(currentTime) < timeToMinutes('08:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('08:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${rerunDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: rerunDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: weekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                rerunPart: 1,\n                rerunCycle: Math.floor(itemSequenceIndex / dayItems.length) + 1,\n                mediaItem: item.mediaItem,\n                originalId: item.id\n            });\n            console.log(`✅ جزء 1: [${itemSequenceIndex}] ${item.mediaItem?.name || 'مؤقت'} (مادة ${itemSequenceIndex % dayItems.length + 1}/${dayItems.length}, دورة ${Math.floor(itemSequenceIndex / dayItems.length) + 1}) ${currentTime}-${rerunEndTime}`);\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            console.log(`⏰ توقف الجزء الأول عند ${currentTime} - المادة التالية: ${item.mediaItem?.name || 'مؤقت'}`);\n            break;\n        }\n    }\n    console.log(`📊 انتهى الجزء الأول: فهرس التسلسل ${itemSequenceIndex}, المادة التالية: ${getNextItem(itemSequenceIndex).mediaItem?.name || 'مؤقت'}`);\n    // حساب اليوم التالي مع مراعاة الأسبوع الجديد\n    let nextDay;\n    let nextWeekStart = weekStart;\n    if (dayOfWeek === 6) {\n        nextDay = 0; // الأحد في الأسبوع التالي\n        const nextWeekDate = new Date(weekStart);\n        nextWeekDate.setDate(nextWeekDate.getDate() + 7);\n        nextWeekStart = formatDate(nextWeekDate);\n        console.log(`🔄 إعادات السبت تذهب للأحد في الأسبوع التالي: ${nextWeekStart}`);\n    } else {\n        nextDay = (rerunDay + 1) % 7;\n    }\n    currentTime = '08:00';\n    console.log(`🚀 بدء الجزء الثاني: العمود ${nextDay} - استكمال من المادة ${getNextItem(itemSequenceIndex).mediaItem?.name || 'مؤقت'} (فهرس ${itemSequenceIndex})`);\n    // الجزء الثاني: استكمال من حيث توقف الجزء الأول\n    while(timeToMinutes(currentTime) < timeToMinutes('18:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('18:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${nextDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: nextDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: nextWeekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                rerunPart: 2,\n                rerunCycle: Math.floor(itemSequenceIndex / dayItems.length) + 1,\n                mediaItem: item.mediaItem,\n                originalId: item.id,\n                isNextWeekRerun: dayOfWeek === 6\n            });\n            console.log(`✅ جزء 2: [${itemSequenceIndex}] ${item.mediaItem?.name || 'مؤقت'} (مادة ${itemSequenceIndex % dayItems.length + 1}/${dayItems.length}, دورة ${Math.floor(itemSequenceIndex / dayItems.length) + 1}) ${currentTime}-${rerunEndTime}`);\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            console.log(`⏰ توقف الجزء الثاني عند ${currentTime} لتجاوز 18:00`);\n            break;\n        }\n    }\n    return reruns;\n}\n// قائمة الإعادات المحذوفة (لترك الحقول فارغة)\nconst deletedReruns = new Map(); // weekStart -> Set of rerun IDs\n// GET - جلب الجدول الأسبوعي\nasync function GET(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const { searchParams } = new URL(request.url);\n        const weekStartParam = searchParams.get('weekStart');\n        const tempItemsParam = searchParams.get('tempItems');\n        const weekStart = weekStartParam ? new Date(weekStartParam) : getWeekStart(new Date());\n        const weekStartStr = formatDate(weekStart);\n        console.log('📅 جلب الجدول الأسبوعي لتاريخ:', weekStartStr);\n        // الحصول على الجدول أو إنشاء جدول فارغ\n        let scheduleItems = weeklySchedules.get(weekStartStr) || [];\n        // الحصول على المواد المؤقتة المحفوظة\n        let weekTempItems = tempItems.get(weekStartStr) || [];\n        console.log(`📦 المواد المؤقتة المحفوظة: ${weekTempItems.length}`);\n        // توليد الإعادات التلقائية\n        console.log('🚀 بدء توليد الإعادات...');\n        const reruns = generateReruns(scheduleItems, weekStartStr, weekTempItems);\n        console.log(`📊 تم توليد ${reruns.length} إعادة`);\n        // فلترة الإعادات المحذوفة\n        const deletedSet = deletedReruns.get(weekStartStr) || new Set();\n        // فصل الإعادات حسب الأسبوع وفلترة المحذوفة\n        const currentWeekReruns = reruns.filter((rerun)=>{\n            if (rerun.weekStart !== weekStartStr) return false;\n            if (deletedSet.has(rerun.id)) return false;\n            // فلترة إضافية: التحقق من عدم وجود مادة أخرى في نفس المكان\n            const hasConflict = [\n                ...scheduleItems,\n                ...weekTempItems\n            ].some((item)=>!item.isRerun && item.dayOfWeek === rerun.dayOfWeek && item.startTime === rerun.startTime);\n            if (hasConflict) {\n                console.log(`🚫 تم تجاهل إعادة ${rerun.mediaItem.name} في ${rerun.startTime} بسبب وجود مادة أخرى`);\n                return false;\n            }\n            return true;\n        });\n        // إضافة إعادات السبت من الأسبوع السابق (تظهر في الأحد)\n        const prevWeekDate = new Date(weekStartStr);\n        prevWeekDate.setDate(prevWeekDate.getDate() - 7);\n        const prevWeekStr = formatDate(prevWeekDate);\n        // توليد إعادات الأسبوع السابق للحصول على إعادات السبت\n        const prevWeekScheduleItems = weeklySchedules.get(prevWeekStr) || [];\n        const prevWeekTempItems = tempItems.get(prevWeekStr) || [];\n        // التحقق من وجود مواد في السبت من الأسبوع السابق\n        const saturdayItems = [\n            ...prevWeekScheduleItems,\n            ...prevWeekTempItems\n        ].filter((item)=>!item.isRerun && item.dayOfWeek === 6);\n        let saturdayReruns = [];\n        if (saturdayItems.length > 0) {\n            console.log(`📅 وجدت ${saturdayItems.length} مادة في السبت من الأسبوع السابق`);\n            const prevWeekReruns = generateReruns(prevWeekScheduleItems, prevWeekStr, prevWeekTempItems);\n            // فلترة إعادات السبت التي تذهب للأسبوع التالي (الأحد)\n            saturdayReruns = prevWeekReruns.filter((rerun)=>rerun.isNextWeekRerun && rerun.weekStart === weekStartStr && rerun.dayOfWeek === 0 && !deletedSet.has(rerun.id));\n            console.log(`📅 إعادات السبت من الأسبوع السابق: ${saturdayReruns.length}`);\n        } else {\n            console.log(`📅 لا توجد مواد في السبت من الأسبوع السابق - لن يتم توليد إعادات`);\n        }\n        // دمج جميع المواد: عادية + مؤقتة + إعادات\n        const allItems = [\n            ...scheduleItems,\n            ...weekTempItems,\n            ...currentWeekReruns,\n            ...saturdayReruns\n        ];\n        // فلترة المواد الكبيرة للعرض مع إضافة المدة المحسوبة\n        const mediaItems = (0,_shared_data__WEBPACK_IMPORTED_MODULE_3__.getAllMediaItems)();\n        const bigMediaTypes = [\n            'PROGRAM',\n            'SERIES',\n            'MOVIE'\n        ];\n        const availableMedia = mediaItems.filter((item)=>bigMediaTypes.includes(item.type)).map((item)=>({\n                ...item,\n                duration: calculateTotalDuration(item.segments || [])\n            }));\n        // إضافة المواد المؤقتة للاستجابة (فقط المواد في القائمة الجانبية، ليس في الجدول)\n        const currentTempItems = tempItems.get(weekStartStr) || [];\n        const sidebarTempItems = currentTempItems.filter((item)=>!item.dayOfWeek && !item.startTime && !item.endTime);\n        console.log(`📦 إرسال ${sidebarTempItems.length} مادة مؤقتة في الاستجابة (من أصل ${currentTempItems.length})`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                weekStart: weekStartStr,\n                scheduleItems: allItems,\n                availableMedia,\n                tempItems: sidebarTempItems\n            }\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب الجدول:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الجدول الأسبوعي'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مادة للجدول أو حفظ مادة مؤقتة\nasync function POST(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const body = await request.json();\n        const { mediaItemId, dayOfWeek, startTime, endTime, weekStart, episodeNumber, seasonNumber, partNumber, isTemporary, mediaItem: tempMediaItem } = body;\n        // التعامل مع المواد المؤقتة\n        if (isTemporary && tempMediaItem) {\n            // تنظيف وتصحيح البيانات\n            const cleanTempItem = {\n                ...tempMediaItem,\n                name: tempMediaItem.name || tempMediaItem.title || 'مادة مؤقتة',\n                id: tempMediaItem.id || `temp_${Date.now()}`,\n                type: tempMediaItem.type || 'PROGRAM',\n                duration: tempMediaItem.duration || '01:00:00'\n            };\n            console.log('💾 حفظ مادة مؤقتة في API:', cleanTempItem.name);\n            console.log('📋 بيانات المادة المؤقتة المنظفة:', JSON.stringify(cleanTempItem, null, 2));\n            // الحصول على المواد المؤقتة الحالية\n            let currentTempItems = tempItems.get(weekStart) || [];\n            // البحث عن المواد الموجودة في نفس الوقت والمكان لحذف إعاداتها\n            const tempItemsToRemove = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            const regularItemsToRemove = scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n            // حذف الإعادات المرتبطة بجميع المواد التي سيتم استبدالها\n            const allItemsToRemove = [\n                ...tempItemsToRemove,\n                ...regularItemsToRemove\n            ];\n            for (const itemToRemove of allItemsToRemove){\n                if (!deletedReruns.has(weekStart)) {\n                    deletedReruns.set(weekStart, new Set());\n                }\n                const deletedSet = deletedReruns.get(weekStart);\n                // البحث عن جميع الإعادات المرتبطة بهذه المادة\n                const allItems = [\n                    ...scheduleItems,\n                    ...currentTempItems\n                ];\n                const relatedReruns = allItems.filter((item)=>item.isRerun && (item.mediaItemId === itemToRemove.mediaItemId || item.id.includes(itemToRemove.id) || item.originalStartTime === itemToRemove.startTime));\n                relatedReruns.forEach((rerun)=>deletedSet.add(rerun.id));\n                console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة ${itemToRemove.mediaItem?.name || 'مؤقتة'}`);\n            }\n            // حذف أي مواد موجودة في نفس الوقت والمكان\n            currentTempItems = currentTempItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n            // حذف المواد العادية من نفس الوقت والمكان\n            scheduleItems = scheduleItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n            weeklySchedules.set(weekStart, scheduleItems);\n            console.log(`🔄 تم تنظيف المكان للمادة المؤقتة في اليوم ${dayOfWeek} الوقت ${startTime}`);\n            // إضافة المادة المؤقتة الجديدة مع معرف فريد\n            const tempItem = {\n                id: `temp_${Date.now()}`,\n                mediaItemId: cleanTempItem.id,\n                dayOfWeek,\n                startTime,\n                endTime,\n                weekStart,\n                isRerun: false,\n                isTemporary: true,\n                mediaItem: cleanTempItem,\n                createdAt: new Date().toISOString()\n            };\n            console.log('✅ تم إنشاء المادة المؤقتة:', {\n                id: tempItem.id,\n                name: tempItem.mediaItem.name,\n                type: tempItem.mediaItem.type,\n                duration: tempItem.mediaItem.duration\n            });\n            currentTempItems.push(tempItem);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملفات\n            await saveTempItemsToFile();\n            await saveSchedulesToFile();\n            console.log(`✅ تم حفظ المادة المؤقتة. إجمالي المواد المؤقتة: ${currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: tempItem\n            });\n        }\n        if (!mediaItemId || dayOfWeek === undefined || !startTime || !endTime || !weekStart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'جميع البيانات مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود المادة\n        const mediaItem = (0,_shared_data__WEBPACK_IMPORTED_MODULE_3__.getMediaItemById)(mediaItemId);\n        if (!mediaItem) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المادة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        // الحصول على الجدول الحالي\n        let scheduleItems = weeklySchedules.get(weekStart) || [];\n        // التحقق من التداخل\n        const startMinutes = timeToMinutes(startTime);\n        const endMinutes = timeToMinutes(endTime);\n        const conflict = scheduleItems.find((item)=>{\n            if (item.dayOfWeek !== dayOfWeek) return false;\n            const itemStart = timeToMinutes(item.startTime);\n            const itemEnd = timeToMinutes(item.endTime);\n            return startMinutes < itemEnd && endMinutes > itemStart;\n        });\n        if (conflict) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يوجد تداخل في الأوقات'\n            }, {\n                status: 400\n            });\n        }\n        // البحث عن المواد الموجودة في نفس الوقت والمكان لحذف إعاداتها\n        const itemsToRemove = scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        // حذف الإعادات المرتبطة بالمواد التي سيتم استبدالها\n        for (const itemToRemove of itemsToRemove){\n            // إضافة جميع الإعادات المرتبطة بهذه المادة للقائمة المحذوفة\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            const deletedSet = deletedReruns.get(weekStart);\n            // البحث عن جميع الإعادات المرتبطة بهذه المادة\n            const allItems = [\n                ...scheduleItems,\n                ...tempItems.get(weekStart) || []\n            ];\n            const relatedReruns = allItems.filter((item)=>item.isRerun && (item.mediaItemId === itemToRemove.mediaItemId || item.id.includes(itemToRemove.id) || item.originalStartTime === itemToRemove.startTime));\n            relatedReruns.forEach((rerun)=>deletedSet.add(rerun.id));\n            console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة ${itemToRemove.mediaItem?.name}`);\n        }\n        // حذف أي مواد موجودة في نفس الوقت والمكان (بما في ذلك المواد المؤقتة)\n        scheduleItems = scheduleItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        // حذف المواد المؤقتة من نفس الوقت والمكان\n        let currentTempItems = tempItems.get(weekStart) || [];\n        const tempItemsToRemove = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        console.log(`🔍 البحث عن مواد مؤقتة للحذف في اليوم ${dayOfWeek} الوقت ${startTime}: وجد ${tempItemsToRemove.length} مادة`);\n        // حذف إعادات المواد المؤقتة أيضاً\n        for (const tempItem of tempItemsToRemove){\n            console.log(`🗑️ حذف المادة المؤقتة: ${tempItem.mediaItem?.name} (ID: ${tempItem.id})`);\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            // البحث عن إعادات المادة المؤقتة في جميع الأسابيع وحذفها نهائياً\n            const allWeeks = Array.from(weeklySchedules.keys());\n            for (const week of allWeeks){\n                const weekItems = weeklySchedules.get(week) || [];\n                const relatedReruns = weekItems.filter((item)=>item.isRerun && item.isTemporary && (item.mediaItemId === tempItem.mediaItemId || item.id.includes(tempItem.id) || item.originalId === tempItem.id || item.mediaItem && item.mediaItem.name === tempItem.mediaItem?.name));\n                // إضافة الإعادات للقائمة المحذوفة\n                relatedReruns.forEach((rerun)=>{\n                    if (!deletedReruns.has(week)) {\n                        deletedReruns.set(week, new Set());\n                    }\n                    deletedReruns.get(week).add(rerun.id);\n                });\n                // حذف الإعادات من الجدول نهائياً\n                const updatedWeekItems = weekItems.filter((item)=>!relatedReruns.some((rerun)=>rerun.id === item.id));\n                weeklySchedules.set(week, updatedWeekItems);\n                console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة المؤقتة ${tempItem.mediaItem?.name} في الأسبوع ${week} نهائياً`);\n            }\n            // إضافة معرف المادة المؤقتة لقائمة المحذوفات الدائمة\n            if (!deletedReruns.has('permanent_temp_deletions')) {\n                deletedReruns.set('permanent_temp_deletions', new Set());\n            }\n            deletedReruns.get('permanent_temp_deletions').add(tempItem.mediaItemId || tempItem.id);\n        }\n        // حذف المواد المؤقتة من tempItems نهائياً\n        const beforeCount = currentTempItems.length;\n        const tempItemsToDelete = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        // طباعة تفاصيل المواد التي سيتم حذفها\n        tempItemsToDelete.forEach((item)=>{\n            console.log(`🗑️ سيتم حذف المادة المؤقتة نهائياً: ${item.mediaItem?.name} (ID: ${item.id})`);\n        });\n        currentTempItems = currentTempItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        tempItems.set(weekStart, currentTempItems);\n        // حذف المواد المؤقتة من weeklySchedules أيضاً\n        scheduleItems = scheduleItems.filter((item)=>!(item.isTemporary && item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        weeklySchedules.set(weekStart, scheduleItems);\n        // حذف المواد المؤقتة من جميع الأسابيع في weeklySchedules\n        const allWeeks = Array.from(weeklySchedules.keys());\n        for (const week of allWeeks){\n            let weekItems = weeklySchedules.get(week) || [];\n            const beforeWeekCount = weekItems.length;\n            // حذف المواد المؤقتة والإعادات المرتبطة بها\n            weekItems = weekItems.filter((item)=>{\n                // حذف المواد المؤقتة التي تطابق المواد المحذوفة\n                if (item.isTemporary) {\n                    const shouldDelete = tempItemsToDelete.some((deletedItem)=>item.mediaItem?.name === deletedItem.mediaItem?.name && item.dayOfWeek === deletedItem.dayOfWeek && item.startTime === deletedItem.startTime || item.id === deletedItem.id || item.mediaItemId === deletedItem.mediaItemId);\n                    if (shouldDelete) {\n                        console.log(`🗑️ حذف مادة مؤقتة من weeklySchedules: ${item.mediaItem?.name} (${item.startTime})`);\n                        return false;\n                    }\n                }\n                // حذف الإعادات المرتبطة بالمواد المؤقتة المحذوفة\n                if (item.isRerun && item.isTemporary) {\n                    const shouldDelete = tempItemsToDelete.some((deletedItem)=>item.mediaItem?.name === deletedItem.mediaItem?.name || item.originalId === deletedItem.id || item.mediaItemId === deletedItem.mediaItemId);\n                    if (shouldDelete) {\n                        console.log(`🗑️ حذف إعادة مؤقتة من weeklySchedules: ${item.mediaItem?.name} (${item.startTime})`);\n                        return false;\n                    }\n                }\n                return true;\n            });\n            weeklySchedules.set(week, weekItems);\n            if (beforeWeekCount !== weekItems.length) {\n                console.log(`🗑️ تم حذف ${beforeWeekCount - weekItems.length} مادة/إعادة مؤقتة من الأسبوع ${week}`);\n            }\n        }\n        console.log(`🗑️ تم حذف ${beforeCount - currentTempItems.length} مادة مؤقتة من tempItems نهائياً`);\n        // حفظ المواد المؤقتة المحدثة\n        await saveTempItemsToFile();\n        await saveSchedulesToFile();\n        console.log(`💾 تم حفظ الملفات بعد حذف المواد المؤقتة. المواد المتبقية: ${currentTempItems.length}`);\n        console.log(`🔄 تم تنظيف المكان في اليوم ${dayOfWeek} الوقت ${startTime}`);\n        // إضافة المادة الجديدة مع تفاصيل الحلقة/الجزء\n        const newItem = {\n            id: `schedule_${Date.now()}`,\n            mediaItemId,\n            dayOfWeek,\n            startTime,\n            endTime,\n            weekStart,\n            isRerun: false,\n            // حفظ تفاصيل الحلقة/الجزء على مستوى العنصر\n            episodeNumber,\n            seasonNumber,\n            partNumber,\n            // إنشاء نسخة محدثة من المادة مع التفاصيل\n            mediaItem: {\n                ...mediaItem,\n                episodeNumber: episodeNumber || mediaItem.episodeNumber,\n                seasonNumber: seasonNumber || mediaItem.seasonNumber,\n                partNumber: partNumber || mediaItem.partNumber\n            },\n            createdAt: new Date().toISOString()\n        };\n        scheduleItems.push(newItem);\n        weeklySchedules.set(weekStart, scheduleItems);\n        // حفظ في الملف\n        await saveSchedulesToFile();\n        console.log('✅ تم إضافة المادة:', mediaItem.name);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newItem\n        });\n    } catch (error) {\n        console.error('❌ خطأ في إضافة المادة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة المادة'\n        }, {\n            status: 500\n        });\n    }\n}\n// دعم إضافي للمواد المؤقتة في القائمة الجانبية\nasync function PATCH(request) {\n    try {\n        await initializeData();\n        const body = await request.json();\n        const { action } = body;\n        // حفظ مادة مؤقتة في القائمة الجانبية\n        if (action === 'saveTempToSidebar') {\n            const { tempMedia, weekStart: weekStartParam } = body;\n            const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));\n            console.log(`💾 حفظ مادة مؤقتة في القائمة الجانبية: ${tempMedia.name}`);\n            // إضافة المادة المؤقتة إلى tempItems\n            let currentTempItems = tempItems.get(weekStart) || [];\n            currentTempItems.push(tempMedia);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملف\n            await saveTempItemsToFile();\n            console.log(`✅ تم حفظ المادة المؤقتة في القائمة الجانبية. إجمالي: ${currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حفظ المادة المؤقتة في القائمة الجانبية'\n            });\n        }\n        // حذف مادة مؤقتة من القائمة الجانبية\n        if (action === 'deleteTempFromSidebar') {\n            const { tempMediaId, weekStart: weekStartParam } = body;\n            const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));\n            console.log(`🗑️ حذف مادة مؤقتة من القائمة الجانبية: ${tempMediaId}`);\n            // حذف المادة المؤقتة من tempItems\n            let currentTempItems = tempItems.get(weekStart) || [];\n            const beforeCount = currentTempItems.length;\n            currentTempItems = currentTempItems.filter((item)=>item.id !== tempMediaId);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملف\n            await saveTempItemsToFile();\n            console.log(`✅ تم حذف المادة المؤقتة من القائمة الجانبية. المحذوف: ${beforeCount - currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف المادة المؤقتة من القائمة الجانبية'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'إجراء غير مدعوم'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('❌ خطأ في PATCH:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في العملية'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مادة\nasync function DELETE(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        const weekStart = searchParams.get('weekStart');\n        if (!id || !weekStart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المعرف والتاريخ مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق إذا كانت المادة مؤقتة\n        if (id.startsWith('temp_')) {\n            console.log('🗑️ حذف مادة مؤقتة:', id);\n            // حذف من المواد المؤقتة\n            let currentTempItems = tempItems.get(weekStart) || [];\n            const originalLength = currentTempItems.length;\n            currentTempItems = currentTempItems.filter((item)=>item.id !== id && item.originalId !== id);\n            tempItems.set(weekStart, currentTempItems);\n            // حذف من الجدول الأساسي أيضاً\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            scheduleItems = scheduleItems.filter((item)=>item.id !== id && item.mediaItemId !== id && !(item.mediaItemId && item.mediaItemId.startsWith('temp_') && item.mediaItemId === id));\n            weeklySchedules.set(weekStart, scheduleItems);\n            console.log(`🗑️ حذف المادة المؤقتة من tempItems: ${id}`);\n            console.log(`🗑️ حذف المادة المؤقتة من weeklySchedules: ${id}`);\n            // حفظ في الملفات\n            await saveTempItemsToFile();\n            await saveSchedulesToFile();\n            console.log(`✅ تم حذف المادة المؤقتة نهائياً. المواد المتبقية: ${currentTempItems.length} (كان ${originalLength})`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true\n            });\n        }\n        // التحقق إذا كانت المادة إعادة\n        if (id.startsWith('rerun_')) {\n            // إضافة الإعادة للقائمة المحذوفة\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            deletedReruns.get(weekStart).add(id);\n            console.log('🗑️ تم حذف الإعادة (ترك فارغ):', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف الإعادة بنجاح'\n            });\n        } else {\n            // حذف المادة الأصلية\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            const index = scheduleItems.findIndex((item)=>item.id === id);\n            if (index === -1) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'المادة غير موجودة'\n                }, {\n                    status: 404\n                });\n            }\n            scheduleItems.splice(index, 1);\n            weeklySchedules.set(weekStart, scheduleItems);\n            // حفظ في الملف\n            await saveSchedulesToFile();\n            console.log('🗑️ تم حذف المادة الأصلية:', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف المادة بنجاح'\n            });\n        }\n    } catch (error) {\n        console.error('❌ خطأ في حذف المادة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف المادة'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/weekly-schedule/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fweekly-schedule%2Froute&page=%2Fapi%2Fweekly-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fweekly-schedule%2Froute.ts&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();