"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const validateTimeFormat = (time)=>{\n        const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;\n        return timeRegex.test(time);\n    };\n    const formatTimeInput = (value)=>{\n        // إزالة أي أحرف غير رقمية أو نقطتين\n        const cleaned = value.replace(/[^\\d:]/g, '');\n        // تقسيم النص إلى أجزاء\n        const parts = cleaned.split(':');\n        // تنسيق كل جزء\n        const hours = parts[0] ? parts[0].padStart(2, '0').slice(0, 2) : '00';\n        const minutes = parts[1] ? parts[1].padStart(2, '0').slice(0, 2) : '00';\n        const seconds = parts[2] ? parts[2].padStart(2, '0').slice(0, 2) : '00';\n        return \"\".concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    };\n    const calculateDuration = (timeIn, timeOut)=>{\n        if (!timeIn || !timeOut || !validateTimeFormat(timeIn) || !validateTimeFormat(timeOut)) {\n            return '00:00:00';\n        }\n        const [inHours, inMinutes, inSeconds] = timeIn.split(':').map(Number);\n        const [outHours, outMinutes, outSeconds] = timeOut.split(':').map(Number);\n        const inTotalSeconds = inHours * 3600 + inMinutes * 60 + inSeconds;\n        const outTotalSeconds = outHours * 3600 + outMinutes * 60 + outSeconds;\n        const durationSeconds = outTotalSeconds - inTotalSeconds;\n        if (durationSeconds <= 0) return '00:00:00';\n        const hours = Math.floor(durationSeconds / 3600);\n        const minutes = Math.floor(durationSeconds % 3600 / 60);\n        const seconds = durationSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    const updateSegmentCount = (count)=>{\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            const existingSegment = segments.find((s)=>s.id === i);\n            newSegments.push(existingSegment || {\n                id: i,\n                segmentCode: \"SEG\".concat(i.toString().padStart(3, '0')),\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حفظ المادة الإعلامية بنجاح!', 'success');\n                // إعادة تعيين النموذج\n                setFormData({\n                    name: '',\n                    type: 'PROGRAM',\n                    description: '',\n                    channel: 'DOCUMENTARY',\n                    source: '',\n                    status: 'WAITING',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: 'SERVER'\n                });\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: 'SEG001',\n                        timeIn: '00:00:00',\n                        timeOut: '00:00:00',\n                        duration: '00:00:00'\n                    }\n                ]);\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            showToast('حدث خطأ أثناء حفظ البيانات', 'error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: '#1a1d29',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl',\n            color: 'white'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '800px',\n                    margin: '0 auto',\n                    background: '#2d3748',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.3)',\n                    border: '1px solid #4a5568'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    fontWeight: 'bold',\n                                    color: '#333',\n                                    margin: 0,\n                                    background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent'\n                                },\n                                children: \"➕ إضافة مادة إعلامية جديدة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push('/'),\n                                style: {\n                                    background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '10px',\n                                    padding: '10px 20px',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem'\n                                },\n                                children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #dee2e6'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#495057',\n                                            marginBottom: '20px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '200px 1fr',\n                                                    gap: '15px',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            color: '#495057',\n                                                            fontWeight: 'bold',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDCBE رقم الهارد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"رقم الهارد\",\n                                                        value: formData.hardDiskNumber,\n                                                        onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            maxWidth: '200px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة *\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: inputStyle,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '80px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"المصدر\",\n                                                value: formData.source,\n                                                onChange: (e)=>handleInputChange('source', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: formData.status,\n                                                onChange: (e)=>handleInputChange('status', e.target.value),\n                                                style: inputStyle,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"VALID\",\n                                                        children: \"صالح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_CENSORSHIP\",\n                                                        children: \"مرفوض رقابي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"REJECTED_TECHNICAL\",\n                                                        children: \"مرفوض هندسي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"WAITING\",\n                                                        children: \"في الانتظار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ البداية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ الانتهاء\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الجزء\",\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الموسم\",\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 362,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '60px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #90caf9'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#1565c0',\n                                            marginBottom: '10px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83C\\uDFAC السيجمانت\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#1565c0',\n                                            fontSize: '0.9rem',\n                                            marginBottom: '20px',\n                                            fontStyle: 'italic'\n                                        },\n                                        children: \"\\uD83D\\uDCA1 تنسيق الوقت: HH:MM:SS (مثال: 01:30:45 للساعة الواحدة و30 دقيقة و45 ثانية)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '10px',\n                                                    color: '#1565c0',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: \"عدد السيجمانت (1-10):\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '10px',\n                                                    flexWrap: 'wrap'\n                                                },\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3,\n                                                    4,\n                                                    5,\n                                                    6,\n                                                    7,\n                                                    8,\n                                                    9,\n                                                    10\n                                                ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>updateSegmentCount(num),\n                                                        style: {\n                                                            background: segmentCount === num ? 'linear-gradient(45deg, #1976d2, #42a5f5)' : '#f8f9fa',\n                                                            color: segmentCount === num ? 'white' : '#495057',\n                                                            border: segmentCount === num ? 'none' : '2px solid #dee2e6',\n                                                            borderRadius: '8px',\n                                                            padding: '8px 16px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '0.9rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: num\n                                                    }, num, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '20px'\n                                        },\n                                        children: segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: 'rgba(255,255,255,0.8)',\n                                                    borderRadius: '10px',\n                                                    padding: '20px',\n                                                    border: '2px solid #90caf9'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        style: {\n                                                            color: '#1565c0',\n                                                            marginBottom: '15px',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: [\n                                                            \"السيجمانت \",\n                                                            segment.id\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'grid',\n                                                            gap: '15px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'grid',\n                                                                gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                                gap: '15px'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"Time In (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"00:00:00\",\n                                                                            value: segment.timeIn,\n                                                                            onChange: (e)=>{\n                                                                                let newValue = e.target.value;\n                                                                                // السماح فقط بالأرقام والنقطتين\n                                                                                newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                                // تحديد الطول الأقصى\n                                                                                if (newValue.length <= 8) {\n                                                                                    handleSegmentChange(segment.id, 'timeIn', newValue);\n                                                                                    // حساب المدة إذا كان التنسيق صحيح\n                                                                                    if (validateTimeFormat(newValue)) {\n                                                                                        const duration = calculateDuration(newValue, segment.timeOut);\n                                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                                    }\n                                                                                }\n                                                                            },\n                                                                            onBlur: (e)=>{\n                                                                                // تنسيق القيمة عند فقدان التركيز\n                                                                                const formatted = formatTimeInput(e.target.value);\n                                                                                handleSegmentChange(segment.id, 'timeIn', formatted);\n                                                                                const duration = calculateDuration(formatted, segment.timeOut);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            },\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                borderColor: validateTimeFormat(segment.timeIn) ? '#28a745' : '#e0e0e0'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"Time Out (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 491,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"00:00:00\",\n                                                                            value: segment.timeOut,\n                                                                            onChange: (e)=>{\n                                                                                let newValue = e.target.value;\n                                                                                // السماح فقط بالأرقام والنقطتين\n                                                                                newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                                // تحديد الطول الأقصى\n                                                                                if (newValue.length <= 8) {\n                                                                                    handleSegmentChange(segment.id, 'timeOut', newValue);\n                                                                                    // حساب المدة إذا كان التنسيق صحيح\n                                                                                    if (validateTimeFormat(newValue)) {\n                                                                                        const duration = calculateDuration(segment.timeIn, newValue);\n                                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                                    }\n                                                                                }\n                                                                            },\n                                                                            onBlur: (e)=>{\n                                                                                // تنسيق القيمة عند فقدان التركيز\n                                                                                const formatted = formatTimeInput(e.target.value);\n                                                                                handleSegmentChange(segment.id, 'timeOut', formatted);\n                                                                                const duration = calculateDuration(segment.timeIn, formatted);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            },\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                borderColor: validateTimeFormat(segment.timeOut) ? '#28a745' : '#e0e0e0'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 494,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 490,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"المدة (HH:MM:SS)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            value: segment.duration,\n                                                                            readOnly: true,\n                                                                            style: {\n                                                                                ...inputStyle,\n                                                                                background: '#f8f9fa',\n                                                                                color: '#6c757d'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            style: {\n                                                                                display: 'block',\n                                                                                marginBottom: '5px',\n                                                                                color: '#1565c0',\n                                                                                fontSize: '0.9rem'\n                                                                            },\n                                                                            children: \"كود السيجمانت\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"SEG001\",\n                                                                            value: segment.segmentCode,\n                                                                            onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                            style: inputStyle\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 549,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, segment.id, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        style: {\n                                            background: isSubmitting ? 'linear-gradient(45deg, #6c757d, #adb5bd)' : 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)',\n                                            opacity: isSubmitting ? 0.7 : 1\n                                        },\n                                        children: isSubmitting ? '⏳ جاري الحفظ...' : '💾 حفظ البيانات'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>{\n                                            setFormData({\n                                                name: '',\n                                                type: 'PROGRAM',\n                                                description: '',\n                                                channel: 'DOCUMENTARY',\n                                                source: '',\n                                                status: 'WAITING',\n                                                startDate: new Date().toISOString().split('T')[0],\n                                                endDate: '',\n                                                notes: '',\n                                                episodeNumber: '',\n                                                seasonNumber: '',\n                                                partNumber: '',\n                                                hardDiskNumber: 'SERVER'\n                                            });\n                                            setSegmentCount(1);\n                                            setSegments([\n                                                {\n                                                    id: 1,\n                                                    segmentCode: 'SEG001',\n                                                    timeIn: '00:00:00',\n                                                    timeOut: '00:00:00',\n                                                    duration: '00:00:00'\n                                                }\n                                            ]);\n                                        },\n                                        style: {\n                                            background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(220,53,69,0.3)'\n                                        },\n                                        children: \"\\uD83D\\uDDD1️ مسح البيانات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 630,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"zuKwFwf3n4PMZi1YCRQjm69g+B8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});