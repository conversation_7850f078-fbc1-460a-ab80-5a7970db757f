'use client';
import { useState } from 'react';
import { useToast } from '@/components/Toast';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/DashboardLayout';

export default function AddMediaPage() {
  const router = useRouter();
  const { showToast, ToastContainer } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    type: 'PROGRAM',
    description: '',
    channel: 'DOCUMENTARY',
    source: '',
    status: 'WAITING',
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    notes: '',
    episodeNumber: '',
    seasonNumber: '',
    partNumber: '',
    hardDiskNumber: 'SERVER', // رقم الهارد الافتراضي
  });

  const [segmentCount, setSegmentCount] = useState(1);
  const [segments, setSegments] = useState([
    {
      id: 1,
      segmentCode: 'SEG001',
      timeIn: '00:00:00',
      timeOut: '00:00:00',
      duration: '00:00:00'
    }
  ]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSegmentChange = (segmentId: number, field: string, value: string) => {
    setSegments(prev => prev.map(segment =>
      segment.id === segmentId
        ? { ...segment, [field]: value }
        : segment
    ));
  };

  const validateTimeFormat = (time: string): boolean => {
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;
    return timeRegex.test(time);
  };

  const formatTimeInput = (value: string): string => {
    // إزالة أي أحرف غير رقمية أو نقطتين
    const cleaned = value.replace(/[^\d:]/g, '');

    // تقسيم النص إلى أجزاء
    const parts = cleaned.split(':');

    // تنسيق كل جزء
    const hours = parts[0] ? parts[0].padStart(2, '0').slice(0, 2) : '00';
    const minutes = parts[1] ? parts[1].padStart(2, '0').slice(0, 2) : '00';
    const seconds = parts[2] ? parts[2].padStart(2, '0').slice(0, 2) : '00';

    return `${hours}:${minutes}:${seconds}`;
  };

  const calculateDuration = (timeIn: string, timeOut: string): string => {
    if (!timeIn || !timeOut || !validateTimeFormat(timeIn) || !validateTimeFormat(timeOut)) {
      return '00:00:00';
    }

    const [inHours, inMinutes, inSeconds] = timeIn.split(':').map(Number);
    const [outHours, outMinutes, outSeconds] = timeOut.split(':').map(Number);

    const inTotalSeconds = inHours * 3600 + inMinutes * 60 + inSeconds;
    const outTotalSeconds = outHours * 3600 + outMinutes * 60 + outSeconds;

    const durationSeconds = outTotalSeconds - inTotalSeconds;

    if (durationSeconds <= 0) return '00:00:00';

    const hours = Math.floor(durationSeconds / 3600);
    const minutes = Math.floor((durationSeconds % 3600) / 60);
    const seconds = durationSeconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const updateSegmentCount = (count: number) => {
    setSegmentCount(count);
    const newSegments = [];
    for (let i = 1; i <= count; i++) {
      const existingSegment = segments.find(s => s.id === i);
      newSegments.push(existingSegment || {
        id: i,
        segmentCode: `SEG${i.toString().padStart(3, '0')}`,
        timeIn: '00:00:00',
        timeOut: '00:00:00',
        duration: '00:00:00'
      });
    }
    setSegments(newSegments);
  };

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/media', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          formData,
          segments
        }),
      });

      const result = await response.json();

      if (result.success) {
        showToast('تم حفظ المادة الإعلامية بنجاح!', 'success');

        // إعادة تعيين النموذج
        setFormData({
          name: '',
          type: 'PROGRAM',
          description: '',
          channel: 'DOCUMENTARY',
          source: '',
          status: 'WAITING',
          startDate: new Date().toISOString().split('T')[0],
          endDate: '',
          notes: '',
          episodeNumber: '',
          seasonNumber: '',
          partNumber: '',
          hardDiskNumber: 'SERVER',
        });
        setSegmentCount(1);
        setSegments([{
          id: 1,
          segmentCode: 'SEG001',
          timeIn: '00:00:00',
          timeOut: '00:00:00',
          duration: '00:00:00'
        }]);
      } else {
        showToast('خطأ: ' + result.error, 'error');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      showToast('حدث خطأ أثناء حفظ البيانات', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const inputStyle: React.CSSProperties = {
    width: '100%',
    padding: '12px',
    border: '2px solid #e0e0e0',
    borderRadius: '8px',
    fontSize: '1rem',
    fontFamily: 'Cairo, Arial, sans-serif',
    direction: 'rtl' as const,
    outline: 'none',
  };

  return (
    <DashboardLayout
      title="إضافة مادة إعلامية جديدة"
      subtitle="إدارة المحتوى الإعلامي"
      icon="➕"
      requiredPermissions={['MEDIA_CREATE']}
    >

        <form onSubmit={handleSubmit}>
          <div style={{
            background: 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #81d4fa'
          }}>
            <h2 style={{ color: '#0277bd', marginBottom: '20px', fontSize: '1.3rem' }}>📝 المعلومات الأساسية</h2>

            <div style={{ display: 'grid', gap: '15px' }}>
              {/* حقل رقم الهارد */}
              <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr', gap: '15px', alignItems: 'center' }}>
                <label style={{ color: '#0277bd', fontWeight: 'bold', fontSize: '0.9rem' }}>
                  💾 رقم الهارد:
                </label>
                <input
                  type="text"
                  placeholder="رقم الهارد"
                  value={formData.hardDiskNumber}
                  onChange={(e) => handleInputChange('hardDiskNumber', e.target.value)}
                  style={{
                    ...inputStyle,
                    maxWidth: '200px'
                  }}
                />
              </div>

              <input
                type="text"
                placeholder="اسم المادة *"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                style={{
                  ...inputStyle,
                  background: 'white',
                  color: '#333'
                }}
                required
              />

              <div style={{ display: 'grid', gridTemplateColumns: '200px 180px 1fr', gap: '15px', alignItems: 'end' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#0277bd', fontSize: '0.9rem' }}>
                    نوع المادة
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: 'white',
                      color: '#333'
                    }}
                  >
                    <option value="PROGRAM">برنامج</option>
                    <option value="SERIES">مسلسل</option>
                    <option value="MOVIE">فيلم</option>
                    <option value="SONG">أغنية</option>
                    <option value="STING">Sting</option>
                    <option value="FILL_IN">Fill IN</option>
                    <option value="FILLER">Filler</option>
                    <option value="PROMO">Promo</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#0277bd', fontSize: '0.9rem' }}>
                    القناة
                  </label>
                  <select
                    value={formData.channel}
                    onChange={(e) => handleInputChange('channel', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: 'white',
                      color: '#333'
                    }}
                  >
                    <option value="DOCUMENTARY">الوثائقية</option>
                    <option value="NEWS">الأخبار</option>
                    <option value="OTHER">أخرى</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#0277bd', fontSize: '0.9rem' }}>
                    الحالة
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: 'white',
                      color: '#333'
                    }}
                  >
                    <option value="approved">معتمد</option>
                    <option value="pending">قيد المراجعة</option>
                    <option value="rejected">مرفوض</option>
                    <option value="expired">منتهي الصلاحية</option>
                  </select>
                </div>
              </div>

              <textarea
                placeholder="وصف المادة"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                style={{
                  ...inputStyle,
                  background: 'white',
                  color: '#333',
                  minHeight: '80px',
                  resize: 'vertical'
                }}
              />

              <input
                type="text"
                placeholder="المصدر"
                value={formData.source}
                onChange={(e) => handleInputChange('source', e.target.value)}
                style={{
                  ...inputStyle,
                  background: 'white',
                  color: '#333'
                }}
              />

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#0277bd', fontSize: '0.9rem' }}>
                    تاريخ البداية
                  </label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: 'white',
                      color: '#333'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#0277bd', fontSize: '0.9rem' }}>
                    تاريخ الانتهاء
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: 'white',
                      color: '#333'
                    }}
                  />
                </div>
              </div>

              {/* الحقول الخاصة بنوع المادة */}
              {(formData.type === 'SERIES' || formData.type === 'PROGRAM') && (
                <div style={{ display: 'grid', gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr', gap: '15px' }}>
                  <input
                    type="number"
                    placeholder="رقم الحلقة"
                    value={formData.episodeNumber}
                    onChange={(e) => handleInputChange('episodeNumber', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: 'white',
                      color: '#333'
                    }}
                  />
                  {formData.type === 'SERIES' ? (
                    <input
                      type="number"
                      placeholder="رقم الجزء"
                      value={formData.partNumber}
                      onChange={(e) => handleInputChange('partNumber', e.target.value)}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  ) : (
                    <input
                      type="number"
                      placeholder="رقم الموسم"
                      value={formData.seasonNumber}
                      onChange={(e) => handleInputChange('seasonNumber', e.target.value)}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  )}
                </div>
              )}

              {formData.type === 'MOVIE' && (
                <input
                  type="number"
                  placeholder="رقم الجزء"
                  value={formData.partNumber}
                  onChange={(e) => handleInputChange('partNumber', e.target.value)}
                  style={{
                    ...inputStyle,
                    background: 'white',
                    color: '#333'
                  }}
                />
              )}

              <textarea
                placeholder="ملاحظات"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                style={{
                  ...inputStyle,
                  background: 'white',
                  color: '#333',
                  minHeight: '60px',
                  resize: 'vertical'
                }}
              />
            </div>
          </div>

          {/* قسم السيجمانت */}
          <div style={{
            background: '#374151',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #4b5563'
          }}>
            <h2 style={{ color: '#60a5fa', marginBottom: '10px', fontSize: '1.3rem' }}>🎬 السيجمانت</h2>
            <p style={{ color: '#93c5fd', fontSize: '0.9rem', marginBottom: '20px', fontStyle: 'italic' }}>
              💡 تنسيق الوقت: HH:MM:SS (مثال: 01:30:45 للساعة الواحدة و30 دقيقة و45 ثانية)
            </p>

            <div style={{ marginBottom: '20px' }}>
              <label style={{ display: 'block', marginBottom: '10px', color: '#1565c0', fontSize: '1rem' }}>
                عدد السيجمانت (1-10):
              </label>
              <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                  <button
                    key={num}
                    type="button"
                    onClick={() => updateSegmentCount(num)}
                    style={{
                      background: segmentCount === num ? 'linear-gradient(45deg, #1976d2, #42a5f5)' : '#f8f9fa',
                      color: segmentCount === num ? 'white' : '#495057',
                      border: segmentCount === num ? 'none' : '2px solid #dee2e6',
                      borderRadius: '8px',
                      padding: '8px 16px',
                      cursor: 'pointer',
                      fontSize: '0.9rem',
                      fontWeight: 'bold'
                    }}
                  >
                    {num}
                  </button>
                ))}
              </div>
            </div>

            {/* تفاصيل السيجمانت */}
            <div style={{ display: 'grid', gap: '20px' }}>
              {segments.map((segment) => (
                <div key={segment.id} style={{
                  background: 'rgba(255,255,255,0.8)',
                  borderRadius: '10px',
                  padding: '20px',
                  border: '2px solid #90caf9'
                }}>
                  <h3 style={{ color: '#1565c0', marginBottom: '15px', fontSize: '1.1rem' }}>
                    السيجمانت {segment.id}
                  </h3>

                  <div style={{ display: 'grid', gap: '15px' }}>
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr 1fr', gap: '15px' }}>
                      <div>
                        <label style={{ display: 'block', marginBottom: '5px', color: '#1565c0', fontSize: '0.9rem' }}>
                          Time In (HH:MM:SS)
                        </label>
                        <input
                          type="text"
                          placeholder="00:00:00"
                          value={segment.timeIn}
                          onChange={(e) => {
                            let newValue = e.target.value;

                            // السماح فقط بالأرقام والنقطتين
                            newValue = newValue.replace(/[^\d:]/g, '');

                            // تحديد الطول الأقصى
                            if (newValue.length <= 8) {
                              handleSegmentChange(segment.id, 'timeIn', newValue);

                              // حساب المدة إذا كان التنسيق صحيح
                              if (validateTimeFormat(newValue)) {
                                const duration = calculateDuration(newValue, segment.timeOut);
                                handleSegmentChange(segment.id, 'duration', duration);
                              }
                            }
                          }}
                          onBlur={(e) => {
                            // تنسيق القيمة عند فقدان التركيز
                            const formatted = formatTimeInput(e.target.value);
                            handleSegmentChange(segment.id, 'timeIn', formatted);
                            const duration = calculateDuration(formatted, segment.timeOut);
                            handleSegmentChange(segment.id, 'duration', duration);
                          }}
                          style={{
                            ...inputStyle,
                            borderColor: validateTimeFormat(segment.timeIn) ? '#28a745' : '#e0e0e0'
                          }}
                        />
                      </div>

                      <div>
                        <label style={{ display: 'block', marginBottom: '5px', color: '#1565c0', fontSize: '0.9rem' }}>
                          Time Out (HH:MM:SS)
                        </label>
                        <input
                          type="text"
                          placeholder="00:00:00"
                          value={segment.timeOut}
                          onChange={(e) => {
                            let newValue = e.target.value;

                            // السماح فقط بالأرقام والنقطتين
                            newValue = newValue.replace(/[^\d:]/g, '');

                            // تحديد الطول الأقصى
                            if (newValue.length <= 8) {
                              handleSegmentChange(segment.id, 'timeOut', newValue);

                              // حساب المدة إذا كان التنسيق صحيح
                              if (validateTimeFormat(newValue)) {
                                const duration = calculateDuration(segment.timeIn, newValue);
                                handleSegmentChange(segment.id, 'duration', duration);
                              }
                            }
                          }}
                          onBlur={(e) => {
                            // تنسيق القيمة عند فقدان التركيز
                            const formatted = formatTimeInput(e.target.value);
                            handleSegmentChange(segment.id, 'timeOut', formatted);
                            const duration = calculateDuration(segment.timeIn, formatted);
                            handleSegmentChange(segment.id, 'duration', duration);
                          }}
                          style={{
                            ...inputStyle,
                            borderColor: validateTimeFormat(segment.timeOut) ? '#28a745' : '#e0e0e0'
                          }}
                        />
                      </div>

                      <div>
                        <label style={{ display: 'block', marginBottom: '5px', color: '#1565c0', fontSize: '0.9rem' }}>
                          المدة (HH:MM:SS)
                        </label>
                        <input
                          type="text"
                          value={segment.duration}
                          readOnly
                          style={{
                            ...inputStyle,
                            background: '#f8f9fa',
                            color: '#6c757d'
                          }}
                        />
                      </div>

                      <div>
                        <label style={{ display: 'block', marginBottom: '5px', color: '#1565c0', fontSize: '0.9rem' }}>
                          كود السيجمانت
                        </label>
                        <input
                          type="text"
                          placeholder="SEG001"
                          value={segment.segmentCode}
                          onChange={(e) => handleSegmentChange(segment.id, 'segmentCode', e.target.value)}
                          style={inputStyle}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                background: isSubmitting
                  ? 'linear-gradient(45deg, #6c757d, #adb5bd)'
                  : 'linear-gradient(45deg, #28a745, #20c997)',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                padding: '15px 40px',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                boxShadow: '0 4px 15px rgba(40,167,69,0.3)',
                opacity: isSubmitting ? 0.7 : 1,
              }}
            >
              {isSubmitting ? '⏳ جاري الحفظ...' : '💾 حفظ البيانات'}
            </button>

            <button
              type="button"
              onClick={() => {
                setFormData({
                  name: '',
                  type: 'PROGRAM',
                  description: '',
                  channel: 'DOCUMENTARY',
                  source: '',
                  status: 'WAITING',
                  startDate: new Date().toISOString().split('T')[0],
                  endDate: '',
                  notes: '',
                  episodeNumber: '',
                  seasonNumber: '',
                  partNumber: '',
                  hardDiskNumber: 'SERVER',
                });
                setSegmentCount(1);
                setSegments([{
                  id: 1,
                  segmentCode: 'SEG001',
                  timeIn: '00:00:00',
                  timeOut: '00:00:00',
                  duration: '00:00:00'
                }]);
              }}
              style={{
                background: 'linear-gradient(45deg, #dc3545, #c82333)',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                padding: '15px 40px',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                boxShadow: '0 4px 15px rgba(220,53,69,0.3)',
              }}
            >
              🗑️ مسح البيانات
            </button>
          </div>
        </form>
      <ToastContainer />
    </DashboardLayout>
  );
}