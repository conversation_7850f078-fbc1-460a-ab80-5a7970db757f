/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/add-media/page";
exports.ids = ["app/add-media/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadd-media%2Fpage&page=%2Fadd-media%2Fpage&appPaths=%2Fadd-media%2Fpage&pagePath=private-next-app-dir%2Fadd-media%2Fpage.tsx&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadd-media%2Fpage&page=%2Fadd-media%2Fpage&appPaths=%2Fadd-media%2Fpage&pagePath=private-next-app-dir%2Fadd-media%2Fpage.tsx&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/add-media/page.tsx */ \"(rsc)/./src/app/add-media/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'add-media',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/add-media/page\",\n        pathname: \"/add-media\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadd-media%2Fpage&page=%2Fadd-media%2Fpage&appPaths=%2Fadd-media%2Fpage&pagePath=private-next-app-dir%2Fadd-media%2Fpage.tsx&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadd-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadd-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/add-media/page.tsx */ \"(rsc)/./src/app/add-media/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkZC1tZWRpYSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcYWRkLW1lZGlhXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadd-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Doc database\\media-dashboard-clean\\media-dashboard\\src\\app\\add-media\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2b7bd8a9d60a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcRG9jIGRhdGFiYXNlXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmI3YmQ4YTlkNjBhXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"نظام إدارة المحتوى الإعلامي\",\n    description: \"نظام متكامل لإدارة المحتوى الإعلامي والخريطة البرامجية\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap\",\n                    rel: \"stylesheet\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                style: {\n                    margin: 0,\n                    padding: 0,\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3VCO0FBRWhCLE1BQU1BLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsS0FBSTs7MEJBQ2xCLDhEQUFDQzswQkFDQyw0RUFBQ0M7b0JBQUtDLE1BQUs7b0JBQXVGQyxLQUFJOzs7Ozs7Ozs7OzswQkFFeEcsOERBQUNDO2dCQUFLQyxPQUFPO29CQUFFQyxRQUFRO29CQUFHQyxTQUFTO29CQUFHQyxZQUFZO2dCQUEyQjswQkFDMUVaOzs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCLZhti42KfZhSDYpdiv2KfYsdipINin2YTZhdit2KrZiNmJINin2YTYpdi52YTYp9mF2YpcIixcbiAgZGVzY3JpcHRpb246IFwi2YbYuNin2YUg2YXYqtmD2KfZhdmEINmE2KXYr9in2LHYqSDYp9mE2YXYrdiq2YjZiSDYp9mE2KXYudmE2KfZhdmKINmI2KfZhNiu2LHZiti32Kkg2KfZhNio2LHYp9mF2KzZitipXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGhlYWQ+XG4gICAgICAgIDxsaW5rIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PUNhaXJvOndnaHRAMzAwOzQwMDs1MDA7NjAwOzcwMCZkaXNwbGF5PXN3YXBcIiByZWw9XCJzdHlsZXNoZWV0XCIgLz5cbiAgICAgIDwvaGVhZD5cbiAgICAgIDxib2R5IHN0eWxlPXt7IG1hcmdpbjogMCwgcGFkZGluZzogMCwgZm9udEZhbWlseTogJ0NhaXJvLCBBcmlhbCwgc2Fucy1zZXJpZicgfX0+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJkaXIiLCJoZWFkIiwibGluayIsImhyZWYiLCJyZWwiLCJib2R5Iiwic3R5bGUiLCJtYXJnaW4iLCJwYWRkaW5nIiwiZm9udEZhbWlseSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDRG9jJTIwZGF0YWJhc2UlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTJKO0FBQzNKO0FBQ0EsME9BQThKO0FBQzlKO0FBQ0EsME9BQThKO0FBQzlKO0FBQ0Esb1JBQW9MO0FBQ3BMO0FBQ0Esd09BQTZKO0FBQzdKO0FBQ0EsNFBBQXdLO0FBQ3hLO0FBQ0Esa1FBQTJLO0FBQzNLO0FBQ0Esc1FBQTRLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxEb2MgZGF0YWJhc2VcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGxheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxccmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadd-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadd-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/add-media/page.tsx */ \"(ssr)/./src/app/add-media/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNEb2MlMjBkYXRhYmFzZSU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkZC1tZWRpYSU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvS0FBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcYWRkLW1lZGlhXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadd-media%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Toast */ \"(ssr)/./src/components/Toast.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AddMediaPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        description: '',\n        channel: '',\n        source: '',\n        status: '',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: ''\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const validateTimeFormat = (time)=>{\n        const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5]?[0-9]):([0-5]?[0-9])$/;\n        return timeRegex.test(time);\n    };\n    const formatTimeInput = (value)=>{\n        // إزالة أي أحرف غير رقمية أو نقطتين\n        const cleaned = value.replace(/[^\\d:]/g, '');\n        // تقسيم النص إلى أجزاء\n        const parts = cleaned.split(':');\n        // تنسيق كل جزء\n        const hours = parts[0] ? parts[0].padStart(2, '0').slice(0, 2) : '00';\n        const minutes = parts[1] ? parts[1].padStart(2, '0').slice(0, 2) : '00';\n        const seconds = parts[2] ? parts[2].padStart(2, '0').slice(0, 2) : '00';\n        return `${hours}:${minutes}:${seconds}`;\n    };\n    const calculateDuration = (timeIn, timeOut)=>{\n        if (!timeIn || !timeOut || !validateTimeFormat(timeIn) || !validateTimeFormat(timeOut)) {\n            return '00:00:00';\n        }\n        const [inHours, inMinutes, inSeconds] = timeIn.split(':').map(Number);\n        const [outHours, outMinutes, outSeconds] = timeOut.split(':').map(Number);\n        const inTotalSeconds = inHours * 3600 + inMinutes * 60 + inSeconds;\n        const outTotalSeconds = outHours * 3600 + outMinutes * 60 + outSeconds;\n        const durationSeconds = outTotalSeconds - inTotalSeconds;\n        if (durationSeconds <= 0) return '00:00:00';\n        const hours = Math.floor(durationSeconds / 3600);\n        const minutes = Math.floor(durationSeconds % 3600 / 60);\n        const seconds = durationSeconds % 60;\n        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n    };\n    const updateSegmentCount = (count)=>{\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            const existingSegment = segments.find((s)=>s.id === i);\n            newSegments.push(existingSegment || {\n                id: i,\n                segmentCode: `SEG${i.toString().padStart(3, '0')}`,\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // التحقق من الحقول المطلوبة\n        const requiredFields = [];\n        if (!formData.hardDiskNumber.trim()) requiredFields.push('رقم الهارد');\n        if (!formData.type) requiredFields.push('نوع المادة');\n        if (!formData.channel) requiredFields.push('القناة');\n        if (!formData.status) requiredFields.push('الحالة');\n        // التحقق من Time Out للسيجمانت\n        const invalidSegments = segments.filter((segment)=>!segment.timeOut || segment.timeOut === '00:00:00');\n        if (invalidSegments.length > 0) {\n            requiredFields.push('Time Out للسيجمانت');\n        }\n        if (requiredFields.length > 0) {\n            showToast(`يرجى ملء الحقول المطلوبة: ${requiredFields.join(', ')}`, 'error');\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حفظ المادة الإعلامية بنجاح!', 'success');\n                // إعادة تعيين النموذج\n                setFormData({\n                    name: '',\n                    type: 'PROGRAM',\n                    description: '',\n                    channel: 'DOCUMENTARY',\n                    source: '',\n                    status: 'WAITING',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: 'SERVER'\n                });\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: 'SEG001',\n                        timeIn: '00:00:00',\n                        timeOut: '00:00:00',\n                        duration: '00:00:00'\n                    }\n                ]);\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error submitting form:', error);\n            showToast('حدث خطأ أثناء حفظ البيانات', 'error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        title: \"إضافة مادة إعلامية جديدة\",\n        subtitle: \"إدارة المحتوى الإعلامي\",\n        icon: \"➕\",\n        requiredPermissions: [\n            'MEDIA_CREATE'\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 1fr',\n                                            gap: '15px',\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    fontWeight: 'bold',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCBE رقم الهارد: \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 34\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"رقم الهارد\",\n                                                value: formData.hardDiskNumber,\n                                                onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    maxWidth: '200px',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '500px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"اسم المادة \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    width: '100%'\n                                                },\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 180px 200px',\n                                            gap: '15px',\n                                            alignItems: 'end'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            \"نوع المادة \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 32\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر نوع المادة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            \"القناة \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 28\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر القناة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 302,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            \"الحالة \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 28\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر الحالة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"approved\",\n                                                                children: \"معتمد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"pending\",\n                                                                children: \"قيد المراجعة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"rejected\",\n                                                                children: \"مرفوض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"expired\",\n                                                                children: \"منتهي الصلاحية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"وصف المادة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '300px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"المصدر\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"المصدر\",\n                                                value: formData.source,\n                                                onChange: (e)=>handleInputChange('source', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 200px',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"تاريخ البداية\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.startDate,\n                                                        onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280',\n                                                            width: '100%'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"تاريخ الانتهاء\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 388,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.endDate,\n                                                        onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280',\n                                                            width: '100%'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '150px 150px',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"رقم الحلقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280',\n                                                            width: '100%'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: formData.type === 'SERIES' ? 'رقم الجزء' : 'رقم الموسم'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: formData.type === 'SERIES' ? 'رقم الجزء' : 'رقم الموسم',\n                                                        value: formData.type === 'SERIES' ? formData.partNumber : formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange(formData.type === 'SERIES' ? 'partNumber' : 'seasonNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280',\n                                                            width: '100%'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 17\n                                    }, this),\n                                    formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '150px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"رقم الجزء\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '500px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"ملاحظات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '60px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    marginBottom: '10px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: \"\\uD83C\\uDFAC السيجمانت\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    color: '#d1d5db',\n                                    fontSize: '0.9rem',\n                                    marginBottom: '20px',\n                                    fontStyle: 'italic'\n                                },\n                                children: \"\\uD83D\\uDCA1 تنسيق الوقت: HH:MM:SS (مثال: 01:30:45 للساعة الواحدة و30 دقيقة و45 ثانية)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '10px',\n                                            color: '#1565c0',\n                                            fontSize: '1rem'\n                                        },\n                                        children: \"عدد السيجمانت (1-10):\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '10px',\n                                            flexWrap: 'wrap'\n                                        },\n                                        children: [\n                                            1,\n                                            2,\n                                            3,\n                                            4,\n                                            5,\n                                            6,\n                                            7,\n                                            8,\n                                            9,\n                                            10\n                                        ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>updateSegmentCount(num),\n                                                style: {\n                                                    background: segmentCount === num ? 'linear-gradient(45deg, #1976d2, #42a5f5)' : '#f8f9fa',\n                                                    color: segmentCount === num ? 'white' : '#495057',\n                                                    border: segmentCount === num ? 'none' : '2px solid #dee2e6',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: num\n                                            }, num, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '20px'\n                                },\n                                children: segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: 'rgba(255,255,255,0.8)',\n                                            borderRadius: '10px',\n                                            padding: '20px',\n                                            border: '2px solid #90caf9'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: '#1565c0',\n                                                    marginBottom: '15px',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: [\n                                                    \"السيجمانت \",\n                                                    segment.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gap: '15px'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                        gap: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#1565c0',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"Time In (HH:MM:SS)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"00:00:00\",\n                                                                    value: segment.timeIn,\n                                                                    onChange: (e)=>{\n                                                                        let newValue = e.target.value;\n                                                                        // السماح فقط بالأرقام والنقطتين\n                                                                        newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                        // تحديد الطول الأقصى\n                                                                        if (newValue.length <= 8) {\n                                                                            handleSegmentChange(segment.id, 'timeIn', newValue);\n                                                                            // حساب المدة إذا كان التنسيق صحيح\n                                                                            if (validateTimeFormat(newValue)) {\n                                                                                const duration = calculateDuration(newValue, segment.timeOut);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            }\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        // تنسيق القيمة عند فقدان التركيز\n                                                                        const formatted = formatTimeInput(e.target.value);\n                                                                        handleSegmentChange(segment.id, 'timeIn', formatted);\n                                                                        const duration = calculateDuration(formatted, segment.timeOut);\n                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                    },\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        borderColor: validateTimeFormat(segment.timeIn) ? '#28a745' : '#e0e0e0'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#f3f4f6',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: [\n                                                                        \"Time Out (HH:MM:SS) \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: '#ef4444'\n                                                                            },\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 47\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"00:00:00\",\n                                                                    value: segment.timeOut,\n                                                                    onChange: (e)=>{\n                                                                        let newValue = e.target.value;\n                                                                        // السماح فقط بالأرقام والنقطتين\n                                                                        newValue = newValue.replace(/[^\\d:]/g, '');\n                                                                        // تحديد الطول الأقصى\n                                                                        if (newValue.length <= 8) {\n                                                                            handleSegmentChange(segment.id, 'timeOut', newValue);\n                                                                            // حساب المدة إذا كان التنسيق صحيح\n                                                                            if (validateTimeFormat(newValue)) {\n                                                                                const duration = calculateDuration(segment.timeIn, newValue);\n                                                                                handleSegmentChange(segment.id, 'duration', duration);\n                                                                            }\n                                                                        }\n                                                                    },\n                                                                    onBlur: (e)=>{\n                                                                        // تنسيق القيمة عند فقدان التركيز\n                                                                        const formatted = formatTimeInput(e.target.value);\n                                                                        handleSegmentChange(segment.id, 'timeOut', formatted);\n                                                                        const duration = calculateDuration(segment.timeIn, formatted);\n                                                                        handleSegmentChange(segment.id, 'duration', duration);\n                                                                    },\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: '#1f2937',\n                                                                        color: 'white',\n                                                                        border: '1px solid #6b7280',\n                                                                        borderColor: validateTimeFormat(segment.timeOut) ? '#28a745' : '#6b7280'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#f3f4f6',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"المدة (HH:MM:SS)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: segment.duration,\n                                                                    readOnly: true,\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: '#374151',\n                                                                        color: '#d1d5db',\n                                                                        border: '1px solid #6b7280'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 627,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    style: {\n                                                                        display: 'block',\n                                                                        marginBottom: '5px',\n                                                                        color: '#f3f4f6',\n                                                                        fontSize: '0.9rem'\n                                                                    },\n                                                                    children: \"كود السيجمانت\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 645,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    placeholder: \"SEG001\",\n                                                                    value: segment.segmentCode,\n                                                                    onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: '#1f2937',\n                                                                        color: 'white',\n                                                                        border: '1px solid #6b7280'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, segment.id, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '15px',\n                            justifyContent: 'center'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                style: {\n                                    background: isSubmitting ? 'linear-gradient(45deg, #6c757d, #adb5bd)' : 'linear-gradient(45deg, #28a745, #20c997)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '25px',\n                                    padding: '15px 40px',\n                                    fontSize: '1.1rem',\n                                    fontWeight: 'bold',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    boxShadow: '0 4px 15px rgba(40,167,69,0.3)',\n                                    opacity: isSubmitting ? 0.7 : 1\n                                },\n                                children: isSubmitting ? '⏳ جاري الحفظ...' : '💾 حفظ البيانات'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    setFormData({\n                                        name: '',\n                                        type: '',\n                                        description: '',\n                                        channel: '',\n                                        source: '',\n                                        status: '',\n                                        startDate: new Date().toISOString().split('T')[0],\n                                        endDate: '',\n                                        notes: '',\n                                        episodeNumber: '',\n                                        seasonNumber: '',\n                                        partNumber: '',\n                                        hardDiskNumber: ''\n                                    });\n                                    setSegmentCount(1);\n                                    setSegments([\n                                        {\n                                            id: 1,\n                                            segmentCode: 'SEG001',\n                                            timeIn: '00:00:00',\n                                            timeOut: '',\n                                            duration: '00:00:00'\n                                        }\n                                    ]);\n                                },\n                                style: {\n                                    background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '25px',\n                                    padding: '15px 40px',\n                                    fontSize: '1.1rem',\n                                    fontWeight: 'bold',\n                                    cursor: 'pointer',\n                                    boxShadow: '0 4px 15px rgba(220,53,69,0.3)'\n                                },\n                                children: \"\\uD83D\\uDDD1️ مسح البيانات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 733,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/add-media/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \n\n\nfunction AuthGuard({ children, requiredPermissions = [], requiredRole, fallbackComponent }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        // المدير له صلاحيات كاملة\n        if (user.role === 'ADMIN') {\n            return true;\n        }\n        // التحقق من الدور المطلوب\n        if (role && user.role !== role) {\n            return false;\n        }\n        // التحقق من الصلاحيات المطلوبة\n        if (permissions.length > 0) {\n            const userPermissions = getUserPermissions(user.role);\n            return permissions.every((permission)=>userPermissions.includes(permission) || userPermissions.includes('ALL'));\n        }\n        return true;\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user?.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user?.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        if (user.role === 'ADMIN') return true;\n        const userPermissions = getUserPermissions(user.role);\n        return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: user?.role === 'ADMIN',\n        isMediaManager: user?.role === 'MEDIA_MANAGER',\n        isScheduler: user?.role === 'SCHEDULER',\n        isViewer: user?.role === 'VIEWER'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children, title = 'لوحة التحكم', subtitle = 'إدارة النظام', icon = '📊', requiredPermissions, requiredRole, fullWidth = false }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const timer = setInterval({\n                \"DashboardLayout.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"DashboardLayout.useEffect.timer\"], 1000);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>clearInterval(timer)\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: false,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'جدول الإذاعة اليومي',\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: requiredPermissions,\n        requiredRole: requiredRole,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        fontSize: '1.2rem',\n                                        fontWeight: '900',\n                                        fontFamily: 'Arial, sans-serif',\n                                        gap: '5px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '900',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"X\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '1rem',\n                                                fontWeight: '300'\n                                            },\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                WebkitBackgroundClip: 'text',\n                                                WebkitTextFillColor: 'transparent',\n                                                fontWeight: '800',\n                                                letterSpacing: '1px'\n                                            },\n                                            children: \"Prime\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"⚙️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        marginRight: sidebarOpen ? '280px' : '0',\n                        transition: 'margin-right 0.3s ease'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: \"متصل\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: currentTime.toLocaleTimeString('ar-EG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '20px',\n                                padding: '25px',\n                                border: '1px solid #4a5568',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                                maxWidth: fullWidth ? 'none' : '1000px',\n                                margin: '0 auto',\n                                width: fullWidth ? '100%' : 'auto'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Sidebar({ isOpen, onToggle }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const menuItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'جدول الإذاعة اليومي',\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'إدارة المستخدمين',\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null,\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            path: '/statistics',\n            permission: null,\n            adminOnly: true\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 74,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    right: isOpen ? 0 : '-280px',\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    borderLeft: '1px solid #2d3748',\n                    transition: 'right 0.3s ease',\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            fontSize: '1.5rem',\n                                            fontWeight: '900',\n                                            fontFamily: 'Arial, sans-serif',\n                                            gap: '8px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '900',\n                                                    fontSize: '1.8rem'\n                                                },\n                                                children: \"X\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#6c757d',\n                                                    fontSize: '1.2rem',\n                                                    fontWeight: '300'\n                                                },\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                    WebkitBackgroundClip: 'text',\n                                                    WebkitTextFillColor: 'transparent',\n                                                    fontWeight: '800',\n                                                    letterSpacing: '1px'\n                                                },\n                                                children: \"Prime\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '1.1rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: \"نظام الإدارة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: \"المحتوى الإعلامي\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                style: {\n                                    background: 'transparent',\n                                    border: 'none',\n                                    color: '#a0aec0',\n                                    fontSize: '1.2rem',\n                                    cursor: 'pointer',\n                                    padding: '5px'\n                                },\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    marginBottom: '10px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '35px',\n                                            height: '35px',\n                                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                            borderRadius: '50%',\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            color: 'white',\n                                            fontSize: '1rem',\n                                            fontWeight: 'bold'\n                                        },\n                                        children: user?.name?.charAt(0) || 'U'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: 'white',\n                                                    margin: 0,\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: user?.name || 'مستخدم'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    margin: 0,\n                                                    fontSize: '0.8rem'\n                                                },\n                                                children: [\n                                                    user?.role === 'ADMIN' && '👑 مدير النظام',\n                                                    user?.role === 'MEDIA_MANAGER' && '📝 مدير المحتوى',\n                                                    user?.role === 'SCHEDULER' && '📅 مجدول البرامج',\n                                                    user?.role === 'VIEWER' && '👁️ مستخدم عرض'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '5px',\n                                    color: '#68d391',\n                                    fontSize: '0.8rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: '6px',\n                                            height: '6px',\n                                            background: '#68d391',\n                                            borderRadius: '50%'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"متصل\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#4299e1' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    padding: '12px 20px',\n                                    textAlign: 'right',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    fontSize: '0.9rem',\n                                    transition: 'all 0.2s',\n                                    borderRight: isActive ? '3px solid #4299e1' : '3px solid transparent'\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = '#2d3748';\n                                        e.target.style.color = 'white';\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: '1.1rem'\n                                        },\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    // إضافة وظيفة تسجيل الخروج هنا\n                                    localStorage.removeItem('user');\n                                    localStorage.removeItem('token');\n                                    router.push('/login');\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    padding: '10px',\n                                    cursor: 'pointer',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    justifyContent: 'center',\n                                    gap: '8px',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    marginBottom: '15px'\n                                },\n                                children: \"\\uD83D\\uDEAA تسجيل الخروج\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    color: '#6c757d',\n                                    fontSize: '0.75rem',\n                                    fontFamily: 'Arial, sans-serif',\n                                    direction: 'ltr'\n                                },\n                                children: \"Powered By Mahmoud Ismail\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TaWRlYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFeUQ7QUFDbkI7QUFPdkIsU0FBU0csUUFBUSxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBZ0I7SUFDaEUsTUFBTUMsU0FBU04sMERBQVNBO0lBQ3hCLE1BQU1PLFdBQVdOLDREQUFXQTtJQUM1QixNQUFNLEVBQUVPLElBQUksRUFBRUMsYUFBYSxFQUFFLEdBQUdQLG1EQUFPQTtJQUV2QyxNQUFNUSxZQUFZO1FBQ2hCO1lBQ0VDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7WUFDWkMsV0FBVztRQUNiO1FBQ0E7WUFDRUosTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsWUFBWTtZQUNaQyxXQUFXO1FBQ2I7S0FFRDtJQUVELE1BQU1DLG9CQUFvQk4sVUFBVU8sTUFBTSxDQUFDQyxDQUFBQTtRQUN6QyxJQUFJQSxLQUFLSCxTQUFTLElBQUlQLE1BQU1XLFNBQVMsU0FBUyxPQUFPO1FBQ3JELElBQUlELEtBQUtKLFVBQVUsSUFBSSxDQUFDTCxjQUFjUyxLQUFLSixVQUFVLEdBQUcsT0FBTztRQUMvRCxPQUFPO0lBQ1Q7SUFFQSxxQkFDRTs7WUFFR1Ysd0JBQ0MsOERBQUNnQjtnQkFDQ0MsT0FBTztvQkFDTEMsVUFBVTtvQkFDVkMsS0FBSztvQkFDTEMsTUFBTTtvQkFDTkMsT0FBTztvQkFDUEMsUUFBUTtvQkFDUkMsWUFBWTtvQkFDWkMsUUFBUTtvQkFDUkMsU0FBU0MsT0FBT0MsVUFBVSxJQUFJLE1BQU0sVUFBVTtnQkFDaEQ7Z0JBQ0FDLFNBQVMzQjs7Ozs7OzBCQUtiLDhEQUFDZTtnQkFDQ0MsT0FBTztvQkFDTEMsVUFBVTtvQkFDVkMsS0FBSztvQkFDTEUsT0FBT3JCLFNBQVMsSUFBSTtvQkFDcEI2QixPQUFPO29CQUNQQyxRQUFRO29CQUNSUCxZQUFZO29CQUNaUSxZQUFZO29CQUNaQyxZQUFZO29CQUNaUixRQUFRO29CQUNSQyxTQUFTO29CQUNUUSxlQUFlO29CQUNmQyxZQUFZO2dCQUNkOztrQ0FHQSw4REFBQ2xCO3dCQUFJQyxPQUFPOzRCQUNWa0IsU0FBUzs0QkFDVEMsY0FBYzs0QkFDZFgsU0FBUzs0QkFDVFksWUFBWTs0QkFDWkMsZ0JBQWdCO3dCQUNsQjs7MENBQ0UsOERBQUN0QjtnQ0FBSUMsT0FBTztvQ0FBRVEsU0FBUztvQ0FBUVksWUFBWTtvQ0FBVUUsS0FBSztnQ0FBTzs7a0RBQy9ELDhEQUFDdkI7d0NBQUlDLE9BQU87NENBQ1ZRLFNBQVM7NENBQ1RZLFlBQVk7NENBQ1pHLFVBQVU7NENBQ1ZDLFlBQVk7NENBQ1pQLFlBQVk7NENBQ1pLLEtBQUs7d0NBQ1A7OzBEQUNFLDhEQUFDRztnREFBS3pCLE9BQU87b0RBQ1hNLFlBQVk7b0RBQ1pvQixzQkFBc0I7b0RBQ3RCQyxxQkFBcUI7b0RBQ3JCSCxZQUFZO29EQUNaRCxVQUFVO2dEQUNaOzBEQUFHOzs7Ozs7MERBR0gsOERBQUNFO2dEQUFLekIsT0FBTztvREFDWDRCLE9BQU87b0RBQ1BMLFVBQVU7b0RBQ1ZDLFlBQVk7Z0RBQ2Q7MERBQUc7Ozs7OzswREFHSCw4REFBQ0M7Z0RBQUt6QixPQUFPO29EQUNYTSxZQUFZO29EQUNab0Isc0JBQXNCO29EQUN0QkMscUJBQXFCO29EQUNyQkgsWUFBWTtvREFDWkssZUFBZTtnREFDakI7MERBQUc7Ozs7Ozs7Ozs7OztrREFJTCw4REFBQzlCOzswREFDQyw4REFBQytCO2dEQUFHOUIsT0FBTztvREFDVDRCLE9BQU87b0RBQ1BHLFFBQVE7b0RBQ1JSLFVBQVU7b0RBQ1ZDLFlBQVk7Z0RBQ2Q7MERBQUc7Ozs7OzswREFHSCw4REFBQ1E7Z0RBQUVoQyxPQUFPO29EQUNSNEIsT0FBTztvREFDUEcsUUFBUTtvREFDUlIsVUFBVTtnREFDWjswREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtQLDhEQUFDVTtnQ0FDQ3RCLFNBQVMzQjtnQ0FDVGdCLE9BQU87b0NBQ0xNLFlBQVk7b0NBQ1o0QixRQUFRO29DQUNSTixPQUFPO29DQUNQTCxVQUFVO29DQUNWWSxRQUFRO29DQUNSakIsU0FBUztnQ0FDWDswQ0FDRDs7Ozs7Ozs7Ozs7O2tDQU1ILDhEQUFDbkI7d0JBQUlDLE9BQU87NEJBQ1ZrQixTQUFTOzRCQUNUQyxjQUFjO3dCQUNoQjs7MENBQ0UsOERBQUNwQjtnQ0FBSUMsT0FBTztvQ0FDVlEsU0FBUztvQ0FDVFksWUFBWTtvQ0FDWkUsS0FBSztvQ0FDTGMsY0FBYztnQ0FDaEI7O2tEQUNFLDhEQUFDckM7d0NBQUlDLE9BQU87NENBQ1ZZLE9BQU87NENBQ1BDLFFBQVE7NENBQ1JQLFlBQVk7NENBQ1orQixjQUFjOzRDQUNkN0IsU0FBUzs0Q0FDVFksWUFBWTs0Q0FDWkMsZ0JBQWdCOzRDQUNoQk8sT0FBTzs0Q0FDUEwsVUFBVTs0Q0FDVkMsWUFBWTt3Q0FDZDtrREFDR3JDLE1BQU1HLE1BQU1nRCxPQUFPLE1BQU07Ozs7OztrREFFNUIsOERBQUN2Qzs7MERBQ0MsOERBQUNpQztnREFBRWhDLE9BQU87b0RBQ1I0QixPQUFPO29EQUNQRyxRQUFRO29EQUNSUixVQUFVO29EQUNWQyxZQUFZO2dEQUNkOzBEQUNHckMsTUFBTUcsUUFBUTs7Ozs7OzBEQUVqQiw4REFBQzBDO2dEQUFFaEMsT0FBTztvREFDUjRCLE9BQU87b0RBQ1BHLFFBQVE7b0RBQ1JSLFVBQVU7Z0RBQ1o7O29EQUNHcEMsTUFBTVcsU0FBUyxXQUFXO29EQUMxQlgsTUFBTVcsU0FBUyxtQkFBbUI7b0RBQ2xDWCxNQUFNVyxTQUFTLGVBQWU7b0RBQzlCWCxNQUFNVyxTQUFTLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSWxDLDhEQUFDQztnQ0FBSUMsT0FBTztvQ0FDVlEsU0FBUztvQ0FDVFksWUFBWTtvQ0FDWkUsS0FBSztvQ0FDTE0sT0FBTztvQ0FDUEwsVUFBVTtnQ0FDWjs7a0RBQ0UsOERBQUN4Qjt3Q0FBSUMsT0FBTzs0Q0FDVlksT0FBTzs0Q0FDUEMsUUFBUTs0Q0FDUlAsWUFBWTs0Q0FDWitCLGNBQWM7d0NBQ2hCOzs7Ozs7b0NBQVM7Ozs7Ozs7Ozs7Ozs7a0NBTWIsOERBQUN0Qzt3QkFBSUMsT0FBTzs0QkFDVnVDLE1BQU07NEJBQ05yQixTQUFTOzRCQUNUc0IsV0FBVzt3QkFDYjtrQ0FDRzdDLGtCQUFrQjhDLEdBQUcsQ0FBQyxDQUFDNUMsTUFBTTZDOzRCQUM1QixNQUFNQyxXQUFXekQsYUFBYVcsS0FBS0wsSUFBSTs0QkFDdkMscUJBQ0UsOERBQUN5QztnQ0FFQ3RCLFNBQVM7b0NBQ1AxQixPQUFPMkQsSUFBSSxDQUFDL0MsS0FBS0wsSUFBSTtvQ0FDckIsSUFBSWlCLE9BQU9DLFVBQVUsSUFBSSxLQUFLO3dDQUM1QjFCO29DQUNGO2dDQUNGO2dDQUNBZ0IsT0FBTztvQ0FDTFksT0FBTztvQ0FDUE4sWUFBWXFDLFdBQVcsWUFBWTtvQ0FDbkNmLE9BQU9lLFdBQVcsVUFBVTtvQ0FDNUJULFFBQVE7b0NBQ1JoQixTQUFTO29DQUNUMkIsV0FBVztvQ0FDWFYsUUFBUTtvQ0FDUjNCLFNBQVM7b0NBQ1RZLFlBQVk7b0NBQ1pFLEtBQUs7b0NBQ0xDLFVBQVU7b0NBQ1ZSLFlBQVk7b0NBQ1orQixhQUFhSCxXQUFXLHNCQUFzQjtnQ0FDaEQ7Z0NBQ0FJLGNBQWMsQ0FBQ0M7b0NBQ2IsSUFBSSxDQUFDTCxVQUFVO3dDQUNiSyxFQUFFQyxNQUFNLENBQUNqRCxLQUFLLENBQUNNLFVBQVUsR0FBRzt3Q0FDNUIwQyxFQUFFQyxNQUFNLENBQUNqRCxLQUFLLENBQUM0QixLQUFLLEdBQUc7b0NBQ3pCO2dDQUNGO2dDQUNBc0IsY0FBYyxDQUFDRjtvQ0FDYixJQUFJLENBQUNMLFVBQVU7d0NBQ2JLLEVBQUVDLE1BQU0sQ0FBQ2pELEtBQUssQ0FBQ00sVUFBVSxHQUFHO3dDQUM1QjBDLEVBQUVDLE1BQU0sQ0FBQ2pELEtBQUssQ0FBQzRCLEtBQUssR0FBRztvQ0FDekI7Z0NBQ0Y7O2tEQUVBLDhEQUFDSDt3Q0FBS3pCLE9BQU87NENBQUV1QixVQUFVO3dDQUFTO2tEQUFJMUIsS0FBS04sSUFBSTs7Ozs7O29DQUM5Q00sS0FBS1AsSUFBSTs7K0JBcENMb0Q7Ozs7O3dCQXVDWDs7Ozs7O2tDQUlGLDhEQUFDM0M7d0JBQUlDLE9BQU87NEJBQ1ZrQixTQUFTOzRCQUNUaUMsV0FBVzt3QkFDYjs7MENBQ0UsOERBQUNsQjtnQ0FDQ3RCLFNBQVM7b0NBQ1AsK0JBQStCO29DQUMvQnlDLGFBQWFDLFVBQVUsQ0FBQztvQ0FDeEJELGFBQWFDLFVBQVUsQ0FBQztvQ0FDeEJwRSxPQUFPMkQsSUFBSSxDQUFDO2dDQUNkO2dDQUNBNUMsT0FBTztvQ0FDTFksT0FBTztvQ0FDUE4sWUFBWTtvQ0FDWnNCLE9BQU87b0NBQ1BNLFFBQVE7b0NBQ1JHLGNBQWM7b0NBQ2RuQixTQUFTO29DQUNUaUIsUUFBUTtvQ0FDUjNCLFNBQVM7b0NBQ1RZLFlBQVk7b0NBQ1pDLGdCQUFnQjtvQ0FDaEJDLEtBQUs7b0NBQ0xDLFVBQVU7b0NBQ1ZDLFlBQVk7b0NBQ1pZLGNBQWM7Z0NBQ2hCOzBDQUNEOzs7Ozs7MENBS0QsOERBQUNyQztnQ0FBSUMsT0FBTztvQ0FDVjZDLFdBQVc7b0NBQ1hqQixPQUFPO29DQUNQTCxVQUFVO29DQUNWTixZQUFZO29DQUNacUMsV0FBVztnQ0FDYjswQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPYiIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcY29tcG9uZW50c1xcU2lkZWJhci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuL0F1dGhHdWFyZCc7XG5cbmludGVyZmFjZSBTaWRlYmFyUHJvcHMge1xuICBpc09wZW46IGJvb2xlYW47XG4gIG9uVG9nZ2xlOiAoKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaWRlYmFyKHsgaXNPcGVuLCBvblRvZ2dsZSB9OiBTaWRlYmFyUHJvcHMpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcbiAgY29uc3QgeyB1c2VyLCBoYXNQZXJtaXNzaW9uIH0gPSB1c2VBdXRoKCk7XG5cbiAgY29uc3QgbWVudUl0ZW1zID0gW1xuICAgIHtcbiAgICAgIG5hbWU6ICfZhNmI2K3YqSDYp9mE2KrYrdmD2YUnLFxuICAgICAgaWNvbjogJ/Cfk4onLFxuICAgICAgcGF0aDogJy9kYXNoYm9hcmQnLFxuICAgICAgcGVybWlzc2lvbjogbnVsbFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ9in2YTZhdmI2KfYryDYp9mE2KXYudmE2KfZhdmK2KknLFxuICAgICAgaWNvbjogJ/CfjqwnLFxuICAgICAgcGF0aDogJy9tZWRpYS1saXN0JyxcbiAgICAgIHBlcm1pc3Npb246ICdNRURJQV9SRUFEJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ9il2LbYp9mB2Kkg2YXYp9iv2KknLFxuICAgICAgaWNvbjogJ+KelScsXG4gICAgICBwYXRoOiAnL2FkZC1tZWRpYScsXG4gICAgICBwZXJtaXNzaW9uOiAnTUVESUFfQ1JFQVRFJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ9in2YTYrtix2YrYt9ipINin2YTYqNix2KfZhdis2YrYqScsXG4gICAgICBpY29uOiAn8J+ThScsXG4gICAgICBwYXRoOiAnL3dlZWtseS1zY2hlZHVsZScsXG4gICAgICBwZXJtaXNzaW9uOiAnU0NIRURVTEVfUkVBRCdcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYrNiv2YjZhCDYp9mE2KXYsNin2LnYqSDYp9mE2YrZiNmF2YonLFxuICAgICAgaWNvbjogJ/Cfk4onLFxuICAgICAgcGF0aDogJy9kYWlseS1zY2hlZHVsZScsXG4gICAgICBwZXJtaXNzaW9uOiAnU0NIRURVTEVfUkVBRCdcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYpdiv2KfYsdipINin2YTZhdiz2KrYrtiv2YXZitmGJyxcbiAgICAgIGljb246ICfwn5GlJyxcbiAgICAgIHBhdGg6ICcvYWRtaW4tZGFzaGJvYXJkJyxcbiAgICAgIHBlcm1pc3Npb246IG51bGwsXG4gICAgICBhZG1pbk9ubHk6IHRydWVcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYp9mE2KXYrdi12KfYptmK2KfYqicsXG4gICAgICBpY29uOiAn8J+TiCcsXG4gICAgICBwYXRoOiAnL3N0YXRpc3RpY3MnLFxuICAgICAgcGVybWlzc2lvbjogbnVsbCxcbiAgICAgIGFkbWluT25seTogdHJ1ZVxuICAgIH0sXG5cbiAgXTtcblxuICBjb25zdCBmaWx0ZXJlZE1lbnVJdGVtcyA9IG1lbnVJdGVtcy5maWx0ZXIoaXRlbSA9PiB7XG4gICAgaWYgKGl0ZW0uYWRtaW5Pbmx5ICYmIHVzZXI/LnJvbGUgIT09ICdBRE1JTicpIHJldHVybiBmYWxzZTtcbiAgICBpZiAoaXRlbS5wZXJtaXNzaW9uICYmICFoYXNQZXJtaXNzaW9uKGl0ZW0ucGVybWlzc2lvbikpIHJldHVybiBmYWxzZTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSk7XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgey8qINiu2YTZgdmK2Kkg2LTZgdin2YHYqSDZhNmE2YXZiNio2KfZitmEICovfVxuICAgICAge2lzT3BlbiAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgICBib3R0b206IDAsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjUpJyxcbiAgICAgICAgICAgIHpJbmRleDogOTk4LFxuICAgICAgICAgICAgZGlzcGxheTogd2luZG93LmlubmVyV2lkdGggPD0gNzY4ID8gJ2Jsb2NrJyA6ICdub25lJ1xuICAgICAgICAgIH19XG4gICAgICAgICAgb25DbGljaz17b25Ub2dnbGV9XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICB7Lyog2KfZhNi02LHZiti3INin2YTYrNin2YbYqNmKICovfVxuICAgICAgPGRpdlxuICAgICAgICBzdHlsZT17e1xuICAgICAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLFxuICAgICAgICAgIHRvcDogMCxcbiAgICAgICAgICByaWdodDogaXNPcGVuID8gMCA6ICctMjgwcHgnLFxuICAgICAgICAgIHdpZHRoOiAnMjgwcHgnLFxuICAgICAgICAgIGhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzFhMWQyOScsXG4gICAgICAgICAgYm9yZGVyTGVmdDogJzFweCBzb2xpZCAjMmQzNzQ4JyxcbiAgICAgICAgICB0cmFuc2l0aW9uOiAncmlnaHQgMC4zcyBlYXNlJyxcbiAgICAgICAgICB6SW5kZXg6IDk5OSxcbiAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsXG4gICAgICAgICAgZm9udEZhbWlseTogJ0NhaXJvLCBBcmlhbCwgc2Fucy1zZXJpZidcbiAgICAgICAgfX1cbiAgICAgID5cbiAgICAgICAgey8qINix2KPYsyDYp9mE2LTYsdmK2Lcg2KfZhNis2KfZhtio2YogKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICAgICAgYm9yZGVyQm90dG9tOiAnMXB4IHNvbGlkICMyZDM3NDgnLFxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTJweCcgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGZvbnRTaXplOiAnMS41cmVtJyxcbiAgICAgICAgICAgICAgZm9udFdlaWdodDogJzkwMCcsXG4gICAgICAgICAgICAgIGZvbnRGYW1pbHk6ICdBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgICAgICAgICAgIGdhcDogJzhweCdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmZkNzAwIDAlLCAjZmZlZDRlIDUwJSwgI2ZmZDcwMCAxMDAlKScsXG4gICAgICAgICAgICAgICAgV2Via2l0QmFja2dyb3VuZENsaXA6ICd0ZXh0JyxcbiAgICAgICAgICAgICAgICBXZWJraXRUZXh0RmlsbENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc5MDAnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS44cmVtJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICBYXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBjb2xvcjogJyM2Yzc1N2QnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4ycmVtJyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnMzAwJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAtXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKScsXG4gICAgICAgICAgICAgICAgV2Via2l0QmFja2dyb3VuZENsaXA6ICd0ZXh0JyxcbiAgICAgICAgICAgICAgICBXZWJraXRUZXh0RmlsbENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc4MDAnLFxuICAgICAgICAgICAgICAgIGxldHRlclNwYWNpbmc6ICcxcHgnXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIFByaW1lXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgzIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgbWFyZ2luOiAwLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4xcmVtJyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAg2YbYuNin2YUg2KfZhNil2K/Yp9ix2KlcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBjb2xvcjogJyNhMGFlYzAnLFxuICAgICAgICAgICAgICAgIG1hcmdpbjogMCxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuOHJlbSdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAg2KfZhNmF2K3YqtmI2Ykg2KfZhNil2LnZhNin2YXZilxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtvblRvZ2dsZX1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICBjb2xvcjogJyNhMGFlYzAnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzEuMnJlbScsXG4gICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnNXB4J1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICDinJVcbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qINmF2LnZhNmI2YXYp9iqINin2YTZhdiz2KrYrtiv2YUgKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICAgICAgYm9yZGVyQm90dG9tOiAnMXB4IHNvbGlkICMyZDM3NDgnXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgZ2FwOiAnMTJweCcsXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcxMHB4J1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICB3aWR0aDogJzM1cHgnLFxuICAgICAgICAgICAgICBoZWlnaHQ6ICczNXB4JyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzY2N2VlYSwgIzc2NGJhMiknLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICB7dXNlcj8ubmFtZT8uY2hhckF0KDApIHx8ICdVJ31cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPHAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICBtYXJnaW46IDAsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7dXNlcj8ubmFtZSB8fCAn2YXYs9iq2K7Yr9mFJ31cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8cCBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGNvbG9yOiAnI2EwYWVjMCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luOiAwLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44cmVtJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7dXNlcj8ucm9sZSA9PT0gJ0FETUlOJyAmJiAn8J+RkSDZhdiv2YrYsSDYp9mE2YbYuNin2YUnfVxuICAgICAgICAgICAgICAgIHt1c2VyPy5yb2xlID09PSAnTUVESUFfTUFOQUdFUicgJiYgJ/Cfk50g2YXYr9mK2LEg2KfZhNmF2K3YqtmI2YknfVxuICAgICAgICAgICAgICAgIHt1c2VyPy5yb2xlID09PSAnU0NIRURVTEVSJyAmJiAn8J+ThSDZhdis2K/ZiNmEINin2YTYqNix2KfZhdisJ31cbiAgICAgICAgICAgICAgICB7dXNlcj8ucm9sZSA9PT0gJ1ZJRVdFUicgJiYgJ/CfkYHvuI8g2YXYs9iq2K7Yr9mFINi52LHYtid9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgZ2FwOiAnNXB4JyxcbiAgICAgICAgICAgIGNvbG9yOiAnIzY4ZDM5MScsXG4gICAgICAgICAgICBmb250U2l6ZTogJzAuOHJlbSdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgd2lkdGg6ICc2cHgnLFxuICAgICAgICAgICAgICBoZWlnaHQ6ICc2cHgnLFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzY4ZDM5MScsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJSdcbiAgICAgICAgICAgIH19PjwvZGl2PlxuICAgICAgICAgICAg2YXYqti12YRcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qINmC2KfYptmF2Kkg2KfZhNiq2YbZgtmEICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgZmxleDogMSxcbiAgICAgICAgICBwYWRkaW5nOiAnMjBweCAwJyxcbiAgICAgICAgICBvdmVyZmxvd1k6ICdhdXRvJ1xuICAgICAgICB9fT5cbiAgICAgICAgICB7ZmlsdGVyZWRNZW51SXRlbXMubWFwKChpdGVtLCBpbmRleCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgaXNBY3RpdmUgPSBwYXRobmFtZSA9PT0gaXRlbS5wYXRoO1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgcm91dGVyLnB1c2goaXRlbS5wYXRoKTtcbiAgICAgICAgICAgICAgICAgIGlmICh3aW5kb3cuaW5uZXJXaWR0aCA8PSA3NjgpIHtcbiAgICAgICAgICAgICAgICAgICAgb25Ub2dnbGUoKTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogaXNBY3RpdmUgPyAnIzQyOTllMScgOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6IGlzQWN0aXZlID8gJ3doaXRlJyA6ICcjYTBhZWMwJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEycHggMjBweCcsXG4gICAgICAgICAgICAgICAgICB0ZXh0QWxpZ246ICdyaWdodCcsXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgZ2FwOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuOXJlbScsXG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuMnMnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmlnaHQ6IGlzQWN0aXZlID8gJzNweCBzb2xpZCAjNDI5OWUxJyA6ICczcHggc29saWQgdHJhbnNwYXJlbnQnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAoIWlzQWN0aXZlKSB7XG4gICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmJhY2tncm91bmQgPSAnIzJkMzc0OCc7XG4gICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmNvbG9yID0gJ3doaXRlJztcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGlmICghaXNBY3RpdmUpIHtcbiAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICd0cmFuc3BhcmVudCc7XG4gICAgICAgICAgICAgICAgICAgIGUudGFyZ2V0LnN0eWxlLmNvbG9yID0gJyNhMGFlYzAnO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250U2l6ZTogJzEuMXJlbScgfX0+e2l0ZW0uaWNvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0pfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog2KPYs9mB2YQg2KfZhNi02LHZiti3INin2YTYrNin2YbYqNmKICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzIwcHgnLFxuICAgICAgICAgIGJvcmRlclRvcDogJzFweCBzb2xpZCAjMmQzNzQ4J1xuICAgICAgICB9fT5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIC8vINil2LbYp9mB2Kkg2YjYuNmK2YHYqSDYqtiz2KzZitmEINin2YTYrtix2YjYrCDZh9mG2KdcbiAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3VzZXInKTtcbiAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Rva2VuJyk7XG4gICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKTtcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjZjU2NTY1LCAjZTUzZTNlKScsXG4gICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICBnYXA6ICc4cHgnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzAuOXJlbScsXG4gICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMTVweCdcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAg8J+aqiDYqtiz2KzZitmEINin2YTYrtix2YjYrFxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgey8qINmG2LUg2KfZhNmF2LfZiNixICovfVxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgICBjb2xvcjogJyM2Yzc1N2QnLFxuICAgICAgICAgICAgZm9udFNpemU6ICcwLjc1cmVtJyxcbiAgICAgICAgICAgIGZvbnRGYW1pbHk6ICdBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgICAgICAgICBkaXJlY3Rpb246ICdsdHInXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICBQb3dlcmVkIEJ5IE1haG1vdWQgSXNtYWlsXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlUm91dGVyIiwidXNlUGF0aG5hbWUiLCJ1c2VBdXRoIiwiU2lkZWJhciIsImlzT3BlbiIsIm9uVG9nZ2xlIiwicm91dGVyIiwicGF0aG5hbWUiLCJ1c2VyIiwiaGFzUGVybWlzc2lvbiIsIm1lbnVJdGVtcyIsIm5hbWUiLCJpY29uIiwicGF0aCIsInBlcm1pc3Npb24iLCJhZG1pbk9ubHkiLCJmaWx0ZXJlZE1lbnVJdGVtcyIsImZpbHRlciIsIml0ZW0iLCJyb2xlIiwiZGl2Iiwic3R5bGUiLCJwb3NpdGlvbiIsInRvcCIsImxlZnQiLCJyaWdodCIsImJvdHRvbSIsImJhY2tncm91bmQiLCJ6SW5kZXgiLCJkaXNwbGF5Iiwid2luZG93IiwiaW5uZXJXaWR0aCIsIm9uQ2xpY2siLCJ3aWR0aCIsImhlaWdodCIsImJvcmRlckxlZnQiLCJ0cmFuc2l0aW9uIiwiZmxleERpcmVjdGlvbiIsImZvbnRGYW1pbHkiLCJwYWRkaW5nIiwiYm9yZGVyQm90dG9tIiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiZ2FwIiwiZm9udFNpemUiLCJmb250V2VpZ2h0Iiwic3BhbiIsIldlYmtpdEJhY2tncm91bmRDbGlwIiwiV2Via2l0VGV4dEZpbGxDb2xvciIsImNvbG9yIiwibGV0dGVyU3BhY2luZyIsImgzIiwibWFyZ2luIiwicCIsImJ1dHRvbiIsImJvcmRlciIsImN1cnNvciIsIm1hcmdpbkJvdHRvbSIsImJvcmRlclJhZGl1cyIsImNoYXJBdCIsImZsZXgiLCJvdmVyZmxvd1kiLCJtYXAiLCJpbmRleCIsImlzQWN0aXZlIiwicHVzaCIsInRleHRBbGlnbiIsImJvcmRlclJpZ2h0Iiwib25Nb3VzZUVudGVyIiwiZSIsInRhcmdldCIsIm9uTW91c2VMZWF2ZSIsImJvcmRlclRvcCIsImxvY2FsU3RvcmFnZSIsInJlbW92ZUl0ZW0iLCJkaXJlY3Rpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \n\nfunction Toast({ message, type, duration = 3000, onClose }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن\n                }\n            }[\"Toast.useEffect.timer\"], duration);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        duration,\n        onClose\n    ]);\n    const getToastStyles = ()=>{\n        const baseStyles = {\n            position: 'relative',\n            padding: '15px 20px',\n            borderRadius: '10px',\n            color: 'white',\n            fontWeight: 'bold',\n            fontSize: '1rem',\n            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n            transform: isVisible ? 'translateX(0)' : 'translateX(100%)',\n            transition: 'transform 0.3s ease, opacity 0.3s ease',\n            opacity: isVisible ? 1 : 0,\n            minWidth: '300px',\n            maxWidth: '500px',\n            direction: 'rtl',\n            fontFamily: 'Cairo, Arial, sans-serif'\n        };\n        const typeStyles = {\n            success: {\n                background: 'linear-gradient(45deg, #28a745, #20c997)'\n            },\n            error: {\n                background: 'linear-gradient(45deg, #dc3545, #c82333)'\n            },\n            warning: {\n                background: 'linear-gradient(45deg, #ffc107, #e0a800)'\n            },\n            info: {\n                background: 'linear-gradient(45deg, #007bff, #0056b3)'\n            }\n        };\n        return {\n            ...baseStyles,\n            ...typeStyles[type]\n        };\n    };\n    const getIcon = ()=>{\n        const icons = {\n            success: '✅',\n            error: '❌',\n            warning: '⚠️',\n            info: 'ℹ️'\n        };\n        return icons[type];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: getToastStyles(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '10px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        fontSize: '1.2rem'\n                    },\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setIsVisible(false);\n                        setTimeout(onClose, 300);\n                    },\n                    style: {\n                        background: 'rgba(255,255,255,0.2)',\n                        border: 'none',\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '25px',\n                        height: '25px',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        marginLeft: 'auto'\n                    },\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n// Hook لاستخدام Toast\nfunction useToast() {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (message, type = 'info')=>{\n        // التحقق من عدم وجود رسالة مشابهة\n        const existingToast = toasts.find((toast)=>toast.message === message && toast.type === type);\n        if (existingToast) {\n            return; // لا تضيف رسالة مكررة\n        }\n        // إنشاء ID فريد باستخدام timestamp + random number\n        const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        // حد أقصى 5 رسائل في نفس الوقت\n        setToasts((prev)=>prev.slice(-4)); // احتفظ بآخر 4 + الجديدة = 5\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const ToastContainer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: 'fixed',\n                top: '20px',\n                right: '20px',\n                zIndex: 1000,\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '10px'\n            },\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                    message: toast.message,\n                    type: toast.type,\n                    onClose: ()=>removeToast(toast.id)\n                }, toast.id, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 129,\n            columnNumber: 5\n        }, this);\n    return {\n        showToast,\n        ToastContainer\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Toast.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fadd-media%2Fpage&page=%2Fadd-media%2Fpage&appPaths=%2Fadd-media%2Fpage&pagePath=private-next-app-dir%2Fadd-media%2Fpage.tsx&appDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CDoc%20database%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();