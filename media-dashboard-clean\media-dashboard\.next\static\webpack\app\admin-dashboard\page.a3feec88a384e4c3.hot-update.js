"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin-dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin-dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddUser, setShowAddUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        phone: '',\n        role: 'VIEWER'\n    });\n    const roles = {\n        'ADMIN': {\n            name: 'مدير النظام',\n            color: '#dc3545',\n            icon: '👑'\n        },\n        'MEDIA_MANAGER': {\n            name: 'مدير المحتوى',\n            color: '#28a745',\n            icon: '📝'\n        },\n        'SCHEDULER': {\n            name: 'مجدول البرامج',\n            color: '#007bff',\n            icon: '📅'\n        },\n        'VIEWER': {\n            name: 'مستخدم عرض',\n            color: '#6c757d',\n            icon: '👁️'\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchUsers();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch('/api/users');\n            const result = await response.json();\n            if (result.success) {\n                setUsers(result.users);\n            } else {\n                showToast('خطأ في جلب بيانات المستخدمين', 'error');\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddUser = async (e)=>{\n        e.preventDefault();\n        if (!newUser.username || !newUser.password || !newUser.name) {\n            showToast('يرجى ملء جميع الحقول المطلوبة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch('/api/users', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newUser)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم إنشاء المستخدم بنجاح!', 'success');\n                setUsers([\n                    ...users,\n                    result.user\n                ]);\n                setShowAddUser(false);\n                setNewUser({\n                    username: '',\n                    password: '',\n                    name: '',\n                    email: '',\n                    phone: '',\n                    role: 'VIEWER'\n                });\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error adding user:', error);\n            showToast('خطأ في إنشاء المستخدم', 'error');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;\n        try {\n            const response = await fetch(\"/api/users?id=\".concat(userId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حذف المستخدم بنجاح!', 'success');\n                setUsers(users.filter((u)=>u.id !== userId));\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error deleting user:', error);\n            showToast('خطأ في حذف المستخدم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredRole: \"ADMIN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            title: \"إدارة المستخدمين\",\n            subtitle: \"إضافة وتعديل المستخدمين\",\n            icon: \"\\uD83D\\uDC65\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                        gap: '20px',\n                        marginBottom: '20px'\n                    },\n                    children: Object.entries(roles).map((param)=>{\n                        let [roleKey, roleInfo] = param;\n                        const count = users.filter((u)=>u.role === roleKey).length;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#4a5568',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                border: '1px solid #6b7280',\n                                textAlign: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2.5rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: roleInfo.icon\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        margin: '0 0 5px 0',\n                                        fontSize: '1.2rem'\n                                    },\n                                    children: roleInfo.name\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '2rem',\n                                        fontWeight: 'bold',\n                                        color: roleInfo.color\n                                    },\n                                    children: count\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#d1d5db',\n                                        fontSize: '0.9rem',\n                                        margin: 0\n                                    },\n                                    children: \"مستخدم\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, roleKey, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: '30px',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center',\n                                marginBottom: '25px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        fontSize: '1.5rem',\n                                        margin: 0\n                                    },\n                                    children: [\n                                        \"\\uD83D\\uDC65 إدارة المستخدمين (\",\n                                        users.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowAddUser(true),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #10b981, #059669)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '12px 20px',\n                                        cursor: 'pointer',\n                                        fontSize: '1rem',\n                                        fontWeight: 'bold'\n                                    },\n                                    children: \"➕ إضافة مستخدم جديد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        showAddUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#1f2937',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                marginBottom: '25px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        marginBottom: '20px'\n                                    },\n                                    children: \"➕ إضافة مستخدم جديد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleAddUser,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                gap: '15px',\n                                                marginBottom: '20px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"اسم المستخدم *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"اسم المستخدم\",\n                                                            value: newUser.username,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    username: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"كلمة المرور *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            placeholder: \"كلمة المرور\",\n                                                            value: newUser.password,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    password: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"الاسم الكامل *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"الاسم الكامل\",\n                                                            value: newUser.name,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    name: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"البريد الإلكتروني\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 322,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            placeholder: \"البريد الإلكتروني\",\n                                                            value: newUser.email,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    email: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"رقم الهاتف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            placeholder: \"رقم الهاتف\",\n                                                            value: newUser.phone,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    phone: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: \"الدور *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: newUser.role,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    role: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            children: Object.entries(roles).map((param)=>{\n                                                                let [key, role] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: role.name\n                                                                }, key, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '10px',\n                                                justifyContent: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    style: {\n                                                        background: 'linear-gradient(45deg, #10b981, #059669)',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"✅ إنشاء المستخدم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>{\n                                                        setShowAddUser(false);\n                                                        setNewUser({\n                                                            username: '',\n                                                            password: '',\n                                                            name: '',\n                                                            email: '',\n                                                            phone: '',\n                                                            role: 'VIEWER'\n                                                        });\n                                                    },\n                                                    style: {\n                                                        background: '#6c757d',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"❌ إلغاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                overflowX: 'auto',\n                                borderRadius: '10px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                style: {\n                                    width: '100%',\n                                    borderCollapse: 'collapse',\n                                    fontSize: '0.9rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            style: {\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'right',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"المستخدم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"الدور\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"تاريخ الإنشاء\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"آخر دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                    style: {\n                                                        padding: '15px',\n                                                        textAlign: 'center',\n                                                        borderBottom: '2px solid #6b7280',\n                                                        color: '#f3f4f6'\n                                                    },\n                                                    children: \"الإجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                        children: users.map((user)=>{\n                                            var _roles_user_role, _roles_user_role1, _roles_user_role2, _roles_user_role3;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    borderBottom: '1px solid #6b7280',\n                                                    transition: 'background-color 0.2s',\n                                                    background: '#2d3748'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#374151',\n                                                onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = '#2d3748',\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontWeight: 'bold',\n                                                                        color: '#f3f4f6',\n                                                                        marginBottom: '5px'\n                                                                    },\n                                                                    children: user.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 447,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        color: '#d1d5db',\n                                                                        fontSize: '0.85rem'\n                                                                    },\n                                                                    children: [\n                                                                        \"@\",\n                                                                        user.username\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 450,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                user.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        color: '#a0aec0',\n                                                                        fontSize: '0.8rem'\n                                                                    },\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCE7 \",\n                                                                        user.email\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 454,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                user.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        color: '#a0aec0',\n                                                                        fontSize: '0.8rem'\n                                                                    },\n                                                                    children: [\n                                                                        \"\\uD83D\\uDCF1 \",\n                                                                        user.phone\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                background: ((_roles_user_role = roles[user.role]) === null || _roles_user_role === void 0 ? void 0 : _roles_user_role.color) + '20',\n                                                                color: (_roles_user_role1 = roles[user.role]) === null || _roles_user_role1 === void 0 ? void 0 : _roles_user_role1.color,\n                                                                padding: '5px 12px',\n                                                                borderRadius: '20px',\n                                                                fontSize: '0.85rem',\n                                                                fontWeight: 'bold',\n                                                                display: 'inline-flex',\n                                                                alignItems: 'center',\n                                                                gap: '5px'\n                                                            },\n                                                            children: [\n                                                                (_roles_user_role2 = roles[user.role]) === null || _roles_user_role2 === void 0 ? void 0 : _roles_user_role2.icon,\n                                                                (_roles_user_role3 = roles[user.role]) === null || _roles_user_role3 === void 0 ? void 0 : _roles_user_role3.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                background: user.isActive ? '#28a74520' : '#dc354520',\n                                                                color: user.isActive ? '#28a745' : '#dc3545',\n                                                                padding: '5px 12px',\n                                                                borderRadius: '20px',\n                                                                fontSize: '0.85rem',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: user.isActive ? '✅ نشط' : '❌ معطل'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: new Date(user.createdAt).toLocaleDateString('en-GB')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 493,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('en-GB') : 'لم يدخل بعد'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        style: {\n                                                            padding: '15px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                gap: '5px',\n                                                                justifyContent: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>setEditingUser(user),\n                                                                    style: {\n                                                                        background: '#007bff',\n                                                                        color: 'white',\n                                                                        border: 'none',\n                                                                        borderRadius: '5px',\n                                                                        padding: '5px 10px',\n                                                                        cursor: 'pointer',\n                                                                        fontSize: '0.8rem'\n                                                                    },\n                                                                    title: \"تعديل\",\n                                                                    children: \"✏️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                user.id !== '1' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleDeleteUser(user.id),\n                                                                    style: {\n                                                                        background: '#dc3545',\n                                                                        color: 'white',\n                                                                        border: 'none',\n                                                                        borderRadius: '5px',\n                                                                        padding: '5px 10px',\n                                                                        cursor: 'pointer',\n                                                                        fontSize: '0.8rem'\n                                                                    },\n                                                                    title: \"حذف\",\n                                                                    children: \"\\uD83D\\uDDD1️\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 500,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, user.id, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, this),\n                        users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                textAlign: 'center',\n                                padding: '40px',\n                                color: '#6c757d'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        marginBottom: '15px'\n                                    },\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"لا توجد مستخدمين\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"ابدأ بإضافة مستخدم جديد\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 549,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 9\n                }, this),\n                editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.5)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'white',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            width: '400px',\n                            maxWidth: '90vw'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: '20px',\n                                    color: '#333'\n                                },\n                                children: \"تعديل المستخدم\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"اسم المستخدم:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingUser.username,\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                username: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 579,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"الاسم الكامل:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingUser.name,\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                name: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"البريد الإلكتروني:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: editingUser.email || '',\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                email: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"رقم الهاتف:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 628,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        value: editingUser.phone || '',\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                phone: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"الدور:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingUser.role,\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                role: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"ADMIN\",\n                                                children: \"مدير\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MEDIA_MANAGER\",\n                                                children: \"مدير المواد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SCHEDULER\",\n                                                children: \"مجدول\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 658,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"VIEWER\",\n                                                children: \"مشاهد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 643,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '10px',\n                                    justifyContent: 'flex-end'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingUser(null),\n                                        style: {\n                                            background: '#6c757d',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            padding: '10px 20px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            try {\n                                                const response = await fetch('/api/users', {\n                                                    method: 'PUT',\n                                                    headers: {\n                                                        'Content-Type': 'application/json'\n                                                    },\n                                                    body: JSON.stringify(editingUser)\n                                                });\n                                                if (response.ok) {\n                                                    showToast('تم تحديث المستخدم بنجاح', 'success');\n                                                    setEditingUser(null);\n                                                    fetchUsers();\n                                                } else {\n                                                    showToast('خطأ في تحديث المستخدم', 'error');\n                                                }\n                                            } catch (error) {\n                                                showToast('خطأ في الاتصال', 'error');\n                                            }\n                                        },\n                                        style: {\n                                            background: '#28a745',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            padding: '10px 20px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"حفظ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 663,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 558,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"oD1Ydqov7RLN1sR8XtI2GSqwll4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/page.tsx\n"));

/***/ })

});