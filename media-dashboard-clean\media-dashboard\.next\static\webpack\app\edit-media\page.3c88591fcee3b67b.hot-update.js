"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit-media/page",{

/***/ "(app-pages-browser)/./src/app/edit-media/page.tsx":
/*!*************************************!*\
  !*** ./src/app/edit-media/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EditMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const mediaId = searchParams.get('id');\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: 'PROGRAM',\n        description: '',\n        channel: 'DOCUMENTARY',\n        source: '',\n        status: 'WAITING',\n        startDate: '',\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: 'SERVER'\n    });\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: 'SEG001',\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        }\n    ]);\n    // تحميل بيانات المادة للتعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditMediaPage.useEffect\": ()=>{\n            if (!mediaId) {\n                router.push('/media-list');\n                return;\n            }\n            const fetchMediaItem = {\n                \"EditMediaPage.useEffect.fetchMediaItem\": async ()=>{\n                    try {\n                        console.log('📡 جاري تحميل بيانات المادة:', mediaId);\n                        const response = await fetch(\"/api/media?id=\".concat(mediaId));\n                        const result = await response.json();\n                        if (result.success && result.data) {\n                            var _item_episodeNumber, _item_seasonNumber, _item_partNumber;\n                            const item = result.data;\n                            console.log('📋 البيانات المستلمة:', item);\n                            setFormData({\n                                name: item.name || '',\n                                type: item.type || 'PROGRAM',\n                                description: item.description || '',\n                                channel: item.channel || 'DOCUMENTARY',\n                                source: item.source || '',\n                                status: item.status || 'WAITING',\n                                startDate: item.startDate ? item.startDate.split('T')[0] : '',\n                                endDate: item.endDate ? item.endDate.split('T')[0] : '',\n                                notes: item.notes || '',\n                                episodeNumber: ((_item_episodeNumber = item.episodeNumber) === null || _item_episodeNumber === void 0 ? void 0 : _item_episodeNumber.toString()) || '',\n                                seasonNumber: ((_item_seasonNumber = item.seasonNumber) === null || _item_seasonNumber === void 0 ? void 0 : _item_seasonNumber.toString()) || '',\n                                partNumber: ((_item_partNumber = item.partNumber) === null || _item_partNumber === void 0 ? void 0 : _item_partNumber.toString()) || '',\n                                hardDiskNumber: item.hardDiskNumber || 'SERVER'\n                            });\n                            console.log('✅ تم تعيين بيانات النموذج بنجاح');\n                            if (item.segments && item.segments.length > 0) {\n                                const loadedSegments = item.segments.map({\n                                    \"EditMediaPage.useEffect.fetchMediaItem.loadedSegments\": (seg, index)=>({\n                                            id: index + 1,\n                                            segmentCode: seg.code || seg.segmentCode || \"SEG\".concat(String(index + 1).padStart(3, '0')),\n                                            timeIn: seg.timeIn || '00:00:00',\n                                            timeOut: seg.timeOut || '00:00:00',\n                                            duration: seg.duration || '00:00:00'\n                                        })\n                                }[\"EditMediaPage.useEffect.fetchMediaItem.loadedSegments\"]);\n                                setSegments(loadedSegments);\n                                setSegmentCount(item.segments.length);\n                                console.log('🎬 تم تحميل السيجمانت:', loadedSegments);\n                            }\n                        } else {\n                            console.error('❌ فشل في تحميل البيانات:', result);\n                            showToast('فشل في تحميل بيانات المادة', 'error');\n                            router.push('/media-list');\n                        }\n                    } catch (error) {\n                        console.error('❌ خطأ في تحميل البيانات:', error);\n                        showToast('خطأ في تحميل البيانات', 'error');\n                        router.push('/media-list');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"EditMediaPage.useEffect.fetchMediaItem\"];\n            fetchMediaItem();\n        }\n    }[\"EditMediaPage.useEffect\"], [\n        mediaId\n    ]); // تحميل البيانات مرة واحدة فقط عند تغيير mediaId\n    const handleInputChange = (field, value)=>{\n        console.log(\"\\uD83D\\uDD04 تغيير \".concat(field, \":\"), value);\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n    };\n    const addSegment = ()=>{\n        console.log('🔄 محاولة إضافة سيجمانت جديد...');\n        const newSegmentCount = segmentCount + 1;\n        setSegmentCount(newSegmentCount);\n        const newSegment = {\n            id: newSegmentCount,\n            segmentCode: \"SEG\".concat(String(newSegmentCount).padStart(3, '0')),\n            timeIn: '00:00:00',\n            timeOut: '00:00:00',\n            duration: '00:00:00'\n        };\n        setSegments((prev)=>{\n            const updated = [\n                ...prev,\n                newSegment\n            ];\n            console.log('➕ تم إضافة سيجمانت جديد. العدد الجديد:', updated.length);\n            return updated;\n        });\n    };\n    const removeSegment = (segmentId)=>{\n        if (segments.length > 1) {\n            setSegments((prev)=>prev.filter((segment)=>segment.id !== segmentId));\n            console.log('🗑️ تم حذف السيجمانت:', segmentId);\n        } else {\n            alert('لا يمكن حذف السيجمانت الوحيد المتبقي');\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.name.trim()) {\n            showToast('يرجى إدخال اسم المادة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/media?id=\".concat(mediaId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم تحديث المادة الإعلامية بنجاح!', 'success');\n                router.push('/media-list');\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error updating media item:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        transition: 'border-color 0.3s',\n        direction: 'rtl'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            padding: '20px',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: 'rtl'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '800px',\n                    margin: '0 auto',\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            marginBottom: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                style: {\n                                    fontSize: '2rem',\n                                    fontWeight: 'bold',\n                                    color: '#333',\n                                    margin: 0,\n                                    background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                    WebkitBackgroundClip: 'text',\n                                    WebkitTextFillColor: 'transparent'\n                                },\n                                children: \"✏️ تعديل المادة الإعلامية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push('/media-list'),\n                                    style: {\n                                        background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                        color: 'white',\n                                        border: 'none',\n                                        borderRadius: '10px',\n                                        padding: '10px 20px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: \"\\uD83D\\uDD19 العودة للقائمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #dee2e6'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#495057',\n                                            marginBottom: '20px',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD المعلومات الأساسية\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '200px 1fr',\n                                                    gap: '15px',\n                                                    alignItems: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            color: '#495057',\n                                                            fontWeight: 'bold',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: \"\\uD83D\\uDCBE رقم الهارد:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"رقم الهارد\",\n                                                        value: formData.hardDiskNumber,\n                                                        onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            maxWidth: '200px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة *\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: inputStyle,\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"برنامج\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"مسلسل\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MOVIE\",\n                                                                children: \"فيلم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"أغنية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILL_IN\",\n                                                                children: \"Fill IN\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 320,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"وصف المادة\",\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '80px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"المصدر\",\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: inputStyle,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"WAITING\",\n                                                                children: \"في الانتظار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: \"صالح\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"APPROVED\",\n                                                                children: \"مقبول\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED\",\n                                                                children: \"مرفوض\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: \"منتهي الصلاحية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ البداية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.startDate,\n                                                                onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#495057',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: \"تاريخ النهاية:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"date\",\n                                                                value: formData.endDate,\n                                                                onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                                style: inputStyle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this),\n                                            (formData.type === 'SERIES' || formData.type === 'PROGRAM') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: formData.type === 'SERIES' ? '1fr 1fr' : '1fr 1fr',\n                                                    gap: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الحلقة\",\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    formData.type === 'SERIES' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الجزء\",\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: \"رقم الموسم\",\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: inputStyle\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            formData.type === 'MOVIE' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                placeholder: \"رقم الجزء\",\n                                                value: formData.partNumber,\n                                                onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                style: inputStyle\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"ملاحظات إضافية\",\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    minHeight: '60px',\n                                                    resize: 'vertical'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #90caf9'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-between',\n                                            alignItems: 'center',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    color: '#1976d2',\n                                                    fontSize: '1.3rem',\n                                                    margin: 0\n                                                },\n                                                children: \"\\uD83C\\uDFAC إدارة السيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #4caf50, #45a049)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: \"➕ إضافة سيجمانت\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this),\n                                    segments.map((segment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                background: 'white',\n                                                borderRadius: '10px',\n                                                padding: '20px',\n                                                marginBottom: '15px',\n                                                border: '1px solid #e0e0e0'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            style: {\n                                                                color: '#333',\n                                                                margin: 0\n                                                            },\n                                                            children: [\n                                                                \"سيجمانت \",\n                                                                segment.id\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        segments.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>removeSegment(segment.id),\n                                                            style: {\n                                                                background: '#ff4444',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '15px',\n                                                                padding: '5px 10px',\n                                                                cursor: 'pointer',\n                                                                fontSize: '0.8rem'\n                                                            },\n                                                            children: \"❌ حذف\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: '1fr 1fr 1fr 1fr',\n                                                        gap: '15px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"كود السيجمانت\",\n                                                            value: segment.segmentCode,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"وقت البداية (00:00:00)\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"وقت النهاية (00:00:00)\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"المدة (00:00:00)\",\n                                                            value: segment.duration,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'duration', e.target.value),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, segment.id, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '15px',\n                                    justifyContent: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(40,167,69,0.3)'\n                                        },\n                                        children: \"✅ حفظ التعديلات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.push('/media-list'),\n                                        style: {\n                                            background: 'linear-gradient(45deg, #6c757d, #495057)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '25px',\n                                            padding: '15px 40px',\n                                            fontSize: '1.1rem',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            boxShadow: '0 4px 15px rgba(108,117,125,0.3)'\n                                        },\n                                        children: \"❌ إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n                lineNumber: 565,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\edit-media\\\\page.tsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, this);\n}\n_s(EditMediaPage, \"U/Mp9ns9pODrm6O21lgz1y/hFgg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = EditMediaPage;\nvar _c;\n$RefreshReg$(_c, \"EditMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/edit-media/page.tsx\n"));

/***/ })

});