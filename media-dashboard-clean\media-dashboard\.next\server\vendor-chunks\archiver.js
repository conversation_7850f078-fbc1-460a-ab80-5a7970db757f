/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/archiver";
exports.ids = ["vendor-chunks/archiver"];
exports.modules = {

/***/ "(rsc)/./node_modules/archiver/index.js":
/*!****************************************!*\
  !*** ./node_modules/archiver/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Archiver Vending\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar Archiver = __webpack_require__(/*! ./lib/core */ \"(rsc)/./node_modules/archiver/lib/core.js\");\n\nvar formats = {};\n\n/**\n * Dispenses a new Archiver instance.\n *\n * @constructor\n * @param  {String} format The archive format to use.\n * @param  {Object} options See [Archiver]{@link Archiver}\n * @return {Archiver}\n */\nvar vending = function(format, options) {\n  return vending.create(format, options);\n};\n\n/**\n * Creates a new Archiver instance.\n *\n * @param  {String} format The archive format to use.\n * @param  {Object} options See [Archiver]{@link Archiver}\n * @return {Archiver}\n */\nvending.create = function(format, options) {\n  if (formats[format]) {\n    var instance = new Archiver(format, options);\n    instance.setFormat(format);\n    instance.setModule(new formats[format](options));\n\n    return instance;\n  } else {\n    throw new Error('create(' + format + '): format not registered');\n  }\n};\n\n/**\n * Registers a format for use with archiver.\n *\n * @param  {String} format The name of the format.\n * @param  {Function} module The function for archiver to interact with.\n * @return void\n */\nvending.registerFormat = function(format, module) {\n  if (formats[format]) {\n    throw new Error('register(' + format + '): format already registered');\n  }\n\n  if (typeof module !== 'function') {\n    throw new Error('register(' + format + '): format module invalid');\n  }\n\n  if (typeof module.prototype.append !== 'function' || typeof module.prototype.finalize !== 'function') {\n    throw new Error('register(' + format + '): format module missing methods');\n  }\n\n  formats[format] = module;\n};\n\n/**\n * Check if the format is already registered.\n * \n * @param {String} format the name of the format.\n * @return boolean\n */\nvending.isRegisteredFormat = function (format) {\n  if (formats[format]) {\n    return true;\n  }\n  \n  return false;\n};\n\nvending.registerFormat('zip', __webpack_require__(/*! ./lib/plugins/zip */ \"(rsc)/./node_modules/archiver/lib/plugins/zip.js\"));\nvending.registerFormat('tar', __webpack_require__(/*! ./lib/plugins/tar */ \"(rsc)/./node_modules/archiver/lib/plugins/tar.js\"));\nvending.registerFormat('json', __webpack_require__(/*! ./lib/plugins/json */ \"(rsc)/./node_modules/archiver/lib/plugins/json.js\"));\n\nmodule.exports = vending;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/archiver/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/archiver/lib/core.js":
/*!*******************************************!*\
  !*** ./node_modules/archiver/lib/core.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Archiver Core\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar glob = __webpack_require__(/*! readdir-glob */ \"(rsc)/./node_modules/readdir-glob/index.js\");\nvar async = __webpack_require__(/*! async */ \"(rsc)/./node_modules/async/dist/async.mjs\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(rsc)/./node_modules/archiver-utils/index.js\");\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar ArchiverError = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/archiver/lib/error.js\");\nvar Transform = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\").Transform);\n\nvar win32 = process.platform === 'win32';\n\n/**\n * @constructor\n * @param {String} format The archive format to use.\n * @param {(CoreOptions|TransformOptions)} options See also {@link ZipOptions} and {@link TarOptions}.\n */\nvar Archiver = function(format, options) {\n  if (!(this instanceof Archiver)) {\n    return new Archiver(format, options);\n  }\n\n  if (typeof format !== 'string') {\n    options = format;\n    format = 'zip';\n  }\n\n  options = this.options = util.defaults(options, {\n    highWaterMark: 1024 * 1024,\n    statConcurrency: 4\n  });\n\n  Transform.call(this, options);\n\n  this._format = false;\n  this._module = false;\n  this._pending = 0;\n  this._pointer = 0;\n\n  this._entriesCount = 0;\n  this._entriesProcessedCount = 0;\n  this._fsEntriesTotalBytes = 0;\n  this._fsEntriesProcessedBytes = 0;\n\n  this._queue = async.queue(this._onQueueTask.bind(this), 1);\n  this._queue.drain(this._onQueueDrain.bind(this));\n\n  this._statQueue = async.queue(this._onStatQueueTask.bind(this), options.statConcurrency);\n  this._statQueue.drain(this._onQueueDrain.bind(this));\n\n  this._state = {\n    aborted: false,\n    finalize: false,\n    finalizing: false,\n    finalized: false,\n    modulePiped: false\n  };\n\n  this._streams = [];\n};\n\ninherits(Archiver, Transform);\n\n/**\n * Internal logic for `abort`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._abort = function() {\n  this._state.aborted = true;\n  this._queue.kill();\n  this._statQueue.kill();\n\n  if (this._queue.idle()) {\n    this._shutdown();\n  }\n};\n\n/**\n * Internal helper for appending files.\n *\n * @private\n * @param  {String} filepath The source filepath.\n * @param  {EntryData} data The entry data.\n * @return void\n */\nArchiver.prototype._append = function(filepath, data) {\n  data = data || {};\n\n  var task = {\n    source: null,\n    filepath: filepath\n  };\n\n  if (!data.name) {\n    data.name = filepath;\n  }\n\n  data.sourcePath = filepath;\n  task.data = data;\n  this._entriesCount++;\n\n  if (data.stats && data.stats instanceof fs.Stats) {\n    task = this._updateQueueTaskWithStats(task, data.stats);\n    if (task) {\n      if (data.stats.size) {\n        this._fsEntriesTotalBytes += data.stats.size;\n      }\n\n      this._queue.push(task);\n    }\n  } else {\n    this._statQueue.push(task);\n  }\n};\n\n/**\n * Internal logic for `finalize`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._finalize = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return;\n  }\n\n  this._state.finalizing = true;\n\n  this._moduleFinalize();\n\n  this._state.finalizing = false;\n  this._state.finalized = true;\n};\n\n/**\n * Checks the various state variables to determine if we can `finalize`.\n *\n * @private\n * @return {Boolean}\n */\nArchiver.prototype._maybeFinalize = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return false;\n  }\n\n  if (this._state.finalize && this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n    return true;\n  }\n\n  return false;\n};\n\n/**\n * Appends an entry to the module.\n *\n * @private\n * @fires  Archiver#entry\n * @param  {(Buffer|Stream)} source\n * @param  {EntryData} data\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._moduleAppend = function(source, data, callback) {\n  if (this._state.aborted) {\n    callback();\n    return;\n  }\n\n  this._module.append(source, data, function(err) {\n    this._task = null;\n\n    if (this._state.aborted) {\n      this._shutdown();\n      return;\n    }\n\n    if (err) {\n      this.emit('error', err);\n      setImmediate(callback);\n      return;\n    }\n\n    /**\n     * Fires when the entry's input has been processed and appended to the archive.\n     *\n     * @event Archiver#entry\n     * @type {EntryData}\n     */\n    this.emit('entry', data);\n    this._entriesProcessedCount++;\n\n    if (data.stats && data.stats.size) {\n      this._fsEntriesProcessedBytes += data.stats.size;\n    }\n\n    /**\n     * @event Archiver#progress\n     * @type {ProgressData}\n     */\n    this.emit('progress', {\n      entries: {\n        total: this._entriesCount,\n        processed: this._entriesProcessedCount\n      },\n      fs: {\n        totalBytes: this._fsEntriesTotalBytes,\n        processedBytes: this._fsEntriesProcessedBytes\n      }\n    });\n\n    setImmediate(callback);\n  }.bind(this));\n};\n\n/**\n * Finalizes the module.\n *\n * @private\n * @return void\n */\nArchiver.prototype._moduleFinalize = function() {\n  if (typeof this._module.finalize === 'function') {\n    this._module.finalize();\n  } else if (typeof this._module.end === 'function') {\n    this._module.end();\n  } else {\n    this.emit('error', new ArchiverError('NOENDMETHOD'));\n  }\n};\n\n/**\n * Pipes the module to our internal stream with error bubbling.\n *\n * @private\n * @return void\n */\nArchiver.prototype._modulePipe = function() {\n  this._module.on('error', this._onModuleError.bind(this));\n  this._module.pipe(this);\n  this._state.modulePiped = true;\n};\n\n/**\n * Determines if the current module supports a defined feature.\n *\n * @private\n * @param  {String} key\n * @return {Boolean}\n */\nArchiver.prototype._moduleSupports = function(key) {\n  if (!this._module.supports || !this._module.supports[key]) {\n    return false;\n  }\n\n  return this._module.supports[key];\n};\n\n/**\n * Unpipes the module from our internal stream.\n *\n * @private\n * @return void\n */\nArchiver.prototype._moduleUnpipe = function() {\n  this._module.unpipe(this);\n  this._state.modulePiped = false;\n};\n\n/**\n * Normalizes entry data with fallbacks for key properties.\n *\n * @private\n * @param  {Object} data\n * @param  {fs.Stats} stats\n * @return {Object}\n */\nArchiver.prototype._normalizeEntryData = function(data, stats) {\n  data = util.defaults(data, {\n    type: 'file',\n    name: null,\n    date: null,\n    mode: null,\n    prefix: null,\n    sourcePath: null,\n    stats: false\n  });\n\n  if (stats && data.stats === false) {\n    data.stats = stats;\n  }\n\n  var isDir = data.type === 'directory';\n\n  if (data.name) {\n    if (typeof data.prefix === 'string' && '' !== data.prefix) {\n      data.name = data.prefix + '/' + data.name;\n      data.prefix = null;\n    }\n\n    data.name = util.sanitizePath(data.name);\n\n    if (data.type !== 'symlink' && data.name.slice(-1) === '/') {\n      isDir = true;\n      data.type = 'directory';\n    } else if (isDir) {\n      data.name += '/';\n    }\n  }\n\n  // 511 === 0777; 493 === 0755; 438 === 0666; 420 === 0644\n  if (typeof data.mode === 'number') {\n    if (win32) {\n      data.mode &= 511;\n    } else {\n      data.mode &= 4095\n    }\n  } else if (data.stats && data.mode === null) {\n    if (win32) {\n      data.mode = data.stats.mode & 511;\n    } else {\n      data.mode = data.stats.mode & 4095;\n    }\n\n    // stat isn't reliable on windows; force 0755 for dir\n    if (win32 && isDir) {\n      data.mode = 493;\n    }\n  } else if (data.mode === null) {\n    data.mode = isDir ? 493 : 420;\n  }\n\n  if (data.stats && data.date === null) {\n    data.date = data.stats.mtime;\n  } else {\n    data.date = util.dateify(data.date);\n  }\n\n  return data;\n};\n\n/**\n * Error listener that re-emits error on to our internal stream.\n *\n * @private\n * @param  {Error} err\n * @return void\n */\nArchiver.prototype._onModuleError = function(err) {\n  /**\n   * @event Archiver#error\n   * @type {ErrorData}\n   */\n  this.emit('error', err);\n};\n\n/**\n * Checks the various state variables after queue has drained to determine if\n * we need to `finalize`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._onQueueDrain = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return;\n  }\n\n  if (this._state.finalize && this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n  }\n};\n\n/**\n * Appends each queue task to the module.\n *\n * @private\n * @param  {Object} task\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._onQueueTask = function(task, callback) {\n  var fullCallback = () => {\n    if(task.data.callback) {\n      task.data.callback();\n    }\n    callback();\n  }\n\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    fullCallback();\n    return;\n  }\n\n  this._task = task;\n  this._moduleAppend(task.source, task.data, fullCallback);\n};\n\n/**\n * Performs a file stat and reinjects the task back into the queue.\n *\n * @private\n * @param  {Object} task\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._onStatQueueTask = function(task, callback) {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    callback();\n    return;\n  }\n\n  fs.lstat(task.filepath, function(err, stats) {\n    if (this._state.aborted) {\n      setImmediate(callback);\n      return;\n    }\n\n    if (err) {\n      this._entriesCount--;\n\n      /**\n       * @event Archiver#warning\n       * @type {ErrorData}\n       */\n      this.emit('warning', err);\n      setImmediate(callback);\n      return;\n    }\n\n    task = this._updateQueueTaskWithStats(task, stats);\n\n    if (task) {\n      if (stats.size) {\n        this._fsEntriesTotalBytes += stats.size;\n      }\n\n      this._queue.push(task);\n    }\n\n    setImmediate(callback);\n  }.bind(this));\n};\n\n/**\n * Unpipes the module and ends our internal stream.\n *\n * @private\n * @return void\n */\nArchiver.prototype._shutdown = function() {\n  this._moduleUnpipe();\n  this.end();\n};\n\n/**\n * Tracks the bytes emitted by our internal stream.\n *\n * @private\n * @param  {Buffer} chunk\n * @param  {String} encoding\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._transform = function(chunk, encoding, callback) {\n  if (chunk) {\n    this._pointer += chunk.length;\n  }\n\n  callback(null, chunk);\n};\n\n/**\n * Updates and normalizes a queue task using stats data.\n *\n * @private\n * @param  {Object} task\n * @param  {fs.Stats} stats\n * @return {Object}\n */\nArchiver.prototype._updateQueueTaskWithStats = function(task, stats) {\n  if (stats.isFile()) {\n    task.data.type = 'file';\n    task.data.sourceType = 'stream';\n    task.source = util.lazyReadStream(task.filepath);\n  } else if (stats.isDirectory() && this._moduleSupports('directory')) {\n    task.data.name = util.trailingSlashIt(task.data.name);\n    task.data.type = 'directory';\n    task.data.sourcePath = util.trailingSlashIt(task.filepath);\n    task.data.sourceType = 'buffer';\n    task.source = Buffer.concat([]);\n  } else if (stats.isSymbolicLink() && this._moduleSupports('symlink')) {\n    var linkPath = fs.readlinkSync(task.filepath);\n    var dirName = path.dirname(task.filepath);\n    task.data.type = 'symlink';\n    task.data.linkname = path.relative(dirName, path.resolve(dirName, linkPath));\n    task.data.sourceType = 'buffer';\n    task.source = Buffer.concat([]);\n  } else {\n    if (stats.isDirectory()) {\n      this.emit('warning', new ArchiverError('DIRECTORYNOTSUPPORTED', task.data));\n    } else if (stats.isSymbolicLink()) {\n      this.emit('warning', new ArchiverError('SYMLINKNOTSUPPORTED', task.data));\n    } else {\n      this.emit('warning', new ArchiverError('ENTRYNOTSUPPORTED', task.data));\n    }\n\n    return null;\n  }\n\n  task.data = this._normalizeEntryData(task.data, stats);\n\n  return task;\n};\n\n/**\n * Aborts the archiving process, taking a best-effort approach, by:\n *\n * - removing any pending queue tasks\n * - allowing any active queue workers to finish\n * - detaching internal module pipes\n * - ending both sides of the Transform stream\n *\n * It will NOT drain any remaining sources.\n *\n * @return {this}\n */\nArchiver.prototype.abort = function() {\n  if (this._state.aborted || this._state.finalized) {\n    return this;\n  }\n\n  this._abort();\n\n  return this;\n};\n\n/**\n * Appends an input source (text string, buffer, or stream) to the instance.\n *\n * When the instance has received, processed, and emitted the input, the `entry`\n * event is fired.\n *\n * @fires  Archiver#entry\n * @param  {(Buffer|Stream|String)} source The input source.\n * @param  {EntryData} data See also {@link ZipEntryData} and {@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.append = function(source, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  data = this._normalizeEntryData(data);\n\n  if (typeof data.name !== 'string' || data.name.length === 0) {\n    this.emit('error', new ArchiverError('ENTRYNAMEREQUIRED'));\n    return this;\n  }\n\n  if (data.type === 'directory' && !this._moduleSupports('directory')) {\n    this.emit('error', new ArchiverError('DIRECTORYNOTSUPPORTED', { name: data.name }));\n    return this;\n  }\n\n  source = util.normalizeInputSource(source);\n\n  if (Buffer.isBuffer(source)) {\n    data.sourceType = 'buffer';\n  } else if (util.isStream(source)) {\n    data.sourceType = 'stream';\n  } else {\n    this.emit('error', new ArchiverError('INPUTSTEAMBUFFERREQUIRED', { name: data.name }));\n    return this;\n  }\n\n  this._entriesCount++;\n  this._queue.push({\n    data: data,\n    source: source\n  });\n\n  return this;\n};\n\n/**\n * Appends a directory and its files, recursively, given its dirpath.\n *\n * @param  {String} dirpath The source directory path.\n * @param  {String} destpath The destination path within the archive.\n * @param  {(EntryData|Function)} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.directory = function(dirpath, destpath, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof dirpath !== 'string' || dirpath.length === 0) {\n    this.emit('error', new ArchiverError('DIRECTORYDIRPATHREQUIRED'));\n    return this;\n  }\n\n  this._pending++;\n\n  if (destpath === false) {\n    destpath = '';\n  } else if (typeof destpath !== 'string'){\n    destpath = dirpath;\n  }\n\n  var dataFunction = false;\n  if (typeof data === 'function') {\n    dataFunction = data;\n    data = {};\n  } else if (typeof data !== 'object') {\n    data = {};\n  }\n\n  var globOptions = {\n    stat: true,\n    dot: true\n  };\n\n  function onGlobEnd() {\n    this._pending--;\n    this._maybeFinalize();\n  }\n\n  function onGlobError(err) {\n    this.emit('error', err);\n  }\n\n  function onGlobMatch(match){\n    globber.pause();\n\n    var ignoreMatch = false;\n    var entryData = Object.assign({}, data);\n    entryData.name = match.relative;\n    entryData.prefix = destpath;\n    entryData.stats = match.stat;\n    entryData.callback = globber.resume.bind(globber);\n\n    try {\n      if (dataFunction) {\n        entryData = dataFunction(entryData);\n\n        if (entryData === false) {\n          ignoreMatch = true;\n        } else if (typeof entryData !== 'object') {\n          throw new ArchiverError('DIRECTORYFUNCTIONINVALIDDATA', { dirpath: dirpath });\n        }\n      }\n    } catch(e) {\n      this.emit('error', e);\n      return;\n    }\n\n    if (ignoreMatch) {\n      globber.resume();\n      return;\n    }\n\n    this._append(match.absolute, entryData);\n  }\n\n  var globber = glob(dirpath, globOptions);\n  globber.on('error', onGlobError.bind(this));\n  globber.on('match', onGlobMatch.bind(this));\n  globber.on('end', onGlobEnd.bind(this));\n\n  return this;\n};\n\n/**\n * Appends a file given its filepath using a\n * [lazystream]{@link https://github.com/jpommerening/node-lazystream} wrapper to\n * prevent issues with open file limits.\n *\n * When the instance has received, processed, and emitted the file, the `entry`\n * event is fired.\n *\n * @param  {String} filepath The source filepath.\n * @param  {EntryData} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.file = function(filepath, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof filepath !== 'string' || filepath.length === 0) {\n    this.emit('error', new ArchiverError('FILEFILEPATHREQUIRED'));\n    return this;\n  }\n\n  this._append(filepath, data);\n\n  return this;\n};\n\n/**\n * Appends multiple files that match a glob pattern.\n *\n * @param  {String} pattern The [glob pattern]{@link https://github.com/isaacs/minimatch} to match.\n * @param  {Object} options See [node-readdir-glob]{@link https://github.com/yqnn/node-readdir-glob#options}.\n * @param  {EntryData} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.glob = function(pattern, options, data) {\n  this._pending++;\n\n  options = util.defaults(options, {\n    stat: true,\n    pattern: pattern\n  });\n\n  function onGlobEnd() {\n    this._pending--;\n    this._maybeFinalize();\n  }\n\n  function onGlobError(err) {\n    this.emit('error', err);\n  }\n\n  function onGlobMatch(match){\n    globber.pause();\n    var entryData = Object.assign({}, data);\n    entryData.callback = globber.resume.bind(globber);\n    entryData.stats = match.stat;\n    entryData.name = match.relative;\n\n    this._append(match.absolute, entryData);\n  }\n\n  var globber = glob(options.cwd || '.', options);\n  globber.on('error', onGlobError.bind(this));\n  globber.on('match', onGlobMatch.bind(this));\n  globber.on('end', onGlobEnd.bind(this));\n\n  return this;\n};\n\n/**\n * Finalizes the instance and prevents further appending to the archive\n * structure (queue will continue til drained).\n *\n * The `end`, `close` or `finish` events on the destination stream may fire\n * right after calling this method so you should set listeners beforehand to\n * properly detect stream completion.\n *\n * @return {Promise}\n */\nArchiver.prototype.finalize = function() {\n  if (this._state.aborted) {\n    var abortedError = new ArchiverError('ABORTED');\n    this.emit('error', abortedError);\n    return Promise.reject(abortedError);\n  }\n\n  if (this._state.finalize) {\n    var finalizingError = new ArchiverError('FINALIZING');\n    this.emit('error', finalizingError);\n    return Promise.reject(finalizingError);\n  }\n\n  this._state.finalize = true;\n\n  if (this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n  }\n\n  var self = this;\n\n  return new Promise(function(resolve, reject) {\n    var errored;\n\n    self._module.on('end', function() {\n      if (!errored) {\n        resolve();\n      }\n    })\n\n    self._module.on('error', function(err) {\n      errored = true;\n      reject(err);\n    })\n  })\n};\n\n/**\n * Sets the module format name used for archiving.\n *\n * @param {String} format The name of the format.\n * @return {this}\n */\nArchiver.prototype.setFormat = function(format) {\n  if (this._format) {\n    this.emit('error', new ArchiverError('FORMATSET'));\n    return this;\n  }\n\n  this._format = format;\n\n  return this;\n};\n\n/**\n * Sets the module used for archiving.\n *\n * @param {Function} module The function for archiver to interact with.\n * @return {this}\n */\nArchiver.prototype.setModule = function(module) {\n  if (this._state.aborted) {\n    this.emit('error', new ArchiverError('ABORTED'));\n    return this;\n  }\n\n  if (this._state.module) {\n    this.emit('error', new ArchiverError('MODULESET'));\n    return this;\n  }\n\n  this._module = module;\n  this._modulePipe();\n\n  return this;\n};\n\n/**\n * Appends a symlink to the instance.\n *\n * This does NOT interact with filesystem and is used for programmatically creating symlinks.\n *\n * @param  {String} filepath The symlink path (within archive).\n * @param  {String} target The target path (within archive).\n * @param  {Number} mode Sets the entry permissions.\n * @return {this}\n */\nArchiver.prototype.symlink = function(filepath, target, mode) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof filepath !== 'string' || filepath.length === 0) {\n    this.emit('error', new ArchiverError('SYMLINKFILEPATHREQUIRED'));\n    return this;\n  }\n\n  if (typeof target !== 'string' || target.length === 0) {\n    this.emit('error', new ArchiverError('SYMLINKTARGETREQUIRED', { filepath: filepath }));\n    return this;\n  }\n\n  if (!this._moduleSupports('symlink')) {\n    this.emit('error', new ArchiverError('SYMLINKNOTSUPPORTED', { filepath: filepath }));\n    return this;\n  }\n\n  var data = {};\n  data.type = 'symlink';\n  data.name = filepath.replace(/\\\\/g, '/');\n  data.linkname = target.replace(/\\\\/g, '/');\n  data.sourceType = 'buffer';\n\n  if (typeof mode === \"number\") {\n    data.mode = mode;\n  }\n\n  this._entriesCount++;\n  this._queue.push({\n    data: data,\n    source: Buffer.concat([])\n  });\n\n  return this;\n};\n\n/**\n * Returns the current length (in bytes) that has been emitted.\n *\n * @return {Number}\n */\nArchiver.prototype.pointer = function() {\n  return this._pointer;\n};\n\n/**\n * Middleware-like helper that has yet to be fully implemented.\n *\n * @private\n * @param  {Function} plugin\n * @return {this}\n */\nArchiver.prototype.use = function(plugin) {\n  this._streams.push(plugin);\n  return this;\n};\n\nmodule.exports = Archiver;\n\n/**\n * @typedef {Object} CoreOptions\n * @global\n * @property {Number} [statConcurrency=4] Sets the number of workers used to\n * process the internal fs stat queue.\n */\n\n/**\n * @typedef {Object} TransformOptions\n * @property {Boolean} [allowHalfOpen=true] If set to false, then the stream\n * will automatically end the readable side when the writable side ends and vice\n * versa.\n * @property {Boolean} [readableObjectMode=false] Sets objectMode for readable\n * side of the stream. Has no effect if objectMode is true.\n * @property {Boolean} [writableObjectMode=false] Sets objectMode for writable\n * side of the stream. Has no effect if objectMode is true.\n * @property {Boolean} [decodeStrings=true] Whether or not to decode strings\n * into Buffers before passing them to _write(). `Writable`\n * @property {String} [encoding=NULL] If specified, then buffers will be decoded\n * to strings using the specified encoding. `Readable`\n * @property {Number} [highWaterMark=16kb] The maximum number of bytes to store\n * in the internal buffer before ceasing to read from the underlying resource.\n * `Readable` `Writable`\n * @property {Boolean} [objectMode=false] Whether this stream should behave as a\n * stream of objects. Meaning that stream.read(n) returns a single value instead\n * of a Buffer of size n. `Readable` `Writable`\n */\n\n/**\n * @typedef {Object} EntryData\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n */\n\n/**\n * @typedef {Object} ErrorData\n * @property {String} message The message of the error.\n * @property {String} code The error code assigned to this error.\n * @property {String} data Additional data provided for reporting or debugging (where available).\n */\n\n/**\n * @typedef {Object} ProgressData\n * @property {Object} entries\n * @property {Number} entries.total Number of entries that have been appended.\n * @property {Number} entries.processed Number of entries that have been processed.\n * @property {Object} fs\n * @property {Number} fs.totalBytes Number of bytes that have been appended. Calculated asynchronously and might not be accurate: it growth while entries are added. (based on fs.Stats)\n * @property {Number} fs.processedBytes Number of bytes that have been processed. (based on fs.Stats)\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/archiver/lib/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/archiver/lib/error.js":
/*!********************************************!*\
  !*** ./node_modules/archiver/lib/error.js ***!
  \********************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Archiver Core\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\n\nvar util = __webpack_require__(/*! util */ \"util\");\n\nconst ERROR_CODES = {\n  'ABORTED': 'archive was aborted',\n  'DIRECTORYDIRPATHREQUIRED': 'diretory dirpath argument must be a non-empty string value',\n  'DIRECTORYFUNCTIONINVALIDDATA': 'invalid data returned by directory custom data function',\n  'ENTRYNAMEREQUIRED': 'entry name must be a non-empty string value',\n  'FILEFILEPATHREQUIRED': 'file filepath argument must be a non-empty string value',\n  'FINALIZING': 'archive already finalizing',\n  'QUEUECLOSED': 'queue closed',\n  'NOENDMETHOD': 'no suitable finalize/end method defined by module',\n  'DIRECTORYNOTSUPPORTED': 'support for directory entries not defined by module',\n  'FORMATSET': 'archive format already set',\n  'INPUTSTEAMBUFFERREQUIRED': 'input source must be valid Stream or Buffer instance',\n  'MODULESET': 'module already set',\n  'SYMLINKNOTSUPPORTED': 'support for symlink entries not defined by module',\n  'SYMLINKFILEPATHREQUIRED': 'symlink filepath argument must be a non-empty string value',\n  'SYMLINKTARGETREQUIRED': 'symlink target argument must be a non-empty string value',\n  'ENTRYNOTSUPPORTED': 'entry not supported'\n};\n\nfunction ArchiverError(code, data) {\n  Error.captureStackTrace(this, this.constructor);\n  //this.name = this.constructor.name;\n  this.message = ERROR_CODES[code] || code;\n  this.code = code;\n  this.data = data;\n}\n\nutil.inherits(ArchiverError, Error);\n\nexports = module.exports = ArchiverError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/archiver/lib/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/archiver/lib/plugins/json.js":
/*!***************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/json.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * JSON Format Plugin\n *\n * @module plugins/json\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar Transform = (__webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\").Transform);\n\nvar crc32 = __webpack_require__(/*! buffer-crc32 */ \"(rsc)/./node_modules/buffer-crc32/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(rsc)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {(JsonOptions|TransformOptions)} options\n */\nvar Json = function(options) {\n  if (!(this instanceof Json)) {\n    return new Json(options);\n  }\n\n  options = this.options = util.defaults(options, {});\n\n  Transform.call(this, options);\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.files = [];\n};\n\ninherits(Json, Transform);\n\n/**\n * [_transform description]\n *\n * @private\n * @param  {Buffer}   chunk\n * @param  {String}   encoding\n * @param  {Function} callback\n * @return void\n */\nJson.prototype._transform = function(chunk, encoding, callback) {\n  callback(null, chunk);\n};\n\n/**\n * [_writeStringified description]\n *\n * @private\n * @return void\n */\nJson.prototype._writeStringified = function() {\n  var fileString = JSON.stringify(this.files);\n  this.write(fileString);\n};\n\n/**\n * [append description]\n *\n * @param  {(Buffer|Stream)}   source\n * @param  {EntryData}   data\n * @param  {Function} callback\n * @return void\n */\nJson.prototype.append = function(source, data, callback) {\n  var self = this;\n\n  data.crc32 = 0;\n\n  function onend(err, sourceBuffer) {\n    if (err) {\n      callback(err);\n      return;\n    }\n\n    data.size = sourceBuffer.length || 0;\n    data.crc32 = crc32.unsigned(sourceBuffer);\n\n    self.files.push(data);\n\n    callback(null, data);\n  }\n\n  if (data.sourceType === 'buffer') {\n    onend(null, source);\n  } else if (data.sourceType === 'stream') {\n    util.collectStream(source, onend);\n  }\n};\n\n/**\n * [finalize description]\n *\n * @return void\n */\nJson.prototype.finalize = function() {\n  this._writeStringified();\n  this.end();\n};\n\nmodule.exports = Json;\n\n/**\n * @typedef {Object} JsonOptions\n * @global\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/archiver/lib/plugins/json.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/archiver/lib/plugins/tar.js":
/*!**************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/tar.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * TAR Format Plugin\n *\n * @module plugins/tar\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar zlib = __webpack_require__(/*! zlib */ \"zlib\");\n\nvar engine = __webpack_require__(/*! tar-stream */ \"(rsc)/./node_modules/tar-stream/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(rsc)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {TarOptions} options\n */\nvar Tar = function(options) {\n  if (!(this instanceof Tar)) {\n    return new Tar(options);\n  }\n\n  options = this.options = util.defaults(options, {\n    gzip: false\n  });\n\n  if (typeof options.gzipOptions !== 'object') {\n    options.gzipOptions = {};\n  }\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.engine = engine.pack(options);\n  this.compressor = false;\n\n  if (options.gzip) {\n    this.compressor = zlib.createGzip(options.gzipOptions);\n    this.compressor.on('error', this._onCompressorError.bind(this));\n  }\n};\n\n/**\n * [_onCompressorError description]\n *\n * @private\n * @param  {Error} err\n * @return void\n */\nTar.prototype._onCompressorError = function(err) {\n  this.engine.emit('error', err);\n};\n\n/**\n * [append description]\n *\n * @param  {(Buffer|Stream)} source\n * @param  {TarEntryData} data\n * @param  {Function} callback\n * @return void\n */\nTar.prototype.append = function(source, data, callback) {\n  var self = this;\n\n  data.mtime = data.date;\n\n  function append(err, sourceBuffer) {\n    if (err) {\n      callback(err);\n      return;\n    }\n\n    self.engine.entry(data, sourceBuffer, function(err) {\n      callback(err, data);\n    });\n  }\n\n  if (data.sourceType === 'buffer') {\n    append(null, source);\n  } else if (data.sourceType === 'stream' && data.stats) {\n    data.size = data.stats.size;\n\n    var entry = self.engine.entry(data, function(err) {\n      callback(err, data);\n    });\n\n    source.pipe(entry);\n  } else if (data.sourceType === 'stream') {\n    util.collectStream(source, append);\n  }\n};\n\n/**\n * [finalize description]\n *\n * @return void\n */\nTar.prototype.finalize = function() {\n  this.engine.finalize();\n};\n\n/**\n * [on description]\n *\n * @return this.engine\n */\nTar.prototype.on = function() {\n  return this.engine.on.apply(this.engine, arguments);\n};\n\n/**\n * [pipe description]\n *\n * @param  {String} destination\n * @param  {Object} options\n * @return this.engine\n */\nTar.prototype.pipe = function(destination, options) {\n  if (this.compressor) {\n    return this.engine.pipe.apply(this.engine, [this.compressor]).pipe(destination, options);\n  } else {\n    return this.engine.pipe.apply(this.engine, arguments);\n  }\n};\n\n/**\n * [unpipe description]\n *\n * @return this.engine\n */\nTar.prototype.unpipe = function() {\n  if (this.compressor) {\n    return this.compressor.unpipe.apply(this.compressor, arguments);\n  } else {\n    return this.engine.unpipe.apply(this.engine, arguments);\n  }\n};\n\nmodule.exports = Tar;\n\n/**\n * @typedef {Object} TarOptions\n * @global\n * @property {Boolean} [gzip=false] Compress the tar archive using gzip.\n * @property {Object} [gzipOptions] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n * to control compression.\n * @property {*} [*] See [tar-stream]{@link https://github.com/mafintosh/tar-stream} documentation for additional properties.\n */\n\n/**\n * @typedef {Object} TarEntryData\n * @global\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n */\n\n/**\n * TarStream Module\n * @external TarStream\n * @see {@link https://github.com/mafintosh/tar-stream}\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/archiver/lib/plugins/tar.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/archiver/lib/plugins/zip.js":
/*!**************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/zip.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * ZIP Format Plugin\n *\n * @module plugins/zip\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar engine = __webpack_require__(/*! zip-stream */ \"(rsc)/./node_modules/zip-stream/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(rsc)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {ZipOptions} [options]\n * @param {String} [options.comment] Sets the zip archive comment.\n * @param {Boolean} [options.forceLocalTime=false] Forces the archive to contain local file times instead of UTC.\n * @param {Boolean} [options.forceZip64=false] Forces the archive to contain ZIP64 headers.\n * @param {Boolean} [options.namePrependSlash=false] Prepends a forward slash to archive file paths.\n * @param {Boolean} [options.store=false] Sets the compression method to STORE.\n * @param {Object} [options.zlib] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n */\nvar Zip = function(options) {\n  if (!(this instanceof Zip)) {\n    return new Zip(options);\n  }\n\n  options = this.options = util.defaults(options, {\n    comment: '',\n    forceUTC: false,\n    namePrependSlash: false,\n    store: false\n  });\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.engine = new engine(options);\n};\n\n/**\n * @param  {(Buffer|Stream)} source\n * @param  {ZipEntryData} data\n * @param  {String} data.name Sets the entry name including internal path.\n * @param  {(String|Date)} [data.date=NOW()] Sets the entry date.\n * @param  {Number} [data.mode=D:0755/F:0644] Sets the entry permissions.\n * @param  {String} [data.prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @param  {fs.Stats} [data.stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n * @param  {Boolean} [data.store=ZipOptions.store] Sets the compression method to STORE.\n * @param  {Function} callback\n * @return void\n */\nZip.prototype.append = function(source, data, callback) {\n  this.engine.entry(source, data, callback);\n};\n\n/**\n * @return void\n */\nZip.prototype.finalize = function() {\n  this.engine.finalize();\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.on = function() {\n  return this.engine.on.apply(this.engine, arguments);\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.pipe = function() {\n  return this.engine.pipe.apply(this.engine, arguments);\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.unpipe = function() {\n  return this.engine.unpipe.apply(this.engine, arguments);\n};\n\nmodule.exports = Zip;\n\n/**\n * @typedef {Object} ZipOptions\n * @global\n * @property {String} [comment] Sets the zip archive comment.\n * @property {Boolean} [forceLocalTime=false] Forces the archive to contain local file times instead of UTC.\n * @property {Boolean} [forceZip64=false] Forces the archive to contain ZIP64 headers.\n * @prpperty {Boolean} [namePrependSlash=false] Prepends a forward slash to archive file paths.\n * @property {Boolean} [store=false] Sets the compression method to STORE.\n * @property {Object} [zlib] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n * to control compression.\n * @property {*} [*] See [zip-stream]{@link https://archiverjs.com/zip-stream/ZipStream.html} documentation for current list of properties.\n */\n\n/**\n * @typedef {Object} ZipEntryData\n * @global\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {Boolean} [namePrependSlash=ZipOptions.namePrependSlash] Prepends a forward slash to archive file paths.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n * @property {Boolean} [store=ZipOptions.store] Sets the compression method to STORE.\n */\n\n/**\n * ZipStream Module\n * @external ZipStream\n * @see {@link https://www.archiverjs.com/zip-stream/ZipStream.html}\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/archiver/lib/plugins/zip.js\n");

/***/ })

};
;