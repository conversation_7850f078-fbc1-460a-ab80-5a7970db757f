'use client';

import { useState, useEffect, useRef } from 'react';
import { TimelineItem } from '@/lib/dynamicScheduler';

interface DynamicTimelineProps {
  timeline: TimelineItem[];
  onTimelineUpdate: (newTimeline: TimelineItem[]) => void;
  onItemEdit: (itemId: string, newDuration: string) => void;
  onItemDelete: (itemId: string) => void;
  onBreakContentAdd: (breakId: string, mediaItemId: string) => void;
  availableBreakMedia: any[];
  isEditable?: boolean;
}

export default function DynamicTimeline({
  timeline,
  onTimelineUpdate,
  onItemEdit,
  onItemDelete,
  onBreakContentAdd,
  availableBreakMedia,
  isEditable = true
}: DynamicTimelineProps) {
  const [draggedItem, setDraggedItem] = useState<any>(null);
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editDuration, setEditDuration] = useState('');
  const timelineRef = useRef<HTMLDivElement>(null);

  // حساب العرض الكلي للجدول (24 ساعة = 1440 دقيقة)
  const totalMinutes = 24 * 60;
  const timelineWidth = 1400; // عرض أكبر للوضوح

  // تحويل الوقت إلى موضع على الجدول
  const timeToPosition = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    let totalMinutesFromStart = (hours - 8) * 60 + minutes; // بداية من الساعة 8

    // معالجة الأوقات بعد منتصف الليل (اليوم التالي)
    if (hours < 8) {
      totalMinutesFromStart = (hours + 24 - 8) * 60 + minutes;
    }

    return (totalMinutesFromStart / totalMinutes) * timelineWidth;
  };

  // حساب عرض العنصر بناءً على المدة
  const getItemWidth = (item: TimelineItem): number => {
    const [hours, minutes, seconds] = item.duration.split(':').map(Number);
    const durationMinutes = hours * 60 + minutes + seconds / 60;
    const width = (durationMinutes / totalMinutes) * timelineWidth;
    return Math.max(width, 80); // حد أدنى للعرض للوضوح
  };

  // معالجة بداية السحب من القائمة الجانبية
  const handleExternalDragStart = (item: any) => {
    setDraggedItem(item);
  };

  // معالجة بداية السحب من Timeline
  const handleTimelineDragStart = (e: React.DragEvent, item: TimelineItem) => {
    if (!isEditable) return;
    setDraggedItem(item);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', item.id);
  };

  // معالجة السحب فوق عنصر
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  // معالجة الإفلات
  const handleDrop = (e: React.DragEvent, targetItem: TimelineItem) => {
    e.preventDefault();
    if (!draggedItem || !isEditable) return;

    console.log('Drop detected:', { draggedItem, targetItem });

    if (targetItem.canAddContent && (targetItem.isBreak || targetItem.isGap)) {
      // إضافة مادة للفاصل
      console.log('Adding to break:', targetItem.id, draggedItem.id);
      onBreakContentAdd(targetItem.id, draggedItem.id);
    }

    setDraggedItem(null);
  };

  // بدء تعديل المدة
  const startEdit = (item: TimelineItem) => {
    if (!isEditable) return;
    setEditingItem(item.id);
    setEditDuration(item.duration);
  };

  // حفظ التعديل
  const saveEdit = () => {
    if (editingItem && editDuration) {
      onItemEdit(editingItem, editDuration);
    }
    setEditingItem(null);
    setEditDuration('');
  };

  // إلغاء التعديل
  const cancelEdit = () => {
    setEditingItem(null);
    setEditDuration('');
  };

  // تنسيق الوقت للعرض
  const formatTime = (time: string) => {
    return time.slice(0, 5); // HH:MM
  };

  // تنسيق المدة للعرض
  const formatDuration = (duration: string) => {
    const [hours, minutes, seconds] = duration.split(':').map(Number);
    if (hours > 0) {
      return `${hours}س ${minutes}د`;
    } else if (minutes > 0) {
      return `${minutes}د ${seconds}ث`;
    } else {
      return `${seconds}ث`;
    }
  };

  return (
    <div style={{
      background: 'white',
      borderRadius: '15px',
      padding: '20px',
      boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
      direction: 'rtl',
      fontFamily: 'Cairo, Arial, sans-serif'
    }}>
      <h2 style={{ color: '#2c3e50', marginBottom: '20px', fontSize: '1.3rem' }}>
        ⏰ الجدول الزمني التفاعلي (08:00 - 08:00)
      </h2>

      {/* مقياس الوقت */}
      <div style={{
        position: 'relative',
        height: '50px',
        background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
        borderRadius: '12px',
        marginBottom: '25px',
        border: '2px solid #dee2e6',
        overflow: 'hidden'
      }}>
        {Array.from({ length: 25 }, (_, i) => {
          const hour = (8 + i) % 24;
          const position = (i / 24) * timelineWidth;
          const isPrimeTime = (hour >= 18 && hour <= 23) || (hour >= 0 && hour <= 2);

          return (
            <div
              key={i}
              style={{
                position: 'absolute',
                left: `${position}px`,
                top: 0,
                height: '100%',
                width: `${timelineWidth / 24}px`,
                borderLeft: i === 0 ? 'none' : '1px solid #dee2e6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.85rem',
                color: isPrimeTime ? '#dc3545' : '#495057',
                fontWeight: isPrimeTime ? 'bold' : i % 6 === 0 ? 'bold' : 'normal',
                background: isPrimeTime ? 'rgba(220,53,69,0.1)' : 'transparent'
              }}
            >
              {hour.toString().padStart(2, '0')}:00
              {isPrimeTime && (
                <div style={{
                  position: 'absolute',
                  bottom: '2px',
                  fontSize: '0.6rem',
                  color: '#dc3545',
                  fontWeight: 'bold'
                }}>
                  Prime
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* الجدول الزمني */}
      <div
        ref={timelineRef}
        style={{
          position: 'relative',
          height: `${Math.max(400, timeline.length * 80 + 100)}px`,
          background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
          borderRadius: '12px',
          border: '2px solid #dee2e6',
          overflow: 'auto',
          padding: '20px',
          boxShadow: 'inset 0 2px 8px rgba(0,0,0,0.05)'
        }}
      >
        {timeline.map((item, index) => {
          const leftPosition = timeToPosition(item.startTime);
          const width = getItemWidth(item);
          const isEditing = editingItem === item.id;

          // تحديد لون الخلفية حسب النوع
          let backgroundColor = '#e3f2fd';
          let textColor = '#1565c0';

          if (item.type === 'SEGMENT') {
            backgroundColor = '#e8f5e8';
            textColor = '#2e7d32';
          } else if (item.isBreak) {
            backgroundColor = draggedItem && item.canAddContent ? '#ffeaa7' : '#fff3cd';
            textColor = '#856404';
          } else if (item.isGap) {
            backgroundColor = draggedItem && item.canAddContent ? '#ddd6fe' : '#f8f9fa';
            textColor = '#6c757d';
          }

          return (
            <div
              key={item.id}
              draggable={isEditable && !item.isGap}
              onDragStart={(e) => handleTimelineDragStart(e, item)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, item)}
              style={{
                position: 'absolute',
                left: `${leftPosition}px`,
                top: `${index * 70 + 20}px`,
                width: `${width}px`,
                height: '60px',
                background: backgroundColor,
                color: textColor,
                border: `3px solid ${item.status === 'يُعرض الآن' ? '#28a745' :
                                   item.status === 'منتهي' ? '#6c757d' : textColor}`,
                borderRadius: '12px',
                padding: '8px',
                cursor: isEditable ? 'pointer' : 'default',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                fontSize: '0.85rem',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                transition: 'all 0.2s ease',
                opacity: item.status === 'منتهي' ? 0.7 : 1,
                zIndex: isEditing ? 1000 : index
              }}
              onMouseEnter={(e) => {
                if (isEditable) {
                  e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
                  e.currentTarget.style.boxShadow = '0 6px 20px rgba(0,0,0,0.25)';
                }
              }}
              onMouseLeave={(e) => {
                if (isEditable) {
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
                }
              }}
              onClick={() => {
                if (isEditable && !isEditing) startEdit(item);
              }}
            >
              {/* محتوى العنصر */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                marginBottom: '4px'
              }}>
                {/* أيقونة النوع */}
                <span style={{ fontSize: '1.1rem' }}>
                  {item.type === 'SEGMENT' ? '🎬' :
                   item.isBreak ? '⏸️' :
                   item.isGap ? '⏳' : '📺'}
                </span>

                {/* رقم السيجمانت */}
                {item.segmentNumber && (
                  <span style={{
                    background: 'rgba(255,255,255,0.9)',
                    color: '#333',
                    padding: '2px 6px',
                    borderRadius: '8px',
                    fontSize: '0.7rem',
                    fontWeight: 'bold'
                  }}>
                    {item.segmentNumber}/{item.totalSegments}
                  </span>
                )}

                {/* اسم المادة */}
                <span style={{
                  fontWeight: 'bold',
                  fontSize: '0.85rem',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  flex: 1
                }}>
                  {item.name}
                </span>

                {/* مؤشر قابلية الإفلات */}
                {(item.isBreak || item.isGap) && item.canAddContent && (
                  <span style={{
                    fontSize: '0.7rem',
                    background: 'rgba(40,167,69,0.8)',
                    color: 'white',
                    padding: '1px 4px',
                    borderRadius: '4px',
                    fontWeight: 'bold'
                  }}>
                    {draggedItem ? '⬇️ أفلت هنا' : '📥 قابل للإضافة'}
                  </span>
                )}
              </div>

              {/* معلومات الوقت والمدة */}
              <div style={{
                fontSize: '0.75rem',
                opacity: 0.9,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                background: 'rgba(255,255,255,0.7)',
                padding: '2px 6px',
                borderRadius: '6px'
              }}>
                <span style={{ fontWeight: '500' }}>
                  {formatTime(item.startTime)} - {formatTime(item.endTime)}
                </span>
                <span style={{
                  background: 'rgba(0,0,0,0.1)',
                  padding: '1px 4px',
                  borderRadius: '4px',
                  fontWeight: 'bold'
                }}>
                  {formatDuration(item.duration)}
                </span>
              </div>

              {/* شريط التقدم */}
              {item.status === 'يُعرض الآن' && (
                <div style={{
                  position: 'absolute',
                  bottom: '2px',
                  left: '2px',
                  right: '2px',
                  height: '3px',
                  background: 'rgba(255,255,255,0.3)',
                  borderRadius: '2px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${item.progress}%`,
                    height: '100%',
                    background: '#28a745',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              )}

              {/* أزرار الإجراءات */}
              {isEditable && !isEditing && (
                <div style={{
                  position: 'absolute',
                  top: '2px',
                  right: '2px',
                  display: 'flex',
                  gap: '2px'
                }}>
                  {!item.isGap && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onItemDelete(item.id);
                      }}
                      style={{
                        background: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        padding: '2px 4px',
                        fontSize: '0.6rem',
                        cursor: 'pointer'
                      }}
                    >
                      🗑️
                    </button>
                  )}
                </div>
              )}

              {/* نموذج التعديل */}
              {isEditing && (
                <div style={{
                  position: 'absolute',
                  top: '0',
                  left: '0',
                  right: '0',
                  bottom: '0',
                  background: 'white',
                  border: '2px solid #007bff',
                  borderRadius: '8px',
                  padding: '5px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '5px'
                }}>
                  <input
                    type="text"
                    value={editDuration}
                    onChange={(e) => setEditDuration(e.target.value)}
                    placeholder="HH:MM:SS"
                    style={{
                      width: '100%',
                      padding: '2px',
                      border: '1px solid #ccc',
                      borderRadius: '3px',
                      fontSize: '0.8rem',
                      textAlign: 'center'
                    }}
                  />
                  <div style={{ display: 'flex', gap: '2px' }}>
                    <button
                      onClick={saveEdit}
                      style={{
                        flex: 1,
                        background: '#28a745',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        padding: '2px',
                        fontSize: '0.7rem',
                        cursor: 'pointer'
                      }}
                    >
                      ✓
                    </button>
                    <button
                      onClick={cancelEdit}
                      style={{
                        flex: 1,
                        background: '#dc3545',
                        color: 'white',
                        border: 'none',
                        borderRadius: '3px',
                        padding: '2px',
                        fontSize: '0.7rem',
                        cursor: 'pointer'
                      }}
                    >
                      ✗
                    </button>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* خط الوقت الحالي */}
        {(() => {
          const currentTime = new Date().toTimeString().slice(0, 5);
          const currentPosition = timeToPosition(currentTime);
          
          return (
            <div style={{
              position: 'absolute',
              left: `${currentPosition}px`,
              top: '0',
              bottom: '0',
              width: '2px',
              background: '#dc3545',
              zIndex: 1000,
              boxShadow: '0 0 5px rgba(220,53,69,0.5)'
            }}>
              <div style={{
                position: 'absolute',
                top: '-20px',
                left: '-30px',
                background: '#dc3545',
                color: 'white',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '0.7rem',
                fontWeight: 'bold'
              }}>
                {currentTime}
              </div>
            </div>
          );
        })()}
      </div>

      {/* معلومات الجدول */}
      <div style={{
        marginTop: '15px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '10px',
        background: '#f8f9fa',
        borderRadius: '8px',
        fontSize: '0.9rem'
      }}>
        <div>
          <strong>إجمالي العناصر:</strong> {timeline.length}
        </div>
        <div>
          <strong>السيجمانت:</strong> {timeline.filter(item => item.type === 'SEGMENT').length}
        </div>
        <div>
          <strong>الفواصل:</strong> {timeline.filter(item => item.isBreak || item.isGap).length}
        </div>
        <div>
          <strong>قيد العرض:</strong> {timeline.filter(item => item.status === 'يُعرض الآن').length}
        </div>
      </div>
    </div>
  );
}
