/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/admin-dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXERvYyBkYXRhYmFzZVxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin-dashboard/page.tsx */ \"(app-pages-browser)/./src/app/admin-dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0RvYyUyMGRhdGFiYXNlJTVDJTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDJTVDbWVkaWEtZGFzaGJvYXJkJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDYWRtaW4tZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4TEFBa0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXERvYyBkYXRhYmFzZVxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcYWRtaW4tZGFzaGJvYXJkXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxEb2MgZGF0YWJhc2VcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin-dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin-dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddUser, setShowAddUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        role: 'VIEWER'\n    });\n    const roles = {\n        'ADMIN': {\n            name: 'مدير النظام',\n            color: '#dc3545',\n            icon: '👑'\n        },\n        'MEDIA_MANAGER': {\n            name: 'مدير المحتوى',\n            color: '#28a745',\n            icon: '📝'\n        },\n        'SCHEDULER': {\n            name: 'مجدول البرامج',\n            color: '#007bff',\n            icon: '📅'\n        },\n        'VIEWER': {\n            name: 'مستخدم عرض',\n            color: '#6c757d',\n            icon: '👁️'\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchUsers();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch('/api/users');\n            const result = await response.json();\n            if (result.success) {\n                setUsers(result.users);\n            } else {\n                showToast('خطأ في جلب بيانات المستخدمين', 'error');\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n            showToast('خطأ في الاتصال بالخادم', 'error');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddUser = async (e)=>{\n        e.preventDefault();\n        if (!newUser.username || !newUser.password || !newUser.name) {\n            showToast('يرجى ملء جميع الحقول المطلوبة', 'error');\n            return;\n        }\n        try {\n            const response = await fetch('/api/users', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newUser)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم إنشاء المستخدم بنجاح!', 'success');\n                setUsers([\n                    ...users,\n                    result.user\n                ]);\n                setShowAddUser(false);\n                setNewUser({\n                    username: '',\n                    password: '',\n                    name: '',\n                    email: '',\n                    role: 'VIEWER'\n                });\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error adding user:', error);\n            showToast('خطأ في إنشاء المستخدم', 'error');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) return;\n        try {\n            const response = await fetch(\"/api/users?id=\".concat(userId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                showToast('تم حذف المستخدم بنجاح!', 'success');\n                setUsers(users.filter((u)=>u.id !== userId));\n            } else {\n                showToast('خطأ: ' + result.error, 'error');\n            }\n        } catch (error) {\n            console.error('Error deleting user:', error);\n            showToast('خطأ في حذف المستخدم', 'error');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: 'rtl',\n        outline: 'none'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"⏳ جاري تحميل البيانات...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredRole: \"ADMIN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                padding: '20px',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'white',\n                                borderRadius: '20px',\n                                padding: '30px',\n                                marginBottom: '20px',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.1)',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                fontSize: '1.8rem',\n                                                fontWeight: '900',\n                                                fontFamily: 'Arial, sans-serif',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent',\n                                                        fontWeight: '900',\n                                                        fontSize: '2rem'\n                                                    },\n                                                    children: \"X\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        color: '#6c757d',\n                                                        fontSize: '1.5rem',\n                                                        fontWeight: '300'\n                                                    },\n                                                    children: \"-\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent',\n                                                        fontWeight: '800',\n                                                        letterSpacing: '1px'\n                                                    },\n                                                    children: \"Prime\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        color: '#333',\n                                                        margin: '0 0 10px 0',\n                                                        background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: \"\\uD83D\\uDC51 لوحة تحكم المدير\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#6c757d',\n                                                        margin: 0\n                                                    },\n                                                    children: [\n                                                        \"مرحباً \",\n                                                        user === null || user === void 0 ? void 0 : user.name,\n                                                        \" - إدارة المستخدمين والصلاحيات\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '10px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push('/'),\n                                            style: {\n                                                background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '10px',\n                                                padding: '10px 20px',\n                                                cursor: 'pointer',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"\\uD83C\\uDFE0 الرئيسية\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: logout,\n                                            style: {\n                                                background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '10px',\n                                                padding: '10px 20px',\n                                                cursor: 'pointer',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: \"\\uD83D\\uDEAA تسجيل الخروج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: Object.entries(roles).map((param)=>{\n                                let [roleKey, roleInfo] = param;\n                                const count = users.filter((u)=>u.role === roleKey).length;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: 'white',\n                                        borderRadius: '15px',\n                                        padding: '25px',\n                                        boxShadow: '0 5px 15px rgba(0,0,0,0.1)',\n                                        textAlign: 'center',\n                                        border: \"3px solid \".concat(roleInfo.color, \"20\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2.5rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: roleInfo.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: roleInfo.color,\n                                                margin: '0 0 5px 0',\n                                                fontSize: '1.2rem'\n                                            },\n                                            children: roleInfo.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                fontWeight: 'bold',\n                                                color: '#333'\n                                            },\n                                            children: count\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#6c757d',\n                                                fontSize: '0.9rem',\n                                                margin: 0\n                                            },\n                                            children: \"مستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, roleKey, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'white',\n                                borderRadius: '20px',\n                                padding: '30px',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.1)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '25px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            style: {\n                                                color: '#333',\n                                                fontSize: '1.5rem',\n                                                margin: 0\n                                            },\n                                            children: [\n                                                \"\\uD83D\\uDC65 إدارة المستخدمين (\",\n                                                users.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAddUser(true),\n                                            style: {\n                                                background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '10px',\n                                                padding: '12px 20px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: \"➕ إضافة مستخدم جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                showAddUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#f8f9fa',\n                                        borderRadius: '15px',\n                                        padding: '25px',\n                                        marginBottom: '25px',\n                                        border: '2px solid #e9ecef'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            style: {\n                                                color: '#333',\n                                                marginBottom: '20px'\n                                            },\n                                            children: \"➕ إضافة مستخدم جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleAddUser,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'grid',\n                                                        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                                        gap: '15px',\n                                                        marginBottom: '20px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"اسم المستخدم *\",\n                                                            value: newUser.username,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    username: e.target.value\n                                                                }),\n                                                            style: inputStyle,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            placeholder: \"كلمة المرور *\",\n                                                            value: newUser.password,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    password: e.target.value\n                                                                }),\n                                                            style: inputStyle,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"الاسم الكامل *\",\n                                                            value: newUser.name,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    name: e.target.value\n                                                                }),\n                                                            style: inputStyle,\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            placeholder: \"البريد الإلكتروني\",\n                                                            value: newUser.email,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    email: e.target.value\n                                                                }),\n                                                            style: inputStyle\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: newUser.role,\n                                                            onChange: (e)=>setNewUser({\n                                                                    ...newUser,\n                                                                    role: e.target.value\n                                                                }),\n                                                            style: inputStyle,\n                                                            children: Object.entries(roles).map((param)=>{\n                                                                let [key, role] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: role.name\n                                                                }, key, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        gap: '10px'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            style: {\n                                                                background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '8px',\n                                                                padding: '10px 20px',\n                                                                cursor: 'pointer'\n                                                            },\n                                                            children: \"✅ إنشاء المستخدم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>setShowAddUser(false),\n                                                            style: {\n                                                                background: '#6c757d',\n                                                                color: 'white',\n                                                                border: 'none',\n                                                                borderRadius: '8px',\n                                                                padding: '10px 20px',\n                                                                cursor: 'pointer'\n                                                            },\n                                                            children: \"❌ إلغاء\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        overflowX: 'auto',\n                                        borderRadius: '10px',\n                                        border: '1px solid #e9ecef'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        style: {\n                                            width: '100%',\n                                            borderCollapse: 'collapse',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    style: {\n                                                        background: '#f8f9fa'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'right',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"المستخدم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"الدور\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"الحالة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"تاريخ الإنشاء\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"آخر دخول\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '15px',\n                                                                textAlign: 'center',\n                                                                borderBottom: '2px solid #dee2e6'\n                                                            },\n                                                            children: \"الإجراءات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: users.map((user)=>{\n                                                    var _roles_user_role, _roles_user_role1, _roles_user_role2, _roles_user_role3;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        style: {\n                                                            borderBottom: '1px solid #e9ecef',\n                                                            transition: 'background-color 0.2s'\n                                                        },\n                                                        onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f8f9fa',\n                                                        onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                fontWeight: 'bold',\n                                                                                color: '#333',\n                                                                                marginBottom: '5px'\n                                                                            },\n                                                                            children: user.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                color: '#6c757d',\n                                                                                fontSize: '0.85rem'\n                                                                            },\n                                                                            children: [\n                                                                                \"@\",\n                                                                                user.username\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        user.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                color: '#6c757d',\n                                                                                fontSize: '0.8rem'\n                                                                            },\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCE7 \",\n                                                                                user.email\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 474,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        background: ((_roles_user_role = roles[user.role]) === null || _roles_user_role === void 0 ? void 0 : _roles_user_role.color) + '20',\n                                                                        color: (_roles_user_role1 = roles[user.role]) === null || _roles_user_role1 === void 0 ? void 0 : _roles_user_role1.color,\n                                                                        padding: '5px 12px',\n                                                                        borderRadius: '20px',\n                                                                        fontSize: '0.85rem',\n                                                                        fontWeight: 'bold',\n                                                                        display: 'inline-flex',\n                                                                        alignItems: 'center',\n                                                                        gap: '5px'\n                                                                    },\n                                                                    children: [\n                                                                        (_roles_user_role2 = roles[user.role]) === null || _roles_user_role2 === void 0 ? void 0 : _roles_user_role2.icon,\n                                                                        (_roles_user_role3 = roles[user.role]) === null || _roles_user_role3 === void 0 ? void 0 : _roles_user_role3.name\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 489,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        background: user.isActive ? '#28a74520' : '#dc354520',\n                                                                        color: user.isActive ? '#28a745' : '#dc3545',\n                                                                        padding: '5px 12px',\n                                                                        borderRadius: '20px',\n                                                                        fontSize: '0.85rem',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: user.isActive ? '✅ نشط' : '❌ معطل'\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center',\n                                                                    color: '#6c757d'\n                                                                },\n                                                                children: new Date(user.createdAt).toLocaleDateString('ar-EG')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center',\n                                                                    color: '#6c757d'\n                                                                },\n                                                                children: user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-EG') : 'لم يدخل بعد'\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                style: {\n                                                                    padding: '15px',\n                                                                    textAlign: 'center'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: 'flex',\n                                                                        gap: '5px',\n                                                                        justifyContent: 'center'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setEditingUser(user),\n                                                                            style: {\n                                                                                background: '#007bff',\n                                                                                color: 'white',\n                                                                                border: 'none',\n                                                                                borderRadius: '5px',\n                                                                                padding: '5px 10px',\n                                                                                cursor: 'pointer',\n                                                                                fontSize: '0.8rem'\n                                                                            },\n                                                                            title: \"تعديل\",\n                                                                            children: \"✏️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        user.id !== '1' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleDeleteUser(user.id),\n                                                                            style: {\n                                                                                background: '#dc3545',\n                                                                                color: 'white',\n                                                                                border: 'none',\n                                                                                borderRadius: '5px',\n                                                                                padding: '5px 10px',\n                                                                                cursor: 'pointer',\n                                                                                fontSize: '0.8rem'\n                                                                            },\n                                                                            title: \"حذف\",\n                                                                            children: \"\\uD83D\\uDDD1️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, user.id, true, {\n                                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 13\n                                }, this),\n                                users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '40px',\n                                        color: '#6c757d'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '3rem',\n                                                marginBottom: '15px'\n                                            },\n                                            children: \"\\uD83D\\uDC65\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"لا توجد مستخدمين\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"ابدأ بإضافة مستخدم جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 577,\n                    columnNumber: 9\n                }, this),\n                editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: 0,\n                        left: 0,\n                        right: 0,\n                        bottom: 0,\n                        background: 'rgba(0,0,0,0.5)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        zIndex: 1000\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: 'white',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            width: '400px',\n                            maxWidth: '90vw'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginBottom: '20px',\n                                    color: '#333'\n                                },\n                                children: \"تعديل المستخدم\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"اسم المستخدم:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingUser.username,\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                username: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"الاسم الكامل:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: editingUser.name,\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                name: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        style: {\n                                            display: 'block',\n                                            marginBottom: '5px',\n                                            color: '#333'\n                                        },\n                                        children: \"الدور:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 635,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: editingUser.role,\n                                        onChange: (e)=>setEditingUser({\n                                                ...editingUser,\n                                                role: e.target.value\n                                            }),\n                                        style: {\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ddd',\n                                            borderRadius: '5px',\n                                            direction: 'rtl'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"ADMIN\",\n                                                children: \"مدير\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MEDIA_MANAGER\",\n                                                children: \"مدير المواد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SCHEDULER\",\n                                                children: \"مجدول\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"VIEWER\",\n                                                children: \"مشاهد\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    gap: '10px',\n                                    justifyContent: 'flex-end'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingUser(null),\n                                        style: {\n                                            background: '#6c757d',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            padding: '10px 20px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"إلغاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            try {\n                                                const response = await fetch('/api/users', {\n                                                    method: 'PUT',\n                                                    headers: {\n                                                        'Content-Type': 'application/json'\n                                                    },\n                                                    body: JSON.stringify(editingUser)\n                                                });\n                                                if (response.ok) {\n                                                    showToast('تم تحديث المستخدم بنجاح', 'success');\n                                                    setEditingUser(null);\n                                                    fetchUsers();\n                                                } else {\n                                                    showToast('خطأ في تحديث المستخدم', 'error');\n                                                }\n                                            } catch (error) {\n                                                showToast('خطأ في الاتصال', 'error');\n                                            }\n                                        },\n                                        style: {\n                                            background: '#28a745',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '5px',\n                                            padding: '10px 20px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"حفظ\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 654,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 705,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"QUoxQpgLP3uCmSEx0ZC3Aom6ph4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _components_Toast__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction AuthGuard(param) {\n    let { children, requiredPermissions = [], requiredRole, fallbackComponent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        // المدير له صلاحيات كاملة\n        if (user.role === 'ADMIN') {\n            return true;\n        }\n        // التحقق من الدور المطلوب\n        if (role && user.role !== role) {\n            return false;\n        }\n        // التحقق من الصلاحيات المطلوبة\n        if (permissions.length > 0) {\n            const userPermissions = getUserPermissions(user.role);\n            return permissions.every((permission)=>userPermissions.includes(permission) || userPermissions.includes('ALL'));\n        }\n        return true;\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user === null || user === void 0 ? void 0 : user.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user === null || user === void 0 ? void 0 : user.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AuthGuard, \"fqF8YvhaHbrPIfzSUHTCm0cPUfQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthGuard;\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        if (!user) return false;\n        if (user.role === 'ADMIN') return true;\n        const userPermissions = getUserPermissions(user.role);\n        return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN',\n        isMediaManager: (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER',\n        isScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER',\n        isViewer: (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER'\n    };\n}\n_s1(useAuth, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AuthGuard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nfunction Toast(param) {\n    let { message, type, duration = 3000, onClose } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن\n                }\n            }[\"Toast.useEffect.timer\"], duration);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        duration,\n        onClose\n    ]);\n    const getToastStyles = ()=>{\n        const baseStyles = {\n            position: 'relative',\n            padding: '15px 20px',\n            borderRadius: '10px',\n            color: 'white',\n            fontWeight: 'bold',\n            fontSize: '1rem',\n            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n            transform: isVisible ? 'translateX(0)' : 'translateX(100%)',\n            transition: 'transform 0.3s ease, opacity 0.3s ease',\n            opacity: isVisible ? 1 : 0,\n            minWidth: '300px',\n            maxWidth: '500px',\n            direction: 'rtl',\n            fontFamily: 'Cairo, Arial, sans-serif'\n        };\n        const typeStyles = {\n            success: {\n                background: 'linear-gradient(45deg, #28a745, #20c997)'\n            },\n            error: {\n                background: 'linear-gradient(45deg, #dc3545, #c82333)'\n            },\n            warning: {\n                background: 'linear-gradient(45deg, #ffc107, #e0a800)'\n            },\n            info: {\n                background: 'linear-gradient(45deg, #007bff, #0056b3)'\n            }\n        };\n        return {\n            ...baseStyles,\n            ...typeStyles[type]\n        };\n    };\n    const getIcon = ()=>{\n        const icons = {\n            success: '✅',\n            error: '❌',\n            warning: '⚠️',\n            info: 'ℹ️'\n        };\n        return icons[type];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: getToastStyles(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '10px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        fontSize: '1.2rem'\n                    },\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setIsVisible(false);\n                        setTimeout(onClose, 300);\n                    },\n                    style: {\n                        background: 'rgba(255,255,255,0.2)',\n                        border: 'none',\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '25px',\n                        height: '25px',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        marginLeft: 'auto'\n                    },\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(Toast, \"m22S9IQwDfEe/fCJY7LYj8YPDMo=\");\n_c = Toast;\n// Hook لاستخدام Toast\nfunction useToast() {\n    _s1();\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        // التحقق من عدم وجود رسالة مشابهة\n        const existingToast = toasts.find((toast)=>toast.message === message && toast.type === type);\n        if (existingToast) {\n            return; // لا تضيف رسالة مكررة\n        }\n        // إنشاء ID فريد باستخدام timestamp + random number\n        const id = \"toast_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        // حد أقصى 5 رسائل في نفس الوقت\n        setToasts((prev)=>prev.slice(-4)); // احتفظ بآخر 4 + الجديدة = 5\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const ToastContainer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: 'fixed',\n                top: '20px',\n                right: '20px',\n                zIndex: 1000,\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '10px'\n            },\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                    message: toast.message,\n                    type: toast.type,\n                    onClose: ()=>removeToast(toast.id)\n                }, toast.id, false, {\n                    fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\Doc database\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 129,\n            columnNumber: 5\n        }, this);\n    return {\n        showToast,\n        ToastContainer\n    };\n}\n_s1(useToast, \"nD8TBOiFYf9ajstmZpZK2DP4rNo=\");\nvar _c;\n$RefreshReg$(_c, \"Toast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Toast.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CDoc%20database%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cadmin-dashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);