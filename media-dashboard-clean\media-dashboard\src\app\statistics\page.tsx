'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  status: string;
  segments: any[];
  createdAt: string;
}

interface Statistics {
  totalItems: number;
  byType: { [key: string]: number };
  byStatus: { [key: string]: number };
  totalSegments: number;
  recentItems: MediaItem[];
}

export default function StatisticsPage() {
  const [statistics, setStatistics] = useState<Statistics>({
    totalItems: 0,
    byType: {},
    byStatus: {},
    totalSegments: 0,
    recentItems: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      const response = await fetch('/api/media');
      const result = await response.json();

      if (result.success) {
        const items = result.data;
        
        // حساب الإحصائيات
        const stats: Statistics = {
          totalItems: items.length,
          byType: {},
          byStatus: {},
          totalSegments: 0,
          recentItems: items.slice(0, 5)
        };

        // إحصائيات الأنواع
        items.forEach((item: MediaItem) => {
          stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;
          stats.byStatus[item.status] = (stats.byStatus[item.status] || 0) + 1;
          stats.totalSegments += item.segments?.length || 0;
        });

        setStatistics(stats);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeLabel = (type: string) => {
    const types: { [key: string]: string } = {
      PROGRAM: 'برنامج',
      SERIES: 'مسلسل',
      MOVIE: 'فيلم',
      SONG: 'أغنية',
      STING: 'Sting',
      FILL_IN: 'Fill IN',
      FILLER: 'Filler',
      PROMO: 'Promo'
    };
    return types[type] || type;
  };

  const getStatusLabel = (status: string) => {
    const statuses: { [key: string]: string } = {
      VALID: 'صالح',
      REJECTED_CENSORSHIP: 'مرفوض رقابي',
      REJECTED_TECHNICAL: 'مرفوض هندسي',
      WAITING: 'في الانتظار'
    };
    return statuses[status] || status;
  };

  const getTypeIcon = (type: string) => {
    const icons: { [key: string]: string } = {
      PROGRAM: '📺',
      SERIES: '🎭',
      MOVIE: '🎬',
      SONG: '🎵',
      STING: '⚡',
      FILL_IN: '🔄',
      FILLER: '📦',
      PROMO: '📢'
    };
    return icons[type] || '📺';
  };

  if (loading) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ color: 'white', fontSize: '1.5rem' }}>⏳ جاري تحميل الإحصائيات...</div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '20px',
          padding: '30px',
          marginBottom: '30px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
          textAlign: 'center'
        }}>
          <h1 style={{ 
            color: '#2c3e50', 
            marginBottom: '20px', 
            fontSize: '2.5rem',
            fontWeight: 'bold'
          }}>
            📊 إحصائيات النظام
          </h1>
          
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Link href="/" style={{
              background: 'linear-gradient(45deg, #007bff, #0056b3)',
              color: 'white',
              padding: '12px 25px',
              borderRadius: '25px',
              textDecoration: 'none',
              fontWeight: 'bold',
              boxShadow: '0 4px 15px rgba(0,123,255,0.3)'
            }}>
              🏠 الرئيسية
            </Link>
            
            <Link href="/media-list" style={{
              background: 'linear-gradient(45deg, #28a745, #20c997)',
              color: 'white',
              padding: '12px 25px',
              borderRadius: '25px',
              textDecoration: 'none',
              fontWeight: 'bold',
              boxShadow: '0 4px 15px rgba(40,167,69,0.3)'
            }}>
              📚 قائمة المواد
            </Link>
          </div>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px', marginBottom: '30px' }}>
          <div style={{
            background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
            borderRadius: '15px',
            padding: '25px',
            color: 'white',
            textAlign: 'center',
            boxShadow: '0 8px 25px rgba(40,167,69,0.3)'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '10px' }}>📺</div>
            <h3 style={{ margin: '0 0 10px 0', fontSize: '1.2rem' }}>إجمالي المواد</h3>
            <p style={{ margin: 0, fontSize: '2rem', fontWeight: 'bold' }}>{statistics.totalItems}</p>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
            borderRadius: '15px',
            padding: '25px',
            color: 'white',
            textAlign: 'center',
            boxShadow: '0 8px 25px rgba(0,123,255,0.3)'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '10px' }}>🎬</div>
            <h3 style={{ margin: '0 0 10px 0', fontSize: '1.2rem' }}>إجمالي السيجمانت</h3>
            <p style={{ margin: 0, fontSize: '2rem', fontWeight: 'bold' }}>{statistics.totalSegments}</p>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)',
            borderRadius: '15px',
            padding: '25px',
            color: 'white',
            textAlign: 'center',
            boxShadow: '0 8px 25px rgba(255,193,7,0.3)'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '10px' }}>📈</div>
            <h3 style={{ margin: '0 0 10px 0', fontSize: '1.2rem' }}>أنواع مختلفة</h3>
            <p style={{ margin: 0, fontSize: '2rem', fontWeight: 'bold' }}>{Object.keys(statistics.byType).length}</p>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
            borderRadius: '15px',
            padding: '25px',
            color: 'white',
            textAlign: 'center',
            boxShadow: '0 8px 25px rgba(220,53,69,0.3)'
          }}>
            <div style={{ fontSize: '3rem', marginBottom: '10px' }}>⚡</div>
            <h3 style={{ margin: '0 0 10px 0', fontSize: '1.2rem' }}>متوسط السيجمانت</h3>
            <p style={{ margin: 0, fontSize: '2rem', fontWeight: 'bold' }}>
              {statistics.totalItems > 0 ? Math.round(statistics.totalSegments / statistics.totalItems) : 0}
            </p>
          </div>
        </div>

        {/* إحصائيات الأنواع */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '20px',
          padding: '25px',
          marginBottom: '25px',
          boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ color: '#2c3e50', marginBottom: '20px', fontSize: '1.5rem' }}>
            📊 توزيع المواد حسب النوع
          </h2>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            {Object.entries(statistics.byType).map(([type, count]) => (
              <div key={type} style={{
                background: '#f8f9fa',
                borderRadius: '10px',
                padding: '15px',
                textAlign: 'center',
                border: '2px solid #e9ecef'
              }}>
                <div style={{ fontSize: '2rem', marginBottom: '10px' }}>{getTypeIcon(type)}</div>
                <h4 style={{ margin: '0 0 5px 0', color: '#495057' }}>{getTypeLabel(type)}</h4>
                <p style={{ margin: 0, fontSize: '1.5rem', fontWeight: 'bold', color: '#007bff' }}>{count}</p>
              </div>
            ))}
          </div>
        </div>

        {/* إحصائيات الحالات */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '20px',
          padding: '25px',
          marginBottom: '25px',
          boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
        }}>
          <h2 style={{ color: '#2c3e50', marginBottom: '20px', fontSize: '1.5rem' }}>
            ✅ توزيع المواد حسب الحالة
          </h2>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            {Object.entries(statistics.byStatus).map(([status, count]) => (
              <div key={status} style={{
                background: status === 'VALID' ? '#d4edda' : status.includes('REJECTED') ? '#f8d7da' : '#fff3cd',
                borderRadius: '10px',
                padding: '15px',
                textAlign: 'center',
                border: `2px solid ${status === 'VALID' ? '#28a745' : status.includes('REJECTED') ? '#dc3545' : '#ffc107'}`
              }}>
                <h4 style={{ margin: '0 0 5px 0', color: '#495057' }}>{getStatusLabel(status)}</h4>
                <p style={{ margin: 0, fontSize: '1.5rem', fontWeight: 'bold', 
                  color: status === 'VALID' ? '#28a745' : status.includes('REJECTED') ? '#dc3545' : '#ffc107' 
                }}>{count}</p>
              </div>
            ))}
          </div>
        </div>

        {/* المواد الحديثة */}
        {statistics.recentItems.length > 0 && (
          <div style={{
            background: 'rgba(255,255,255,0.95)',
            borderRadius: '20px',
            padding: '25px',
            boxShadow: '0 8px 25px rgba(0,0,0,0.1)'
          }}>
            <h2 style={{ color: '#2c3e50', marginBottom: '20px', fontSize: '1.5rem' }}>
              🕒 المواد المضافة حديثاً
            </h2>
            
            <div style={{ display: 'grid', gap: '15px' }}>
              {statistics.recentItems.map((item) => (
                <div key={item.id} style={{
                  background: '#f8f9fa',
                  borderRadius: '10px',
                  padding: '15px',
                  border: '1px solid #e9ecef',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div>
                    <h4 style={{ margin: '0 0 5px 0', color: '#495057' }}>{item.name}</h4>
                    <p style={{ margin: 0, color: '#6c757d', fontSize: '0.9rem' }}>
                      {getTypeIcon(item.type)} {getTypeLabel(item.type)} • {item.segments?.length || 0} سيجمانت
                    </p>
                  </div>
                  <div style={{
                    background: item.status === 'VALID' ? '#28a745' : item.status.includes('REJECTED') ? '#dc3545' : '#ffc107',
                    color: 'white',
                    padding: '5px 10px',
                    borderRadius: '15px',
                    fontSize: '0.8rem'
                  }}>
                    {getStatusLabel(item.status)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
